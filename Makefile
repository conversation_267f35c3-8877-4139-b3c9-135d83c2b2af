VERSION = $(shell cd server && python3 -Wignore setup.py --version)

# We can use either Docker Compose v1 (docker-compose), or v2 (docker compose)
DOCKER_COMPOSE = docker compose

.PHONY: build
build:
	VERSION=$(VERSION) $(DOCKER_COMPOSE) build

.PHONY: rebuild
rebuild:
	$(DOCKER_COMPOSE) pull
	VERSION=$(VERSION) $(DOCKER_COMPOSE) build --pull --no-cache

.PHONY: help
help:
	@echo "make build               build docker-compose images for 'make run'"
	@echo "make rebuild             pull and build docker-compose images ignoring caches"
	@echo "make run                 start BolDataAPI locally with docker-compose"
	@echo "make swagger-ui          run Swagger API doc viewer on http://localhost:8123/"
	@echo "make auth-token          get a valid auth token from Alpha Gluu for local testing"
	@echo "make stop                stop docker containers started by 'make run'"
	@echo "make down                stop and remove docker containers started by 'make run'"
	@echo "make test                run unit tests"
	@echo "make coverage            measure unit test coverage"
	@echo "make diff-cover          check if all changes of this branch have test coverage"
	@echo "make lint                run linters"
	@echo "make flake8              run flake8 linter"
	@echo "make mypy                run mypy linter"
	@echo "make requirements        update server/requirements*.txt from server/requirements/*.in"
	@echo "make clean               clean all built things"
	@echo "make clean-db            clean all docker volumes containing databases used for tests"
	@echo "make tags                build ctags for server code and all the dependencies"

.PHONY: run stop down test lint flake8 mypy coverage diff-cover requirements
run swagger-ui stop down test lint flake8 mypy coverage diff-cover requirements:
	$(MAKE) -C server $@

# NB: you need a VPN to talk to auth-azure-alpha
GLUU_SERVER = https://auth-azure-alpha.id06.se
CLIENTID = @!D0B3.42FF.3A77.681D!0001!0105.03F6!0008!6214.003E
CLIENTSECRET = eejiw1ooreet3ohK
SCOPES = $(shell cat server/boldataapi/controllers/*.py| grep scopes.: | sort -u | cut -d "'" -f 4 | tr '\n' ' ')

.PHONY: auth-token
auth-token:
	curl -sS --user "$(CLIENTID):$(CLIENTSECRET)" --data "grant_type=client_credentials&scope=$(SCOPES)" $(GLUU_SERVER)/oxauth/restv1/token

.PHONY: test-integration
test-integration:
	$(MAKE) -C integration_tests test

.PHONY: clean
clean:
	-$(DOCKER_COMPOSE) stop
	$(MAKE) -C integration_tests clean
	$(MAKE) -C server clean

.PHONY: clean-db
clean-db:
	$(MAKE) -C integration_tests db-test-clean
	$(MAKE) -C server clean-db
	$(MAKE) -C server clean-test-db

.PHONY: tags
tags:
	ctags -R server/boldataapi server/env/lib server/tests
