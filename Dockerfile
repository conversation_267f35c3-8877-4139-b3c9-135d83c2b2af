#
# Stage 1: build virtualenv
#
FROM debian:bookworm AS build
  # bookworm is the codename for Debian 12

RUN set -x \
  && apt-get update -qq \
  && DEBIAN_FRONTEND=noninteractive apt-get dist-upgrade -qq -y \
  && DEBIAN_FRONTEND=noninteractive apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-all \
        build-essential \
        libssl-dev \
        libpq-dev \
        libjpeg-dev \
        libffi-dev \
        libxml2-dev \
        libxslt1-dev \
  && apt-get autoremove -y \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

RUN PIP_BREAK_SYSTEM_PACKAGES=1 pip3 install virtualenv
RUN virtualenv -p python3 /opt/app/env
RUN /opt/app/env/bin/pip install -U pip setuptools build

#prepare
RUN mkdir -p /opt/app/src
COPY ./server /opt/app/src/server

#code check, unit tests, build
WORKDIR /opt/app/src/server
RUN rm -rf .tox/ dist/ && py3clean .

ARG VERSION
RUN SETUPTOOLS_SCM_PRETEND_VERSION=$VERSION /opt/app/env/bin/python -m build --wheel

#install
RUN /opt/app/env/bin/pip install -f dist/ -f vendor/ -r requirements.txt bol-data-api


#
# Stage 2: build image
#
FROM debian:bookworm

RUN set -x \
  && apt-get update -qq \
  && DEBIAN_FRONTEND=noninteractive apt-get dist-upgrade -qq -y \
  && DEBIAN_FRONTEND=noninteractive apt-get install -y \
        python3 \
        python3-pip \
        nginx uwsgi \
        uwsgi-plugin-python3 \
        curl \
        wget \
  && apt-get autoremove -y \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

RUN mkdir -p /opt/app
COPY --from=build /opt/app/env /opt/app/env

#take build number from CI pipeline
ARG build
ENV BUILD_NUMBER=$build
ARG APP_PORT=8000
ENV newrelic_license_key=
ENV NEW_RELIC_CONFIG_FILE=/etc/newrelic.ini
ENV NEW_RELIC_ENVIRONMENT=dev

#copy infra files
COPY ./infrastructure/boldataapi.cfg /etc/boldataapi.cfg
COPY ./infrastructure/newrelic.ini /etc/newrelic.ini
COPY ./infrastructure/uwsgi/uwsgi.ini /etc/uwsgi/apps-enabled/bol-data-api-uwsgi.ini
COPY ./infrastructure/nginx/nginx.conf /etc/nginx/nginx.conf
RUN sed -i -e "s/PORT/${APP_PORT}/g" /etc/nginx/nginx.conf
COPY ./infrastructure/run.sh /run.sh
RUN chmod +x /run.sh

#copy Alembic migrations config and migrations
RUN mkdir /opt/app/alembic
COPY ./db/alembic/alembic.ini /opt/app/alembic/
COPY ./db/alembic/migrations /opt/app/alembic/migrations
COPY ./db/alembic/requirements.txt /opt/app/alembic/requirements.txt
# Install packages required by the migrations
RUN PIP_BREAK_SYSTEM_PACKAGES=1 pip3 install -r /opt/app/alembic/requirements.txt

#prepare user account
RUN addgroup --gid 1000 bda && adduser --system --uid 1000 --gid 1000 --disabled-password bda

# nginx starts up as user bda, not as root
RUN chown -R bda:bda /var/lib/nginx/

#execute
USER 1000:1000
ENV PYTHONUNBUFFERED=1
ENTRYPOINT ["/run.sh"]
EXPOSE ${APP_PORT}
CMD ["uwsgi", "--ini", "/etc/uwsgi/apps-enabled/bol-data-api-uwsgi.ini", "--pyargv", "--config /etc/boldataapi.cfg"]
