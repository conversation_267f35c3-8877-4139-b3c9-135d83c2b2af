# BOL Data API


## Local development

On macOS, have Python 3.11 installed and start off in a local `env` with virtualenv:

    brew install python@3.11
    python3.11 -m venv env
    source env/bin/activate
    pip3 install virtualenv

### Build BOLDataAPI locally from CLI

Execute the following statement in the project root directory

    make build

### Start BOLDataAPI locally from CLI

Execute the following statement in the root directory

    make run

BOLDataAPI will respond in http://localhost:9700/api/v1/boldata/, e.g.

    $ http get http://localhost:9700/api/v1/boldata/version
    HTTP/1.1 200 OK
    Connection: keep-alive
    Content-Length: 20
    Content-Type: application/json
    Date: Fri, 29 May 2020 09:28:39 GMT
    Server: nginx

    {
        "version": "0.1.0"
    }

Note: the docker container with the boldataapi server needs to be able to
access the Alpha Gluu server, so you need to use a VPN.  For some reason the
ID06 VPN doesn't work inside Docker for me, so I use the STV VPN.


### Run Alembic database migration tests

To run the database migration tests, install `tox` in your virtualenv and run
the tests from `db/alembic` directory:

    pip3 install tox
    cd db/alembic
    tox -e py3


## API documentation

In progress, based on Swagger.  Do

    make swagger-ui

and open http://localhost:8123/ in a browser.

The documentation is extracted from controller method docstrings.  When writing it I find it useful
to have http://localhost:9700/api/v1/boldata/swagger.json open in a browser tab.  After updating
the docstring you can refresh the tab, select all, copy, switch to a tab with
https://editor.swagger.io/, select all, paste, agree to convert unreadable JSON
to pretty-printed YAML, watch for error messages on the right side.


## Testing the API locally or on Alpha

You need an authentication token.  To get a token from Alpha Gluu do

    make auth-token

Now you can do things like

    http get http://localhost:9700/api/v1/boldata/projects/search/contains/names/foo "Authorization: Bearer $token"
    http get https://bol-data-api.alpha.vaultit.org/api/v1/boldata/projects/search/contains/names/foo "Authorization: Bearer $token"

or paste it into swagger-ui on Firefox (or Chrome, if you find an extension to circumvent CORS).
Note: the auth token is the base64-encoded blob in the response's `"access_token"`
property.  Don't copy the entire JSON wrapper.

The token is valid for 4 hours, after which you have to get a new one.

You can inspect the token with PyJWT:

    >>> import jwt
    >>> token = '...'
    >>> jwt.decode(token, algorithms=['RS256'], options={'verify_signature': False})
    {'iss': 'https://auth-azure-alpha.id06.se', 'aud': '(client ID)', 'exp':
    1591260834, 'iat': 1591257234, 'sub': '(client ID)', 'client_id': '(client ID'),
    'code': '(some GUID)', 'token_type': 'bearer', 'x5t#S256': '', 'scope': '...'}

You can check the expiration time with

    >>> import time
    >>> time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(
    ... jwt.decode(token, algorithms=['RS256'], options={'verify_signature': False})['exp']))
    '2020-06-04 11:53:54'


## Source layout

- `bol-data-api-dist/`: wheel files created when you do `make build-docker` to test Docker builds,
  not used for anything else

- `db/`: Dockerfile for local PostgreSQL builds that has our database schema,
  used for `make test` and `make run`.

- `infrastructure/`: configuration files for various services (uwsg, nginx)
  built in our Docker images.

- `server/`: source code for the BOLDataAPI service (Python, Bottle).


## Deployment

Workflow: GitLab CI builds Docker images and pushes them to registry.vaultit.org.  The Docker tag
corresponds to the GitLab CI pipeline ID.

The manual GitLab CI deploy step triggers a Jenkins job
(https://jenkins.vaultit.org/view/BOL/job/id06-bol-data-api-deploy/)   The job itself is defined
in infrastructure/Jenkinsfile, which uses kubectl to eventuate the deploy itself.


## Data migration from QvarnDB to VaultDB

Data migration from Qvarn to VaultDB is executed from a sub-folder `db/initial-data-migration/`.

Please see a dedicated Readme at https://git.vaultit.org/Foretagsdeklaration/bol-data-api/tree/master/initial-data-migration.


## Environments

### Alpha

https://bol-data-api.alpha.vaultit.org/api/v1/boldata/version

### Beta

https://bol-data-api.beta.vaultit.org/api/v1/boldata/version

### Production

https://bol-data-api.vaultit.org/api/v1/boldata/version

#### Monitoring

- Graylog: not set up (yet)
- New Relic: not set up (yet?)
- Sentry: not set up (yet)
- Kubernetes: you can do `kubectl logs`, I suppose

## Azure Release/Deploy
Go to "Pipelines" in the Azure Devops menu -> Click "BDA Alpha Deploy" in the list -> Click "Run pipeline" in top right corner.

If no "Custom Image Tag" is provided, it will deploy with the BDA image tag built on the master branch.

If we want to deploy the build from a separate branch, we can get that tag name which is the same as the pipeline build name, e.g. `20221215.5-18286`.

Please make sure you're specifying the right branch as the deploy pipline will use the manifests folder files in that branch.
