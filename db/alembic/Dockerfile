FROM python:3.11-alpine

RUN apk add --no-cache postgresql-libs && \
    apk add --no-cache bash && \
    apk add --no-cache --virtual .build-deps gcc g++ python3-dev musl-dev postgresql-dev

RUN addgroup -g 1000 --system alembic && \
    adduser -u 1000 -g 1000 --system alembic

COPY ./requirements.txt /alembic/
COPY ./alembic.ini /alembic/
RUN mkdir /alembic/migrations

RUN chown -R alembic:alembic /alembic

ENV PYTHONPATH "${PYTHONPATH}:/alembic/migrations"
WORKDIR /alembic

RUN PIP_BREAK_SYSTEM_PACKAGES=1 pip install -r requirements.txt


ENTRYPOINT ["alembic"]
CMD ["--help"]
