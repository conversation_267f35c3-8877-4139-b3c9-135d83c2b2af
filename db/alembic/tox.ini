[tox]
envlist = py311
skipsdist = true

[testenv]
deps =
    -rrequirements.txt
    pytest-alembic
    pytest-mock-resources[postgres-binary]
setenv =
    SQLALCHEMY_SILENCE_UBER_WARNING=1
    PMR_POSTGRES_IMAGE=postgres:11
##  PYTHON_ON_WHALES_DEBUG=1
passenv =
    DOCKER_TLS_CERTDIR
    DOCKER_HOST
    PYTEST_MOCK_RESOURCES_HOST
commands =
    pytest {posargs}

[pytest]
testpaths = migrations/tests.py
# we have to tell alembic where our conftest.py is so we can override the alembic_engine fixture
# and use dockerized postgresql instead of sqlite for the builtin pytest-alembic tests
pytest_alembic_tests_path = migrations/
filterwarnings =
    once
    # Yes we have a loop in our table foreign key relationships, can't do anything about it
    ignore::sqlalchemy.exc.SAWarning:env
