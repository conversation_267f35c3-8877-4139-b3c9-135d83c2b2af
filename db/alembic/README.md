# Alembic


## Local development

### Schema migrations on clean database

In general, Alembic will run upgrade operations up to most recent revision

    alembic upgrade head
    INFO  [alembic.context] Context class PostgresqlContext.
    INFO  [alembic.context] Will assume transactional DDL.
    INFO  [alembic.context] Running upgrade None -> 1975ea83b712


The process which occurred here included that Alembic first checked if the database had a table called alembic_version,
and if not, created it. It looks in this table for the current version, if any, and then calculates the path from this
version to the version requested, in this case head, which is known to be 1975ea83b712.
It then invokes the upgrade() method in each file to get to the target revision.


To upgrade schema using docker-compose run (at the top level)

    docker compose run --rm alembic

It will use a PostgreSQL database in the `bol-data-api_db-test-alembic`
container, which is not used for anything else.  You can connect to it using

    PGPASSWORD=dbpwd psql -h localhost -p 54324 -U dbuser bol

You can dump the schema using

    PGPASSWORD=dbpwd pg_dump -h localhost -p 54324 -U dbuser bol --schema-only

Incidentally, if you want to compare the schema with the one currently in use in Kubernetes,
you can get that one with

    POD=$(kubectl get pods -l app=bol-data-api -o jsonpath={.items[0].metadata.name})
    kubectl exec $POD -- apt update -qq
    kubectl exec $POD -- apt install -y postgresql-client
    kubectl exec $POD -- pg_dump "host=*************** port=5432 user=bol_application sslmode=verify-ca sslrootcert=/etc/bolfak/secrets/root.crt sslkey=/etc/bolfak/secrets-600/..data/super.key sslcert=/etc/bolfak/secrets/super.crt dbname=bol" --schema-only


### Schema migrations on unversioned database with preexisting schema

In general, Alembic will stamp revision table up to most recent version and will not run any migrations

    alembic stamp head


To stamp revision using docker-compose run

    docker compose run --rm alembic stamp head


We need to make sure current database schema is inline with Alembic metadata

In general, Alembic will compare database against metadata and  will generate
so-called candidate migrations into our new migrations file.
We review and modify these by hand as needed, then proceed normally.

    alembic revision --autogenerate -m "my first revision"


To autogenerate migration using docker-compose

    docker compose run --rm alembic revision --autogenerate -m "my first revision"

Generated file will appear in `db/alembic/migrations/versions/`
If generated migration file contains any operations - adjust metadata and run autogenerate command again.
When database schema and metadata is inline autogenerate command will generate file with no operations.
At this point the database is versioned by Alembic and ready for future migrations.

NB. Make sure `user` parameter is correct in docker-compose.yml and matches the
user's running docker-compose command `UID` and `GID`.
Autogerated file has to be writable by the user if ids match.

    alembic:
      build: ./db/alembic
      command: "upgrade head"
      user: 1000:1000


### Adding change to database

First you'll want to modify models.py and make the changes you want, e.g. add a
column to some table.

Next you'll want to generate a new revision file

    alembic revision --autogenerate -m "Add a column"
    Generating /path/to/yourapp/alembic/versions/ae1027a6acf_add_a_column.py...
    done

The file can be adjusted if needed.

You can also create revision file using `docker-compose`. First, ensure that
your local DB `db-test-alembic` has all 'old' migrations applied. You can do
this by running:

    docker compose run --rm alembic

Second, ask alembic to generate a new revision file by running:

    docker compose run --rm alembic revision --autogenerate -m "Add a column"

The generated file will appear in `db/alembic/migrations/versions/`

To apply this new migration run

    alembic upgrade head

or using docker-compose

    docker compose run --rm alembic


### Merging diverged heads

Sometimes two people add new revision files in different branches, which leaves two head revisions
when you merge the independent MRs.  This is easiest to resolve by creating a merge migration:

    docker compose run --rm alembic merge heads

See https://alembic.sqlalchemy.org/en/latest/branches.html#merging-branches for details.


## Schema migration in to k8s

Schema migration in k8s is done using k8s jobs. Jobs are defined in `boldataapi-db-migration.yaml` for each k8s environment.

Job has a name with a version number that can to be bumped to preserve the logs of previous job runs.

    metadata:
      name: boldataapi-db-migration-0

The logs are not stored for a long time, so maybe don't worry about it too much.  If you decide to
change the job name, be sure to also update .gitlab-ci.yml, to pass the new name to the Jenkins
job.

Job uses BDA docker image. `alembic` command is installed in the image and configs are copied into the image at `/opt/app/alembic/` as well.

Job is supposed to use a docker tag as provided in Jenkins job to replace `registry.vaultit.org/boldataapi/bol-data-api:latest`

The `alembic` command that is to be executed should be given in job `args`. For example `alembic stamp head` would look like:

    spec:
      template:
        spec:
          containers:
          - name: boldataapi-db-migration
            image: registry.vaultit.org/boldataapi/bol-data-api:latest
            command: ["/run.sh"]
            args: ["alembic", "stamp", "head"]


### Steps

- Edit infrastructure/kubernetes/alpha/boldataapi-db-migration.yml, change command args to the command you want
  (usually it'll be `alembic upgrade head`)

- Commit

- Trigger the `alembic_deploy` job in the GitLab pipeline

- Watch for job status with `kubectl logs job/boldataapi-db-migration-0` or in the Jenkins job
  console log at https://jenkins.vaultit.org/view/BOL/job/id06-bol-data-api-db-migration/
