"""Restrcted views moved to schema support

NB: for the moved views to have proper permissions DBAs execute schema update
after the views have been moved:

```
grant usage on schema support to bol;
grant usage on schema support to bol_ta;

ALTER DEFAULT PRIVILEGES IN SCHEMA support GRANT SELECT, INSERT, UPDATE, DELETE, TRUNCATE ON TABLES TO bol_ta;
ALTER DEFAULT PRIVILEGES IN SCHEMA support GRANT USAGE ON TYPES TO bol_ta;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO bol_ta;
```


Revision ID: 0848d4497d9f
Revises: a52529d8b722
Create Date: 2023-10-11 14:02:59.402655

"""
import sqlalchemy as sa
from alembic import op

from models import PUBLIC_SCHEMA, SUPPORT_SCHEMA
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '0848d4497d9f'
down_revision = 'a52529d8b722'
branch_labels = None
depends_on = None

support_schema = ReplaceableObject(SUPPORT_SCHEMA, None)
public_schema = ReplaceableObject(PUBLIC_SCHEMA, None)
moved_view_names = [
    'alembic_version_restricted',
    'bulk_import_jobs_restricted',
    'internal_project_ids_restricted',
    'notification_reports_restricted',
    'preannouncement_forms_restricted',
    'preannouncements_restricted',
    'project_users_restricted',
    'projects_restricted',
    'report_cache_restricted',
    'status_reports_history_restricted',
    'status_reports_restricted',
    'supplier_contacts_restricted',
    'suppliers_restricted',
]


def upgrade():
    op.create_schema(support_schema)

    for view_name in moved_view_names:
        op.execute(f'ALTER VIEW IF EXISTS {view_name} SET SCHEMA {SUPPORT_SCHEMA}')


def downgrade():
    for view_name in moved_view_names:
        op.execute(f'ALTER VIEW IF EXISTS {SUPPORT_SCHEMA}.{view_name} SET SCHEMA {PUBLIC_SCHEMA}')

    op.drop_schema(support_schema)
