"""Add contract work areas column

Revision ID: 53216d19a638
Revises: 584acd4dc6eb
Create Date: 2022-01-13 09:18:55.728628

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '53216d19a638'
down_revision = '584acd4dc6eb'
branch_labels = None
depends_on = None


def upgrade():
    t_supplier_work_area_type = sa.Enum(
        'cleaning', 'demolition', 'sanitation', 'drywall', 'assembly', 'scaffolding', 'land_works',
        'casting_all_materials', 'framework', 'masonry_and_plastering', 'other',
        name='t_supplier_work_area_type', create_constraint=False, native_enum=False)
    t_supplier_work_area_type.create(op.get_bind())
    op.add_column('suppliers', sa.Column('contract_work_areas', postgresql.ARRAY(t_supplier_work_area_type), nullable=True))

def downgrade():
    op.drop_column('suppliers', 'contract_work_areas')
    # t_supplier_work_area_type is special and is not actually created in postgresql, so we cannot
    # drop it here
