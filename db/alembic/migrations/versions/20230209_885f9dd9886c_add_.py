"""add created_on, last_changed columns to report_cache

Revision ID: 885f9dd9886c
Revises: e1bd38852f1c
Create Date: 2023-02-09 09:44:06.698770

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql
from triggers import ReplaceableObjectTRG


# revision identifiers, used by Alembic.
revision = '885f9dd9886c'
down_revision = 'e1bd38852f1c'
branch_labels = None
depends_on = None

trigger_set_report_cache_last_changed = ReplaceableObjectTRG(
    "set_report_cache_last_changed",
    "report_cache",
    """
    BEFORE UPDATE ON report_cache
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report_cache', sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('report_cache', sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.create_trg(trigger_set_report_cache_last_changed)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_trg(trigger_set_report_cache_last_changed)
    op.drop_column('report_cache', 'last_changed')
    op.drop_column('report_cache', 'created_on')
    # ### end Alembic commands ###
