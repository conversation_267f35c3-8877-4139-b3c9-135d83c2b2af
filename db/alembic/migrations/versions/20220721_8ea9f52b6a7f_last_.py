"""Last assigned to columns added

Revision ID: 8ea9f52b6a7f
Revises: e49a13efa1c8
Create Date: 2022-07-21 00:42:06.217047

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '8ea9f52b6a7f'
down_revision = 'e49a13efa1c8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('last_assigned_to_company', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('last_assigned_to_supplier', postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('last_assigned_to_time', postgresql.TIMESTAMP(precision=6), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('last_assigned_to_business_id', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('last_assigned_to_business_id_type', sa.Text(), nullable=True))
    op.create_foreign_key('fk_preannouncement_last_assigned_to_supplier_id', 'preannouncement_forms', 'suppliers', ['last_assigned_to_supplier'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_preannouncement_last_assigned_to_supplier_id', 'preannouncement_forms', type_='foreignkey')
    op.drop_column('preannouncement_forms', 'last_assigned_to_business_id_type')
    op.drop_column('preannouncement_forms', 'last_assigned_to_business_id')
    op.drop_column('preannouncement_forms', 'last_assigned_to_time')
    op.drop_column('preannouncement_forms', 'last_assigned_to_supplier')
    op.drop_column('preannouncement_forms', 'last_assigned_to_company')
    # ### end Alembic commands ###
