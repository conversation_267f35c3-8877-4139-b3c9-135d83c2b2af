"""Expose value field in support.report_cache_restricted view

Revision ID: 4c9b59204be6
Revises: 60fae8e92e4a
Create Date: 2023-12-06 17:17:54.130103

"""
from alembic import context, op
from alembic.script import ScriptDirectory

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '4c9b59204be6'
down_revision = '60fae8e92e4a'
branch_labels = None
depends_on = None

report_cache_restricted_columns_v2 = [
    "id",
    "external_id",
    "correlation_id",
    "expires_at",
    "interested_org_id",
    "key",
    "provider",
    "type",
    "last_changed",
    "created_on",
    "value",
]


report_cache_restricted_view_v2 = ReplaceableObject(
    'support.report_cache_restricted',
    """
    AS
        SELECT
            %s
        FROM
            report_cache
    ;
    """ % ','.join(report_cache_restricted_columns_v2)
)


def upgrade():
    op.execute('DROP VIEW IF EXISTS support.report_cache_restricted')
    op.create_view(report_cache_restricted_view_v2)


def downgrade():
    # Importing version modules is tricky as module names are parsed as
    # numbers. Thus we use Alembic's mechanism to import modules based on
    # version.
    report_cache_restricted_view_version = 'becc4b843a00'
    config = context.config
    script = ScriptDirectory.from_config(config)
    module = script.get_revision(report_cache_restricted_view_version).module
    report_cache_restricted_view = module.report_cache_restricted_view
    # The original '*_restricted_view' was subsequently moved to schema
    # `support`. We prefix name of the view with schema `support.` so that the
    # view is created in that schema.
    report_cache_restricted_view.name = 'support.report_cache_restricted'
    op.execute('DROP VIEW IF EXISTS support.report_cache_restricted')
    op.create_view(report_cache_restricted_view)
