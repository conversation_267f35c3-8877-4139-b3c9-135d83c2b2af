"""Initial creation of triggers

Revision ID: 3a2fd1f4f0a9
Revises: caa2fd82d7d6
Create Date: 2020-12-01 16:14:21.231382

"""
import textwrap

from alembic import op
from triggers import ReplaceableObjectSP, ReplaceableObjectTRG


# revision identifiers, used by Alembic.
revision = '3a2fd1f4f0a9'
down_revision = 'caa2fd82d7d6'
branch_labels = None
depends_on = None


trigger_set_last_changed = ReplaceableObjectSP(
    "trigger_set_last_changed()",
    textwrap.dedent("""
    RETURNS TRIGGER AS
    $$
    BEGIN
      NEW.last_changed = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """.lstrip('\n'))
)


trigger_set_status_reports_last_changed = ReplaceableObjectTRG(
    "set_status_reports_last_changed",
    "status_reports",
    """
    BEFORE UPDATE ON status_reports
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


trigger_set_projects_last_changed = ReplaceableObjectTRG(
    "set_projects_last_changed",
    "projects",
    """
    BEFORE UPDATE ON projects
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


trigger_set_suppliers_last_changed = ReplaceableObjectTRG(
    "set_suppliers_last_changed",
    "suppliers",
    """
    BEFORE UPDATE ON suppliers
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


trigger_set_internal_project_ids_last_changed = ReplaceableObjectTRG(
    "set_internal_project_ids_last_changed",
    "internal_project_ids",
    """
    BEFORE UPDATE ON internal_project_ids
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


trigger_set_supplier_contacts_last_changed = ReplaceableObjectTRG(
    "set_supplier_contacts_last_changed",
    "supplier_contacts",
    """
    BEFORE UPDATE ON supplier_contacts
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


trigger_set_project_users_last_changed = ReplaceableObjectTRG(
    "set_project_users_last_changed",
    "project_users",
    """
    BEFORE UPDATE ON project_users
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();

    """
)


trigger_set_report_accesses_last_changed = ReplaceableObjectTRG(
    "set_report_accesses_last_changed",
    "report_accesses",
    """
    BEFORE UPDATE ON report_accesses
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


trigger_set_notification_reports_last_changed = ReplaceableObjectTRG(
    # NB: should be called "set_notification_reports_last_changed"
    # but for schema compat resons we call it
    "set_report_accesses_last_changed",
    "notification_reports",
    """
    BEFORE UPDATE ON notification_reports
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


trigger_set_bulk_import_jobs_last_changed = ReplaceableObjectTRG(
    "set_bulk_import_jobs_last_changed",
    "bulk_import_jobs",
    """
    BEFORE UPDATE ON bulk_import_jobs
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


def upgrade():
    op.create_sp(trigger_set_last_changed)
    op.create_trg(trigger_set_status_reports_last_changed)
    op.create_trg(trigger_set_projects_last_changed)
    op.create_trg(trigger_set_suppliers_last_changed)
    op.create_trg(trigger_set_internal_project_ids_last_changed)
    op.create_trg(trigger_set_supplier_contacts_last_changed)
    op.create_trg(trigger_set_project_users_last_changed)
    op.create_trg(trigger_set_report_accesses_last_changed)
    op.create_trg(trigger_set_notification_reports_last_changed)
    op.create_trg(trigger_set_bulk_import_jobs_last_changed)


def downgrade():
    op.drop_trg(trigger_set_status_reports_last_changed)
    op.drop_trg(trigger_set_projects_last_changed)
    op.drop_trg(trigger_set_suppliers_last_changed)
    op.drop_trg(trigger_set_internal_project_ids_last_changed)
    op.drop_trg(trigger_set_supplier_contacts_last_changed)
    op.drop_trg(trigger_set_project_users_last_changed)
    op.drop_trg(trigger_set_report_accesses_last_changed)
    op.drop_trg(trigger_set_notification_reports_last_changed)
    op.drop_trg(trigger_set_bulk_import_jobs_last_changed)
    op.drop_sp(trigger_set_last_changed)
