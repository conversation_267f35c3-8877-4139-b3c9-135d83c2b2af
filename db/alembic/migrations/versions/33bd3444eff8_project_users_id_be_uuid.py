"""Change project_users.id to UUID

Revision ID: 33bd3444eff8
Revises: 3a2fd1f4f0a9
Create Date: 2021-04-19 12:53:07.233876

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '33bd3444eff8'
down_revision = '3a2fd1f4f0a9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'project_users', 'id',
        type_=postgresql.UUID(as_uuid=True),
        server_default=sa.text('uuid_generate_v4()'),
        postgresql_using="id::uuid",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'project_users', 'id',
        type_=sa.Text(),
        server_default=None,
    )
    # ### end Alembic commands ###
