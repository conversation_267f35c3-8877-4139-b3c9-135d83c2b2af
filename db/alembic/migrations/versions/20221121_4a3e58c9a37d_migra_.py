"""Migrate used_providers data

Revision ID: 4a3e58c9a37d
Revises: 1c30096be0c9
Create Date: 2022-11-21 13:07:00.350666

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = '4a3e58c9a37d'
down_revision = '1c30096be0c9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # column used_providers was already added in revision 1c30096be0c9
    # let's update status_report.used_providers
    # with the json field 'used_providers' value from status_report.json_
    connection = op.get_bind()
    qry = sa.sql.text(
        """
           UPDATE status_reports
           SET used_providers = COALESCE(
            ARRAY(
                SELECT jsonb_array_elements_text(json_->'used_providers')
            ),
            ARRAY[]::text[])
           WHERE
            json_->>'used_providers' IS NOT NULL AND
            jsonb_typeof(json_->'used_providers') = 'array';
        """
    )
    connection.execute(qry)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # set status_report.used_providers to initial value NULL
    # removing of used_providers column is implemented in revision 1c30096be0c9
    connection = op.get_bind()
    qry = sa.sql.text(
        """
           UPDATE status_reports
           SET used_providers = NULL
           WHERE
            json_->>'used_providers' IS NOT NULL AND
            jsonb_typeof(json_->'used_providers') = 'array';
        """
    )
    connection.execute(qry)
    # ### end Alembic commands ###
