"""added_client_confirmed should be NULL instead False by default

Revision ID: e2af533b845d
Revises: 032984f8e21e
Create Date: 2024-01-18 14:35:00.839461

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = 'e2af533b845d'
down_revision = '032984f8e21e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('projects', 'added_client_confirmed')
    op.add_column('projects', sa.Column('added_client_confirmed',
                                        sa.BOOLEAN(),
                                        nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('projects', 'added_client_confirmed')
    op.add_column('projects', sa.Column('added_client_confirmed',
                                        sa.BOOLEAN(),
                                        server_default='False',
                                        nullable=True))
    # ### end Alembic commands ###
