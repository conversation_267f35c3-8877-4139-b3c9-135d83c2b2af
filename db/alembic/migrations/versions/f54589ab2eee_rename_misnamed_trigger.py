"""Rename misnamed trigger

Revision ID: f54589ab2eee
Revises: fbd6840f1aae
Create Date: 2021-04-23 10:29:30.068666

"""

from alembic import op

from triggers import ReplaceableObjectTRG


# revision identifiers, used by Alembic.
revision = 'f54589ab2eee'
down_revision = 'fbd6840f1aae'
branch_labels = None
depends_on = None


trigger_set_notification_reports_last_changed = ReplaceableObjectTRG(
    "set_notification_reports_last_changed",
    "notification_reports",
    """
    BEFORE UPDATE ON notification_reports
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


def upgrade():
    op.replace_trg(trigger_set_notification_reports_last_changed,
                   replaces='3a2fd1f4f0a9.trigger_set_notification_reports_last_changed')


def downgrade():
    op.replace_trg(trigger_set_notification_reports_last_changed,
                   replace_with='3a2fd1f4f0a9.trigger_set_notification_reports_last_changed')
