"""Add charge_reference, used_providers columns to status_reports table

Revision ID: 1c30096be0c9
Revises: cfb88e429d34
Create Date: 2022-11-04 09:15:35.225324

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '1c30096be0c9'
down_revision = 'cfb88e429d34'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('status_reports', sa.Column('charge_reference', sa.Text(), nullable=True))
    op.add_column('status_reports', sa.Column('used_providers', postgresql.ARRAY(sa.Text()), nullable=True))
    op.create_index('idx_status_reports_charge_reference', 'status_reports', ['charge_reference'], unique=False)


def downgrade():
    op.drop_index('idx_status_reports_charge_reference', table_name='status_reports')
    op.drop_column('status_reports', 'used_providers')
    op.drop_column('status_reports', 'charge_reference')
