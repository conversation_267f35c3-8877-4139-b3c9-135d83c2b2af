"""Add a column contract_type to Supplier

Revision ID: e0ef89fb1b01
Revises: c4770471db97
Create Date: 2021-11-19 06:48:45.649992

"""
import sqlalchemy as sa
from alembic import op

from models import SupplierContractTypeEnum


# revision identifiers, used by Alembic.
revision = 'e0ef89fb1b01'
down_revision = 'c4770471db97'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    t_supplier_contract_type = sa.Enum(SupplierContractTypeEnum, name='t_supplier_contract_type')
    t_supplier_contract_type.create(op.get_bind())
    op.add_column('suppliers', sa.Column('contract_type', t_supplier_contract_type, nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('suppliers', 'contract_type')
    op.execute("DROP TYPE t_supplier_contract_type;")
    # ### end Alembic commands ###
