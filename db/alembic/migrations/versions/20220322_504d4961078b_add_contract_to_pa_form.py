"""Add contract_* to PA form

Revision ID: 504d4961078b
Revises: 5fb78640714e
Create Date: 2022-03-22 13:30:27.467868

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '504d4961078b'
down_revision = '5fb78640714e'
branch_labels = None
depends_on = None


def upgrade():
    t_supplier_work_area_type = sa.Enum(
        'cleaning', 'demolition', 'sanitation', 'drywall', 'assembly', 'scaffolding', 'land_works',
        'casting_all_materials', 'framework', 'masonry_and_plastering', 'other',
        name='t_supplier_work_area_type', create_constraint=False, native_enum=False)
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('contract_end_date', sa.Date(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('contract_start_date', sa.Date(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('contract_type', sa.Enum('contracting', 'consulting', 'personnel_leasing', 'machine_equipment_leasing', 'materials_and_products', 'transportation', name='t_supplier_contract_type'), nullable=True))
    op.add_column('preannouncement_forms',
                  sa.Column('contract_work_areas',
                            postgresql.ARRAY(t_supplier_work_area_type)))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('preannouncement_forms', 'contract_work_areas')
    op.drop_column('preannouncement_forms', 'contract_type')
    op.drop_column('preannouncement_forms', 'contract_start_date')
    op.drop_column('preannouncement_forms', 'contract_end_date')
    # ### end Alembic commands ###
