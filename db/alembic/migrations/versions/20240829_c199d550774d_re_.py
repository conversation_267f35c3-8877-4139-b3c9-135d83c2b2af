"""Re-add project creator role to the support view

Revision ID: c199d550774d
Revises: 8162f31258f7
Create Date: 2024-08-29 13:11:14.062597

"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'c199d550774d'
down_revision = '8162f31258f7'
branch_labels = None
depends_on = None

projects_restricted_columns_v5 = [
    'id',
    'external_id',
    'name',
    'tax_id',
    'client_company_id',
    'start_date',
    'end_date',
    'state',
    'last_changed',
    'created_on',
    'pa_form_enabled',
    'created_by_org_id',
    'client_contact_person_id',
    'client_contact_person_email',
    'added_client_confirmed',
    'added_client_can_view',
    'project_creator_role',
]

projects_restricted_view_v5 = ReplaceableObject(
    "support.projects_restricted",
    """
    AS
        SELECT
            %s
        FROM
            projects
    ;
    """ % ', '.join(projects_restricted_columns_v5)
)


def upgrade():
    op.replace_view(projects_restricted_view_v5,
                    replaces='03a5a4a5b399.projects_restricted_view_v4')


def downgrade():
    op.replace_view(projects_restricted_view_v5,
                    replace_with='03a5a4a5b399.projects_restricted_view_v4')
