"""Add submitted_time to PreannouncementForms

Revision ID: afdcc5a20ef9
Revises: e40d13610178
Create Date: 2022-07-12 07:57:23.408314

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'afdcc5a20ef9'
down_revision = 'e40d13610178'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('submitted_time', postgresql.TIMESTAMP(precision=6), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('preannouncement_forms', 'submitted_time')
    # ### end Alembic commands ###
