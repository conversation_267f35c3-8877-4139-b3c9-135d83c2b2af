"""added_client_can_view should be set to true for all projects where added_client_confirmed is not null

Revision ID: 8162f31258f7
Revises: 69b72509c4c6
Create Date: 2024-08-19 12:45:55.584827

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '8162f31258f7'
down_revision = '69b72509c4c6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
    op.execute("""
        UPDATE projects
        SET added_client_can_view = TRUE
        WHERE added_client_confirmed IS NOT NULL AND project_creator_role = 'main_contractor'
    """)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
