"""Make preannouncement columns nullable

Revision ID: 38342ac9c518
Revises: ed2010294455
Create Date: 2021-12-22 08:26:47.294429

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '38342ac9c518'
down_revision = 'ed2010294455'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('preannouncements', 'created_by_supplier_id',
               existing_type=postgresql.UUID(),
               nullable=True)
    op.alter_column('preannouncements', 'for_supplier_id',
               existing_type=postgresql.UUID(),
               nullable=True)


def downgrade():
    op.alter_column('preannouncements', 'for_supplier_id',
               existing_type=postgresql.UUID(),
               nullable=False)
    op.alter_column('preannouncements', 'created_by_supplier_id',
               existing_type=postgresql.UUID(),
               nullable=False)
