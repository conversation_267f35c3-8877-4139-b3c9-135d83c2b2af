"""Add project_id column to bulk_import_jobs_restricted

Revision ID: a655d63276b1
Revises: becc4b843a00
Create Date: 2023-05-30 11:09:12.364234
"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'a655d63276b1'
down_revision = 'becc4b843a00'
branch_labels = None
depends_on = None


bulk_import_jobs_restricted_view_v2 = ReplaceableObject(
    "bulk_import_jobs_restricted",
    """
    AS
        SELECT
            id,
            status,
            project_id,
            imported,
            canceled,
            companies,
            interested_org_id,
            last_changed,
            created_on
        FROM
            bulk_import_jobs
    ;
    """
)


def upgrade():
    op.replace_view(bulk_import_jobs_restricted_view_v2,
                    replaces='acd3300dc9bf.bulk_import_jobs_restricted_view')


def downgrade():
    op.replace_view(bulk_import_jobs_restricted_view_v2,
                    replace_with='acd3300dc9bf.bulk_import_jobs_restricted_view')
