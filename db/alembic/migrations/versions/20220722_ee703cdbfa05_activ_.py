"""Active PA Form column added

Revision ID: ee703cdbfa05
Revises: e49a13efa1c8
Create Date: 2022-07-22 11:38:15.534286

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'ee703cdbfa05'
down_revision = 'e49a13efa1c8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncements', sa.Column('active_pa_form', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key('fk_preannouncement_active_pa_form_id', 'preannouncements', 'preannouncement_forms', ['active_pa_form'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_preannouncement_active_pa_form_id', 'preannouncements', type_='foreignkey')
    op.drop_column('preannouncements', 'active_pa_form')
    # ### end Alembic commands ###
