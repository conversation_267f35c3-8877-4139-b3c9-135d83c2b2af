"""add project_restricted.project_creator_role

Revision ID: 1333f54bdd3b
Revises: faeb0e0ea299
Create Date: 2024-03-04 14:54:27.858752

"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '1333f54bdd3b'
down_revision = 'faeb0e0ea299'
branch_labels = None
depends_on = None


projects_restricted_columns_v4 = [
    'id',
    'external_id',
    'name',
    'tax_id',
    'client_company_id',
    'start_date',
    'end_date',
    'state',
    'last_changed',
    'created_on',
    'pa_form_enabled',
    'created_by_org_id',
    'client_contact_person_id',
    'client_contact_person_email',
    'added_client_confirmed',
    'project_creator_role',
]


projects_restricted_view_v4 = ReplaceableObject(
    "support.projects_restricted",
    """
    AS
        SELECT
            %s
        FROM
            projects
    ;
    """ % ', '.join(projects_restricted_columns_v4)
)


def upgrade():
    op.replace_view(projects_restricted_view_v4,
                    replaces='f42ba6ad3212.projects_restricted_view_v3')


def downgrade():
    op.replace_view(projects_restricted_view_v4,
                    replace_with='f42ba6ad3212.projects_restricted_view_v3')
