"""Remove duplicates with same external_id from status_reports_history table

Revision ID: 4329457a1281
Revises: 62967d784720
Create Date: 2022-12-07 08:34:18.783473

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '4329457a1281'
down_revision = '62967d784720'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
    op.execute("""
        DELETE FROM
        status_reports_history s1
                USING status_reports_history s2
        WHERE
            s1.id > s2.id
            AND s1.external_id = s2.external_id;
    """)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
