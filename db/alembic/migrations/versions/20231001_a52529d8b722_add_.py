"""Add collective agreement columns to suppliers

Revision ID: a52529d8b722
Revises: 3ff98b0101aa
Create Date: 2023-10-01 20:47:46.523345

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = 'a52529d8b722'
down_revision = '3ff98b0101aa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('suppliers', sa.Column('is_one_man_company', sa.BOOLEAN(), nullable=True))
    op.add_column('suppliers', sa.Column('has_collective_agreement', sa.BOOLEAN(), nullable=True))
    op.add_column('suppliers', sa.Column('collective_agreement_name', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('suppliers', 'collective_agreement_name')
    op.drop_column('suppliers', 'has_collective_agreement')
    op.drop_column('suppliers', 'is_one_man_company')
    # ### end Alembic commands ###
