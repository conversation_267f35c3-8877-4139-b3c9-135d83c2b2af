"""Add added_client_can_view to support view

Revision ID: 03a5a4a5b399
Revises: 20e38d7c55df
Create Date: 2024-06-06 14:05:45.649314

"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '03a5a4a5b399'
down_revision = '20e38d7c55df'
branch_labels = None
depends_on = None

projects_restricted_columns_v4 = [
    'id',
    'external_id',
    'name',
    'tax_id',
    'client_company_id',
    'start_date',
    'end_date',
    'state',
    'last_changed',
    'created_on',
    'pa_form_enabled',
    'created_by_org_id',
    'client_contact_person_id',
    'client_contact_person_email',
    'added_client_confirmed',
    'added_client_can_view'
]

projects_restricted_view_v4 = ReplaceableObject(
    "support.projects_restricted",
    """
    AS
        SELECT
            %s
        FROM
            projects
    ;
    """ % ', '.join(projects_restricted_columns_v4)
)


def upgrade():
    op.replace_view(projects_restricted_view_v4,
                    replaces='f42ba6ad3212.projects_restricted_view_v3')


def downgrade():
    op.replace_view(projects_restricted_view_v4,
                    replace_with='f42ba6ad3212.projects_restricted_view_v3')
