"""Custom client fields added  support.projects_restricted view

Revision ID: 60fae8e92e4a
Revises: b9d014919f81
Create Date: 2023-11-09 07:57:44.349282

"""
from alembic import context, op
from alembic.script import ScriptDirectory

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '60fae8e92e4a'
down_revision = 'b9d014919f81'
branch_labels = None
depends_on = None


projects_restricted_columns_v2 = [
    'id',
    'external_id',
    'name',
    'tax_id',
    'client_company_id',
    'start_date',
    'end_date',
    'state',
    'last_changed',
    'created_on',
    'pa_form_enabled',
    'created_by_org_id',
    'client_contact_person_id',
    'client_contact_person_email'
]

projects_restricted_view_v2 = ReplaceableObject(
    "support.projects_restricted",
    """
    AS
        SELECT
            %s
        FROM
            projects
    ;
    """ % ', '.join(projects_restricted_columns_v2)
)


def upgrade():
    op.execute('DROP VIEW IF EXISTS support.projects_restricted')
    op.create_view(projects_restricted_view_v2)


def downgrade():
    # Importing version modules is tricky as module names are parsed as
    # numbers. Thus we use Alembic's mechanism to import modules based on
    # version.
    projects_restricted_view_version = 'e80e2a9841f0'
    config = context.config
    script = ScriptDirectory.from_config(config)
    module = script.get_revision(projects_restricted_view_version).module
    projects_restricted_view = module.projects_restricted_view

    # The original '*_restricted_view' was subsequently moved to schema
    # `support`. We prefix name of the view with schema `support.` so that the
    # view is created in that schema.
    projects_restricted_view.name = 'support.projects_restricted'
    op.execute('DROP VIEW IF EXISTS support.projects_restricted')
    op.create_view(projects_restricted_view)
