"""Skip pa fields added to support.suppliers_restricted view

Revision ID: b9d014919f81
Revises: 0848d4497d9f
Create Date: 2023-11-08 06:19:59.351773

"""
from alembic import context, op
from alembic.script import ScriptDirectory

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'b9d014919f81'
down_revision = '0848d4497d9f'
branch_labels = None
depends_on = None

suppliers_restricted_columns_v2 = [
    'id',
    'external_id',
    'role',
    'type',
    'contract_type',
    'contract_start_date',
    'contract_end_date',
    'contract_work_areas',
    'materialized_path',
    'project_id',
    'parent_supplier_id',
    'parent_company_id',
    'company_id',
    'revision',
    'last_visited',
    'last_changed',
    'created_on',
    'first_visited',
    'visitor_type',
    'is_one_man_company',
    'has_collective_agreement',
    'collective_agreement_name'
]

suppliers_restricted_view_v2 = ReplaceableObject(
    'support.suppliers_restricted',
    """
    AS
        SELECT
            %s
        FROM
            suppliers
    ;
    """ % ','.join(suppliers_restricted_columns_v2)
)


def upgrade():
    op.execute('DROP VIEW IF EXISTS support.suppliers_restricted')
    op.create_view(suppliers_restricted_view_v2)


def downgrade():
    # Importing version modules is tricky as module names are parsed as
    # numbers. Thus we use Alembic's mechanism to import modules based on
    # version.
    suppliers_restricted_view_version = 'f6a1637d1f69'
    config = context.config
    script = ScriptDirectory.from_config(config)
    module = script.get_revision(suppliers_restricted_view_version).module
    suppliers_restricted_view = module.suppliers_restricted_view

    # The original '*_restricted_view' was subsequently moved to schema
    # `support`. We prefix name of the view with schema `support.` so that the
    # view is created in that schema.
    suppliers_restricted_view.name = 'support.suppliers_restricted'
    op.execute('DROP VIEW IF EXISTS support.suppliers_restricted')
    op.create_view(suppliers_restricted_view)
