"""person_id_to_support_view

Revision ID: 6019e7f0fc86
Revises: 15987c0f2877
Create Date: 2025-01-03 18:39:42.549146

"""
from alembic import op

from views import ReplaceableObject

# revision identifiers, used by Alembic.
revision = '6019e7f0fc86'
down_revision = '15987c0f2877'
branch_labels = None
depends_on = None


project_users_restricted_view_v1 = ReplaceableObject(
    "support.project_users_restricted",
    """
    AS
        SELECT
            id,
            project_id,
            role,
            notify,
            user_account_id,
            represented_company_id,
            last_changed,
            created_on,
            external_id
        FROM
            project_users
    ;
    """
)
project_users_restricted_view_v2 = ReplaceableObject(
    "support.project_users_restricted",
    """
    AS
        SELECT
            id,
            project_id,
            role,
            notify,
            user_account_id,
            represented_company_id,
            last_changed,
            created_on,
            external_id,
            person_id
        FROM
            project_users
    ;
    """
)


def upgrade():
    op.replace_view(project_users_restricted_view_v2,
                    replaces=project_users_restricted_view_v1)


def downgrade():
    op.replace_view(project_users_restricted_view_v2,
                    replace_with=project_users_restricted_view_v1)
