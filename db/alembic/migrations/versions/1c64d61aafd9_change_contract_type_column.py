"""Add a column

Revision ID: 1c64d61aafd9
Revises: 786bae40c6e4
Create Date: 2021-12-08 13:22:51.959443

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = '1c64d61aafd9'
down_revision = '786bae40c6e4'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column('suppliers', 'contract_type')
    op.execute("DROP TYPE t_supplier_contract_type;")
    t_supplier_contract_type = sa.Enum('contracting', 'consulting', 'personnel_leasing', 'machine_equipment_leasing',
        'materials_and_products', 'transportation', name='t_supplier_contract_type')
    t_supplier_contract_type.create(op.get_bind())
    op.add_column('suppliers', sa.Column('contract_type', t_supplier_contract_type, nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
