"""add added_client_can_view

Revision ID: 20e38d7c55df
Revises: b41a7f2a0087
Create Date: 2024-05-06 09:46:52.897148

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = '20e38d7c55df'
down_revision = 'b41a7f2a0087'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('projects', sa.Column(
        'added_client_can_view', sa.BOOLEAN(), nullable=True)
    )
    # ### end Alembic commands ###
    op.execute("""
        UPDATE projects
        SET added_client_can_view = TRUE
        WHERE project_creator_role = 'main_contractor'
    """)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('projects', 'added_client_can_view')
    # ### end Alembic commands ###
