"""Project creator column add

Revision ID: faeb0e0ea299
Revises: f42ba6ad3212
Create Date: 2024-02-19 10:35:10.518322

"""
import enum

import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = 'faeb0e0ea299'
down_revision = 'f42ba6ad3212'
branch_labels = None
depends_on = None


# Copy of models.ProjectCreatorRoleEnum.
# Prevent edits in models.ProjectCreatorRoleEnum from landing in this version.
class ProjectCreatorRoleEnum(enum.Enum):
    client = 'client'
    main_contractor = 'main_contractor'


def upgrade():
    t_project_creator_role = sa.Enum(ProjectCreatorRoleEnum, name='t_project_creator_role')
    t_project_creator_role.create(op.get_bind())

    op.add_column('projects', sa.Column('project_creator_role',
                                        t_project_creator_role, nullable=True))


def downgrade():
    op.drop_column('projects', 'project_creator_role')
    op.execute("DROP TYPE t_project_creator_role")
