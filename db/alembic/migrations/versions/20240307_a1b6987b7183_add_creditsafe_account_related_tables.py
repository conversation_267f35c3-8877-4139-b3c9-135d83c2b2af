"""Add creditsafe account related tables

Revision ID: a1b6987b7183
Revises: 1333f54bdd3b
Create Date: 2024-03-07 15:12:24.273683

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql
from triggers import ReplaceableObjectTRG
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'a1b6987b7183'
down_revision = '1333f54bdd3b'
branch_labels = None
depends_on = None


creditsafe_account_restricted_view = ReplaceableObject(
    "creditsafe_account_restricted",
    """
    AS
        SELECT
            id,
            person_id,
            org_id,
            username,
            -- password omitted
            state,
            last_changed,
            created_on
        FROM
            creditsafe_account
    ;
    """
)


creditsafe_account_history_restricted_view = ReplaceableObject(
    "creditsafe_account_history_restricted",
    """
    AS
        SELECT
            id,
            creditsafe_account_id,
            changed_by_person_id,
            person_id,
            org_id,
            username,
            state,
            comment,
            created_on
        FROM
            creditsafe_account_history
    ;
    """
)


trigger_set_creditsafe_account_last_changed = ReplaceableObjectTRG(
    "set_creditsafe_account_last_changed",
    "creditsafe_account",
    """
    BEFORE UPDATE ON creditsafe_account
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'creditsafe_account',
        sa.Column(
            'id',
            postgresql.UUID(as_uuid=True),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False
        ),
        sa.Column('person_id', sa.Text(), nullable=True),
        sa.Column('org_id', sa.Text(), nullable=True),
        sa.Column('username', sa.Text(), nullable=True),
        sa.Column('password', sa.Text(), nullable=True),
        sa.Column(
            'state',
            sa.Enum(
                'pending',
                'active',
                'inactive',
                name='t_creditsafe_account_state'
            ),
            nullable=True
        ),
        sa.Column(
            'last_changed',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            nullable=False
        ),
        sa.Column(
            'created_on',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            nullable=False
        ),
        sa.PrimaryKeyConstraint('id', name='pkey_creditsafe_account_ids')
    )
    op.create_index(
        'idx_creditsafe_account_created_on',
        'creditsafe_account',
        ['created_on'],
        unique=False
    )
    op.create_index(
        'idx_creditsafe_account_org_id',
        'creditsafe_account',
        ['org_id'],
        unique=False
    )
    op.create_index(
        'idx_creditsafe_account_person_id',
        'creditsafe_account',
        ['person_id'],
        unique=False
    )
    op.create_index(
        'idx_creditsafe_account_state',
        'creditsafe_account',
        ['state'],
        unique=False
    )
    op.create_index(
        'idx_creditsafe_account_username',
        'creditsafe_account',
        ['username'],
        unique=False
    )
    op.create_table(
        'creditsafe_account_history',
        sa.Column(
            'id',
            postgresql.UUID(as_uuid=True),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False
        ),
        sa.Column('creditsafe_account_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('changed_by_person_id', sa.Text(), nullable=True),
        sa.Column('person_id', sa.Text(), nullable=True),
        sa.Column('org_id', sa.Text(), nullable=True),
        sa.Column('username', sa.Text(), nullable=True),
        sa.Column(
            'state',
            sa.Enum('pending', 'active', 'inactive', name='t_creditsafe_account_history_state'),
            nullable=True
        ),
        sa.Column('comment', sa.Text(), nullable=True),
        sa.Column(
            'created_on',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            nullable=False
        ),
        sa.ForeignKeyConstraint(
            ['creditsafe_account_id'],
            ['creditsafe_account.id'],
            name='fk_creditsafe_account_history_creditsafe_account_id',
            ondelete='CASCADE'
        ),
        sa.PrimaryKeyConstraint('id', name='pkey_creditsafe_account_history_ids')
    )
    op.create_index(
        'idx_creditsafe_account_history_creditsafe_account_id',
        'creditsafe_account_history',
        ['creditsafe_account_id'],
        unique=False
    )
    op.create_trg(trigger_set_creditsafe_account_last_changed)
    op.create_view(creditsafe_account_restricted_view)
    op.create_view(creditsafe_account_history_restricted_view)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_view(creditsafe_account_restricted_view)
    op.drop_view(creditsafe_account_history_restricted_view)
    op.drop_trg(trigger_set_creditsafe_account_last_changed)
    op.drop_index(
        'idx_creditsafe_account_history_creditsafe_account_id',
        table_name='creditsafe_account_history'
    )
    op.drop_table('creditsafe_account_history')
    op.drop_index('idx_creditsafe_account_username', table_name='creditsafe_account')
    op.drop_index('idx_creditsafe_account_state', table_name='creditsafe_account')
    op.drop_index('idx_creditsafe_account_person_id', table_name='creditsafe_account')
    op.drop_index('idx_creditsafe_account_org_id', table_name='creditsafe_account')
    op.drop_index('idx_creditsafe_account_created_on', table_name='creditsafe_account')
    op.drop_table('creditsafe_account')
    op.execute("DROP TYPE t_creditsafe_account_state;")
    op.execute("DROP TYPE t_creditsafe_account_history_state;")
    # ### end Alembic commands ###
