"""Add pa.assigned_to

Revision ID: 7d7742de0b4c
Revises: 53216d19a638
Create Date: 2022-03-04 10:49:33.981194

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '7d7742de0b4c'
down_revision = '53216d19a638'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncements',
                  sa.Column(
                      'assigned_to_company_id', sa.Text(), nullable=True)
                  )
    op.add_column('preannouncements',
                  sa.Column(
                      'assigned_to_supplier_id',
                      postgresql.UUID(as_uuid=True),
                      nullable=True)
                  )
    op.add_column('preannouncements',
                  sa.Column(
                      'assigned_to_time',
                      postgresql.TIMESTAMP(precision=6),
                      nullable=True)
                  )
    op.create_foreign_key(
        'fk_preannouncement_assigned_to_supplier_id',
        'preannouncements',
        'suppliers',
        ['assigned_to_supplier_id'],
        ['id'],
        ondelete='NO ACTION'
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_preannouncement_assigned_to_supplier_id', 'preannouncements', type_='foreignkey')
    op.drop_column('preannouncements', 'assigned_to_time')
    op.drop_column('preannouncements', 'assigned_to_supplier_id')
    op.drop_column('preannouncements', 'assigned_to_company_id')
    # ### end Alembic commands ###
