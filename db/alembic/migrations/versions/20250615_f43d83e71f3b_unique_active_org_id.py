"""creditsafe account unique active orgid

Revision ID: f43d83e71f3b
Revises: 041e1818f2c4
Create Date: 2025-06-15 21:37:23.805296

"""

import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = 'f43d83e71f3b'
down_revision = '041e1818f2c4'
branch_labels = None
depends_on = None


def upgrade():
    # Make all pending statuses inactive
    # If some company has multiple active accounts, only keep the first one
    # and make the rest inactive
    op.execute("""
        UPDATE creditsafe_account
        SET state = 'inactive'
        WHERE state = 'pending'
    """)
    op.execute("""
        WITH ranked_rows AS (
            SELECT
                id,
                ROW_NUMBER() OVER (
                    PARTITION BY org_id
                    ORDER BY created_on DESC
                ) AS rn
            FROM creditsafe_account
            WHERE state = 'active'
        )

        UPDATE creditsafe_account t
        SET state = 'inactive'
        FROM ranked_rows r
        WHERE t.id = r.id AND r.rn > 1;
    """)
    op.create_index(
        'idx_creditsafe_account_unique_active_org_id',
        'creditsafe_account',
        ['org_id'],
        unique=True,
        postgresql_where=sa.text("state = 'active'"),
    )


def downgrade():
    op.drop_index('idx_creditsafe_account_unique_active_org_id')
