"""Add view notification_reports_restricted

Revision ID: 6b361365c810
Revises: acd3300dc9bf
Create Date: 2023-05-24 11:59:49.261497
"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '6b361365c810'
down_revision = 'acd3300dc9bf'
branch_labels = None
depends_on = None


notification_reports_restricted_view = ReplaceableObject(
    "notification_reports_restricted",
    """
    AS
        SELECT
            id,
            external_id,
            generated_timestamp,
            period,
            to_timestamp,
            from_timestamp,
            statuses,
            company_ids,
            user_company_id,
            user_locale,
            -- omitting user_report because it is currently unused, but might
            -- contain sensitive info in the future
            jsonb_build_object(
                'kind', qvarn_report -> 'kind',
                'old_status_cutoff_timestamp', qvarn_report -> 'old_status_cutoff_timestamp',
                'new_status_cutoff_timestamp', qvarn_report -> 'new_status_cutoff_timestamp',
                'statuses', qvarn_report -> 'statuses',
                'org_ids', qvarn_report -> 'org_ids',
                'users_to_notify', COALESCE((
                    SELECT jsonb_object_agg(
                        regexp_replace(
                            key, '^[^ ]*',
                            'anonymous_user_' || row_num
                        ),
                        jsonb_build_object(
                            'user_org_id', value -> 'user_org_id',
                            'projects', COALESCE((
                                SELECT jsonb_object_agg(key, jsonb_build_object(
                                    'project_name', value -> 'project_name',
                                    'project_status', value -> 'project_status',
                                    'companies', COALESCE((
                                        SELECT jsonb_object_agg(key, jsonb_build_object(
                                            'old_status', value -> 'old_status',
                                            'new_status', value -> 'new_status',
                                            'old_report_id', value -> 'old_report_id',
                                            'new_report_id', value -> 'new_report_id'
                                        ))
                                        FROM json_each(value -> 'companies')
                                    ), '{}'::jsonb)
                                ))
                                FROM json_each(value -> 'projects')
                            ), '{}'::jsonb)
                        )
                    )
                    FROM (
                        SELECT key, value, row_number() OVER (ORDER BY key) row_num
                        FROM json_each(qvarn_report -> 'users_to_notify')
                        ORDER BY key
                    ) AS user_dict
                ), '{}'::jsonb)
            ) AS qvarn_report_restricted,
            last_changed,
            created_on
        FROM
            notification_reports
    ;
    """
)


def upgrade():
    op.create_view(notification_reports_restricted_view)


def downgrade():
    op.drop_view(notification_reports_restricted_view)
