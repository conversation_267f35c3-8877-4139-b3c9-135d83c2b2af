"""Add language to qvarn_report_access view

Revision ID: fbd6840f1aae
Revises: 33bd3444eff8
Create Date: 2021-04-19 13:55:57.749216

"""
from alembic import op
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'fbd6840f1aae'
down_revision = '33bd3444eff8'
branch_labels = None
depends_on = None


qvarn_report_access_view_v2 = ReplaceableObject(
    "qvarn_report_access",
    """
    (
        id,
        access_time,
        arkisto_id,
        report_id,
        customer_org_id,
        org_id,
        person_id,
        status,
        language,
        client_id,  -- defunct
        type,  -- defunct
        revision   -- defunct
        -- gov_org_ids []
    )
    AS
        SELECT
            report_accesses.external_id,
            TO_CHAR(report_accesses.access_time::TIMESTAMPTZ, 'YYYY-MM-DD"T"HH24:MI:SSTZH:TZM'),
            report_accesses.arkisto_id,
            report_accesses.report_id,
            report_accesses.customer_company_id,
            report_accesses.company_id,
            report_accesses.person_id,
            report_accesses.status::TEXT,
            report_accesses.language::TEXT,
            NULL,
            'report_access',
            NULL
        FROM
            report_accesses AS report_accesses
    ;
    """
)


def upgrade():
    op.replace_view(qvarn_report_access_view_v2,
                    replaces='caa2fd82d7d6.qvarn_report_access_view')


def downgrade():
    op.replace_view(qvarn_report_access_view_v2,
                    replace_with='caa2fd82d7d6.qvarn_report_access_view')
