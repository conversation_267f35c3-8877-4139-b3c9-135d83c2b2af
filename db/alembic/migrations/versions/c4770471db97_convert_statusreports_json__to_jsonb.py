"""Convert StatusReports.json_ to JSONB

Revision ID: c4770471db97
Revises: 5e2d5385adce
Create Date: 2021-08-24 09:30:29.654231

"""

from sqlalchemy import JSO<PERSON>
from sqlalchemy.dialects.postgresql import JSONB
from alembic import op


# revision identifiers, used by Alembic.
revision = 'c4770471db97'
down_revision = '5e2d5385adce'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        'status_reports', 'json_',
        type_=JSONB(),
    )


def downgrade():
    op.alter_column(
        'status_reports', 'json_',
        type_=JSON(),
    )
