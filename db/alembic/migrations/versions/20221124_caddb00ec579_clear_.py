"""Clear contract work areas where not supported

Revision ID: caddb00ec579
Revises: 1c30096be0c9
Create Date: 2022-11-24 12:04:39.386946

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = 'caddb00ec579'
down_revision = '1c30096be0c9'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        UPDATE suppliers
        SET contract_work_areas = NULL
        WHERE contract_work_areas IS NOT NULL
          AND (contract_type NOT IN ('consulting', 'contracting', 'personnel_leasing')
               OR contract_type IS NULL)
    """)


def downgrade():
    # this is not undoable
    pass
