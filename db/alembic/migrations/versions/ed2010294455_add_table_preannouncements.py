"""Add table preannouncements

Revision ID: ed2010294455
Revises: 786bae40c6e4
Create Date: 2021-12-09 08:50:19.244962

"""
import citext
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'ed2010294455'
down_revision = '1c64d61aafd9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'preannouncements',
        sa.Column('id',
                  postgresql.UUID(as_uuid=True),
                  server_default=sa.text('uuid_generate_v4()'),
                  nullable=False),
        sa.Column('external_id', citext.CIText(), nullable=True),
        sa.Column('status',
                  sa.Enum('created', 'registered', 'confirmed', 'rejected',
                          name='t_preannouncement_status'),
                  nullable=True),
        sa.Column('project_id',
                  postgresql.UUID(as_uuid=True),
                  nullable=True),
        sa.Column('created_by_supplier_id',
                  postgresql.UUID(as_uuid=True),
                  nullable=False),
        sa.Column('for_supplier_id',
                  postgresql.UUID(as_uuid=True),
                  nullable=False),
        sa.Column('last_changed',
                  postgresql.TIMESTAMP(timezone=True),
                  server_default=sa.text('now()'),
                  nullable=False),
        sa.Column('created_on',
                  postgresql.TIMESTAMP(timezone=True),
                  server_default=sa.text('now()'),
                  nullable=False),
        sa.ForeignKeyConstraint(
            ['created_by_supplier_id'],
            ['suppliers.id'],
            name='fk_preannouncement_created_by_supplier_id',
            ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(
            ['for_supplier_id'],
            ['suppliers.id'],
            name='fk_preannouncement_for_supplier_id',
            ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(
            ['project_id'],
            ['projects.id'],
            name='fk_preannouncement_project_id',
            ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint(
            'id',
            name='pkey_preannouncement_ids')
    )
    op.create_index(
        'idx_preannouncements_external_id',
        'preannouncements',
        ['external_id'],
        unique=True
    )
    op.create_index(
        'idx_preannouncements_project_id',
        'preannouncements',
        ['project_id'],
        unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        'idx_preannouncements_project_id',
        table_name='preannouncements')
    op.drop_index(
        'idx_preannouncements_external_id',
        table_name='preannouncements')
    op.drop_table('preannouncements')
    # ### end Alembic commands ###
    op.execute("DROP TYPE t_preannouncement_status")
