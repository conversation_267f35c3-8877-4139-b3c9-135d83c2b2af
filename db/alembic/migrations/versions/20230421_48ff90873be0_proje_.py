"""project_user ondelete CASCADE

Revision ID: 48ff90873be0
Revises: e80e2a9841f0
Create Date: 2023-04-21 14:15:12.807277

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '48ff90873be0'
down_revision = 'e80e2a9841f0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_project_users_project_id', 'project_users', type_='foreignkey')
    op.create_foreign_key('fk_project_users_project_id', 'project_users', 'projects', ['project_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_project_users_project_id', 'project_users', type_='foreignkey')
    op.create_foreign_key('fk_project_users_project_id', 'project_users', 'projects', ['project_id'], ['id'])
    # ### end Alembic commands ###
