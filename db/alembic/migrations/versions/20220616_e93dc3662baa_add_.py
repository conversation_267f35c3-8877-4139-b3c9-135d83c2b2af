"""Add pa form confirmed and rejected company IDs

Revision ID: e93dc3662baa
Revises: 689996cba70e
Create Date: 2022-06-16 13:11:21.639236

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = 'e93dc3662baa'
down_revision = '689996cba70e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('confirmed_gov_org_id', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('confirmed_id_type', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('rejected_gov_org_id', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('rejected_id_type', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('preannouncement_forms', 'rejected_id_type')
    op.drop_column('preannouncement_forms', 'rejected_gov_org_id')
    op.drop_column('preannouncement_forms', 'confirmed_id_type')
    op.drop_column('preannouncement_forms', 'confirmed_gov_org_id')
    # ### end Alembic commands ###
