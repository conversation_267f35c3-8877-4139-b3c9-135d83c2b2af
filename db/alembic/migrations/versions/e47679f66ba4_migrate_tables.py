"""Initial migration: creates tables

Revision ID: e47679f66ba4
Revises: nothing
Create Date: 2021-03-08 14:07:36.437486

"""
import citext
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'e47679f66ba4'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.execute('CREATE EXTENSION "uuid-ossp"')  # For uuid_generate_v4
    op.execute("CREATE EXTENSION citext")
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bulk_import_jobs',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('status', sa.Enum('pending', 'in_progress', 'done', 'failed', name='t_bulk_import_job_status'), nullable=True),
    sa.Column('project_id', sa.Text(), nullable=False),
    sa.Column('imported', postgresql.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('canceled', postgresql.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('companies', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('interested_org_id', sa.Text(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='pkey_bulk_import_jobs')
    )
    op.create_index('idx_bulk_import_jobs_creared_on', 'bulk_import_jobs', ['created_on'], unique=False)
    op.create_index('idx_bulk_import_jobs_status', 'bulk_import_jobs', ['status'], unique=False)
    op.create_table('notification_reports',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('external_id', citext.CIText(), nullable=True),
    sa.Column('generated_timestamp', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.Column('period', sa.Enum('daily', 'weekly', 'monthly', name='t_period'), nullable=True),
    sa.Column('to_timestamp', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.Column('from_timestamp', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.Column('statuses', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.Column('company_ids', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.Column('user_company_id', sa.Text(), nullable=True),
    sa.Column('user_email', sa.Text(), nullable=True),
    sa.Column('user_name', sa.Text(), nullable=True),
    sa.Column('user_locale', sa.Text(), nullable=True),
    sa.Column('user_report', sa.JSON(), nullable=True),
    sa.Column('qvarn_report', sa.JSON(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='pkey_notification_reports_ids')
    )
    op.create_index('idx_notification_reports_external_id', 'notification_reports', ['external_id'], unique=False)
    op.create_index('idx_notification_reports_generated_timestamp', 'notification_reports', ['generated_timestamp'], unique=False)
    op.create_table('projects',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('external_id', citext.CIText(), nullable=True),
    sa.Column('name', sa.Text(), nullable=True),
    sa.Column('tax_id', sa.Text(), nullable=True),
    sa.Column('client_company_id', sa.Text(), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('state', sa.Enum('draft', 'active', 'closed', name='t_project_state'), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='pkey_projects')
    )
    op.create_index('idx_projects_client_company_id', 'projects', ['client_company_id'], unique=False)
    op.create_index('idx_projects_external_id', 'projects', ['external_id'], unique=False)
    op.create_index('idx_projects_name', 'projects', ['name'], unique=False)
    op.create_index('idx_projects_state', 'projects', ['state'], unique=False)
    op.create_index('idx_projects_tax_id', 'projects', ['tax_id'], unique=False)
    op.create_table('report_accesses',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('access_time', postgresql.TIMESTAMP(timezone=True, precision=6), nullable=True),
    sa.Column('external_id', citext.CIText(), nullable=True),
    sa.Column('arkisto_id', sa.Text(), nullable=True),
    sa.Column('report_id', sa.Text(), nullable=True),
    sa.Column('status', sa.Enum('active', 'hidden', name='t_report_access_status'), nullable=True),
    sa.Column('customer_company_id', sa.Text(), nullable=True),
    sa.Column('company_id', sa.Text(), nullable=True),
    sa.Column('company_gov_id', sa.Text(), nullable=True),
    sa.Column('company_gov_id_country', sa.Enum('FI', 'EE', name='t_company_country'), nullable=True),
    sa.Column('company_gov_id_type', sa.Enum('registration_number', name='t_company_gov_id_type'), nullable=True),
    sa.Column('person_id', sa.Text(), nullable=True),
    sa.Column('language', sa.Text(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='pkey_report_accesses')
    )
    op.create_index('idx_report_accesses_access_time', 'report_accesses', ['access_time'], unique=False)
    op.create_index('idx_report_accesses_arkisto_id', 'report_accesses', ['arkisto_id'], unique=False)
    op.create_index('idx_report_accesses_company_id', 'report_accesses', ['company_id'], unique=False)
    op.create_index('idx_report_accesses_customer_company_id', 'report_accesses', ['customer_company_id'], unique=False)
    op.create_index('idx_report_accesses_external_id', 'report_accesses', ['external_id'], unique=False)
    op.create_index('idx_report_accesses_person_id', 'report_accesses', ['person_id'], unique=False)
    op.create_index('idx_report_accesses_report_id', 'report_accesses', ['report_id'], unique=False)
    op.create_table('status_reports',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('external_id', citext.CIText(), nullable=True),
    sa.Column('status', sa.Enum('100 STOP', '200 INVESTIGATE', '300 INCOMPLETE', '400 ATTENTION', '500 OK', name='t_status_report_status'), nullable=True),
    sa.Column('generated_timestamp', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.Column('interested_company_id', sa.Text(), nullable=True),
    sa.Column('company_id', sa.Text(), nullable=True),
    sa.Column('json_', sa.JSON(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='pkey_status_reports')
    )
    op.create_index('idx_status_reports_company_id', 'status_reports', ['company_id'], unique=False)
    op.create_index('idx_status_reports_external_id', 'status_reports', ['external_id'], unique=False)
    op.create_index('idx_status_reports_generated_timestamp', 'status_reports', ['generated_timestamp'], unique=False)
    op.create_index('idx_status_reports_interested_company_id', 'status_reports', ['interested_company_id'], unique=False)
    op.create_index('idx_status_reports_status', 'status_reports', ['status'], unique=False)
    op.create_table('internal_project_ids',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('internal_project_id', sa.Text(), nullable=True),
    sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('company_id', sa.Text(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name='fk_internal_project_project_id', ondelete='NO ACTION'),
    sa.PrimaryKeyConstraint('id', name='pkey_internal_project_ids'),
    sa.UniqueConstraint('project_id', 'company_id', name='internal_project_ids_project_id_company_id_key')
    )
    op.create_index('idx_internal_project_ids_company_id', 'internal_project_ids', ['company_id'], unique=False)
    op.create_index('idx_internal_project_ids_internal_project_id', 'internal_project_ids', ['internal_project_id'], unique=False)
    op.create_index('idx_internal_project_ids_project_id', 'internal_project_ids', ['project_id'], unique=False)
    op.create_table('project_users',
    sa.Column('id', sa.Text(), nullable=False),
    sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('role', sa.Enum('leader', 'manager', 'member', name='t_project_user_role'), nullable=True),
    sa.Column('notify', sa.BOOLEAN(), server_default='t', nullable=True),
    sa.Column('user_account_id', sa.Text(), nullable=True),
    sa.Column('represented_company_id', sa.Text(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name='fk_project_users_project_id', ondelete='NO ACTION'),
    sa.PrimaryKeyConstraint('id', name='pkey_project_users')
    )
    op.create_index('idx_poject_users_project_id', 'project_users', ['project_id'], unique=False)
    op.create_index('idx_poject_users_represented_company_id', 'project_users', ['represented_company_id'], unique=False)
    op.create_index('idx_poject_users_role', 'project_users', ['role'], unique=False)
    op.create_index('idx_poject_users_user_account_id', 'project_users', ['user_account_id'], unique=False)
    op.create_table('suppliers',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('external_id', citext.CIText(), nullable=True),
    sa.Column('role', sa.Enum('main_contractor', 'supervisor', 'supplier', name='t_supplier_role'), nullable=True),
    sa.Column('type', sa.Enum('linked', 'unlinked', 'visitor', name='t_supplier_type'), nullable=True),
    sa.Column('contract_start_date', sa.Date(), nullable=True),
    sa.Column('contract_end_date', sa.Date(), nullable=True),
    sa.Column('materialized_path', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('parent_supplier_id', sa.Text(), nullable=True),
    sa.Column('parent_company_id', sa.Text(), nullable=True),
    sa.Column('company_id', sa.Text(), nullable=True),
    sa.Column('revision', sa.Text(), nullable=True),
    sa.Column('last_visited', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('first_visited', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name='fk_suppliers_project_id', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='pkey_suppliers')
    )
    op.create_index('idx_suppliers_company_id', 'suppliers', ['company_id'], unique=False)
    op.create_index('idx_suppliers_external_id', 'suppliers', ['external_id'], unique=False)
    op.create_index('idx_suppliers_project_id', 'suppliers', ['project_id'], unique=False)
    op.create_index('idx_suppliers_role', 'suppliers', ['role'], unique=False)
    op.create_index('idx_suppliers_type', 'suppliers', ['type'], unique=False)
    op.create_table('supplier_contacts',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('supplier_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('person_id', sa.Text(), nullable=True),
    sa.Column('person_email', sa.Text(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], name='fk_suppliers_id', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='pkey_supplier_contacts')
    )
    op.create_index('idx_supplier_contacts_supplier_id', 'supplier_contacts', ['supplier_id'], unique=False)
    op.create_index('idx_supplier_person_id', 'supplier_contacts', ['person_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_supplier_person_id', table_name='supplier_contacts')
    op.drop_index('idx_supplier_contacts_supplier_id', table_name='supplier_contacts')
    op.drop_table('supplier_contacts')
    op.drop_index('idx_suppliers_type', table_name='suppliers')
    op.drop_index('idx_suppliers_role', table_name='suppliers')
    op.drop_index('idx_suppliers_project_id', table_name='suppliers')
    op.drop_index('idx_suppliers_external_id', table_name='suppliers')
    op.drop_index('idx_suppliers_company_id', table_name='suppliers')
    op.drop_table('suppliers')
    op.drop_index('idx_poject_users_user_account_id', table_name='project_users')
    op.drop_index('idx_poject_users_role', table_name='project_users')
    op.drop_index('idx_poject_users_represented_company_id', table_name='project_users')
    op.drop_index('idx_poject_users_project_id', table_name='project_users')
    op.drop_table('project_users')
    op.drop_index('idx_internal_project_ids_project_id', table_name='internal_project_ids')
    op.drop_index('idx_internal_project_ids_internal_project_id', table_name='internal_project_ids')
    op.drop_index('idx_internal_project_ids_company_id', table_name='internal_project_ids')
    op.drop_table('internal_project_ids')
    op.drop_index('idx_status_reports_status', table_name='status_reports')
    op.drop_index('idx_status_reports_interested_company_id', table_name='status_reports')
    op.drop_index('idx_status_reports_generated_timestamp', table_name='status_reports')
    op.drop_index('idx_status_reports_external_id', table_name='status_reports')
    op.drop_index('idx_status_reports_company_id', table_name='status_reports')
    op.drop_table('status_reports')
    op.drop_index('idx_report_accesses_report_id', table_name='report_accesses')
    op.drop_index('idx_report_accesses_person_id', table_name='report_accesses')
    op.drop_index('idx_report_accesses_external_id', table_name='report_accesses')
    op.drop_index('idx_report_accesses_customer_company_id', table_name='report_accesses')
    op.drop_index('idx_report_accesses_company_id', table_name='report_accesses')
    op.drop_index('idx_report_accesses_arkisto_id', table_name='report_accesses')
    op.drop_index('idx_report_accesses_access_time', table_name='report_accesses')
    op.drop_table('report_accesses')
    op.drop_index('idx_projects_tax_id', table_name='projects')
    op.drop_index('idx_projects_state', table_name='projects')
    op.drop_index('idx_projects_name', table_name='projects')
    op.drop_index('idx_projects_external_id', table_name='projects')
    op.drop_index('idx_projects_client_company_id', table_name='projects')
    op.drop_table('projects')
    op.drop_index('idx_notification_reports_generated_timestamp', table_name='notification_reports')
    op.drop_index('idx_notification_reports_external_id', table_name='notification_reports')
    op.drop_table('notification_reports')
    op.drop_index('idx_bulk_import_jobs_status', table_name='bulk_import_jobs')
    op.drop_index('idx_bulk_import_jobs_creared_on', table_name='bulk_import_jobs')
    op.drop_table('bulk_import_jobs')
    # ### end Alembic commands ###
    op.execute("DROP TYPE t_period")
    op.execute("DROP TYPE t_bulk_import_job_status")
    op.execute("DROP TYPE t_project_state")
    op.execute("DROP TYPE t_report_access_status")
    op.execute("DROP TYPE t_company_country")
    op.execute("DROP TYPE t_company_gov_id_type")
    op.execute("DROP TYPE t_status_report_status")
    op.execute("DROP TYPE t_project_user_role")
    op.execute("DROP TYPE t_supplier_role")
    op.execute("DROP TYPE t_supplier_type")
    op.execute("DROP EXTENSION citext")
    op.execute('DROP EXTENSION "uuid-ossp"')
