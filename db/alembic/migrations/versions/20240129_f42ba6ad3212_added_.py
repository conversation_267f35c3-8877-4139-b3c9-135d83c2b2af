"""added_client_confirmed to projects support view

Revision ID: f42ba6ad3212
Revises: e2af533b845d
Create Date: 2024-01-29 13:27:02.616152

"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'f42ba6ad3212'
down_revision = 'e2af533b845d'
branch_labels = None
depends_on = None


projects_restricted_columns_v3 = [
    'id',
    'external_id',
    'name',
    'tax_id',
    'client_company_id',
    'start_date',
    'end_date',
    'state',
    'last_changed',
    'created_on',
    'pa_form_enabled',
    'created_by_org_id',
    'client_contact_person_id',
    'client_contact_person_email',
    'added_client_confirmed'
]

projects_restricted_view_v3 = ReplaceableObject(
    "support.projects_restricted",
    """
    AS
        SELECT
            %s
        FROM
            projects
    ;
    """ % ', '.join(projects_restricted_columns_v3)
)


def upgrade():
    op.replace_view(projects_restricted_view_v3,
                    replaces='60fae8e92e4a.projects_restricted_view_v2')


def downgrade():
    op.replace_view(projects_restricted_view_v3,
                    replace_with='60fae8e92e4a.projects_restricted_view_v2')
