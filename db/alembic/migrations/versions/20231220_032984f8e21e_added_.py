"""added added_client_confirmed

Revision ID: 032984f8e21e
Revises: 4c9b59204be6
Create Date: 2023-12-20 00:29:25.346156

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '032984f8e21e'
down_revision = '4c9b59204be6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('projects', sa.Column('added_client_confirmed', sa.BOOLEAN(), server_default='False', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('projects', 'added_client_confirmed')
    # ### end Alembic commands ###
