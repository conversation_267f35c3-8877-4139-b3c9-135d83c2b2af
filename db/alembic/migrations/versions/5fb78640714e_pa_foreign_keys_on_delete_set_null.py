"""pa foreign keys on delete set null

Revision ID: 5fb78640714e
Revises: 7d7742de0b4c
Create Date: 2022-03-17 10:41:15.196414

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '5fb78640714e'
down_revision = '7d7742de0b4c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_preannouncement_for_supplier_id', 'preannouncements', type_='foreignkey')
    op.drop_constraint('fk_preannouncement_assigned_to_supplier_id', 'preannouncements', type_='foreignkey')
    op.drop_constraint('fk_preannouncement_project_id', 'preannouncements', type_='foreignkey')
    op.drop_constraint('fk_preannouncement_created_by_supplier_id', 'preannouncements', type_='foreignkey')
    op.create_foreign_key('fk_preannouncement_for_supplier_id', 'preannouncements', 'suppliers', ['for_supplier_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('fk_preannouncement_project_id', 'preannouncements', 'projects', ['project_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('fk_preannouncement_assigned_to_supplier_id', 'preannouncements', 'suppliers', ['assigned_to_supplier_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('fk_preannouncement_created_by_supplier_id', 'preannouncements', 'suppliers', ['created_by_supplier_id'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_preannouncement_created_by_supplier_id', 'preannouncements', type_='foreignkey')
    op.drop_constraint('fk_preannouncement_assigned_to_supplier_id', 'preannouncements', type_='foreignkey')
    op.drop_constraint('fk_preannouncement_project_id', 'preannouncements', type_='foreignkey')
    op.drop_constraint('fk_preannouncement_for_supplier_id', 'preannouncements', type_='foreignkey')
    op.create_foreign_key('fk_preannouncement_created_by_supplier_id', 'preannouncements', 'suppliers', ['created_by_supplier_id'], ['id'])
    op.create_foreign_key('fk_preannouncement_project_id', 'preannouncements', 'projects', ['project_id'], ['id'])
    op.create_foreign_key('fk_preannouncement_assigned_to_supplier_id', 'preannouncements', 'suppliers', ['assigned_to_supplier_id'], ['id'])
    op.create_foreign_key('fk_preannouncement_for_supplier_id', 'preannouncements', 'suppliers', ['for_supplier_id'], ['id'])
    # ### end Alembic commands ###
