"""Update informant supplier fields

Revision ID: e40d13610178
Revises: 64a9fbe4f090
Create Date: 2022-07-08 10:43:17.973075

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = 'e40d13610178'
down_revision = '64a9fbe4f090'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('informant_supplier_email', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('informant_supplier_first_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('informant_supplier_last_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('informant_supplier_phone', sa.Text(), nullable=True))
    op.drop_column('preannouncement_forms', 'informant_email')
    op.drop_column('preannouncement_forms', 'informant_name')
    op.drop_column('preannouncement_forms', 'informant_phone')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('informant_phone', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('preannouncement_forms', sa.Column('informant_name', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('preannouncement_forms', sa.Column('informant_email', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_column('preannouncement_forms', 'informant_supplier_phone')
    op.drop_column('preannouncement_forms', 'informant_supplier_last_name')
    op.drop_column('preannouncement_forms', 'informant_supplier_first_name')
    op.drop_column('preannouncement_forms', 'informant_supplier_email')
    # ### end Alembic commands ###
