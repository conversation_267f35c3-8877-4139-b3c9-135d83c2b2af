"""add interested_org_id to idx_report_cache_unique_cols

Revision ID: 58489b3ba0a2
Revises: 48ff90873be0
Create Date: 2023-04-28 10:57:58.822403

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '58489b3ba0a2'
down_revision = '48ff90873be0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_report_cache_unique_cols', table_name='report_cache')
    op.create_index('idx_report_cache_unique_cols', 'report_cache', ['key', 'provider', 'type', 'interested_org_id'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_report_cache_unique_cols', table_name='report_cache')
    op.create_index('idx_report_cache_unique_cols', 'report_cache', ['key', 'provider', 'type'], unique=True)
    # ### end Alembic commands ###
