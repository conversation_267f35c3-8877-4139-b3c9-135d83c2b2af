"""Add person_id and person_email to supplier_contacts_restricted

Revision ID: 69b72509c4c6
Revises: 03a5a4a5b399
Create Date: 2024-08-06 12:52:37.775353

"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '69b72509c4c6'
down_revision = '03a5a4a5b399'
branch_labels = None
depends_on = None


# This should be the same as supplier_contacts_restricted_view in 20230523_acd3300dc9bf_add_.py,
# except in the 'support' schema rather than the public schema.
supplier_contacts_restricted_view_v1 = ReplaceableObject(
    "support.supplier_contacts_restricted",
    """
    AS
        SELECT
            id,
            supplier_id,
            last_changed,
            created_on
        FROM
            supplier_contacts
    ;
    """
)

supplier_contacts_restricted_view_v2 = ReplaceableObject(
    "support.supplier_contacts_restricted",
    """
    AS
        SELECT
            id,
            supplier_id,
            last_changed,
            created_on,
            person_id,
            person_email
        FROM
            supplier_contacts
    ;
    """
)


def upgrade():
    op.replace_view(supplier_contacts_restricted_view_v2,
                    replaces=supplier_contacts_restricted_view_v1)


def downgrade():
    op.replace_view(supplier_contacts_restricted_view_v2,
                    replace_with=supplier_contacts_restricted_view_v1)
