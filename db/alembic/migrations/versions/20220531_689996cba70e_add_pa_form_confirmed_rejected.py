"""Add pa form confirmed and rejected

Revision ID: 689996cba70e
Revises: 504d4961078b
Create Date: 2022-05-31 07:05:45.773305

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '689996cba70e'
down_revision = '504d4961078b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('confirmed_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('confirmed_time', postgresql.TIMESTAMP(precision=6), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('rejected_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('rejected_time', postgresql.TIMESTAMP(precision=6), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('preannouncement_forms', 'rejected_time')
    op.drop_column('preannouncement_forms', 'rejected_name')
    op.drop_column('preannouncement_forms', 'confirmed_time')
    op.drop_column('preannouncement_forms', 'confirmed_name')
    # ### end Alembic commands ###
