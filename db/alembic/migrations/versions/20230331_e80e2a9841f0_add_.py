"""Add view projects_restricted

Revision ID: e80e2a9841f0
Revises: f6a1637d1f69
Create Date: 2023-03-31 06:53:19.109334

"""
from alembic import op
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'e80e2a9841f0'
down_revision = 'f6a1637d1f69'
branch_labels = None
depends_on = None

projects_restricted_columns = [
    'id',
    'external_id',
    'name',
    'tax_id',
    'client_company_id',
    'start_date',
    'end_date',
    'state',
    'last_changed',
    'created_on',
    'pa_form_enabled',
]

projects_restricted_view = ReplaceableObject(
    "projects_restricted",
    """
    AS
        SELECT
            %s
        FROM
            projects
    ;
    """ % ','.join(projects_restricted_columns)
)


def upgrade():
    op.create_view(projects_restricted_view)


def downgrade():
    op.drop_view(projects_restricted_view)
