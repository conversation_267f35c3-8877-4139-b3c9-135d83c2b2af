"""Add client_contact_person_id, client_contact_person_email

Revision ID: 3ff98b0101aa
Revises: 68c937953557
Create Date: 2023-09-05 06:57:59.640668

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '3ff98b0101aa'
down_revision = '68c937953557'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('projects', sa.Column('client_contact_person_id', sa.Text(), nullable=True))
    op.add_column('projects', sa.Column('client_contact_person_email', sa.Text(), nullable=True))
    op.create_index('idx_projects_client_contact_person_email', 'projects', ['client_contact_person_email'], unique=False)
    op.create_index('idx_projects_client_contact_person_id', 'projects', ['client_contact_person_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_projects_client_contact_person_id', table_name='projects')
    op.drop_index('idx_projects_client_contact_person_email', table_name='projects')
    op.drop_column('projects', 'client_contact_person_email')
    op.drop_column('projects', 'client_contact_person_id')
    # ### end Alembic commands ###
