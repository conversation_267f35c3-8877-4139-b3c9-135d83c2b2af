"""Add pa_form_enabled

Revision ID: 715254d4a0b9
Revises: c4770471db97
Create Date: 2021-11-23 15:54:18.030686

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '715254d4a0b9'
down_revision = 'c4770471db97'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('projects', sa.Column('pa_form_enabled',
                                        sa.<PERSON>(),
                                        server_default='False',
                                        nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('projects', 'pa_form_enabled')
    # ### end Alembic commands ###
