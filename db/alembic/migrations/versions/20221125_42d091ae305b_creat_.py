"""Create status_reports_history table

Revision ID: 42d091ae305b
Revises: 4a3e58c9a37d
Create Date: 2022-11-25 09:33:39.571756

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql
import citext


# revision identifiers, used by Alembic.
revision = '42d091ae305b'
down_revision = '4a3e58c9a37d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('status_reports_history',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('external_id', citext.CIText(), nullable=True),
    sa.Column('status', sa.Text(), nullable=True),
    sa.Column('generated_timestamp', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.Column('interested_company_id', sa.Text(), nullable=True),
    sa.Column('company_id', sa.Text(), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('charge_reference', sa.Text(), nullable=True),
    sa.Column('used_providers', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id', name='pkey_status_reports_history')
    )
    op.create_index('idx_status_reports_history_charge_reference', 'status_reports_history', ['charge_reference'], unique=False)
    op.create_index('idx_status_reports_history_company_id', 'status_reports_history', ['company_id'], unique=False)
    op.create_index('idx_status_reports_history_external_id', 'status_reports_history', ['external_id'], unique=False)
    op.create_index('idx_status_reports_history_generated_timestamp', 'status_reports_history', ['generated_timestamp'], unique=False)
    op.create_index('idx_status_reports_history_interested_company_id', 'status_reports_history', ['interested_company_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_status_reports_history_interested_company_id', table_name='status_reports_history')
    op.drop_index('idx_status_reports_history_generated_timestamp', table_name='status_reports_history')
    op.drop_index('idx_status_reports_history_external_id', table_name='status_reports_history')
    op.drop_index('idx_status_reports_history_company_id', table_name='status_reports_history')
    op.drop_index('idx_status_reports_history_charge_reference', table_name='status_reports_history')
    op.drop_table('status_reports_history')
    # ### end Alembic commands ###
