"""Initial creation of PostgreSQL views

Revision ID: caa2fd82d7d6
Revises: e47679f66ba4
Create Date: 2020-11-30 12:44:54.076314

"""
from alembic import op
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'caa2fd82d7d6'
down_revision = 'e47679f66ba4'
branch_labels = None
depends_on = None


bol_supplier_view = ReplaceableObject(
    "qvarn_bol_supplier",
    """
    (
        id,
        project_resource_id,
        supplier_type,
        parent_supplier_id,
        parent_org_id,
        supplier_org_id,
        contract_start_date,
        contract_end_date,
        supplier_role,
        internal_project_id,
        revision,
        bolagsfakta_status,  -- defunct
        type -- defunct
    )
    AS
        SELECT
            suppliers.external_id,
            projects.external_id,
            suppliers.type::text,
            suppliers.parent_supplier_id,
            suppliers.parent_company_id,
            suppliers.company_id,
            TO_CHAR(suppliers.contract_start_date :: DATE, 'yyyy-mm-dd'),
            TO_CHAR(suppliers.contract_end_date :: DATE, 'yyyy-mm-dd'),
            suppliers.role::text,
            internal_project_ids.internal_project_id,
            suppliers.revision,
            NULL,
            'bol_supplier'
        FROM
            suppliers AS suppliers
        LEFT OUTER JOIN
            internal_project_ids AS internal_project_ids
        ON
            suppliers.project_id = internal_project_ids.project_id
            AND
            suppliers.company_id = internal_project_ids.company_id
        JOIN projects as projects
        ON projects.id = suppliers.project_id
    ;
    """
)


qvarn_bol_supplier_materialized_path_view = ReplaceableObject(
    "qvarn_bol_supplier_materialized_path",
    """
    (
        id,
        list_pos,
        materialized_path
    )
    AS
        SELECT
            external_id as id,
            mp.nr,
            mp.elem
        FROM
            suppliers,
            unnest(materialized_path) WITH ORDINALITY mp(elem, nr)
    ;
    """
)


bol_supplier_supplier_contacts_view = ReplaceableObject(
    "qvarn_bol_supplier_supplier_contacts",
    """
    (
        id,
        supplier_contact_person_id,
        supplier_contact_email,
        list_pos
    )
    AS
        SELECT
            suppliers.external_id as id,
            person_id,
            person_email,
            0  -- We don't support more than 1 supplier contact per supplier
        FROM
            supplier_contacts
        JOIN suppliers ON suppliers.id = supplier_contacts.supplier_id
    ;
    """
)


qvarn_project_projects_ids_view = ReplaceableObject(
    "qvarn_project_project_ids",
    """
    (
        id,
        project_id,
        project_id_type,
        list_pos
    )
    AS
    SELECT
        unified_internal_ids.id,
        unified_internal_ids.project_id,
        unified_internal_ids.project_id_type,
        ROW_NUMBER () OVER (ORDER BY unified_internal_ids.id) - 1
    FROM
        (
            SELECT DISTINCT
                projects.external_id AS id,
                projects.tax_id AS project_id,
                'tax_id' AS project_id_type
            FROM
                projects AS projects
            WHERE
                projects.tax_id IS NOT NULL

            UNION

            SELECT DISTINCT
                projects.external_id AS id,
                internal_project_ids.internal_project_id AS project_id,
                'trafikverket_project_id' AS project_id_type
            FROM
                internal_project_ids AS internal_project_ids

            JOIN projects as projects
            ON projects.id = internal_project_ids.project_id
            WHERE
                internal_project_ids.internal_project_id IS NOT NULL
        ) unified_internal_ids
    ;
    """
)


qvarn_project_view = ReplaceableObject(
    "qvarn_project",
    """
    (
        id,
        project_responsible_org,
        state,
        start_date,
        end_date,
        project_responsible_person,  -- defunct
        type,  -- defunct
        revision,  -- defunct
        country -- defunct
    )
    AS
        SELECT
            projects.external_id,
            client_company_id,
            projects.state::text,
            TO_CHAR(projects.start_date :: DATE, 'yyyy-mm-dd'),
            TO_CHAR(projects.end_date :: DATE, 'yyyy-mm-dd'),
            NULL,
            'project',
            NULL,
            NULL
        FROM
            projects AS projects
    ;
    """
)


qvarn_project_names_view = ReplaceableObject(
    "qvarn_project_names",
    """
    (
        id,
        names,
        list_pos
    )
    AS
    SELECT
        projects.external_id,
        project_names.elem,
        project_names.nr  -- We don't support more than 1 project name
    FROM
        projects
    LEFT JOIN LATERAL
    unnest(string_to_array(projects.name, '; ')) WITH ORDINALITY AS project_names(elem, nr)
    ON TRUE
    ;
    """
)


qvarn_report_access_view = ReplaceableObject(
    "qvarn_report_access",
    """
    (
        id,
        access_time,
        arkisto_id,
        report_id,
        customer_org_id,
        org_id,
        person_id,
        status,
        client_id,  -- defunct
        type,  -- defunct
        revision   -- defunct
        -- gov_org_ids []
    )
    AS
        SELECT
            report_accesses.external_id,
            TO_CHAR(report_accesses.access_time::TIMESTAMPTZ, 'YYYY-MM-DD"T"HH24:MI:SSTZH:TZM'),
            report_accesses.arkisto_id,
            report_accesses.report_id,
            report_accesses.customer_company_id,
            report_accesses.company_id,
            report_accesses.person_id,
            report_accesses.status::TEXT,
            NULL,
            'report_access',
            NULL
        FROM
            report_accesses AS report_accesses
    ;
    """
)


qvarn_report_access_gov_org_ids_view = ReplaceableObject(
    "qvarn_report_access_gov_org_ids",
    """
    (
        id,
        gov_org_id,
        org_id_type,
        country,
        list_pos
    )
    AS
    SELECT
        report_accesses.external_id,
        report_accesses.company_gov_id,
        report_accesses.company_gov_id_type::TEXT,
        report_accesses.company_gov_id_country::TEXT,
        0  -- We don't support more than 1 supplier contact per supplier
    FROM
        report_accesses AS report_accesses
    ;
    """
)


qvarn_report_view = ReplaceableObject(
    "qvarn_report",
    """
    (
        id,
        report_type,
        generated_timestamp,
        org,
        interested_org_id,
        status,
        tilaajavastuu_status,  -- defunct
        type,  -- defunct
        revision,  -- defunct
        archive_code,  -- defunct
        certificates,  -- defunct
        company_information,  -- defunct
        company_relations,  -- defunct
        interpretation,  -- defunct
        notes,  -- defunct
        oldest_source_date,  -- defunct
        operating_licenses,  -- defunct
        ratings,  -- defunct
        registry_information,  -- defunct
        registry_memberships,  -- defunct
        report_version,  -- defunct
        reported_information,  -- defunct
        service_provider,  -- defunct
        valid_from_date,  -- defunct
        valid_until_date  -- defunct
    )
    AS
    SELECT
        reports.id,
        reports.report_type,
        reports.generated_timestamp,
        reports.org,
        reports.interested_org_id,
        reports.status,
        reports.tilaajavastuu_status,  -- defunct
        reports.type,  -- defunct
        reports.revision,  -- defunct
        reports.archive_code,  -- defunct
        reports.certificates,  -- defunct
        reports.company_information,  -- defunct
        reports.company_relations,  -- defunct
        reports.interpretation,  -- defunct
        reports.notes,  -- defunct
        reports.oldest_source_date,  -- defunct
        reports.operating_licenses,  -- defunct
        reports.ratings,  -- defunct
        reports.registry_information,  -- defunct
        reports.registry_memberships,  -- defunct
        reports.report_version,  -- defunct
        reports.reported_information,  -- defunct
        reports.service_provider,  -- defunct
        reports.valid_from_date,  -- defunct
        reports.valid_until_date  -- defunct
    FROM
        (
        SELECT
            status_reports.external_id::TEXT AS id,
            'bolagsfakta.company_report' AS report_type,
            TO_CHAR(
                status_reports.generated_timestamp::TIMESTAMP, 'YYYY-MM-DD"T"HH24:MI:SS.US'
                ) AS generated_timestamp,
            status_reports.company_id AS org,
            status_reports.interested_company_id AS interested_org_id,
            NULL AS status,
            status_reports.status::TEXT AS tilaajavastuu_status,
            'report' AS type,
            NULL AS revision,
            NULL AS archive_code,
            ARRAY[]::TEXT[] AS certificates,
            ARRAY[]::TEXT[] AS company_information,
            ARRAY[]::TEXT[] AS company_relations,
            NULL AS interpretation,
            ARRAY[]::TEXT[] AS notes,
            NULL AS oldest_source_date,
            ARRAY[]::TEXT[] AS operating_licenses,
            ARRAY[]::TEXT[] AS ratings,
            ARRAY[]::TEXT[] AS registry_information,
            ARRAY[]::TEXT[] AS registry_memberships,
            NULL AS report_version,
            ARRAY[]::TEXT[] AS reported_information,
            NULL AS service_provider,
            NULL AS valid_from_date,
            NULL AS valid_until_date
        FROM
            status_reports AS status_reports
        UNION

        SELECT
            notification_reports.external_id AS id,
            'bolagsfakta.status_change_report' AS report_type,
            TO_CHAR(
            notification_reports.generated_timestamp::TIMESTAMP, 'YYYY-MM-DD"T"HH24:MI:SS.US'
            ) AS generated_timestamp,
            NULL AS org,
            NULL AS interested_org_id,
            NULL AS status,
            NULL AS tilaajavastuu_status,
            'report' AS type,
            NULL AS revision,
            NULL AS archive_code,
            ARRAY[]::TEXT[] AS certificates,
            ARRAY[]::TEXT[] AS company_information,
            ARRAY[]::TEXT[] AS company_relations,
            NULL AS interpretation,
            ARRAY[]::TEXT[] AS notes,
            NULL AS oldest_source_date,
            ARRAY[]::TEXT[] AS operating_licenses,
            ARRAY[]::TEXT[] AS ratings,
            ARRAY[]::TEXT[] AS registry_information,
            ARRAY[]::TEXT[] AS registry_memberships,
            NULL AS report_version,
            ARRAY[]::TEXT[] AS reported_information,
            NULL AS service_provider,
            NULL AS valid_from_date,
            NULL AS valid_until_date
        FROM
            notification_reports AS notification_reports
        ) reports
    ;
    """
)


def upgrade():
    op.create_view(bol_supplier_view)
    op.create_view(qvarn_bol_supplier_materialized_path_view)
    op.create_view(bol_supplier_supplier_contacts_view)
    op.create_view(qvarn_project_projects_ids_view)
    op.create_view(qvarn_project_view)
    op.create_view(qvarn_project_names_view)
    op.create_view(qvarn_report_access_view)
    op.create_view(qvarn_report_access_gov_org_ids_view)
    op.create_view(qvarn_report_view)


def downgrade():
    op.drop_view(bol_supplier_view)
    op.drop_view(qvarn_bol_supplier_materialized_path_view)
    op.drop_view(bol_supplier_supplier_contacts_view)
    op.drop_view(qvarn_project_projects_ids_view)
    op.drop_view(qvarn_project_view)
    op.drop_view(qvarn_project_names_view)
    op.drop_view(qvarn_report_access_view)
    op.drop_view(qvarn_report_access_gov_org_ids_view)
    op.drop_view(qvarn_report_view)
