"""person_id on project_user

Revision ID: 15987c0f2877
Revises: c199d550774d
Create Date: 2024-09-25 09:36:38.886903

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '15987c0f2877'
down_revision = 'c199d550774d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('project_users', sa.Column('person_id', sa.Text(), nullable=True))
    op.create_index('idx_project_users_person_id', 'project_users', ['person_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_project_users_person_id', table_name='project_users')
    op.drop_column('project_users', 'person_id')
    # ### end Alembic commands ###
