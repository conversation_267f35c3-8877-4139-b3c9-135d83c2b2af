"""Move creditsafe account support view to support schema

Revision ID: b41a7f2a0087
Revises: a1b6987b7183
Create Date: 2024-03-19 15:07:13.056449

"""
from alembic import op
from views import ReplaceableObject

from models import PUBLIC_SCHEMA, SUPPORT_SCHEMA


# revision identifiers, used by Alembic.
revision = 'b41a7f2a0087'
down_revision = 'a1b6987b7183'
branch_labels = None
depends_on = None

support_schema = ReplaceableObject(SUPPORT_SCHEMA, None)
public_schema = ReplaceableObject(PUBLIC_SCHEMA, None)
moved_view_names = [
    'creditsafe_account_restricted',
    'creditsafe_account_history_restricted',
]


def upgrade():
    for view_name in moved_view_names:
        op.execute(f'ALTER VIEW IF EXISTS {view_name} SET SCHEMA {SUPPORT_SCHEMA}')


def downgrade():
    for view_name in moved_view_names:
        op.execute(
            f'ALTER VIEW IF EXISTS {SUPPORT_SCHEMA}.{view_name} SET SCHEMA {PUBLIC_SCHEMA}'
        )
