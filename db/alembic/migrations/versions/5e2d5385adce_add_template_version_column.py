"""Add template_version column

Revision ID: 5e2d5385adce
Revises: f54589ab2eee
Create Date: 2021-05-05 09:28:24.286043

"""
import sqlalchemy as sa
from alembic import op
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '5e2d5385adce'
down_revision = 'f54589ab2eee'
branch_labels = None
depends_on = None


qvarn_report_access_view_v3 = ReplaceableObject(
    "qvarn_report_access",
    """
    (
        id,
        access_time,
        arkisto_id,
        report_id,
        customer_org_id,
        org_id,
        person_id,
        status,
        language,
        template_version,
        client_id,  -- defunct
        type,  -- defunct
        revision   -- defunct
        -- gov_org_ids []
    )
    AS
        SELECT
            report_accesses.external_id,
            TO_CHAR(report_accesses.access_time::TIMESTAMPTZ, 'YYYY-MM-DD"T"HH24:MI:SSTZH:TZM'),
            report_accesses.arkisto_id,
            report_accesses.report_id,
            report_accesses.customer_company_id,
            report_accesses.company_id,
            report_accesses.person_id,
            report_accesses.status::TEXT,
            report_accesses.language::TEXT,
            report_accesses.template_version::TEXT,
            NULL,
            'report_access',
            NULL
        FROM
            report_accesses AS report_accesses
    ;
    """
)


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report_accesses', sa.Column('template_version', sa.Text(), nullable=True))
    # ### end Alembic commands ###
    op.replace_view(qvarn_report_access_view_v3,
                    replaces='fbd6840f1aae.qvarn_report_access_view_v2')


def downgrade():
    op.replace_view(qvarn_report_access_view_v3,
                    replace_with='fbd6840f1aae.qvarn_report_access_view_v2')
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('report_accesses', 'template_version')
    # ### end Alembic commands ###
