"""Add multiple _restricted views

Revision ID: acd3300dc9bf
Revises: 58489b3ba0a2
Create Date: 2023-05-23 10:58:53.454049
"""
from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'acd3300dc9bf'
down_revision = '58489b3ba0a2'
branch_labels = None
depends_on = None


preannouncements_restricted_view = ReplaceableObject(
    "preannouncements_restricted",
    """
    AS
        SELECT
            id,
            external_id,
            status,
            project_id,
            created_by_supplier_id,
            for_supplier_id,
            assigned_to_company_id,
            assigned_to_supplier_id,
            assigned_to_time,
            active_pa_form,
            created_on,
            last_changed
        FROM
            preannouncements
    ;
    """
)


preannouncement_forms_restricted_view = ReplaceableObject(
    "preannouncement_forms_restricted",
    """
    AS
        SELECT
            id,
            external_id,
            company_gov_org_id,
            company_id_type,
            company_country,
            has_permanent_establishment,
            is_one_man_company,
            has_collective_agreement,
            collective_agreement_name,
            buyer_gov_org_id,
            buyer_id_type,
            buyer_country,
            foreman_is_on_site,
            -- foreman personal details omitted
            pa_id,
            last_changed,
            created_on,
            contract_end_date,
            contract_start_date,
            contract_type,
            contract_work_areas,
            confirmed_name,
            confirmed_time,
            rejected_name,
            rejected_time,
            confirmed_gov_org_id,
            confirmed_id_type,
            rejected_gov_org_id,
            rejected_id_type,
            -- informant supplier personal details omitted
            submitted_time,
            -- submitter personal details omitted
            last_assigned_to_company,
            last_assigned_to_supplier,
            last_assigned_to_time,
            last_assigned_to_business_id,
            last_assigned_to_business_id_type,
            buyer_name,
            company_name,
            confirmed_country,
            rejected_country
        FROM
            preannouncement_forms
    ;
    """
)


bulk_import_jobs_restricted_view = ReplaceableObject(
    "bulk_import_jobs_restricted",
    """
    AS
        SELECT
            id,
            status,
            project_id
            imported,
            canceled,
            companies,
            interested_org_id,
            last_changed,
            created_on
        FROM
            bulk_import_jobs
    ;
    """
)


alembic_version_restricted_view = ReplaceableObject(
    "alembic_version_restricted",
    """
    AS
        SELECT
            version_num
        FROM
            alembic_version
    ;
    """
)


supplier_contacts_restricted_view = ReplaceableObject(
    "supplier_contacts_restricted",
    """
    AS
        SELECT
            id,
            supplier_id,
            last_changed,
            created_on
        FROM
            supplier_contacts
    ;
    """
)


internal_project_ids_restricted_view = ReplaceableObject(
    "internal_project_ids_restricted",
    """
    AS
        SELECT
            id,
            internal_project_id,
            project_id,
            company_id,
            last_changed,
            created_on
        FROM
            internal_project_ids
    ;
    """
)

project_users_restricted_view = ReplaceableObject(
    "project_users_restricted",
    """
    AS
        SELECT
            id,
            project_id,
            role,
            notify,
            user_account_id,
            represented_company_id,
            last_changed,
            created_on,
            external_id
        FROM
            project_users
    ;
    """
)

status_reports_history_restricted_view = ReplaceableObject(
    "status_reports_history_restricted",
    """
    AS
        SELECT
            id,
            external_id,
            status,
            generated_timestamp,
            interested_company_id,
            company_id,
            last_changed,
            created_on,
            charge_reference,
            used_providers
        FROM
            status_reports_history
    ;
    """
)


def upgrade():
    op.create_view(preannouncements_restricted_view)
    op.create_view(preannouncement_forms_restricted_view)
    op.create_view(bulk_import_jobs_restricted_view)
    op.create_view(alembic_version_restricted_view)
    op.create_view(supplier_contacts_restricted_view)
    op.create_view(internal_project_ids_restricted_view)
    op.create_view(project_users_restricted_view)
    op.create_view(status_reports_history_restricted_view)


def downgrade():
    op.drop_view(status_reports_history_restricted_view)
    op.drop_view(project_users_restricted_view)
    op.drop_view(internal_project_ids_restricted_view)
    op.drop_view(supplier_contacts_restricted_view)
    op.drop_view(alembic_version_restricted_view)
    op.drop_view(bulk_import_jobs_restricted_view)
    op.drop_view(preannouncement_forms_restricted_view)
    op.drop_view(preannouncements_restricted_view)
