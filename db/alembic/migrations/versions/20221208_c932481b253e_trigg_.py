"""Triggers for the new tables (preannouncements, status_report_history)

Revision ID: c932481b253e
Revises: 3c61d15dbbb3
Create Date: 2022-12-08 12:24:45.638738
"""
from alembic import op
from triggers import ReplaceableObjectTRG


# revision identifiers, used by Alembic.
revision = 'c932481b253e'
down_revision = '3c61d15dbbb3'
branch_labels = None
depends_on = None


trigger_set_preannouncements_last_changed = ReplaceableObjectTRG(
    "set_preannouncements_last_changed",
    "preannouncements",
    """
    BEFORE UPDATE ON preannouncements
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)

trigger_set_preannouncement_forms_last_changed = ReplaceableObjectTRG(
    "set_preannouncement_forms_last_changed",
    "preannouncement_forms",
    """
    BEFORE UPDATE ON preannouncement_forms
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)

trigger_set_status_reports_history_last_changed = ReplaceableObjectTRG(
    "set_status_reports_history_last_changed",
    "status_reports_history",
    """
    BEFORE UPDATE ON status_reports_history
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)


def upgrade():
    op.create_trg(trigger_set_preannouncements_last_changed)
    op.create_trg(trigger_set_preannouncement_forms_last_changed)
    op.create_trg(trigger_set_status_reports_history_last_changed)


def downgrade():
    op.drop_trg(trigger_set_status_reports_history_last_changed)
    op.drop_trg(trigger_set_preannouncement_forms_last_changed)
    op.drop_trg(trigger_set_preannouncements_last_changed)
