"""Fix view status_reports_restricted

Revision ID: 0ee578c21c14
Revises: 885f9dd9886c
Create Date: 2023-02-14 09:05:06.218035

"""
from alembic import op
from views import ReplaceableObject

# revision identifiers, used by Alembic.
revision = '0ee578c21c14'
down_revision = '885f9dd9886c'
branch_labels = None
depends_on = None


status_reports_restricted_view_v2 = ReplaceableObject(
    "status_reports_restricted",
    """
    AS
        SELECT 
            jsonb_object(
                ARRAY[
                    'account_period', json_ ->> 'account_period',
                    'account_period_full', json_ ->> 'account_period_full',
                    'affiliates', json_ ->> 'affiliates',
                    'auditor_control_approved', json_ ->> 'auditor_control_approved',
                    'auditor_control_comments', json_ ->> 'auditor_control_comments',
                    'auditor_control_interpretation', json_ ->> 'auditor_control_interpretation',
                    'auditor_control_source', json_ ->> 'auditor_control_source',
                    'auditor_control_status', json_ ->> 'auditor_control_status',
                    'auditor_control_updated', json_ ->> 'auditor_control_updated',
                    'balance_liquidity_percent', json_ ->> 'balance_liquidity_percent',
                    'bis_source', json_ ->> 'bis_source',
                    'bis_updated', json_ ->> 'bis_updated',
                    'business_code', json_ ->> 'business_code',
                    'business_desc', json_ ->> 'business_desc',
                    'charge_reference', json_ ->> 'charge_reference',
                    'clreports_interpretation', json_ ->> 'clreports_interpretation',
                    'company_form_interpretation', json_ ->> 'company_form_interpretation',
                    'company_form_source', json_ ->> 'company_form_source',
                    'company_form_status', json_ ->> 'company_form_status',
                    'company_form_updated', json_ ->> 'company_form_updated',
                    'company_history', json_ ->> 'company_history',
                    'control_balance_sheet', json_ ->> 'control_balance_sheet',
                    'control_balance_sheet_source', json_ ->> 'control_balance_sheet_source',
                    'control_balance_sheet_updated', json_ ->> 'control_balance_sheet_updated',
                    'country_alpha3', json_ ->> 'country_alpha3',
                    'current_name_reg', json_ ->> 'current_name_reg',
                    'creditsafe_status_interpretation', json_ ->> 'creditsafe_status_interpretation',
                    'creditsafe_status_source', json_ ->> 'creditsafe_status_source',
                    'creditsafe_status_status', json_ ->> 'creditsafe_status_status',
                    'creditsafe_status_updated', json_ ->> 'creditsafe_status_updated',
                    'debt_company_or_private_enforcement_debt', json_ ->> 'debt_company_or_private_enforcement_debt',
                    'debt_company_or_private_enforcement_debt_sum', json_ ->> 'debt_company_or_private_enforcement_debt_sum',
                    'debt_company_or_private_enforcement_interpretation', json_ ->> 'debt_company_or_private_enforcement_interpretation',
                    'debt_company_or_private_enforcement_source', json_ ->> 'debt_company_or_private_enforcement_source',
                    'debt_company_or_private_enforcement_status', json_ ->> 'debt_company_or_private_enforcement_status',
                    'debt_company_or_private_enforcement_updated', json_ ->> 'debt_company_or_private_enforcement_updated',
                    'debt_liabilities_payments_source', json_ ->> 'debt_liabilities_payments_source',
                    'debt_liabilities_payments_updated', json_ ->> 'debt_liabilities_payments_updated',
                    'debt_taxes_fees_enforcement_debt', json_ ->> 'debt_taxes_fees_enforcement_debt',
                    'debt_taxes_fees_enforcement_debt_sum', json_ ->> 'debt_taxes_fees_enforcement_debt_sum',
                    'debt_taxes_fees_enforcement_interpretation', json_ ->> 'debt_taxes_fees_enforcement_interpretation',
                    'debt_taxes_fees_enforcement_source', json_ ->> 'debt_taxes_fees_enforcement_source',
                    'debt_taxes_fees_enforcement_status', json_ ->> 'debt_taxes_fees_enforcement_status',
                    'debt_taxes_fees_enforcement_updated', json_ ->> 'debt_taxes_fees_enforcement_updated',
                    'distraint_attempt', json_ ->> 'distraint_attempt',
                    'employer_contributions', json_ ->> 'employer_contributions',
                    'equity', json_ ->> 'equity',
                    'equity_share_percent', json_ ->> 'equity_share_percent',
                    'estimated_payroll', json_ ->> 'estimated_payroll',
                    'fin_info_source', json_ ->> 'fin_info_source',
                    'fin_info_updated', json_ ->> 'fin_info_updated',
                    'foundation_date', json_ ->> 'foundation_date',
                    'groupmother_country', json_ ->> 'groupmother_country',
                    'groupmother_organisation_name', json_ ->> 'groupmother_organisation_name',
                    'groupmother_organisation_nr', json_ ->> 'groupmother_organisation_nr',
                    'kronofogden_confirmed_claims', json_ ->> 'kronofogden_confirmed_claims',
                    'kronofogden_confirmed_private_amount', json_ ->> 'kronofogden_confirmed_private_amount',
                    'kronofogden_confirmed_private_amount_currency', json_ ->> 'kronofogden_confirmed_private_amount_currency',
                    'kronofogden_confirmed_private_claims', json_ ->> 'kronofogden_confirmed_private_claims',
                    'kronofogden_confirmed_public_amount', json_ ->> 'kronofogden_confirmed_public_amount',
                    'kronofogden_confirmed_public_amount_currency', json_ ->> 'kronofogden_confirmed_public_amount_currency',
                    'kronofogden_confirmed_public_claims', json_ ->> 'kronofogden_confirmed_public_claims',
                    'kronofogden_debt', json_ ->> 'kronofogden_debt',
                    'kronofogden_debt_currency', json_ ->> 'kronofogden_debt_currency',
                    'kronofogden_debt_date', json_ ->> 'kronofogden_debt_date',
                    'kronofogden_remarks_amount', json_ ->> 'kronofogden_remarks_amount',
                    'kronofogden_remarks_amount_currency', json_ ->> 'kronofogden_remarks_amount_currency',
                    'kronofogden_remarks_claims', json_ ->> 'kronofogden_remarks_claims',
                    'kronofogden_remarks_private_amount', json_ ->> 'kronofogden_remarks_private_amount',
                    'kronofogden_remarks_private_amount_currency', json_ ->> 'kronofogden_remarks_private_amount_currency',
                    'kronofogden_remarks_private_claims', json_ ->> 'kronofogden_remarks_private_claims',
                    'kronofogden_remarks_public_amount', json_ ->> 'kronofogden_remarks_public_amount',
                    'kronofogden_remarks_public_amount_currency', json_ ->> 'kronofogden_remarks_public_amount_currency',
                    'kronofogden_remarks_public_claims', json_ ->> 'kronofogden_remarks_public_claims',
                    'kronofogden_remarks_source', json_ ->> 'kronofogden_remarks_source',
                    'kronofogden_remarks_updated', json_ ->> 'kronofogden_remarks_updated',
                    'legal_form', json_ ->> 'legal_form',
                    'legal_form_desc', json_ ->> 'legal_form_desc',
                    'long_term_liabilities', json_ ->> 'long_term_liabilities',
                    'main_business', json_ ->> 'main_business',
                    'net_turnover', json_ ->> 'net_turnover',
                    'nr_employees', json_ ->> 'nr_employees',
                    'nr_employees_int', json_ ->> 'nr_employees_int',
                    'organisation_alternate_name', json_ ->> 'organisation_alternate_name',
                    'overdue_tax_liabilities_amount', json_ ->> 'overdue_tax_liabilities_amount',
                    'payment_applications_claims', json_ ->> 'payment_applications_claims',
                    'payment_applications_sum', json_ ->> 'payment_applications_sum',
                    'payment_applications_sum_currency', json_ ->> 'payment_applications_sum_currency',
                    'payroll_register_interpretation', json_ ->> 'payroll_register_interpretation',
                    'payroll_register_source', json_ ->> 'payroll_register_source',
                    'payroll_register_status', json_ ->> 'payroll_register_status',
                    'payroll_register_status_date', json_ ->> 'payroll_register_status_date',
                    'payroll_register_updated', json_ ->> 'payroll_register_updated',
                    'payroll_tax', json_ ->> 'payroll_tax',
                    'profit_after_tax', json_ ->> 'profit_after_tax',
                    'profit_before_tax', json_ ->> 'profit_before_tax',
                    'quick_ratio_percent', json_ ->> 'quick_ratio_percent',
                    'rating_commentaries', json_ ->> 'rating_commentaries',
                    'rating_commentary_count', json_ ->> 'rating_commentary_count',
                    'rating_interpretation', json_ ->> 'rating_interpretation',
                    'rating_source', json_ ->> 'rating_source',
                    'rating_statuses', json_ ->> 'rating_statuses',
                    'rating_updated', json_ ->> 'rating_updated',
                    'report_creation_date', json_ ->> 'report_creation_date',
                    'report_reject_code', json_ ->> 'report_reject_code',
                    'report_reject_interpretation', json_ ->> 'report_reject_interpretation',
                    'report_reject_source', json_ ->> 'report_reject_source',
                    'report_reject_status', json_ ->> 'report_reject_status',
                    'report_reject_updated', json_ ->> 'report_reject_updated',
                    'report_version_id06', json_ ->> 'report_version_id06',
                    'representative_check_interpretation', json_ ->> 'representative_check_interpretation',
                    'representative_check_source', json_ ->> 'representative_check_source',
                    'representative_check_status', json_ ->> 'representative_check_status',
                    'representative_check_updated', json_ ->> 'representative_check_updated',
                    'representatives_source', json_ ->> 'representatives_source',
                    'representatives_updated', json_ ->> 'representatives_updated',
                    'share_capital', json_ ->> 'share_capital',
                    'short_term_liabilities', json_ ->> 'short_term_liabilities',
                    'solvency_percent', json_ ->> 'solvency_percent',
                    'tax_format', json_ ->> 'tax_format',
                    'tax_format_start_date', json_ ->> 'tax_format_start_date',
                    'tax_interpretation', json_ ->> 'tax_interpretation',
                    'tax_source', json_ ->> 'tax_source',
                    'tax_updated', json_ ->> 'tax_updated',
                    'total_equity_and_liabilities', json_ ->> 'total_equity_and_liabilities',
                    'trustees_source', json_ ->> 'trustees_source',
                    'trustees_updated', json_ ->> 'trustees_updated',
                    'turnover_interval', json_ ->> 'turnover_interval',
                    'turnover_per_employee', json_ ->> 'turnover_per_employee',
                    'url', json_ ->> 'url',
                    'used_providers', json_ ->> 'used_providers',
                    'vat_register', json_ ->> 'vat_register',
                    'vat_register_interpretation', json_ ->> 'vat_register_interpretation',
                    'vat_register_source', json_ ->> 'vat_register_source',
                    'vat_register_status', json_ ->> 'vat_register_status',
                    'vat_register_updated', json_ ->> 'vat_register_updated',
                    'vat_start_date', json_ ->> 'vat_start_date'
                ] 
            ) as json_restricted,
            id,
            external_id,
            status,
            generated_timestamp,
            interested_company_id,
            company_id,
            last_changed,
            created_on,
            charge_reference,
            used_providers
        FROM
            status_reports;
    """
)

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###

    # Drop the old view created in revision 378dedca1e14
    op.replace_view(status_reports_restricted_view_v2,
                    replaces='378dedca1e14.status_reports_restricted_view')

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
    op.replace_view(status_reports_restricted_view_v2,
                    replace_with='378dedca1e14.status_reports_restricted_view')

