"""Add view suppliers_restricted

Revision ID: f6a1637d1f69
Revises: 0ee578c21c14
Create Date: 2023-03-22 12:42:11.920334
"""
from alembic import op
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'f6a1637d1f69'
down_revision = '0ee578c21c14'
branch_labels = None
depends_on = None

suppliers_restricted_columns = [
    'id',
    'external_id',
    'role',
    'type',
    'contract_type',
    'contract_start_date',
    'contract_end_date',
    'contract_work_areas',
    'materialized_path',
    'project_id',
    'parent_supplier_id',
    'parent_company_id',
    'company_id',
    'revision',
    'last_visited',
    'last_changed',
    'created_on',
    'first_visited',
    'visitor_type'
]

suppliers_restricted_view = ReplaceableObject(
    "suppliers_restricted",
    """
    AS
        SELECT
            %s
        FROM
            suppliers
    ;
    """ % ','.join(suppliers_restricted_columns)
)


def upgrade():
    op.create_view(suppliers_restricted_view)


def downgrade():
    op.drop_view(suppliers_restricted_view)
