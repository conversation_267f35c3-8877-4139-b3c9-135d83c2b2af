"""Add created_by_org_id attribute to project

Revision ID: 68c937953557
Revises: 58489b3ba0a2
Create Date: 2023-06-30 11:56:50.543787

"""
import sqlalchemy as sa
from alembic import op



# revision identifiers, used by Alembic.
revision = '68c937953557'
down_revision = 'a655d63276b1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('projects', sa.Column('created_by_org_id', sa.Text(), nullable=True))
    op.create_index('idx_projects_created_by_org_id', 'projects', ['created_by_org_id'], unique=False)
    # ### end Alembic commands ###
    # Any project that has been created prior this feature,
    # the “create_by_org_id” should be the “client_company_id”
    op.execute("""
        UPDATE projects
        SET created_by_org_id = client_company_id
    """)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_projects_created_by_org_id', table_name='projects')
    op.drop_column('projects', 'created_by_org_id')
    # ### end Alembic commands ###
