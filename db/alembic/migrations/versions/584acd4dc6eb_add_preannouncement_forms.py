"""Add preannouncement forms

Revision ID: 584acd4dc6eb
Revises: 38342ac9c518
Create Date: 2022-01-10 13:40:08.475285

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql
import citext


# revision identifiers, used by Alembic.
revision = '584acd4dc6eb'
down_revision = '38342ac9c518'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('preannouncement_forms',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('external_id', citext.CIText(), nullable=False),
    sa.Column('company_gov_org_id', sa.Text(), nullable=False),
    sa.Column('company_id_type', sa.Text(), nullable=True),
    sa.Column('company_country', sa.Text(), nullable=False),
    sa.Column('has_permanent_establishment', sa.BOOLEAN(), nullable=True),
    sa.Column('is_one_man_company', sa.BOOLEAN(), nullable=True),
    sa.Column('has_collective_agreement', sa.BOOLEAN(), nullable=True),
    sa.Column('collective_agreement_name', sa.Text(), nullable=True),
    sa.Column('buyer_gov_org_id', sa.Text(), nullable=False),
    sa.Column('buyer_id_type', sa.Text(), nullable=True),
    sa.Column('buyer_country', sa.Text(), nullable=False),
    sa.Column('foreman_is_on_site', sa.BOOLEAN(), nullable=True),
    sa.Column('foreman_first_name', sa.Text(), nullable=True),
    sa.Column('foreman_last_name', sa.Text(), nullable=True),
    sa.Column('foreman_phone_number', sa.Text(), nullable=True),
    sa.Column('foreman_email', sa.Text(), nullable=True),
    sa.Column('pa_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['pa_id'], ['preannouncements.id'], name='fk_preannouncements_id', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='pkey_preannouncement_form_ids')
    )
    op.create_index(op.f('ix_preannouncement_forms_external_id'), 'preannouncement_forms', ['external_id'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_preannouncement_forms_external_id'), table_name='preannouncement_forms')
    op.drop_table('preannouncement_forms')
    # ### end Alembic commands ###
