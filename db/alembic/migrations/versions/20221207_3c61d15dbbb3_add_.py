"""Add status_reports_history external_id unique constraint

Revision ID: 3c61d15dbbb3
Revises: 4329457a1281
Create Date: 2022-12-07 09:21:15.231122

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = '3c61d15dbbb3'
down_revision = '4329457a1281'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.create_unique_constraint(None, 'status_reports_history', ['external_id'])
    # ### end Alembic commands ###

    # Added name 'status_reports_history_external_id_unique_constraint' to avoid error
    op.create_unique_constraint('status_reports_history_external_id_unique_constraint', 'status_reports_history', ['external_id'])



def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.drop_constraint(None, 'status_reports_history', type_='unique')
    # ### end Alembic commands ###

    # Added name 'status_reports_history_external_id_unique_constraint' to avoid error
    op.drop_constraint('status_reports_history_external_id_unique_constraint', 'status_reports_history', type_='unique')

