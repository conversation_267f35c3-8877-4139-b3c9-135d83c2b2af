"""Add suppliers.visitor_type

Revision ID: cfb88e429d34
Revises: db6065d0c6e2
Create Date: 2022-09-29 11:46:06.868786

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = 'cfb88e429d34'
down_revision = 'db6065d0c6e2'
branch_labels = None
depends_on = None


def upgrade():
    t_supplier_visitor_type = sa.Enum('raw', 'nonpaed', name='t_supplier_visitor_type')
    t_supplier_visitor_type.create(op.get_bind())
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('suppliers', sa.Column('visitor_type', t_supplier_visitor_type, nullable=True))
    # ### end Alembic commands ###
    # Visitors from companies confirmed as suppliers are 'nonpaed' (non-preannounced)
    # This does a self-join on the suppliers table.
    op.execute("""
        UPDATE suppliers s1
        SET visitor_type = 'nonpaed'
        FROM suppliers s2
        WHERE s1.project_id = s2.project_id AND s1.company_id = s2.company_id
          AND s1.type = 'visitor' AND s2.type = 'linked'
    """)
    # Visitors from all other companies are 'raw'
    op.execute("""
        UPDATE suppliers s1
        SET visitor_type = 'raw'
        WHERE s1.type = 'visitor' AND s1.visitor_type IS NULL
    """)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('suppliers', 'visitor_type')
    # ### end Alembic commands ###
    op.execute("DROP TYPE t_supplier_visitor_type")
