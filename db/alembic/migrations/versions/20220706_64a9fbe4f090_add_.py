"""Add informant columns to PA form

Revision ID: 64a9fbe4f090
Revises: e93dc3662baa
Create Date: 2022-07-06 09:40:33.596511

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = '64a9fbe4f090'
down_revision = 'e93dc3662baa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('informant_email', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('informant_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('informant_phone', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('preannouncement_forms', 'informant_phone')
    op.drop_column('preannouncement_forms', 'informant_name')
    op.drop_column('preannouncement_forms', 'informant_email')
    # ### end Alembic commands ###
