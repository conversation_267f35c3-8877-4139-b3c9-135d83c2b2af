"""Adding submitted by columns

Revision ID: e49a13efa1c8
Revises: afdcc5a20ef9
Create Date: 2022-07-13 12:47:03.162994

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = 'e49a13efa1c8'
down_revision = 'afdcc5a20ef9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('submitted_by_email', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('submitted_by_first_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('submitted_by_last_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('submitted_by_phone', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('preannouncement_forms', 'submitted_by_phone')
    op.drop_column('preannouncement_forms', 'submitted_by_last_name')
    op.drop_column('preannouncement_forms', 'submitted_by_first_name')
    op.drop_column('preannouncement_forms', 'submitted_by_email')
    # ### end Alembic commands ###
