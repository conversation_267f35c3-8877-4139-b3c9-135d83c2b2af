"""Add project_users.external_id

Revision ID: 451b2cb6ecf1
Revises: c932481b253e
Create Date: 2023-01-12 12:38:37.406330

"""
import sqlalchemy as sa
from alembic import op
import citext


# revision identifiers, used by Alembic.
revision = '451b2cb6ecf1'
down_revision = 'c932481b253e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('project_users', sa.Column('external_id', citext.CIText(), nullable=True))
    op.create_index('idx_project_user_external_id', 'project_users', ['external_id'], unique=True)
    op.create_index('idx_project_users_project_id', 'project_users', ['project_id'], unique=False)
    op.create_index('idx_project_users_represented_company_id', 'project_users',
                    ['represented_company_id'], unique=False)
    op.create_index('idx_project_users_role', 'project_users', ['role'], unique=False)
    op.create_index('idx_project_users_user_account_id', 'project_users', ['user_account_id'],
                    unique=False)
    op.drop_index('idx_poject_users_project_id', table_name='project_users')
    op.drop_index('idx_poject_users_represented_company_id', table_name='project_users')
    op.drop_index('idx_poject_users_role', table_name='project_users')
    op.drop_index('idx_poject_users_user_account_id', table_name='project_users')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_poject_users_user_account_id', 'project_users', ['user_account_id'],
                    unique=False)
    op.create_index('idx_poject_users_role', 'project_users', ['role'], unique=False)
    op.create_index('idx_poject_users_represented_company_id', 'project_users',
                    ['represented_company_id'], unique=False)
    op.create_index('idx_poject_users_project_id', 'project_users', ['project_id'], unique=False)
    op.drop_index('idx_project_users_user_account_id', table_name='project_users')
    op.drop_index('idx_project_users_role', table_name='project_users')
    op.drop_index('idx_project_users_represented_company_id', table_name='project_users')
    op.drop_index('idx_project_users_project_id', table_name='project_users')
    op.drop_index('idx_project_user_external_id', table_name='project_users')
    op.drop_column('project_users', 'external_id')
    # ### end Alembic commands ###
