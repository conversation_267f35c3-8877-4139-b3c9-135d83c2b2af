"""Add project_supplier_comments and project_comment_viewers tables

Revision ID: 041e1818f2c4
Revises: 334afc5e6e4d
Create Date: 2025-05-13 12:41:10.199309

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql
from models import SUPPORT_SCHEMA
from triggers import ReplaceableObjectTRG
from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = '041e1818f2c4'
down_revision = '334afc5e6e4d'
branch_labels = None
depends_on = None

# Trigger for project_supplier_comments table
trigger_set_project_supplier_comments_last_changed = ReplaceableObjectTRG(
    "set_project_supplier_comments_last_changed",
    "project_supplier_comments",
    """
    BEFORE UPDATE ON project_supplier_comments
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)

# Trigger for project_comment_viewers table
trigger_set_project_comment_viewers_last_changed = ReplaceableObjectTRG(
    "set_project_comment_viewers_last_changed",
    "project_comment_viewers",
    """
    BEFORE UPDATE ON project_comment_viewers
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_last_changed();
    """
)

# Support view for project_supplier_comments
project_supplier_comments_restricted_view = ReplaceableObject(
    f"{SUPPORT_SCHEMA}.project_supplier_comments_restricted",
    """
    AS
        SELECT
            id,
            project_id,
            supplier_id,
            org_id,
            comment,
            created_by_org_id,
            created_by_person_id,
            created_timestamp,
            is_deleted,
            deleted_by_org_id,
            deleted_by_person_id,
            deleted_timestamp,
            is_updated,
            updated_by_org_id,
            updated_by_person_id,
            updated_timestamp,
            modified_timestamp,
            last_changed,
            created_on
        FROM
            project_supplier_comments
    ;
    """
)

# Support view for project_comment_viewers
project_comment_viewers_restricted_view = ReplaceableObject(
    f"{SUPPORT_SCHEMA}.project_comment_viewers_restricted",
    """
    AS
        SELECT
            id,
            project_comment_id,
            read_by_person_id,
            last_changed,
            created_on
        FROM
            project_comment_viewers
    ;
    """
)


def upgrade():
    op.create_table(
        'project_supplier_comments',
        sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('supplier_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('org_id', sa.Text(), nullable=False),
        sa.Column('comment', sa.Text(), nullable=False),
        sa.Column('created_by_org_id', sa.Text(), nullable=False),
        sa.Column('created_by_person_id', sa.Text(), nullable=False),
        sa.Column('created_timestamp', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('is_deleted', sa.BOOLEAN(), server_default='false', nullable=False),
        sa.Column('deleted_by_org_id', sa.Text(), nullable=True),
        sa.Column('deleted_by_person_id', sa.Text(), nullable=True),
        sa.Column('deleted_timestamp', postgresql.TIMESTAMP(timezone=True), nullable=True),
        sa.Column('is_updated', sa.BOOLEAN(), server_default='false', nullable=False),
        sa.Column('updated_by_org_id', sa.Text(), nullable=True),
        sa.Column('updated_by_person_id', sa.Text(), nullable=True),
        sa.Column('updated_timestamp', postgresql.TIMESTAMP(timezone=True), nullable=True),
        sa.Column('modified_timestamp', postgresql.TIMESTAMP(timezone=True), nullable=True),
        sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name='fk_project_supplier_comments_project_id', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], name='fk_project_supplier_comments_supplier_id', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id', name='pkey_project_supplier_comments_ids')
    )
    op.create_index('idx_project_supplier_comments_created_by_person_id', 'project_supplier_comments', ['created_by_person_id'], unique=False)
    op.create_index('idx_project_supplier_comments_org_id', 'project_supplier_comments', ['org_id'], unique=False)
    op.create_index('idx_project_supplier_comments_project_id', 'project_supplier_comments', ['project_id'], unique=False)
    op.create_index('idx_project_supplier_comments_supplier_id', 'project_supplier_comments', ['supplier_id'], unique=False)

    op.create_table(
        'project_comment_viewers',
        sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('project_comment_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('read_by_person_id', sa.Text(), nullable=False),
        sa.Column('last_changed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_on', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['project_comment_id'], ['project_supplier_comments.id'], name='fk_project_comment_viewers_project_comment_id', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id', name='pkey_project_comment_viewers_ids'),
        sa.UniqueConstraint('project_comment_id', 'read_by_person_id', name='uq_project_comment_viewers_project_comment_id_read_by_person_id')
    )
    op.create_index('idx_project_comment_viewers_project_comment_id', 'project_comment_viewers', ['project_comment_id'], unique=False)
    op.create_index('idx_project_comment_viewers_read_by_person_id', 'project_comment_viewers', ['read_by_person_id'], unique=False)

    # Create triggers
    op.create_trg(trigger_set_project_supplier_comments_last_changed)
    op.create_trg(trigger_set_project_comment_viewers_last_changed)

    # Create support views
    op.create_view(project_supplier_comments_restricted_view)
    op.create_view(project_comment_viewers_restricted_view)


def downgrade():
    # Drop support views
    op.drop_view(project_comment_viewers_restricted_view)
    op.drop_view(project_supplier_comments_restricted_view)

    # Drop triggers
    op.drop_trg(trigger_set_project_comment_viewers_last_changed)
    op.drop_trg(trigger_set_project_supplier_comments_last_changed)

    op.drop_table('project_comment_viewers')
    op.drop_table('project_supplier_comments')
