"""Add view report_cache_restricted

Revision ID: becc4b843a00
Revises: cf118d694563
Create Date: 2023-05-26 09:24:26.226593
"""

from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'becc4b843a00'
down_revision = 'cf118d694563'
branch_labels = None
depends_on = None


report_cache_restricted_view = ReplaceableObject(
    "report_cache_restricted",
    """
    AS
        SELECT
            id,
            external_id,
            correlation_id,
            expires_at,
            interested_org_id,
            key,
            provider,
            type,
            last_changed,
            created_on
        FROM
            report_cache
    ;
    """
)


def upgrade():
    op.create_view(report_cache_restricted_view)


def downgrade():
    op.drop_view(report_cache_restricted_view)
