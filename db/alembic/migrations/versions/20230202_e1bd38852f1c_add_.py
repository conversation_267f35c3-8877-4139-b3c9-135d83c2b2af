"""Add report_cache table

Revision ID: e1bd38852f1c
Revises: 378dedca1e14
Create Date: 2023-02-02 13:12:14.859110

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql
import citext


# revision identifiers, used by Alembic.
revision = 'e1bd38852f1c'
down_revision = '378dedca1e14'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('report_cache',
    sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('external_id', citext.CIText(), nullable=False),
    sa.Column('correlation_id', sa.Text(), nullable=True),
    sa.Column('expires_at', postgresql.TIMESTAMP(precision=6), nullable=True),
    sa.Column('interested_org_id', sa.Text(), nullable=True),
    sa.Column('key', sa.Text(), nullable=True),
    sa.Column('provider', sa.Text(), nullable=True),
    sa.Column('type', sa.Text(), nullable=True),
    sa.Column('value', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id', name='pkey_report_cache_ids')
    )
    op.create_index('idx_report_cache_correlation_id', 'report_cache', ['correlation_id'], unique=False)
    op.create_index('idx_report_cache_expires_at', 'report_cache', ['expires_at'], unique=False)
    op.create_index('idx_report_cache_interested_org_id', 'report_cache', ['interested_org_id'], unique=False)
    op.create_index('idx_report_cache_key', 'report_cache', ['key'], unique=False)
    op.create_index(op.f('ix_report_cache_external_id'), 'report_cache', ['external_id'], unique=True)
    op.create_index('idx_report_cache_external_id', 'report_cache', ['external_id'], unique=True)
    op.create_index('idx_report_cache_unique_cols', 'report_cache', ['key', 'provider', 'type'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_report_cache_external_id', table_name='report_cache')
    op.drop_index(op.f('ix_report_cache_external_id'), table_name='report_cache')
    op.drop_index('idx_report_cache_key', table_name='report_cache')
    op.drop_index('idx_report_cache_interested_org_id', table_name='report_cache')
    op.drop_index('idx_report_cache_expires_at', table_name='report_cache')
    op.drop_index('idx_report_cache_correlation_id', table_name='report_cache')
    op.drop_index('idx_report_cache_unique_cols', table_name='report_cache')
    op.drop_table('report_cache')
    # ### end Alembic commands ###
