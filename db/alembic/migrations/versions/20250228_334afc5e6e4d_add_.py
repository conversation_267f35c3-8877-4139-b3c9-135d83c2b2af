"""Add null external_id

Revision ID: 334afc5e6e4d
Revises: 6019e7f0fc86
Create Date: 2025-02-28 15:19:55.335896

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = '334afc5e6e4d'
down_revision = '6019e7f0fc86'
branch_labels = None
depends_on = None


def upgrade():
    # Update companies column in bulk_import_jobs table
    # Add null as the 9th element to all inner lists that have a length of 8
    op.execute("""
        UPDATE bulk_import_jobs
        SET companies = (
            SELECT jsonb_agg(
                CASE 
                    WHEN jsonb_array_length(elem) = 8 THEN elem || jsonb_build_array(NULL) \
               || jsonb_build_array(NULL)
                    ELSE elem
                END
            )
            FROM jsonb_array_elements(companies) AS elem
        )
        WHERE companies IS NOT NULL AND jsonb_typeof(companies) = 'array'
    """)


def downgrade():
    # Remove the null as the 9th element from all inner lists that have a length of 9
    # and have a null as the 9th element
    op.execute("""
        UPDATE bulk_import_jobs
        SET companies = (
            SELECT jsonb_agg(
                CASE 
                    WHEN jsonb_array_length(elem) = 10 AND 
                         elem->>8 IS NULL AND elem->>9 IS NULL THEN 
                        elem - 8 - 8
                    ELSE elem
                END
            )
            FROM jsonb_array_elements(companies) AS elem
        )
        WHERE companies IS NOT NULL AND jsonb_typeof(companies) = 'array'
    """)
