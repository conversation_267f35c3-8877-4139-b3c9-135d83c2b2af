"""Drop gov_org_id and company name columns from preannouncement_forms_restricted

Revision ID: cf118d694563
Revises: 6b361365c810
Create Date: 2023-05-26 11:22:21.589965
"""

from alembic import op

from views import ReplaceableObject


# revision identifiers, used by Alembic.
revision = 'cf118d694563'
down_revision = '6b361365c810'
branch_labels = None
depends_on = None


preannouncement_forms_restricted_view_v2 = ReplaceableObject(
    "preannouncement_forms_restricted",
    """
    AS
        SELECT
            id,
            external_id,
            -- company_gov_org_id omitted
            company_id_type,
            company_country,
            has_permanent_establishment,
            is_one_man_company,
            has_collective_agreement,
            collective_agreement_name,
            -- buyer_gov_org_id omitted
            buyer_id_type,
            buyer_country,
            foreman_is_on_site,
            -- foreman personal details omitted
            pa_id,
            last_changed,
            created_on,
            contract_end_date,
            contract_start_date,
            contract_type,
            contract_work_areas,
            -- confirmed_name omitted
            confirmed_time,
            -- rejected_name omitted
            rejected_time,
            -- confirmed_gov_org_id omitted
            confirmed_id_type,
            -- rejected_gov_org_id omitted
            rejected_id_type,
            -- informant supplier personal details omitted
            submitted_time,
            -- submitter personal details omitted
            last_assigned_to_company,
            last_assigned_to_supplier,
            last_assigned_to_time,
            -- last_assigned_to_business_id omitted
            last_assigned_to_business_id_type,
            -- buyer_name omitted
            -- company_name omitted
            confirmed_country,
            rejected_country
        FROM
            preannouncement_forms
    ;
    """
)


def upgrade():
    op.replace_view(preannouncement_forms_restricted_view_v2,
                    replaces='acd3300dc9bf.preannouncement_forms_restricted_view')


def downgrade():
    op.replace_view(preannouncement_forms_restricted_view_v2,
                    replace_with='acd3300dc9bf.preannouncement_forms_restricted_view')
