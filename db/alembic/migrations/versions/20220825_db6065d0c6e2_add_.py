"""Add PA form name and country columns

Revision ID: db6065d0c6e2
Revises: 73af9b771387
Create Date: 2022-08-25 07:35:38.706207

"""
import sqlalchemy as sa
from alembic import op


# revision identifiers, used by Alembic.
revision = 'db6065d0c6e2'
down_revision = '73af9b771387'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('preannouncement_forms', sa.Column('buyer_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('company_name', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('confirmed_country', sa.Text(), nullable=True))
    op.add_column('preannouncement_forms', sa.Column('rejected_country', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('preannouncement_forms', 'rejected_country')
    op.drop_column('preannouncement_forms', 'confirmed_country')
    op.drop_column('preannouncement_forms', 'company_name')
    op.drop_column('preannouncement_forms', 'buyer_name')
    # ### end Alembic commands ###
