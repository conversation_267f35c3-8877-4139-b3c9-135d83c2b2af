import enum

import sqlalchemy as sa
from citext import CIText
from sqlalchemy import (
    BOOLEAN,
    Column,
    Date,
    Enum,
    ForeignKey,
    Index,
    JSON,
    PrimaryKeyConstraint,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import ARRAY, JSONB, TIMESTAMP, UUID
from sqlalchemy.ext.declarative import declarative_base


SUPPORT_SCHEMA = 'support'
PUBLIC_SCHEMA = 'public'

Base = declarative_base()


class StatusReportsEnum(enum.Enum):
    stop = '100 STOP'
    investigate = '200 INVESTIGATE'
    incomplete = '300 INCOMPLETE'
    attention = '400 ATTENTION'
    ok = '500 OK'


class SupplierRoleEnum(enum.Enum):
    main_contractor = 'main_contractor'
    supervisor = 'supervisor'
    supplier = 'supplier'


class SupplierTypeEnum(enum.Enum):
    linked = 'linked'
    unlinked = 'unlinked'
    visitor = 'visitor'


class SupplierContractTypeEnum(enum.Enum):
    contracting = 'contracting'
    consulting = 'consulting'
    personnel_leasing = 'personnel_leasing'
    machine_equipment_leasing = 'machine_equipment_leasing'
    materials_and_products = 'materials_and_products'
    transportation = 'transportation'


class SupplierWorkAreaEnum(enum.Enum):
    cleaning = 'cleaning'
    demolition = 'demolition'
    sanitation = 'sanitation'
    drywall = 'drywall'
    assembly = 'assembly'
    scaffolding = 'scaffolding'
    land_works = 'land_works'
    casting_all_materials = 'casting_all_materials'
    framework = 'framework'
    masonry_and_plastering = 'masonry_and_plastering'
    other = 'other'


class SupplierVisitorTypeEnum(enum.Enum):
    raw = 'raw'
    nonpaed = 'nonpaed'  # short for 'non-preannounced'


class ProjectUserRoleEnum(enum.Enum):
    leader = 'leader'
    manager = 'manager'
    member = 'member'


class BulkImportJobStatusEnum(enum.Enum):
    pending = 'pending'
    in_progress = 'in_progress'
    done = 'done'
    failed = 'failed'


class PreAnnouncementEnum(enum.Enum):
    created = 'created'
    registered = 'registered'
    confirmed = 'confirmed'
    rejected = 'rejected'


class ProjectCreatorRoleEnum(enum.Enum):
    client = 'client'
    main_contractor = 'main_contractor'


class CreditsafeAccountStateEnum(enum.Enum):
    pending = 'pending'
    active = 'active'
    inactive = 'inactive'


class Project(Base):
    __tablename__ = "projects"
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_projects'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    external_id = Column(CIText())
    name = Column(Text)
    tax_id = Column(Text)
    client_company_id = Column(Text)
    client_contact_person_id = Column(Text)
    client_contact_person_email = Column(Text)
    created_by_org_id = Column(Text)
    start_date = Column(Date)
    end_date = Column(Date)
    state = Column(Enum('draft', 'active', 'closed', name='t_project_state'))
    pa_form_enabled = Column(BOOLEAN, server_default='False', nullable=True)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    added_client_confirmed = Column(BOOLEAN, nullable=True)
    project_creator_role = Column(
        Enum(ProjectCreatorRoleEnum,
             name='t_project_creator_role',
             values_callable=lambda obj: [e.value for e in obj])
    )
    added_client_can_view = Column(BOOLEAN, nullable=True)


Index('idx_projects_name', Project.name)
Index('idx_projects_tax_id', Project.tax_id)
Index('idx_projects_client_company_id', Project.client_company_id)
Index('idx_projects_client_contact_person_id', Project.client_contact_person_id)
Index('idx_projects_client_contact_person_email', Project.client_contact_person_email)
Index('idx_projects_created_by_org_id', Project.created_by_org_id)
Index('idx_projects_state', Project.state)
Index('idx_projects_external_id', Project.external_id)


class StatusReports(Base):
    __tablename__ = "status_reports"
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_status_reports'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    external_id = Column(CIText())
    status = Column(
        Enum(StatusReportsEnum,
             name='t_status_report_status',
             values_callable=lambda obj: [e.value for e in obj])
    )
    generated_timestamp = Column(TIMESTAMP(precision=6))
    interested_company_id = Column(Text)
    company_id = Column(Text)
    json_ = Column(JSONB)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    charge_reference = Column(Text)
    used_providers = Column(ARRAY(Text))


Index('idx_status_reports_generated_timestamp', StatusReports.generated_timestamp)
Index('idx_status_reports_company_id', StatusReports.company_id)
Index('idx_status_reports_interested_company_id', StatusReports.interested_company_id)
Index('idx_status_reports_status', StatusReports.status)
Index('idx_status_reports_external_id', StatusReports.external_id)
Index('idx_status_reports_charge_reference', StatusReports.charge_reference)


class StatusReportsHistory(Base):
    __tablename__ = "status_reports_history"
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_status_reports_history'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    external_id = Column(CIText(), unique=True)
    status = Column(Text)
    generated_timestamp = Column(TIMESTAMP(precision=6))
    interested_company_id = Column(Text)
    company_id = Column(Text)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    charge_reference = Column(Text)
    used_providers = Column(ARRAY(Text))


Index('idx_status_reports_history_generated_timestamp', StatusReportsHistory.generated_timestamp)
Index('idx_status_reports_history_company_id', StatusReportsHistory.company_id)
Index('idx_status_reports_history_interested_company_id',
      StatusReportsHistory.interested_company_id)
Index('idx_status_reports_history_external_id', StatusReportsHistory.external_id)
Index('idx_status_reports_history_charge_reference', StatusReportsHistory.charge_reference)


class Suppliers(Base):
    __tablename__ = 'suppliers'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_suppliers'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    external_id = Column(CIText())
    role = Column(
        Enum(SupplierRoleEnum,
             name='t_supplier_role',
             values_callable=lambda obj: [e.value for e in obj]),
    )
    type = Column(
        Enum(SupplierTypeEnum,
             name='t_supplier_type',
             values_callable=lambda obj: [e.value for e in obj])
    )
    contract_type = Column(
        Enum(SupplierContractTypeEnum,
             name='t_supplier_contract_type',
             values_callable=lambda obj: [e.value for e in obj])
    )
    contract_start_date = Column(Date)
    contract_end_date = Column(Date)
    contract_work_areas = Column(ARRAY(
        Enum(SupplierWorkAreaEnum,
             name='t_supplier_work_area_type',
             values_callable=lambda obj: [e.value for e in obj],
             create_constraint=False,
             native_enum=False)
    ))
    materialized_path = Column(ARRAY(Text))
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey('projects.id', ondelete="CASCADE", name='fk_suppliers_project_id'),
        nullable=False,
    )
    parent_supplier_id = Column(Text)
    parent_company_id = Column(Text)
    company_id = Column(Text)
    revision = Column(Text)
    last_visited = Column(TIMESTAMP(precision=6))
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    first_visited = Column(TIMESTAMP(precision=6))
    visitor_type = Column(
        Enum(SupplierVisitorTypeEnum,
             name='t_supplier_visitor_type',
             values_callable=lambda obj: [e.value for e in obj])
    )
    is_one_man_company = Column(BOOLEAN)
    has_collective_agreement = Column(BOOLEAN)
    collective_agreement_name = Column(Text)


Index('idx_suppliers_role', Suppliers.role)
Index('idx_suppliers_type', Suppliers.type)
Index('idx_suppliers_project_id', Suppliers.project_id)
Index('idx_suppliers_company_id', Suppliers.company_id)
Index('idx_suppliers_external_id', Suppliers.external_id)


class InternalProjectIds(Base):
    __tablename__ = 'internal_project_ids'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_internal_project_ids'),
        UniqueConstraint(
            'project_id', 'company_id', name='internal_project_ids_project_id_company_id_key',
        ),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    internal_project_id = Column(Text)
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey('projects.id', ondelete="NO ACTION", name='fk_internal_project_project_id'),
        nullable=False,
    )
    company_id = Column(Text)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index('idx_internal_project_ids_project_id', InternalProjectIds.project_id)
Index('idx_internal_project_ids_company_id', InternalProjectIds.company_id)
Index('idx_internal_project_ids_internal_project_id', InternalProjectIds.internal_project_id)


class SupplierContacts(Base):
    __tablename__ = 'supplier_contacts'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_supplier_contacts'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    supplier_id = Column(
        UUID(as_uuid=True),
        ForeignKey('suppliers.id', ondelete="CASCADE", name='fk_suppliers_id'),
        nullable=False,
    )
    person_id = Column(Text)
    person_email = Column(Text)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index('idx_supplier_contacts_supplier_id', SupplierContacts.supplier_id)
Index('idx_supplier_person_id', SupplierContacts.person_id)


class ProjectUsers(Base):
    __tablename__ = 'project_users'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_project_users'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False,
    )
    external_id = Column(CIText())
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey('projects.id', ondelete="CASCADE", name='fk_project_users_project_id'),
        nullable=False,
    )
    role = Column(
        Enum(ProjectUserRoleEnum,
             name='t_project_user_role',
             values_callable=lambda obj: [e.value for e in obj]),
    )
    notify = Column(BOOLEAN, server_default='t')
    user_account_id = Column(Text)
    person_id = Column(Text)
    represented_company_id = Column(Text)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index('idx_project_users_project_id', ProjectUsers.project_id)
Index('idx_project_users_role', ProjectUsers.role)
Index('idx_project_users_user_account_id', ProjectUsers.user_account_id)
Index("idx_project_users_person_id", ProjectUsers.person_id)
Index('idx_project_users_represented_company_id', ProjectUsers.represented_company_id)
Index('idx_project_user_external_id', ProjectUsers.external_id, unique=True)


class ReportAccesses(Base):
    __tablename__ = 'report_accesses'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_report_accesses'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    access_time = Column(TIMESTAMP(timezone=True, precision=6))
    external_id = Column(CIText())
    arkisto_id = Column(Text)
    report_id = Column(Text)
    status = Column(Enum('active', 'hidden', name='t_report_access_status'))
    customer_company_id = Column(Text)
    company_id = Column(Text)
    company_gov_id = Column(Text)
    company_gov_id_country = Column(Enum('FI', 'EE', name='t_company_country'))
    company_gov_id_type = Column(Enum('registration_number',
                                      name='t_company_gov_id_type'))
    person_id = Column(Text)
    language = Column(Text)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    template_version = Column(Text)


Index('idx_report_accesses_access_time', ReportAccesses.access_time)
Index('idx_report_accesses_arkisto_id', ReportAccesses.arkisto_id)
Index('idx_report_accesses_report_id', ReportAccesses.report_id)
Index('idx_report_accesses_customer_company_id', ReportAccesses.customer_company_id)
Index('idx_report_accesses_company_id', ReportAccesses.company_id)
Index('idx_report_accesses_person_id', ReportAccesses.person_id)
Index('idx_report_accesses_external_id', ReportAccesses.external_id)


class NotificationReports(Base):
    __tablename__ = 'notification_reports'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_notification_reports_ids'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    external_id = Column(CIText())
    generated_timestamp = Column(TIMESTAMP(precision=6))
    period = Column(Enum('daily', 'weekly', 'monthly', name='t_period'))
    to_timestamp = Column(TIMESTAMP(precision=6))
    from_timestamp = Column(TIMESTAMP(precision=6))
    statuses = Column(ARRAY(Text))
    company_ids = Column(ARRAY(Text))
    user_company_id = Column(Text)
    user_email = Column(Text)
    user_name = Column(Text)
    user_locale = Column(Text)
    user_report = Column(JSON)
    qvarn_report = Column(JSON)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index('idx_notification_reports_generated_timestamp', NotificationReports.generated_timestamp)
Index('idx_notification_reports_external_id', NotificationReports.external_id)


class BulkImportJobs(Base):
    __tablename__ = 'bulk_import_jobs'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_bulk_import_jobs'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    status = Column(
        Enum(BulkImportJobStatusEnum,
             name='t_bulk_import_job_status',
             values_callable=lambda obj: [e.value for e in obj])
    )
    project_id = Column(Text, nullable=False)
    imported = Column(TIMESTAMP(timezone=True), nullable=True)
    canceled = Column(TIMESTAMP(timezone=True), nullable=True)
    companies = Column(JSONB)
    interested_org_id = Column(Text)
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index('idx_bulk_import_jobs_creared_on', BulkImportJobs.created_on)
Index('idx_bulk_import_jobs_status', BulkImportJobs.status)


class PreAnnouncements(Base):
    __tablename__ = "preannouncements"
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_preannouncement_ids'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text("uuid_generate_v4()"), nullable=False
    )
    external_id = Column(CIText())
    status = Column(
        Enum(PreAnnouncementEnum,
             name='t_preannouncement_status',
             values_callable=lambda obj: [e.value for e in obj])
    )
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey('projects.id', ondelete="SET NULL", name='fk_preannouncement_project_id'),
        nullable=True,
    )
    created_by_supplier_id = Column(
        UUID(as_uuid=True),
        ForeignKey('suppliers.id', ondelete="SET NULL",
                   name='fk_preannouncement_created_by_supplier_id'),
        nullable=True,
    )
    for_supplier_id = Column(
        UUID(as_uuid=True),
        ForeignKey('suppliers.id', ondelete="SET NULL", name='fk_preannouncement_for_supplier_id'),
        nullable=True,
    )
    assigned_to_company_id = Column(Text)
    assigned_to_supplier_id = Column(
        UUID(as_uuid=True),
        ForeignKey('suppliers.id', ondelete="SET NULL",
                   name='fk_preannouncement_assigned_to_supplier_id'),
        nullable=True,
    )
    assigned_to_time = Column(TIMESTAMP(precision=6))

    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    active_pa_form = Column(
        UUID(as_uuid=True),
        ForeignKey('preannouncement_forms.id', ondelete="SET NULL",
                   name='fk_preannouncement_active_pa_form_id'),
        nullable=True,
    )


Index('idx_preannouncements_project_id', PreAnnouncements.project_id)
Index('idx_preannouncements_external_id', PreAnnouncements.external_id, unique=True)


class PreAnnouncementForms(Base):
    __tablename__ = 'preannouncement_forms'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_preannouncement_form_ids'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text('uuid_generate_v4()'), nullable=False
    )
    external_id = Column(CIText(), unique=True, nullable=False, index=True)

    company_name = Column(Text)
    company_gov_org_id = Column(Text, nullable=False)
    company_id_type = Column(Text)
    company_country = Column(Text, nullable=False)

    has_permanent_establishment = Column(BOOLEAN)
    is_one_man_company = Column(BOOLEAN)
    has_collective_agreement = Column(BOOLEAN)
    collective_agreement_name = Column(Text)

    buyer_name = Column(Text)
    buyer_gov_org_id = Column(Text, nullable=False)
    buyer_id_type = Column(Text)
    buyer_country = Column(Text, nullable=False)

    foreman_is_on_site = Column(BOOLEAN)
    foreman_first_name = Column(Text)
    foreman_last_name = Column(Text)
    foreman_phone_number = Column(Text)
    foreman_email = Column(Text)

    contract_type = Column(
        Enum(SupplierContractTypeEnum,
             name='t_supplier_contract_type',
             values_callable=lambda obj: [e.value for e in obj])
    )
    contract_start_date = Column(Date)
    contract_end_date = Column(Date)
    contract_work_areas = Column(ARRAY(
        Enum(SupplierWorkAreaEnum,
             name='t_supplier_work_area_type',
             values_callable=lambda obj: [e.value for e in obj],
             create_constraint=False,
             native_enum=False)
    ))

    pa_id = Column(
        UUID(as_uuid=True),
        ForeignKey('preannouncements.id', ondelete='SET NULL', name='fk_preannouncements_id'),
    )

    confirmed_name = Column(Text)
    confirmed_gov_org_id = Column(Text)
    confirmed_id_type = Column(Text)
    confirmed_time = Column(TIMESTAMP(precision=6))
    confirmed_country = Column(Text)
    rejected_name = Column(Text)
    rejected_gov_org_id = Column(Text)
    rejected_id_type = Column(Text)
    rejected_time = Column(TIMESTAMP(precision=6))
    rejected_country = Column(Text)

    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())

    informant_supplier_first_name = Column(Text)
    informant_supplier_last_name = Column(Text)
    informant_supplier_phone = Column(Text)
    informant_supplier_email = Column(Text)

    submitted_by_first_name = Column(Text)
    submitted_by_last_name = Column(Text)
    submitted_by_phone = Column(Text)
    submitted_by_email = Column(Text)
    submitted_time = Column(TIMESTAMP(precision=6))

    last_assigned_to_supplier = Column(
        UUID(as_uuid=True),
        ForeignKey('suppliers.id', ondelete="SET NULL",
                   name='fk_preannouncement_last_assigned_to_supplier_id'),
        nullable=True,
    )
    last_assigned_to_company = Column(Text)
    last_assigned_to_business_id = Column(Text)
    last_assigned_to_business_id_type = Column(Text)
    last_assigned_to_time = Column(TIMESTAMP(precision=6))


class ReportCache(Base):
    __tablename__ = 'report_cache'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_report_cache_ids'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text('uuid_generate_v4()'), nullable=False
    )
    external_id = Column(CIText(), unique=True, nullable=False, index=True)

    correlation_id = Column(Text)
    expires_at = Column(TIMESTAMP(precision=6))
    interested_org_id = Column(Text)
    key = Column(Text)
    provider = Column(Text)
    type = Column(Text)
    value = Column(Text)

    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index('idx_report_cache_external_id', ReportCache.external_id, unique=True)
Index('idx_report_cache_correlation_id', ReportCache.correlation_id)
Index('idx_report_cache_expires_at', ReportCache.expires_at)
Index('idx_report_cache_interested_org_id', ReportCache.interested_org_id)
Index('idx_report_cache_key', ReportCache.key)
Index('idx_report_cache_unique_cols', ReportCache.key, ReportCache.provider, ReportCache.type,
      ReportCache.interested_org_id, unique=True)


class CreditsafeAccount(Base):
    __tablename__ = 'creditsafe_account'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_creditsafe_account_ids'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text('uuid_generate_v4()'), nullable=False
    )
    person_id = Column(Text)
    org_id = Column(Text)
    username = Column(Text)
    password = Column(Text)
    state = Column(
        Enum(CreditsafeAccountStateEnum,
             name='t_creditsafe_account_state',
             values_callable=lambda obj: [e.value for e in obj])
    )
    last_changed = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index('idx_creditsafe_account_org_id', CreditsafeAccount.org_id)
Index('idx_creditsafe_account_person_id', CreditsafeAccount.person_id)
Index('idx_creditsafe_account_username', CreditsafeAccount.username)
Index('idx_creditsafe_account_state', CreditsafeAccount.state)
Index('idx_creditsafe_account_created_on', CreditsafeAccount.created_on)
Index(
    'idx_creditsafe_account_unique_active_org_id',
    CreditsafeAccount.org_id,
    unique=True,
    postgresql_where=CreditsafeAccount.state == CreditsafeAccountStateEnum.active,
)  # Partial index: ensure unique org_id for each active account


class CreditsafeAccountHistory(Base):
    __tablename__ = 'creditsafe_account_history'
    __table_args__ = (
        PrimaryKeyConstraint(name='pkey_creditsafe_account_history_ids'),
    )

    id = Column(
        UUID(as_uuid=True), primary_key=True,
        server_default=sa.text('uuid_generate_v4()'), nullable=False
    )
    creditsafe_account_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            'creditsafe_account.id',
            ondelete="CASCADE",
            name='fk_creditsafe_account_history_creditsafe_account_id'),
        nullable=False,
    )
    changed_by_person_id = Column(Text)
    person_id = Column(Text)
    org_id = Column(Text)
    username = Column(Text)
    state = Column(
        Enum(CreditsafeAccountStateEnum,
             name='t_creditsafe_account_history_state',
             values_callable=lambda obj: [e.value for e in obj])
    )
    comment = Column(Text)
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now())


Index(
    'idx_creditsafe_account_history_creditsafe_account_id',
    CreditsafeAccountHistory.creditsafe_account_id
)


class ProjectSupplierComments(Base):
    __tablename__ = "project_supplier_comments"
    __table_args__ = (PrimaryKeyConstraint(name="pkey_project_supplier_comments_ids"),)

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sa.text("uuid_generate_v4()"),
        nullable=False,
    )
    project_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            "projects.id",
            ondelete="CASCADE",
            name="fk_project_supplier_comments_project_id",
        ),
        nullable=False,
    )
    supplier_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            "suppliers.id",
            ondelete="CASCADE",
            name="fk_project_supplier_comments_supplier_id",
        ),
        nullable=False,
    )
    org_id = Column(Text, nullable=False)
    comment = Column(Text, nullable=False)
    created_by_org_id = Column(Text, nullable=False)
    created_by_person_id = Column(Text, nullable=False)
    created_timestamp = Column(
        TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now()
    )
    is_deleted = Column(BOOLEAN, server_default="false", nullable=False)
    deleted_by_org_id = Column(Text, nullable=True)
    deleted_by_person_id = Column(Text, nullable=True)
    deleted_timestamp = Column(TIMESTAMP(timezone=True), nullable=True)
    is_updated = Column(BOOLEAN, server_default="false", nullable=False)
    updated_by_org_id = Column(Text, nullable=True)
    updated_by_person_id = Column(Text, nullable=True)
    updated_timestamp = Column(TIMESTAMP(timezone=True), nullable=True)
    modified_timestamp = Column(TIMESTAMP(timezone=True), nullable=True)
    last_changed = Column(
        TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now()
    )
    created_on = Column(
        TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now()
    )


Index("idx_project_supplier_comments_project_id", ProjectSupplierComments.project_id)
Index("idx_project_supplier_comments_supplier_id", ProjectSupplierComments.supplier_id)
Index("idx_project_supplier_comments_org_id", ProjectSupplierComments.org_id)
Index(
    "idx_project_supplier_comments_created_by_person_id",
    ProjectSupplierComments.created_by_person_id,
)


class ProjectCommentViewers(Base):
    __tablename__ = "project_comment_viewers"
    __table_args__ = (
        PrimaryKeyConstraint(name="pkey_project_comment_viewers_ids"),
        UniqueConstraint(
            "project_comment_id",
            "read_by_person_id",
            name="uq_project_comment_viewers_project_comment_id_read_by_person_id",
        ),
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=sa.text("uuid_generate_v4()"),
        nullable=False,
    )
    project_comment_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            "project_supplier_comments.id",
            ondelete="CASCADE",
            name="fk_project_comment_viewers_project_comment_id",
        ),
        nullable=False,
    )
    read_by_person_id = Column(Text, nullable=False)
    last_changed = Column(
        TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now()
    )
    created_on = Column(
        TIMESTAMP(timezone=True), nullable=False, server_default=sa.func.now()
    )


Index(
    "idx_project_comment_viewers_project_comment_id",
    ProjectCommentViewers.project_comment_id,
)
Index(
    "idx_project_comment_viewers_read_by_person_id",
    ProjectCommentViewers.read_by_person_id,
)
