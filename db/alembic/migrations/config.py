import os

from sqlalchemy.engine.url import make_url, URL
from sqlalchemy.util.langhelpers import asbool


def update_query_dict(db_url, query):
    # sqlalchemy >= 1.4
    if hasattr(db_url, 'update_query_dict'):
        return db_url.update_query_dict(query)
    # sqlalchemy 1.3, which is only used for `make test` in local development
    # for technical debt reasons
    db_url.query.update(query)
    return db_url


def determine_sqlalchemy_url(environ=os.environ):
    # this will overwrite the ini-file sqlalchemy.url path
    # with the path given in the config of the main code
    if environ.get('BOLDATAAPI_DB_SQLALCHEMY_URL'):
        db_url = make_url(environ['BOLDATAAPI_DB_SQLALCHEMY_URL'])
        db_username = environ.get('BOLDATAAPI_DB_VAULT_DB_USERNAME')
        if db_username:
            db_url = update_query_dict(db_url, dict(user=db_username))
        db_password = environ.get('BOLDATAAPI_DB_VAULT_DB_PASSWORD')
        if db_password:
            db_url = update_query_dict(db_url, dict(password=db_password))
    elif environ.get('DATABASE_URL'):
        db_url = make_url(environ['DATABASE_URL'])
    else:
        psql_user = environ["POSTGRES_USER"]
        psql_password = environ["POSTGRES_PASSWORD"]
        psql_host = environ["POSTGRES_HOST"]
        psql_db = environ["POSTGRES_DB"]
        psql_port = environ["POSTGRES_PORT"]
        db_url = URL.create(
            drivername='postgresql+psycopg2',
            username=psql_user,
            password=psql_password,
            host=psql_host,
            port=psql_port,
            database=psql_db,
        )
    use_client_cert = environ.get('BOLDATAAPI_DB_SQLALCHEMY_USE_CLIENT_CERT', 'false')
    if asbool(use_client_cert):
        db_url = update_query_dict(db_url, {
            'sslmode': 'verify-ca',
            'sslrootcert': '/etc/bolfak/secrets/root.crt',
            'sslcert': '/etc/bolfak/secrets/super.crt',
            # libpq-ssl will check the permissions of the sslkeyfile refuse to
            # use it if it allows any access to world or group, as documented
            # in https://www.postgresql.org/docs/9.1/libpq-ssl.html.  Somehow
            # the file being a symlink confuses the check sufficiently that
            # it's wrongly rejected.  The link target is a 0600 mode file
            # in a `..data/` subdirectory.
            'sslkey': os.path.realpath('/etc/bolfak/secrets-600/super.key'),
        })

    return db_url
