import logging
import os
import sys
import time
from logging.config import fileConfig

import citext
from alembic import context
from sqlalchemy import engine_from_config, pool, text
from sqlalchemy.exc import OperationalError

from config import determine_sqlalchemy_url
from models import Base  # noqa
# https://alembic.sqlalchemy.org/en/latest/cookbook.html#replaceable-objects
# invoking Operations.register_operation() and Operations.implementation_for() sequences.
from operations import (  # noqa: F401
    create_sp,
    create_trg,
    create_view,
    drop_sp,
    drop_trg,
    drop_view,
)


# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# pytest-alembic will give us a connection, making the above logic unnecessary
if context.config.attributes.get("connection", None) is None:
    db_url = determine_sqlalchemy_url()
    print(f'Database URL: {db_url!r}', file=sys.stderr)
    # Extra fun: ConfigParser supports interpolation so % characters in the
    # password cause parse errors!  We should switch to using RawConfigParser, but for now
    # let's work around it.
    config.set_main_option("sqlalchemy.url", str(db_url).replace('%', '%%'))


# Interpret the config file for Python logging.
# This line sets up loggers basically.
fileConfig(config.config_file_name, disable_existing_loggers=False)


# we want reduced logging during test runs
if context.config.attributes.get("connection", None) is not None:
    logging.getLogger('alembic').setLevel('WARNING')


# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def retry(callable, timeout=10, exceptions=(OperationalError,), poll_interval=0.1):
    """Retry an operation until it no longer raises a specific exception.

    ``timeout`` and ``poll_interval`` are specified in seconds.
    """
    deadline = time.monotonic() + timeout
    while True:
        try:
            return callable()
        except exceptions as e:
            if time.monotonic() > deadline:
                raise
            print(f"{e}, retrying", file=sys.stderr)
            time.sleep(poll_interval)


def render_item(type_, obj, autogen_context):
    """Apply custom rendering for selected items."""
    if type_ == 'type' and isinstance(obj, citext.CIText):
        autogen_context.imports.add("import citext")
    # default rendering for everything
    return False


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Allow pytest-alembic to provide a connection for us
    connectable = context.config.attributes.get("connection", None)

    if connectable is None:
        connectable = engine_from_config(
            config.get_section(config.config_ini_section),
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )

    role = os.environ.get('BOLDATAAPI_DB_VAULT_DB_ROLE')
    with retry(connectable.connect) as connection:
        if role:
            connection.execute(text("SET ROLE :role"), role=role)
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            render_item=render_item,
            compare_type=True,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
