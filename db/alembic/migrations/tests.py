import dataclasses
import datetime
import os
import uuid
import warnings

import alembic.migration
import pytest
import sqlalchemy as sa
from alembic.autogenerate import produce_migrations, render_python_code
from pytest_alembic.config import duplicate_alembic_config
from pytest_alembic.plugin.error import AlembicTestFailure
from pytest_alembic.runner import MigrationContext
from pytest_alembic.tests import (  # noqa: re-export so the tests are included
    test_model_definitions_match_ddl,
    test_single_head_revision,
    test_up_down_consistency,
    test_upgrade,
)
from pytest_alembic.tests.default import NOT_IMPLEMENTED_WARNING
from pytest_alembic.tests.experimental.downgrade_leaves_no_trace import WrappingConnection
from sqlalchemy import MetaData
from sqlalchemy.engine import Connection
from sqlalchemy.engine.url import URL

from config import determine_sqlalchemy_url
from models import Base, PUBLIC_SCHEMA, SUPPORT_SCHEMA


def get_table_columns(alembic_engine, table_schema, table_name):
    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text("""
            SELECT column_name
            FROM INFORMATION_SCHEMA.columns
            WHERE table_schema = :table_schema
            AND table_name = :table_name
        """), dict(table_schema=table_schema, table_name=table_name)).fetchall()
    return [row[0] for row in rows]


def test_downgrade_leaves_no_trace(alembic_runner):
    alembic_runner.connection_executor.run_task(
        _test_downgrade_leaves_no_trace,
        alembic_runner=alembic_runner,
        skip_cycle_test_for_revisions=[
            'e0ef89fb1b01',
            '786bae40c6e4',
            'ee703cdbfa05',
            'caddb00ec579',
            '62967d784720',
            '03a5a4a5b399',
        ]
    )


def inspect_support_views_state(connection: Connection):
    inspector = sa.inspect(subject=connection)
    state = dict()
    for v in inspector.get_view_names('support'):
        state[v] = inspector.get_view_definition(v, 'support')

    return state


def _test_downgrade_leaves_no_trace(
        connection: Connection,
        alembic_runner: MigrationContext,
        skip_cycle_test_for_revisions=None):
    """
    Modified test from
    pytest_alembic/tests/experimental/downgrade_leaves_no_trace.py
    to include optional skip for given irreversible migrations
    """
    if skip_cycle_test_for_revisions is None:
        skip_cycle_test_for_revisions = []

    wrapped_connection = WrappingConnection(connection)

    # Swap the original engine for a connection to enable us to rollback the transaction
    # midway through.
    alembic_config = duplicate_alembic_config(alembic_runner.command_executor.alembic_config)
    alembic_config.attributes["connection"] = wrapped_connection

    alembic_runner = dataclasses.replace(
        alembic_runner,
        connection_executor=dataclasses.replace(
            alembic_runner.connection_executor,
            connection=connection,
        ),
        command_executor=dataclasses.replace(
            alembic_runner.command_executor,
            alembic_config=alembic_config,
        ),
    )

    history = alembic_runner.history
    revisions = history.revisions[:-1]
    if len(revisions) == 1:
        return

    below_minimum = alembic_runner.config.minimum_downgrade_revision is not None
    for revision in revisions:
        if below_minimum and revision == alembic_runner.config.minimum_downgrade_revision:
            below_minimum = False

        # Semantically, we'll solely upgrade for as long as we're below
        # the `minimum_downgrade_revision`,
        # if set. If not set, then this is always done.

        if not below_minimum and revision not in skip_cycle_test_for_revisions:
            print(f"checking revision {revision}")
            # Leaves the database in its previous state, to avoid subtle
            # upgrade -> downgrade issues.
            check_revision_cycle(alembic_runner, connection, revision)
        else:
            print(f"skipping revision {revision}")
        # So we need to proceed by one.
        alembic_runner.migrate_up_to(revision)


def check_revision_cycle(alembic_runner, connection, original_revision):
    migration_context = alembic.migration.MigrationContext.configure(connection)

    # We first need to produce a `MetaData` which represents the state of the database
    # we're trying to get to.
    with connection.begin_nested() as trans:
        alembic_runner.migrate_up_one()
        upgrade_revision = alembic_runner.current

        upgrade_metadata = MetaData()
        upgrade_metadata.reflect(connection)

        # Having procured the target `MetaData`, we need the database back in its original state.
        trans.rollback()

    with connection.begin_nested() as trans:
        # Produce a canonically autogenerated upgrade relative to the original.
        autogenerated_upgrade = produce_migrations(migration_context, upgrade_metadata)
        rendered_autogenerated_upgrade = render_python_code(autogenerated_upgrade.upgrade_ops)

        # Now, we can perform the upgrade -> downgrade cycle!
        support_view_before = inspect_support_views_state(connection)
        alembic_runner.migrate_up_one()
        try:
            alembic_runner.migrate_down_one()
            support_view_after = inspect_support_views_state(connection)
            # Support views should be exact the same before and after the upgrade-downgrade cycle
            assert support_view_before == support_view_after

        except NotImplementedError:
            # In the event of a `NotImplementedError`, we should have the same semantics,
            # as-if `minimum_downgrade_revision` was specified, but we'll emit a warning
            # to suggest that feature is used instead.
            warnings.warn(NOT_IMPLEMENTED_WARNING.format(revision=upgrade_revision), stacklevel=1)

        else:
            downgrade_metadata = MetaData()
            downgrade_metadata.reflect(connection)

            # Produce a canonically autogenerated upgrade relative to the post-downgrade state.
            autogenerated_post_downgrade = produce_migrations(migration_context, upgrade_metadata)
            rendered_autogenerated_post_downgrade = render_python_code(
                autogenerated_post_downgrade.upgrade_ops
            )

            if rendered_autogenerated_upgrade != rendered_autogenerated_post_downgrade:
                message = (
                    f"There is a difference between the pre-'{upgrade_revision}'-upgrade "
                    f"`MetaData`, and the post-'{upgrade_revision}'-downgrade `MetaData`. This "
                    f"implies that the upgrade performs some set of DDL changes which the "
                    f"downgrade does not precisely undo."
                )
                raise AlembicTestFailure(
                    message,
                    context=[
                        (
                            f"DDL diff for {original_revision} -> {upgrade_revision}",
                            rendered_autogenerated_upgrade,
                        ),
                        (
                            f"DDL diff after performing the {upgrade_revision} -> "
                            f"{original_revision} downgrade",
                            rendered_autogenerated_post_downgrade,
                        ),
                    ],
                )
        finally:
            # **This** rollback is to ensure we leave the database back in it's original state
            # for the next revision.
            trans.rollback()


@pytest.mark.parametrize("environ, expected", [
    (
        dict(
            BOLDATAAPI_DB_SQLALCHEMY_URL='sqlite://',
        ),
        URL.create(drivername='sqlite')
    ),
    (
        dict(
            BOLDATAAPI_DB_SQLALCHEMY_URL='*************************************/dbname',
        ),
        URL.create(
            drivername='postgresql',
            username='dbuser',
            password='dbpwd',
            host='dbhost',
            port=1234,
            database='dbname',
        ),
    ),
    (
        dict(
            BOLDATAAPI_DB_SQLALCHEMY_URL='postgresql://dbhost:1234/dbname',
            BOLDATAAPI_DB_VAULT_DB_USERNAME='dbuser',
            BOLDATAAPI_DB_VAULT_DB_PASSWORD='dbpwd',
        ),
        URL.create(
            drivername='postgresql',
            host='dbhost',
            port=1234,
            database='dbname',
            query=dict(
                user='dbuser',
                password='dbpwd',
            ),
        ),
    ),
    (
        dict(
            BOLDATAAPI_DB_SQLALCHEMY_URL='postgresql://dbhost:1234/dbname',
            BOLDATAAPI_DB_VAULT_DB_USERNAME='dbuser',
            BOLDATAAPI_DB_SQLALCHEMY_USE_CLIENT_CERT='true',
        ),
        URL.create(
            drivername='postgresql',
            host='dbhost',
            port=1234,
            database='dbname',
            query=dict(
                user='dbuser',
                sslmode='verify-ca',
                sslrootcert='/etc/bolfak/secrets/root.crt',
                sslcert='/etc/bolfak/secrets/super.crt',
                sslkey='/etc/bolfak/secrets-600/super.key.real',
            ),
        ),
    ),
    (
        dict(
            DATABASE_URL='sqlite://',
        ),
        URL.create(drivername='sqlite')
    ),
    (
        dict(
            POSTGRES_USER='dbuser',
            POSTGRES_PASSWORD='dbpwd',
            POSTGRES_HOST='dbhost',
            POSTGRES_DB='dbname',
            POSTGRES_PORT='1234',
        ),
        URL.create(
            drivername='postgresql+psycopg2',
            username='dbuser',
            password='dbpwd',
            host='dbhost',
            port=1234,
            database='dbname',
        ),
    ),
])
def test_determine_sqlalchemy_url(environ, expected, monkeypatch):
    monkeypatch.setattr(os.path, 'realpath', lambda x: f'{x}.real')
    assert determine_sqlalchemy_url(environ) == expected


def test_all_tables_have_last_change_trigger(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_to("heads")
    with alembic_engine.connect() as conn:
        rows = conn.execute("SELECT trigger_name FROM information_schema.triggers").fetchall()
    triggers_that_exist = {row[0] for row in rows}
    triggers_that_should_exist = {
        f"set_{table.name}_last_changed"
        for table in Base.metadata.tables.values()
        if "last_changed" in table.columns
    }
    assert triggers_that_exist == triggers_that_should_exist


def test_supplier_visitor_type_creation(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('cfb88e429d34')
    # unfortunately we have to use a valid UUID representation, the
    # shortest of which is a 32-character hex string
    project1 = '20000000000000000000000000000001'
    project2 = '20000000000000000000000*********'
    project3 = '20000000000000000000000000000003'
    alembic_runner.insert_into('projects', [
        dict(id=project1),
        dict(id=project2),
        dict(id=project3),
    ])
    supplier1 = '10000000000000000000000000000001'
    supplier2 = '10000000000000000000000*********'
    supplier3 = '10000000000000000000000000000003'
    supplier4 = '10000000000000000000000000000004'
    supplier5 = '10000000000000000000000000000005'
    supplier6 = '10000000000000000000000000000006'
    supplier7 = '10000000000000000000000000000007'
    supplier8 = '10000000000000000000000000000008'
    supplier9 = '10000000000000000000000000000009'
    alembic_runner.insert_into('suppliers', [
        # project 1, with three kinds of visitors
        dict(id=supplier1, project_id=project1, company_id='company1', type='linked'),
        dict(id=supplier2, project_id=project1, company_id='company2', type='unlinked'),
        dict(id=supplier3, project_id=project1, company_id='company1', type='visitor'),
        dict(id=supplier4, project_id=project1, company_id='company2', type='visitor'),
        dict(id=supplier5, project_id=project1, company_id='company3', type='visitor'),
        # project 2, which has company 3 as supplier that should not affect the
        # company 3 visitor in project 1
        dict(id=supplier6, project_id=project2, company_id='company3', type='linked'),
        # project 3, which has two confirmed suppliers for the same visitor,
        # just to see what happens
        dict(id=supplier7, project_id=project3, company_id='company4', type='linked'),
        dict(id=supplier8, project_id=project3, company_id='company4', type='linked'),
        dict(id=supplier9, project_id=project3, company_id='company4', type='visitor'),
    ])
    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text("SELECT id, visitor_type FROM suppliers")).fetchall()
        visitor_types = {
            row[0].hex: row[1] for row in rows
        }

    assert visitor_types == {
        supplier1: None,
        supplier2: None,
        supplier3: 'nonpaed',
        supplier4: 'raw',
        supplier5: 'raw',
        supplier6: None,
        supplier7: None,
        supplier8: None,
        supplier9: 'nonpaed',
    }


def mkid(n):
    """Produce an unique ID that can be parsed as a UUID."""
    return f'{n:032x}'


def supplier(id, *, project_id=mkid(1000), company_id=mkid(2000), **kw):
    return dict(
        id=mkid(id),
        project_id=project_id,
        company_id=company_id,
        **kw,
    )


def test_contract_work_areas_cleanup(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('caddb00ec579')
    alembic_runner.insert_into('projects', [
        dict(id=mkid(1000)),
    ])
    alembic_runner.insert_into('suppliers', [
        supplier(1, contract_type='consulting', contract_work_areas=['other']),
        supplier(2, contract_type='contracting', contract_work_areas=['other']),
        supplier(3, contract_type='personnel_leasing', contract_work_areas=['other']),
        supplier(4, contract_type='machine_equipment_leasing', contract_work_areas=['other']),
        supplier(5, contract_type='materials_and_products', contract_work_areas=['other']),
        supplier(6, contract_type='transportation', contract_work_areas=['other']),
        supplier(7, contract_type=None, contract_work_areas=['other']),
    ])
    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text("SELECT id, contract_work_areas FROM suppliers")).fetchall()
        work_areas = {
            row[0].hex: row[1] for row in rows
        }

    assert work_areas == {
        mkid(1): ['other'],
        mkid(2): ['other'],
        mkid(3): ['other'],
        mkid(4): None,
        mkid(5): None,
        mkid(6): None,
        mkid(7): None,
    }


def test_remove_duplicates_status_reports_history(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('4329457a1281')

    # unfortunately we have to use a valid UUID representation, the
    # shortest of which is a 32-character hex string
    report_id = '10000000000000000000000000000001'
    duplicated_id1 = '20000000000000000000000000000001'
    duplicated_id2 = '20000000000000000000000*********'
    report_external_id = 'same'

    report2_id = '30000000000000000000000000000001'
    report2_external_id = 'same2'
    duplicated2_id1 = '40000000000000000000000000000001'

    report3_id = '50000000000000000000000000000001'
    report3_external_id = 'notsame'

    status = '500 OK'
    company = 'company'
    interested_org = 'interested_org'

    report = dict(
            id=report_id,
            external_id=report_external_id,
            status=status,
            interested_company_id=interested_org,
            company_id=company,
    )

    report_duplicate1 = dict(
            id=duplicated_id1,
            external_id=report_external_id,
            status=status,
            interested_company_id=interested_org,
            company_id=company,
    )

    report_duplicate2 = dict(
            id=duplicated_id2,
            external_id=report_external_id,
            status=status,
            interested_company_id=interested_org,
            company_id=company,
    )

    report2 = dict(
            id=report2_id,
            external_id=report2_external_id,
            status=status,
            interested_company_id=interested_org,
            company_id=company,
    )

    report2_duplicate = dict(
            id=duplicated2_id1,
            external_id=report2_external_id,
            status=status,
            interested_company_id=interested_org,
            company_id=company,
    )

    report3 = dict(
            id=report3_id,
            external_id=report3_external_id,
            status=status,
            interested_company_id=interested_org,
            company_id=company,
    )

    alembic_runner.insert_into('status_reports_history', [
        report,
        report_duplicate1,
        report_duplicate2,
        report2,
        report2_duplicate,
        report3
    ])

    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        stored_historic_rows = conn.execute(
            sa.text(
                "SELECT \
                    id, \
                    external_id, \
                    status, \
                    interested_company_id, \
                    company_id \
                FROM status_reports_history"
            )).fetchall()

    # only three reports should remain
    assert len(stored_historic_rows) == 3
    row1 = stored_historic_rows[0]
    assert row1[0].hex == report['id']
    assert row1[1] == report['external_id']
    assert row1[2] == report['status']
    assert row1[3] == report['interested_company_id']
    assert row1[4] == report['company_id']

    row2 = stored_historic_rows[1]
    assert row2[0].hex == report2['id']
    assert row2[1] == report2['external_id']
    assert row2[2] == report2['status']
    assert row2[3] == report2['interested_company_id']
    assert row2[4] == report2['company_id']

    row3 = stored_historic_rows[2]
    assert row3[0].hex == report3['id']
    assert row3[1] == report3['external_id']
    assert row3[2] == report3['status']
    assert row3[3] == report3['interested_company_id']
    assert row3[4] == report3['company_id']


def test_status_reports_restricted_view_omitted_json_vals(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('0ee578c21c14')

    # unfortunately we have to use a valid UUID representation, the
    # shortest of which is a 32-character hex string
    report_id = '10000000000000000000000000000001'
    report_external_id = 'random'

    status = '500 OK'
    company = 'company'
    interested_org = 'interested_org'

    sensitive_fields = [
        'board_members',
        'ceos',
        'company_sole_trader',
        'co_address',
        'domicile',
        'phone',
        'post_code',
        'vat_nr',
        'visiting_addresses',
        'email',
        'organisation_name',
        'organisation_nr',
        'representatives',
        'signatories',
        'town',
        'trustees',
    ]

    non_sensitive_fields = [
        'account_period',
        'account_period_full',
        'affiliates',
        'auditor_control_approved',
        'auditor_control_comments',
        'auditor_control_interpretation',
        'auditor_control_source',
        'auditor_control_status',
        'auditor_control_updated',
        'balance_liquidity_percent',
        'bis_source',
        'bis_updated',
        'business_code',
        'business_desc',
        'charge_reference',
        'clreports_interpretation',
        'company_form_interpretation',
        'company_form_source',
        'company_form_status',
        'company_form_updated',
        'company_history',
        'control_balance_sheet',
        'control_balance_sheet_source',
        'control_balance_sheet_updated',
        'country_alpha3',
        'creditsafe_status_interpretation',
        'creditsafe_status_source',
        'creditsafe_status_status',
        'creditsafe_status_updated',
        'current_name_reg',
        'debt_company_or_private_enforcement_debt',
        'debt_company_or_private_enforcement_debt_sum',
        'debt_company_or_private_enforcement_interpretation',
        'debt_company_or_private_enforcement_source',
        'debt_company_or_private_enforcement_status',
        'debt_company_or_private_enforcement_updated',
        'debt_liabilities_payments_source',
        'debt_liabilities_payments_updated',
        'debt_taxes_fees_enforcement_debt',
        'debt_taxes_fees_enforcement_debt_sum',
        'debt_taxes_fees_enforcement_interpretation',
        'debt_taxes_fees_enforcement_source',
        'debt_taxes_fees_enforcement_status',
        'debt_taxes_fees_enforcement_updated',
        'distraint_attempt',
        'employer_contributions',
        'equity',
        'equity_share_percent',
        'estimated_payroll',
        'fin_info_source',
        'fin_info_updated',
        'foundation_date',
        'groupmother_country',
        'groupmother_organisation_name',
        'groupmother_organisation_nr',
        'kronofogden_confirmed_claims',
        'kronofogden_confirmed_private_amount',
        'kronofogden_confirmed_private_amount_currency',
        'kronofogden_confirmed_private_claims',
        'kronofogden_confirmed_public_amount',
        'kronofogden_confirmed_public_amount_currency',
        'kronofogden_confirmed_public_claims',
        'kronofogden_debt',
        'kronofogden_debt_currency',
        'kronofogden_debt_date',
        'kronofogden_remarks_amount',
        'kronofogden_remarks_amount_currency',
        'kronofogden_remarks_claims',
        'kronofogden_remarks_private_amount',
        'kronofogden_remarks_private_amount_currency',
        'kronofogden_remarks_private_claims',
        'kronofogden_remarks_public_amount',
        'kronofogden_remarks_public_amount_currency',
        'kronofogden_remarks_public_claims',
        'kronofogden_remarks_source',
        'kronofogden_remarks_updated',
        'legal_form',
        'legal_form_desc',
        'long_term_liabilities',
        'main_business',
        'net_turnover',
        'nr_employees',
        'nr_employees_int',
        'organisation_alternate_name',
        'overdue_tax_liabilities_amount',
        'payment_applications_claims',
        'payment_applications_sum',
        'payment_applications_sum_currency',
        'payroll_register_interpretation',
        'payroll_register_source',
        'payroll_register_status',
        'payroll_register_status_date',
        'payroll_register_updated',
        'payroll_tax',
        'profit_after_tax',
        'profit_before_tax',
        'quick_ratio_percent',
        'rating_commentaries',
        'rating_commentary_count',
        'rating_interpretation',
        'rating_source',
        'rating_statuses',
        'rating_updated',
        'report_creation_date',
        'report_reject_code',
        'report_reject_interpretation',
        'report_reject_source',
        'report_reject_status',
        'report_reject_updated',
        'report_version_id06',
        'representative_check_interpretation',
        'representative_check_source',
        'representative_check_status',
        'representative_check_updated',
        'representatives_source',
        'representatives_updated',
        'share_capital',
        'short_term_liabilities',
        'solvency_percent',
        'tax_format',
        'tax_format_start_date',
        'tax_interpretation',
        'tax_source',
        'tax_updated',
        'total_equity_and_liabilities',
        'trustees_source',
        'trustees_updated',
        'turnover_interval',
        'turnover_per_employee',
        'url',
        'used_providers',
        'vat_register',
        'vat_register_interpretation',
        'vat_register_source',
        'vat_register_status',
        'vat_register_updated',
        'vat_start_date',
    ]

    json_dict = {field: 'sensitive_info' for field in sensitive_fields}

    for field in non_sensitive_fields:
        json_dict[field] = f'not_sensitive_info_{field}'

    report = dict(
        json_=json_dict,
        id=report_id,
        external_id=report_external_id,
        status=status,
        interested_company_id=interested_org,
        company_id=company,
        charge_reference='charge_reference',
        used_providers=['creditsafe_v2'],
    )

    alembic_runner.insert_into('status_reports', [
        report,
    ])

    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        rows = conn.execute(
            sa.text(
                """
                    SELECT
                        json_restricted,
                        id,
                        external_id,
                        status,
                        generated_timestamp,
                        interested_company_id,
                        company_id,
                        last_changed,
                        created_on,
                        charge_reference,
                        used_providers
                    FROM
                        status_reports_restricted
                """
            )).fetchall()

    row = rows[0]
    json_restricted = row[0]
    assert all(field not in json_restricted for field in sensitive_fields)

    for field in non_sensitive_fields:
        assert json_restricted[field] == f'not_sensitive_info_{field}'

    assert row[1].hex == report['id']
    assert row[2] == report['external_id']
    assert row[3] == report['status']
    assert row[5] == report['interested_company_id']
    assert row[6] == report['company_id']
    assert row[9] == report['charge_reference']
    assert row[10] == report['used_providers']


def test_notification_reports_restricted_view(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_to('6b361365c810')
    alembic_runner.insert_into(
        'notification_reports',
        [
            dict(
                id=mkid(1),
                external_id="0f75-ca0032a84a6128dc309e85a6a8bf9910-aef6001f",
                generated_timestamp=datetime.datetime(2023, 5, 24, 15, 26, 22),
                # currently most of the fields are unused and we rely on the
                # legacy qvarn_report field
                period=None,
                to_timestamp=None,
                from_timestamp=None,
                statuses=None,
                company_ids=None,
                user_company_id=None,
                user_email=None,
                user_name=None,
                user_locale=None,
                user_report=None,
                qvarn_report={
                    "kind": "monthly",
                    'old_status_cutoff_timestamp': '2017-10-19T16:39:42.014562',
                    'new_status_cutoff_timestamp': '2017-10-20T16:39:42.014562',
                    'statuses': ['stop'],
                    'org_ids': ['f392-e1133c174ba8337d760dbd1ae9c2fae5-a10a2b99'],
                    'users_to_notify': {
                        '<EMAIL> ac23-4b92d2fa282433610327927495cb3d24-11111111': {
                            'user_info': {
                                'name': 'Test User 1',
                                'email': '<EMAIL>',
                            },
                            'user_org_id': 'ac23-4b92d2fa282433610327927495cb3d24-11111111',
                            'projects': {
                                'ac23-4b92d2fa282433610327927495cb3d24-a9c956d4': {
                                    'project_name': 'Some project',
                                    'companies': {
                                        'f392-e1133c174ba8337d760dbd1ae9c2fae5-a10a2b99': {
                                            'company_name': 'Test Company 15',
                                            'old_status': 'incomplete',
                                            'new_status': 'stop',
                                            'old_report_id': 'dc87-c786e8f4187069992463dcedf91a93f9-b192f311',  # noqa: E501
                                            'new_report_id': '4fde-70596f5f9dc6dd877daf744eba49df92-100cef68',  # noqa: E501
                                        },
                                    },
                                    'project_status': 'stop',
                                },
                            },
                        },
                        '<EMAIL> ac23-4b92d2fa282433610327927495cb3d24-22222222': {
                            'user_info': {
                                'name': 'Test User 2',
                                'email': '<EMAIL>',
                            },
                            'user_org_id': 'ac23-4b92d2fa282433610327927495cb3d24-22222222',
                            'projects': {
                                'ac23-4b92d2fa282433610327927495cb3d24-bbbbbbbb': {
                                    'project_name': 'Some project',
                                    'companies': {
                                        'f392-e1133c174ba8337d760dbd1ae9c2fae5-a1a1a1a1': {
                                            'company_name': 'Test Company 22',
                                            'old_status': 'ok',
                                            'new_status': 'stop',
                                            'old_report_id': 'dc87-c786e8f4187069992463dcedf91a93f9-aaaaaaaa',  # noqa: E501
                                            'new_report_id': '4fde-70596f5f9dc6dd877daf744eba49df92-bbbbbbbb',  # noqa: E501
                                        },
                                        'f392-e1133c174ba8337d760dbd1ae9c2fae5-b2b2b2b2': {
                                            'company_name': 'Test Company 23',
                                            'old_status': 'ok',
                                            'new_status': 'investigate',
                                            'old_report_id': 'dc87-c786e8f4187069992463dcedf91a93f9-cccccccc',  # noqa: E501
                                            'new_report_id': '4fde-70596f5f9dc6dd877daf744eba49df92-dddddddd',  # noqa: E501
                                        },
                                    },
                                    'project_status': 'stop',
                                },
                                'ac23-4b92d2fa282433610327927495cb3d24-cccccccc': {
                                    'project_name': 'Another project',
                                    'companies': {},
                                    'project_status': 'incomplete',
                                },
                            },
                        },
                    },
                },
            ),
        ],
    )
    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text("SELECT * FROM notification_reports_restricted")).fetchall()
    result = rows[0]._asdict()
    assert result == dict(
        id=uuid.UUID(mkid(1)),
        external_id="0f75-ca0032a84a6128dc309e85a6a8bf9910-aef6001f",
        generated_timestamp=datetime.datetime(2023, 5, 24, 15, 26, 22),
        period=None,
        to_timestamp=None,
        from_timestamp=None,
        statuses=None,
        company_ids=None,
        user_company_id=None,
        # user_email is restricted
        # user_name is restricted
        user_locale=None,
        # user_report is restricted
        qvarn_report_restricted={
            "kind": "monthly",
            'old_status_cutoff_timestamp': '2017-10-19T16:39:42.014562',
            'new_status_cutoff_timestamp': '2017-10-20T16:39:42.014562',
            'statuses': ['stop'],
            'org_ids': ['f392-e1133c174ba8337d760dbd1ae9c2fae5-a10a2b99'],
            'users_to_notify': {
                # user email part of the email + org_id key is anonymized
                'anonymous_user_1 ac23-4b92d2fa282433610327927495cb3d24-11111111': {
                    # user_info is restricted
                    'user_org_id': 'ac23-4b92d2fa282433610327927495cb3d24-11111111',
                    'projects': {
                        'ac23-4b92d2fa282433610327927495cb3d24-a9c956d4': {
                            'project_name': 'Some project',
                            'companies': {
                                'f392-e1133c174ba8337d760dbd1ae9c2fae5-a10a2b99': {
                                    # company_name is restricted
                                    'old_status': 'incomplete',
                                    'new_status': 'stop',
                                    'old_report_id': 'dc87-c786e8f4187069992463dcedf91a93f9-b192f311',  # noqa: E501
                                    'new_report_id': '4fde-70596f5f9dc6dd877daf744eba49df92-100cef68',  # noqa: E501
                                },
                            },
                            'project_status': 'stop',
                        },
                    },
                },
                # this works for more than one user/project/company
                'anonymous_user_2 ac23-4b92d2fa282433610327927495cb3d24-22222222': {
                    'user_org_id': 'ac23-4b92d2fa282433610327927495cb3d24-22222222',
                    'projects': {
                        'ac23-4b92d2fa282433610327927495cb3d24-bbbbbbbb': {
                            'project_name': 'Some project',
                            'companies': {
                                'f392-e1133c174ba8337d760dbd1ae9c2fae5-a1a1a1a1': {
                                    'old_status': 'ok',
                                    'new_status': 'stop',
                                    'old_report_id': 'dc87-c786e8f4187069992463dcedf91a93f9-aaaaaaaa',  # noqa: E501
                                    'new_report_id': '4fde-70596f5f9dc6dd877daf744eba49df92-bbbbbbbb',  # noqa: E501
                                },
                                'f392-e1133c174ba8337d760dbd1ae9c2fae5-b2b2b2b2': {
                                    'old_status': 'ok',
                                    'new_status': 'investigate',
                                    'old_report_id': 'dc87-c786e8f4187069992463dcedf91a93f9-cccccccc',  # noqa: E501
                                    'new_report_id': '4fde-70596f5f9dc6dd877daf744eba49df92-dddddddd',  # noqa: E501
                                },
                            },
                            'project_status': 'stop',
                        },
                        'ac23-4b92d2fa282433610327927495cb3d24-cccccccc': {
                            'project_name': 'Another project',
                            # empty json objects do not confuse the sql data mangler
                            'companies': {},
                            'project_status': 'incomplete',
                        },
                    },
                },
            },
        },
        created_on=result['created_on'],  # actual value changes
        last_changed=result['last_changed'],  # actual value changes
    )


def test_restricted_views(alembic_engine, alembic_runner):
    # most of these views were defined in revision acd3300dc9bf, but then some were fixed
    # in cf118d694563 and a655d63276b1
    alembic_runner.migrate_up_to('a655d63276b1')
    alembic_runner.insert_into('preannouncements', [
        # there are no restricted fields in preannouncements
        dict(
            external_id="39af7e20-82fe-4453-9103-6393302117ee",
        ),
    ])
    alembic_runner.insert_into('preannouncement_forms', [
        dict(
            external_id="39af7e20-82fe-4453-9103-6393302117ee",
            company_name='restricted',
            company_gov_org_id='restricted',
            company_country="something-not-null",
            buyer_name='restricted',
            buyer_gov_org_id='restricted',
            buyer_country="something-not-null",
            foreman_first_name='restricted',
            foreman_last_name='restricted',
            foreman_email='restricted',
            foreman_phone_number='restricted',
            informant_supplier_first_name='restricted',
            informant_supplier_last_name='restricted',
            informant_supplier_email='restricted',
            informant_supplier_phone='restricted',
            submitted_by_first_name='restricted',
            submitted_by_last_name='restricted',
            submitted_by_email='restricted',
            submitted_by_phone='restricted',
            confirmed_name='restricted',
            confirmed_gov_org_id='restricted',
            rejected_name='restricted',
            rejected_gov_org_id='restricted',
            last_assigned_to_business_id='restricted',
        ),
    ])
    alembic_runner.insert_into('bulk_import_jobs', [
        # there are no restricted fields in bulk_import_jobs
        dict(
            project_id="something-not-null",
        ),
    ])
    alembic_runner.insert_into('projects', [
        dict(
            id=mkid(0x123),
        ),
    ])
    alembic_runner.insert_into('suppliers', [
        dict(
            id=mkid(0x12345),
            project_id=mkid(0x123),
        ),
    ])
    alembic_runner.insert_into('supplier_contacts', [
        dict(
            supplier_id=mkid(0x12345),
        ),
    ])
    alembic_runner.insert_into('internal_project_ids', [
        dict(
            project_id=mkid(0x123),
        ),
    ])
    alembic_runner.insert_into('project_users', [
        dict(
            project_id=mkid(0x123),
        ),
    ])
    alembic_runner.insert_into('status_reports_history', [
        dict(),
    ])
    with alembic_engine.connect() as conn:
        rows = conn.execute("SELECT * FROM preannouncements_restricted").fetchall()
        assert 'restricted' not in repr(rows)
        rows = conn.execute("SELECT * FROM preannouncement_forms_restricted").fetchall()
        assert 'restricted' not in repr(rows)
        rows = conn.execute("SELECT * FROM bulk_import_jobs_restricted").fetchall()
        assert 'restricted' not in repr(rows)
        assert rows[0].project_id == 'something-not-null'
        rows = conn.execute("SELECT * FROM alembic_version_restricted").fetchall()
        assert 'restricted' not in repr(rows)
        rows = conn.execute("SELECT * FROM supplier_contacts_restricted").fetchall()
        assert 'restricted' not in repr(rows)
        rows = conn.execute("SELECT * FROM internal_project_ids_restricted").fetchall()
        assert 'restricted' not in repr(rows)
        rows = conn.execute("SELECT * FROM project_users").fetchall()
        assert 'restricted' not in repr(rows)
        rows = conn.execute("SELECT * FROM status_reports_history_restricted").fetchall()
        assert 'restricted' not in repr(rows)


def test_migrate_20231011_0848d4497d9f_restr(alembic_engine, alembic_runner):
    moved_view_names = {
        'alembic_version_restricted',
        'bulk_import_jobs_restricted',
        'internal_project_ids_restricted',
        'notification_reports_restricted',
        'preannouncement_forms_restricted',
        'preannouncements_restricted',
        'project_users_restricted',
        'projects_restricted',
        'report_cache_restricted',
        'status_reports_history_restricted',
        'status_reports_restricted',
        'supplier_contacts_restricted',
        'suppliers_restricted',
    }

    alembic_runner.migrate_up_before('0848d4497d9f')

    with alembic_engine.connect() as conn:
        rows = conn.execute("SELECT table_name FROM INFORMATION_SCHEMA.views"
                            f" WHERE table_schema = '{PUBLIC_SCHEMA}'").fetchall()
        existing_public_views = {row[0] for row in rows}

    # All the named views were part of the public schema
    assert moved_view_names.issubset(existing_public_views)

    alembic_runner.migrate_up_to('0848d4497d9f')

    with alembic_engine.connect() as conn:
        support_rows = conn.execute("SELECT table_name FROM INFORMATION_SCHEMA.views"
                                    f" WHERE table_schema = '{SUPPORT_SCHEMA}'").fetchall()
        public_rows = conn.execute("SELECT table_name FROM INFORMATION_SCHEMA.views"
                                   f" WHERE table_schema = '{PUBLIC_SCHEMA}'").fetchall()
        new_support_views = {row[0] for row in support_rows}
        new_public_views = {row[0] for row in public_rows}

    # The views are now part of the support schema
    assert moved_view_names.issubset(new_support_views)
    # The views are no longer part of the public schema
    assert moved_view_names.intersection(new_public_views) == set()


def test_migrate_20231108_b9d014919f81_skip(alembic_engine, alembic_runner):
    revision = 'b9d014919f81'

    suppliers_restricted_columns = {
        'id',
        'external_id',
        'role',
        'type',
        'contract_type',
        'contract_start_date',
        'contract_end_date',
        'contract_work_areas',
        'materialized_path',
        'project_id',
        'parent_supplier_id',
        'parent_company_id',
        'company_id',
        'revision',
        'last_visited',
        'last_changed',
        'created_on',
        'first_visited',
        'visitor_type'
    }

    alembic_runner.migrate_up_before(revision)

    # Check existing columns in suppliers_restricted
    existing_suppliers_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'suppliers_restricted')
    )
    assert suppliers_restricted_columns == existing_suppliers_restricted_columns

    # Do the migration
    alembic_runner.migrate_up_to(revision)

    # Check all columns in new suppliers_restricted
    new_suppliers_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'suppliers_restricted')
    )

    suppliers_restricted_columns_v2 = suppliers_restricted_columns | {'is_one_man_company',
                                                                      'has_collective_agreement',
                                                                      'collective_agreement_name'}

    assert suppliers_restricted_columns_v2 == new_suppliers_restricted_columns


def test_migrate_6019e7f0fc86(alembic_engine, alembic_runner):
    revision = '6019e7f0fc86'

    project_users_restricted_columns_v1 = {
        'id',
        'project_id',
        'role',
        'notify',
        'user_account_id',
        'represented_company_id',
        'last_changed',
        'created_on',
        'external_id',
    }

    alembic_runner.migrate_up_before(revision)

    # Check existing columns in project_users_restricted
    existing_projects_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'project_users_restricted')
    )

    assert project_users_restricted_columns_v1 == existing_projects_restricted_columns

    # Do the migration
    alembic_runner.migrate_up_to(revision)

    # Check all columns in new project_users_restricted
    new_project_users_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'project_users_restricted')
    )

    assert new_project_users_restricted_columns == project_users_restricted_columns_v1 | {
        'person_id'
    }


def test_migrate_c199d550774d(alembic_engine, alembic_runner):
    revision = 'c199d550774d'

    projects_restricted_columns_v4 = {
        'id',
        'external_id',
        'name',
        'tax_id',
        'client_company_id',
        'start_date',
        'end_date',
        'state',
        'last_changed',
        'created_on',
        'pa_form_enabled',
        'created_by_org_id',
        'client_contact_person_id',
        'client_contact_person_email',
        'added_client_confirmed',
        'added_client_can_view'
    }

    alembic_runner.migrate_up_before(revision)

    # Check existing columns in projects_restricted
    existing_projects_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'projects_restricted')
    )

    assert projects_restricted_columns_v4 == existing_projects_restricted_columns

    # Do the migration
    alembic_runner.migrate_up_to(revision)

    # Check all columns in new projects_restricted
    new_projects_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'projects_restricted')
    )

    projects_restricted_columns_v5 = projects_restricted_columns_v4 | {'project_creator_role'}

    assert projects_restricted_columns_v5 == new_projects_restricted_columns


def test_migrate_20231109_60fae8e92e4a_custo(alembic_engine, alembic_runner):
    revision = '60fae8e92e4a'

    projects_restricted_columns = {
        'id',
        'external_id',
        'name',
        'tax_id',
        'client_company_id',
        'start_date',
        'end_date',
        'state',
        'last_changed',
        'created_on',
        'pa_form_enabled',
    }

    alembic_runner.migrate_up_before(revision)

    # Check existing columns in projects_restricted
    existing_projects_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'projects_restricted')
    )

    assert projects_restricted_columns == existing_projects_restricted_columns

    # Do the migration
    alembic_runner.migrate_up_to(revision)

    # Check all columns in new projects_restricted
    new_projects_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'projects_restricted')
    )

    projects_restricted_columns_v2 = projects_restricted_columns | {'created_by_org_id',
                                                                    'client_contact_person_id',
                                                                    'client_contact_person_email'}

    assert projects_restricted_columns_v2 == new_projects_restricted_columns


def test_migrate_20231206_4c9b59204be6_value(alembic_engine, alembic_runner):
    revision = '4c9b59204be6'

    report_cache_restricted_columns = {
        "id",
        "external_id",
        "correlation_id",
        "expires_at",
        "interested_org_id",
        "key",
        "provider",
        "type",
        "last_changed",
        "created_on",
    }

    alembic_runner.migrate_up_before(revision)

    # Check existing columns in report_cache_restricted
    existing_report_cache_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'report_cache_restricted')
    )

    assert report_cache_restricted_columns == existing_report_cache_restricted_columns

    # Do the migration
    alembic_runner.migrate_up_to(revision)

    # Check all columns in new report_cache_restricted
    report_cache_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'report_cache_restricted')
    )

    report_cache_restricted_columns_v2 = report_cache_restricted_columns | {'value'}

    assert report_cache_restricted_columns_v2 == report_cache_restricted_columns


def test_migrate_20240118_e2af533b845d_added(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('e2af533b845d')
    alembic_runner.insert_into('projects', [
        dict(id=mkid(1)),
        dict(id=mkid(2)),
    ])
    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text("SELECT id, added_client_confirmed FROM projects")).fetchall()
        added_client_confirmed = {row[0].hex: row[1] for row in rows}

    assert added_client_confirmed == {
        mkid(1): None,
        mkid(2): None,
    }


def test_migrate_20240219_faeb0e0ea299_project_creator_role_column(
        alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('faeb0e0ea299')

    alembic_runner.insert_into('projects', [
        dict(id=mkid(1)),
    ])
    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text("SELECT id, project_creator_role FROM projects")).fetchall()
        project_creator_roles = [(row[0].hex, row[1]) for row in rows]

    assert project_creator_roles == [(mkid(1), None)]


def test_migrate_20240129_f42ba6ad3212_added(alembic_engine, alembic_runner):
    revision = 'f42ba6ad3212'

    projects_restricted_columns_v2 = {
        'id',
        'external_id',
        'name',
        'tax_id',
        'client_company_id',
        'start_date',
        'end_date',
        'state',
        'last_changed',
        'created_on',
        'pa_form_enabled',
        'created_by_org_id',
        'client_contact_person_id',
        'client_contact_person_email'
    }

    alembic_runner.migrate_up_before(revision)

    # Check existing columns in projects_restricted
    existing_projects_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'projects_restricted')
    )

    assert projects_restricted_columns_v2 == existing_projects_restricted_columns

    # Do the migration
    alembic_runner.migrate_up_to(revision)

    # Check all columns in new projects_restricted
    new_projects_restricted_columns = set(
        get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'projects_restricted')
    )

    projects_restricted_columns_v3 = projects_restricted_columns_v2 | {'added_client_confirmed'}

    assert projects_restricted_columns_v3 == new_projects_restricted_columns


def test_migrate_20240506_20e38d7c55df_added_client_can_view_column(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('20e38d7c55df')

    alembic_runner.insert_into('projects', [
        dict(id=mkid(1), project_creator_role='main_contractor'),
        dict(id=mkid(2), project_creator_role='client'),
    ])
    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text(
            "SELECT id, added_client_can_view FROM projects ORDER BY id")
        ).fetchall()
        projects = [(row[0].hex, row[1]) for row in rows]

    assert projects == [(mkid(1), True), (mkid(2), None)]


def test_migrate_20240806_69b72509c4c6_add_person_id_to_supplier_contacts_restricted(
    alembic_engine, alembic_runner
):
    alembic_runner.migrate_up_before('69b72509c4c6')

    columns = get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'supplier_contacts_restricted')
    assert set(columns) == {'id', 'supplier_id', 'created_on', 'last_changed'}

    alembic_runner.migrate_up_one()

    columns = get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'supplier_contacts_restricted')
    assert set(columns) == {
        'id',
        'supplier_id',
        'created_on',
        'last_changed',
        'person_id',
        'person_email',
    }

    alembic_runner.migrate_down_one()

    columns = get_table_columns(alembic_engine, SUPPORT_SCHEMA, 'supplier_contacts_restricted')
    assert set(columns) == {'id', 'supplier_id', 'created_on', 'last_changed'}


def test_migrate_20240819_8162f31258f7_added_client_can_view_migration(
    alembic_engine, alembic_runner
):
    alembic_runner.migrate_up_before('8162f31258f7')

    alembic_runner.insert_into('projects', [
        dict(id=mkid(1), project_creator_role='main_contractor', added_client_confirmed=None),
        dict(id=mkid(2), project_creator_role='main_contractor', added_client_confirmed=False),
        dict(id=mkid(3), project_creator_role='client', added_client_confirmed=True),
    ])
    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        rows = conn.execute(sa.text(
            "SELECT id, added_client_can_view FROM projects ORDER BY id")
        ).fetchall()
        projects = [(row[0].hex, row[1]) for row in rows]

    assert projects == [(mkid(1), None), (mkid(2), True), (mkid(3), None)]


def test_migrate_20250228_334afc5e6e4d_add_null_to_bulkimportjobs_companies_upgrade(
    alembic_engine, alembic_runner
):
    # Migrate up to the revision before our target
    alembic_runner.migrate_up_before('334afc5e6e4d')

    # Insert test data - a job with companies that have lists of both 8 and 9 elements
    job_id = mkid(9000)
    alembic_runner.insert_into('bulk_import_jobs', [
        dict(
            id=job_id,
            project_id=mkid(1000),
            interested_org_id=mkid(2000),
            companies=sa.text("""
                '[
                    ["38-4521384", null, "pending", null, null, null, "2022-08-30T11:33:10", null],
                    ["41-1985581", null, "pending", null, null, null, "2022-08-30T11:33:10", null,\
                              "12345678", "Storbolaget AB"],
                    ["55-1234567", null, "pending", null, null, null, "2022-08-30T11:33:10", null,\
                              null, null]
                ]'::jsonb
            """),
            status="pending",
        ),
    ])

    # Run our migration
    alembic_runner.migrate_up_one()

    # Check that the companies with 8 elements now have 9 elements (with null at the end)
    with alembic_engine.connect() as conn:
        result = conn.execute(
            sa.text("SELECT companies FROM bulk_import_jobs WHERE id = :id"),
            dict(id=job_id)
        ).fetchone()
        companies = result[0]

    # Verify the results
    assert len(companies) == 3
    assert len(companies[0]) == 10  # Was 8, should now be 10 with null at the end
    assert companies[0][8] is None
    assert companies[0][9] is None
    assert len(companies[1]) == 10  # Was already 10, should still be 10
    assert companies[1][8] == "12345678"
    assert companies[1][9] == "Storbolaget AB"
    assert len(companies[2]) == 10  # Was already 10, should still be 10
    assert companies[2][8] is None
    assert companies[2][9] is None


def test_migrate_20250228_334afc5e6e4d_add_null_to_bulkimportjobs_companies_downgrade(
    alembic_engine, alembic_runner
):
    alembic_runner.migrate_up_to('334afc5e6e4d')

    job_id = mkid(9000)
    alembic_runner.insert_into('bulk_import_jobs', [
        dict(
            id=job_id,
            project_id=mkid(1000),
            interested_org_id=mkid(2000),
            companies=sa.text("""
                '[
                    ["38-4521384", null, "pending", null, null, null, "2022-08-30T11:33:10", null,\
                              null, null],
                    ["41-1985581", null, "pending", null, null, null, "2022-08-30T11:33:10", null,\
                              "12345678", "Storbolaget AB"],
                    ["55-1234567", null, "pending", null, null, null, "2022-08-30T11:33:10", null]
                ]'::jsonb
            """),
            status="pending",
        ),
    ])
    alembic_runner.migrate_down_one()

    # Check the data after downgrade (expecting only null values to be removed)
    with alembic_engine.connect() as conn:
        result = conn.execute(
            sa.text("SELECT companies FROM bulk_import_jobs WHERE id = :id"),
            dict(id=job_id)
        ).fetchone()
        companies = result[0]

    # Verify the results
    assert len(companies) == 3
    # First element had null at 9th position, should now be length 8
    assert len(companies[0]) == 8
    # Second element had values at 9th and 10th positions, should still be length 10
    assert len(companies[1]) == 10
    assert companies[1][8] == "12345678"
    assert companies[1][9] == "Storbolaget AB"
    # Third element had 8 elements, should still be length 8
    assert len(companies[2]) == 8


def test_migrate_20250512_041e1818f2c4_project_supplier_comments(
    alembic_engine, alembic_runner
):
    """Test the migration that creates project supplier comments tables."""

    # Migrate up to the revision before our migration
    alembic_runner.migrate_up_before("041e1818f2c4")

    # Insert test data for projects and suppliers that our tables will reference
    project_id = mkid(1000)
    supplier_id = mkid(2000)

    alembic_runner.insert_into(
        "projects",
        [
            dict(id=project_id),
        ],
    )

    alembic_runner.insert_into(
        "suppliers",
        [
            dict(id=supplier_id, project_id=project_id),
        ],
    )

    # Run the migration
    alembic_runner.migrate_up_one()

    # Verify the tables were created with the correct columns
    with alembic_engine.connect() as conn:
        # Check project_supplier_comments table columns
        project_supplier_comments_columns = set(
            get_table_columns(
                alembic_engine, PUBLIC_SCHEMA, "project_supplier_comments"
            )
        )

        expected_project_supplier_comments_columns = {
            "id",
            "project_id",
            "supplier_id",
            "org_id",
            "comment",
            "created_by_org_id",
            "created_by_person_id",
            "created_timestamp",
            "is_deleted",
            "deleted_by_org_id",
            "deleted_by_person_id",
            "deleted_timestamp",
            "is_updated",
            "updated_by_org_id",
            "updated_by_person_id",
            "updated_timestamp",
            "modified_timestamp",
            "last_changed",
            "created_on",
        }

        assert (
            project_supplier_comments_columns
            == expected_project_supplier_comments_columns
        )

        # Check project_comment_viewers table columns
        project_comment_viewers_columns = set(
            get_table_columns(alembic_engine, PUBLIC_SCHEMA, "project_comment_viewers")
        )

        expected_project_comment_viewers_columns = {
            "id",
            "project_comment_id",
            "read_by_person_id",
            "last_changed",
            "created_on",
        }

        assert (
            project_comment_viewers_columns == expected_project_comment_viewers_columns
        )

        # Check support views were created
        query = "SELECT table_name FROM INFORMATION_SCHEMA.views"
        f" WHERE table_schema = '{SUPPORT_SCHEMA}'"
        support_views = conn.execute(sa.text(query)).fetchall()
        support_view_names = {row[0] for row in support_views}

        assert "project_supplier_comments_restricted" in support_view_names
        assert "project_comment_viewers_restricted" in support_view_names

        # Insert test data to verify foreign key constraints
        comment_id = mkid(3000)
        person_id = "0035-" + mkid(4000)

        # Insert a comment
        conn.execute(
            sa.text(
                """
            INSERT INTO project_supplier_comments (
                id, project_id, supplier_id, org_id, comment,
                created_by_org_id, created_by_person_id, created_timestamp
            ) VALUES (
                :id, :project_id, :supplier_id, :org_id, :comment,
                :created_by_org_id, :created_by_person_id, NOW()
            )
            """
            ),
            {
                "id": uuid.UUID(comment_id),
                "project_id": uuid.UUID(project_id),
                "supplier_id": uuid.UUID(supplier_id),
                "org_id": "f392-" + mkid(5000),
                "comment": "Test comment",
                "created_by_org_id": "f392-" + mkid(6000),
                "created_by_person_id": "0035-" + mkid(7000),
            },
        )

        # Insert a viewer record
        conn.execute(
            sa.text(
                """
            INSERT INTO project_comment_viewers (
                id, project_comment_id, read_by_person_id
            ) VALUES (
                :id, :project_comment_id, :read_by_person_id
            )
            """
            ),
            {
                "id": uuid.UUID(mkid(8000)),
                "project_comment_id": uuid.UUID(comment_id),
                "read_by_person_id": person_id,
            },
        )
        # This should succeed - different person viewing the same comment
        conn.execute(
            sa.text(
                """
            INSERT INTO project_comment_viewers (
                id, project_comment_id, read_by_person_id
            ) VALUES (
                :id, :project_comment_id, :read_by_person_id
            )
            """
            ),
            {
                "id": uuid.UUID(mkid(9000)),
                "project_comment_id": uuid.UUID(comment_id),
                "read_by_person_id": mkid(9001),  # Different person
            },
        )

        # Verify the unique constraint by checking if a duplicate insert fails
        try:
            conn.execute(
                sa.text(
                    """
                INSERT INTO project_comment_viewers (
                    id, project_comment_id, read_by_person_id
                ) VALUES (
                    :id, :project_comment_id, :read_by_person_id
                )
                """
                ),
                {
                    "id": uuid.UUID(mkid(9002)),
                    "project_comment_id": uuid.UUID(comment_id),
                    "read_by_person_id": person_id,  # Same person as before
                },
            )
            assert False, "Unique constraint violation should have raised an exception"
        except Exception as e:
            # Verify that the exception is due to unique constraint violation
            assert (
                "unique constraint" in str(e).lower()
                or "duplicate key" in str(e).lower()
            )


def test_20250615_f43d83e71f3b_unique_active_org_id_upgrade_data_migration(
    alembic_engine, alembic_runner
):
    alembic_runner.migrate_up_before('f43d83e71f3b')
    with alembic_engine.connect() as conn:
        # Clean up any test data and insert new data
        conn.execute('DELETE FROM creditsafe_account')
        conn.execute("""
            INSERT INTO creditsafe_account
            (org_id, state, username, password, created_on)
            VALUES
            ('test-org-1', 'active',    'user1', 'pass1', NOW()),
            ('test-org-1', 'active',    'user2', 'pass2', NOW() - INTERVAL '1 day'),
            ('test-org-2', 'pending',   'user3', 'pass3', NOW()),
            ('test-org-3', 'inactive',  'user4', 'pass4', NOW()),
            ('test-org-4', 'active',    'user5', 'pass5', NOW())
        """)
    alembic_runner.migrate_up_to('f43d83e71f3b')

    with alembic_engine.connect() as conn:
        # Check that the newer active account was not affected
        result = conn.execute("""
            SELECT state
            FROM creditsafe_account
            WHERE org_id = 'test-org-1' AND username = 'user1'
        """)
        assert result.scalar() == 'active'

        # Check that the older active account was made inactive
        result = conn.execute("""
            SELECT state
            FROM creditsafe_account
            WHERE org_id = 'test-org-1' AND username = 'user2'
        """)
        assert result.scalar() == 'inactive'

        # Check that the pending account was made inactive
        result = conn.execute("""
            SELECT state
            FROM creditsafe_account
            WHERE org_id = 'test-org-2' AND username = 'user3'
        """)
        assert result.scalar() == 'inactive'

        # Check that the inactive account was not affected
        result = conn.execute("""
            SELECT state
            FROM creditsafe_account
            WHERE org_id = 'test-org-3' AND username = 'user4'
        """)
        assert result.scalar() == 'inactive'

        # Check that the only active account on org4 was not affected
        result = conn.execute("""
            SELECT state
            FROM creditsafe_account
            WHERE org_id = 'test-org-4' AND username = 'user5'
        """)
        assert result.scalar() == 'active'


def test_20250615_f43d83e71f3b_unique_active_org_id_upgrade(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_before('f43d83e71f3b')
    with alembic_engine.connect() as conn:
        # Clean up any test data
        conn.execute('DELETE FROM creditsafe_account')

    alembic_runner.migrate_up_to('f43d83e71f3b')

    # Verify we can insert one active account per org
    with alembic_engine.connect() as conn:
        # First active account for org1 - should succeed
        conn.execute("""
            INSERT INTO creditsafe_account
            (org_id, state, username, password)
            VALUES ('test-org-1', 'active', 'user1', 'pass1')
        """)

        # Second active account for org1 - should fail
        with pytest.raises(Exception) as e:
            conn.execute("""
                INSERT INTO creditsafe_account
                (org_id, state, username, password)
                VALUES ('test-org-1', 'active', 'user2', 'pass2')
            """)
        assert 'duplicate key value violates unique constraint' in str(e.value)

        # Inactive account for same org - should succeed
        conn.execute("""
            INSERT INTO creditsafe_account
            (org_id, state, username, password)
            VALUES ('test-org-1', 'inactive', 'user3', 'pass3')
        """)

        # Second inactive account for same org - should succeed
        conn.execute("""
            INSERT INTO creditsafe_account
            (org_id, state, username, password)
            VALUES ('test-org-1', 'inactive', 'user4', 'pass4')
        """)

        # Active account for different org - should succeed
        conn.execute("""
            INSERT INTO creditsafe_account
            (org_id, state, username, password)
            VALUES ('test-org-2', 'active', 'user5', 'pass5')
        """)


def test_20250615_f43d83e71f3b_unique_active_org_id_downgrade(alembic_engine, alembic_runner):
    alembic_runner.migrate_up_to('f43d83e71f3b')
    with alembic_engine.connect() as conn:
        conn.execute('DELETE FROM creditsafe_account')
    alembic_runner.migrate_down_one()
    with alembic_engine.connect() as conn:
        # First active account for org1 - should succeed
        conn.execute("""
            INSERT INTO creditsafe_account
            (org_id, state, username, password)
            VALUES ('test-org-1', 'active', 'user1', 'pass1')
        """)
        # Second active account for org1 - should also succeed after downgrade
        conn.execute("""
            INSERT INTO creditsafe_account
            (org_id, state, username, password)
            VALUES ('test-org-1', 'active', 'user2', 'pass2')
        """)
        # Verify both accounts were inserted
        query = """
            SELECT COUNT(*) FROM creditsafe_account
            WHERE org_id = 'test-org-1' AND state = 'active'
        """
        result = conn.execute(query).scalar()
        assert result == 2, 'Should have two active accounts for the same org after downgrade'
