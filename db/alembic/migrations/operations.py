from alembic.operations import Migrate<PERSON>peration, Operations


class ReversibleOp(MigrateOperation):
    def __init__(self, target):
        self.target = target

    @classmethod
    def invoke_for_target(cls, operations, target):
        op = cls(target)
        return operations.invoke(op)

    def reverse(self):
        raise NotImplementedError()

    @classmethod
    def _get_object_from_version(cls, operations, ident):
        version, objname = ident.split(".")

        module = operations.get_context().script.get_revision(version).module
        obj = getattr(module, objname)
        return obj

    @classmethod
    def _resolve_object(cls, operations, object_or_ident):
        if isinstance(object_or_ident, str):
            return cls._get_object_from_version(operations, object_or_ident)
        else:
            return object_or_ident

    @classmethod
    def replace(cls, operations, target, replaces=None, replace_with=None):

        if replaces and replace_with:
            raise TypeError("replaces and replace_with are mutually exclusive")
        if replaces:
            old_obj = cls._resolve_object(operations, replaces)
            drop_old = cls(old_obj).reverse()
            create_new = cls(target)
        elif replace_with:
            old_obj = cls._resolve_object(operations, replace_with)
            drop_old = cls(target).reverse()
            create_new = cls(old_obj)
        else:
            raise TypeError("replaces or replace_with is required")

        operations.invoke(drop_old)
        operations.invoke(create_new)


# Operation for views
@Operations.register_operation("create_view", "invoke_for_target")
@Operations.register_operation("replace_view", "replace")
class CreateViewOp(ReversibleOp):
    def reverse(self):
        return DropViewOp(self.target)


@Operations.register_operation("drop_view", "invoke_for_target")
class DropViewOp(ReversibleOp):
    def reverse(self):
        return CreateViewOp(self.target)


# Operations for functionn
@Operations.register_operation("create_sp", "invoke_for_target")
@Operations.register_operation("replace_sp", "replace")
class CreateSPOp(ReversibleOp):
    def reverse(self):
        return DropSPOp(self.target)


@Operations.register_operation("drop_sp", "invoke_for_target")
class DropSPOp(ReversibleOp):
    def reverse(self):
        return CreateSPOp(self.target)


# Operations for triggers
@Operations.register_operation("create_trg", "invoke_for_target")
@Operations.register_operation("replace_trg", "replace")
class CreateTRGOp(ReversibleOp):
    def reverse(self):
        return DropTRGOp(self.target)


@Operations.register_operation("drop_trg", "invoke_for_target")
class DropTRGOp(ReversibleOp):
    def reverse(self):
        return CreateTRGOp(self.target)


# Operations for triggers
@Operations.register_operation("create_schema", "invoke_for_target")
class CreateSchemaOp(ReversibleOp):
    def reverse(self):
        return DropSchemaOp(self.target)


@Operations.register_operation("drop_schema", "invoke_for_target")
class DropSchemaOp(ReversibleOp):
    def reverse(self):
        return CreateSchemaOp(self.target)


# IMPLEMENTATIONS
@Operations.implementation_for(CreateViewOp)
def create_view(operations, operation):
    operations.execute("CREATE OR REPLACE VIEW %s %s" % (
        operation.target.name,
        operation.target.sqltext
    ))


@Operations.implementation_for(DropViewOp)
def drop_view(operations, operation):
    operations.execute("DROP VIEW %s" % operation.target.name)


@Operations.implementation_for(CreateSPOp)
def create_sp(operations, operation):
    operations.execute(
        "CREATE OR REPLACE FUNCTION %s %s" % (
            operation.target.name, operation.target.sqltext
        )
    )


@Operations.implementation_for(DropSPOp)
def drop_sp(operations, operation):
    operations.execute("DROP FUNCTION %s" % operation.target.name)


@Operations.implementation_for(CreateTRGOp)
def create_trg(operations, operation):
    operations.execute(
        "CREATE TRIGGER %s %s" % (
            operation.target.name, operation.target.sqltext
        )
    )


@Operations.implementation_for(DropTRGOp)
def drop_trg(operations, operation):
    operations.execute("DROP TRIGGER %s ON %s" % (
        operation.target.name,
        operation.target.table,
    ))


@Operations.implementation_for(CreateSchemaOp)
def create_schema(operations, operation):
    operations.execute(
        "CREATE SCHEMA IF NOT EXISTS %s" % (
            operation.target.name
        )
    )


@Operations.implementation_for(DropSchemaOp)
def drop_schema(operations, operation):
    # Deliberately skipping CASCADE to make sure user knows what they are doing
    operations.execute("DROP SCHEMA IF EXISTS %s" % (
        operation.target.name
    ))
