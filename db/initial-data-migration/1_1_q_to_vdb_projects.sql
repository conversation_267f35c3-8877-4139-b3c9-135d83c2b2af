DROP TABLE IF EXISTS bol.projects CASCADE;

SELECT
  uuid_generate_v4() as id,
  project.id as external_id,
  string_agg(DISTINCT project_names.names, '; ') as name,  -- concatenate project name, don't loose
  project_ids.project_id AS tax_id,
  project.project_responsible_org AS client_company_id,
  start_date AS start_date,
  end_date AS end_date,
  state AS state
INTO bol.projects
FROM project
LEFT OUTER JOIN project_names ON project.id = project_names.id
LEFT OUTER JOIN (
  SELECT *
  FROM project_project_ids
  WHERE
    project_project_ids.project_id_type = 'tax_id'
  ) project_ids
  ON project.id = project_ids.id
WHERE
    project.country IS NULL  -- Ignore STV-imported projects
GROUP BY
  external_id,
  tax_id,
  client_company_id,
  start_date,
  end_date,
  state
;


DROP TABLE IF EXISTS bol.internal_project_ids CASCADE;

SELECT
  uuid_generate_v4() as id,
  unified_ids.internal_project_id,
  unified_ids.project_id,
  unified_ids.company_id
INTO bol.internal_project_ids
FROM
  (
    SELECT
      project_project_ids.project_id AS internal_project_id,
      bol.projects.id AS project_id,
      project.project_responsible_org AS company_id
    FROM project
    JOIN project_project_ids ON project_project_ids.id = project.id
    JOIN bol.projects ON bol.projects.external_id = project.id
    WHERE
      project_project_ids.project_id_type = 'trafikverket_project_id'
    UNION
    SELECT
      bol_supplier.internal_project_id AS internal_project_id,
      bol.projects.id AS project_id,
      bol_supplier.supplier_org_id AS company_id
    FROM project
    JOIN bol_supplier ON project.id = bol_supplier.id
    JOIN bol.projects ON bol_supplier.project_resource_id = bol.projects.external_id
  ) unified_ids
;
