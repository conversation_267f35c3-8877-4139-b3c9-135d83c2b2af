DROP TABLE IF EXISTS bol.report_accesses;

SELECT
  uuid_generate_v4() as id,
  report_access.id AS external_id,
  report_access.access_time AS access_time,
  report_access.arkisto_id AS arkisto_id,
  report_access.report_id AS report_id,
  report_access.status AS status,
  report_access.customer_org_id AS customer_company_id,
  report_access.org_id AS company_id,
  report_access.person_id AS person_id,
  report_access_gov_org_ids.gov_org_id AS company_gov_id,
  report_access_gov_org_ids.country AS company_gov_id_country,
  report_access_gov_org_ids.org_id_type AS company_gov_id_type
INTO bol.report_accesses
FROM
  (
    report_access
    LEFT OUTER JOIN report_access_gov_org_ids
    ON report_access.id = report_access_gov_org_ids.id
  )
WHERE
  (
    report_access_gov_org_ids.list_pos = 0  -- unpacking a list that always contains a single value
    OR
    report_access_gov_org_ids.list_pos is NULL
  )
;
