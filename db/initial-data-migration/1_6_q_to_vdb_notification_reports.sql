-- Temporary castrated version of the original transformation until DBAs have
-- time to investigate why it hangs on ALPHA while it passes withing 10s on
-- ALPHA dump.

DROP TABLE IF EXISTS bol.notification_reports;

SELECT
  uuid_generate_v4() as id,
  id AS external_id,
  report AS qvarn_report,  -- the original Qvarn report in JSON format
  generated_timestamp AS generated_timestamp,
  -- report_json->>'kind' AS period,
  CAST(NULL AS text) AS period,
  -- report_json->>'new_status_cutoff_timestamp' AS to_timestamp,
  CAST(NULL AS text) AS to_timestamp,
  -- report_json->>'old_status_cutoff_timestamp' AS from_timestamp,
  CAST(NULL AS text) AS from_timestamp,
  -- ARRAY(SELECT json_array_elements_text(report_json->'statuses')) AS statuses,
  CAST(NULL AS text) AS statuses,
  -- coalesce(
  --   case
  --     when (json_typeof(report_json->'org_ids') = 'null') then NULL  -- convert json null to SQL NULL
  --     else (report_json->'org_ids')
  --   end
  -- ) AS company_ids,
  CAST(NULL AS text) AS company_ids,
  -- users_to_notify.value->>'user_org_id' AS user_company_id,
  CAST(NULL AS text) AS user_company_id,
  -- users_to_notify.value->'user_info'->>'email' AS user_email,
  CAST(NULL AS text) AS user_email,
  -- users_to_notify.value->'user_info'->>'name' AS user_name,
  CAST(NULL AS text) AS user_name,
  -- users_to_notify.value->'user_info'->>'locale' AS user_locale,
  CAST(NULL AS text) AS user_locale,
  -- json_build_object('projects', users_to_notify.value->'projects') AS user_report
  CAST(NULL AS text) AS user_report
INTO bol.notification_reports
FROM
    (
      SELECT
        report_meta.id,
        report_meta.generated_timestamp,
        report_meta.report,
        report_meta.report_json,
        report_meta.report_json->'users_to_notify' AS users_to_notify
      FROM
        (
          SELECT
            report.id AS id,
            report.generated_timestamp AS generated_timestamp,
            convert_from(report__path_pdf.body, 'utf-8') AS report,
            convert_from(report__path_pdf.body, 'utf-8')::json AS report_json
          FROM report, report__path_pdf
          WHERE report.id=report__path_pdf.id AND report.report_type='bolagsfakta.status_change_report'
        ) report_meta
    ) users_report_meta
;
