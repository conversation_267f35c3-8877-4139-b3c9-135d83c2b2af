DROP TABLE IF EXISTS bol.status_reports;

SELECT
  report.id as external_id,
  report.org AS company_id,
  report.generated_timestamp AS generated_timestamp,
  report.tilaajavastuu_status AS status,
  report.interested_org_id AS interested_company_id,
  convert_from(report__path_pdf.body, 'utf-8') AS json_
INTO bol.status_reports
FROM report, report__path_pdf
WHERE report.id=report__path_pdf.id AND report.report_type='bolagsfakta.company_report';
