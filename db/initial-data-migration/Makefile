
# select default environment
ENV ?= alpha
ROUTE_TO_CONFIGS = server_configs/$(ENV)

DB_PATH = ..
DB_INIT_SCRIPT_DEV = $(DB_PATH)/0_bda_init_schema.sql
DB_INIT_SCRIPT_LIVE = $(TRANSFORM_PATH)/0_q_to_vdb_vdb_init_schema.sql
DB_TABLES_SCRIPT = $(DB_PATH)/1_bda_init_tables.sql
DB_INIT_FK_SCRIPT = $(DB_PATH)/2_bda_init_foreign_keys.sql

# Include DB connection config from a file
include  $(ROUTE_TO_CONFIGS)/db_connection_parameters.cfg

# VaultDB

# VaultDB access: SSL authentication
VAULTDB_SSLROOTCERT_FILE = $(ROUTE_TO_CONFIGS)/root.crt
VAULTDB_SSLKEY_FILE = $(ROUTE_TO_CONFIGS)/super.key
VAULTDB_SSLCERT_FILE = $(ROUTE_TO_CONFIGS)/super.crt

define VAULTDB_CREDENTIALS_BASIC
 host=$(VAULTDB_HOST) \
 port=$(VAULTDB_PORT) \
 user=$(VAULTDB_USER) \
 dbname=$(VAULTDB_DB_NAME)
endef

define VAULTDB_CREDENTIALS_SSL
 host=$(VAULTDB_HOST) \
 port=$(VAULTDB_PORT) \
 user=$(VAULTDB_USER) \
 sslmode=verify-ca \
 sslrootcert=$(VAULTDB_SSLROOTCERT_FILE) \
 sslkey=$(VAULTDB_SSLKEY_FILE) \
 sslcert=$(VAULTDB_SSLCERT_FILE) \
 dbname=$(VAULTDB_DB_NAME)
endef

# QvarnDB

define QVARNDB_CREDENTIALS
 --host=$(QVARNDB_HOST)\
 --port=$(QVARNDB_PORT)\
 --username=$(QVARNDB_USER)\
 --dbname=$(QVARNDB_DB_NAME)
endef

# VaultDB: transformation

TRANSFORM_PATH = .

define QVARN_INIT_SCHEMA_SCRIPT
	--file=$(TRANSFORM_PATH)/0_q_to_vdb_qdb_init_schema.sql
endef

define TRANSFORM_SCRIPTS
  --file=$(TRANSFORM_PATH)/1_1_q_to_vdb_projects.sql\
  --file=$(TRANSFORM_PATH)/1_2_q_to_vdb_suppliers.sql\
  --file=$(TRANSFORM_PATH)/1_3_q_to_vdb_supplier_contacts.sql\
  --file=$(TRANSFORM_PATH)/1_4_q_to_vdb_report_accesses.sql\
	--file=$(TRANSFORM_PATH)/1_5_q_to_vdb_status_reports.sql\
  --file=$(TRANSFORM_PATH)/1_6_q_to_vdb_notification_reports.sql
endef


# VaultDB: data pre-check

PRE_CHECK_PATH = .
define PRE_CHECK_SCRIPTS
  --file=$(PRE_CHECK_PATH)/1_q_to_vdb_check.sql
endef


# VaultDB: validation

VALIDATE_PATH = .
define VALIDATE_SCRIPTS
  --file=$(VALIDATE_PATH)/2_1_q_to_vdb_projects_validate.sql\
  --file=$(VALIDATE_PATH)/2_2_q_to_vdb_suppliers_validate.sql\
  --file=$(VALIDATE_PATH)/2_3_q_to_vdb_supplier_contacts_validate.sql\
  --file=$(VALIDATE_PATH)/2_4_q_to_vdb_report_accesses_validate.sql\
	--file=$(VALIDATE_PATH)/2_5_q_to_vdb_status_reports_validate.sql\
  --file=$(VALIDATE_PATH)/2_6_q_to_vdb_notification_reports_validate.sql
endef

# VaultDB: dump

SOURCE_DB_SCHEMA=bol.
TARGET_DB_SCHEMA=public.
DUMP_PATH = tmp_dumps
DUMP_FILE = $(DUMP_PATH)/q_to_vdb_tables_dump.sql
define WHAT_TO_DUMP
 --table=bol.projects\
 --table=bol.internal_project_ids\
 --table=bol.report_accesses\
 --table=bol.suppliers\
 --table=bol.supplier_contacts\
 --table=bol.status_reports\
 --table=bol.notification_reports
endef


.PHONY: help
help:
	@echo "export ENV=<env>             # <env> is one of: alpha, beta, prod"
	@echo "make <env>-init-schema       # DROP ALPHA data and schema and re-create schema"
	@echo "make <env>-migrate-pre-check  # pre-checke migrated data on Qvarn server"
	@echo "make <env>-migrate-transform # transform data into new schema on Qvarn server"
	@echo "make <env>-migrate-validate  # validate migrated data on Qvarn server"
	@echo "make <env>-migrate-dump      # dump migrated data from Qvarn to localhost"
	@echo "make <env>-migrate-upload    # upload dumped data from locahost to VaultDB"
	@echo "make <env>-init-fk           # Create foreign keys in VaultDB"
	@echo "make clean                   # remove local data"

.PHONY: directories
directories:
	mkdir -p logs
	mkdir -p $(DUMP_PATH)
	mkdir -p $(ROUTE_TO_CONFIGS)

.PHONY: clean
clean: directories
	rm -rf $(DUMP_PATH)
	rm -rf $(ROUTE_TO_CONFIGS)/alpha/*.crt
	rm -rf $(ROUTE_TO_CONFIGS)/alpha/*.key
	rm -rf $(ROUTE_TO_CONFIGS)/beta/*.crt
	rm -rf $(ROUTE_TO_CONFIGS)/beta/*.key
	rm -rf $(ROUTE_TO_CONFIGS)/prod/*.crt
	rm -rf $(ROUTE_TO_CONFIGS)/prod/*.key

.PHONY: replace-target-schema
replace-target-schema:
	@echo "Replacing schema from $(SOURCE_DB_SCHEMA) to $(TARGET_DB_SCHEMA) in the dump $(DUMP_FILE)"
	sed -i 's/COPY $(SOURCE_DB_SCHEMA)/COPY $(TARGET_DB_SCHEMA)/g' $(DUMP_FILE)
	@echo "Replacing schema in the dump done"

.PHONY: is-alpha-env
is-alpha-env:
ifneq ($(ENV),alpha)
	@echo "Please set ENV to alpha first like: export ENV=alpha"
	@exit 1
endif

.PHONY: is-beta-env
is-beta-env:
ifneq ($(ENV),beta)
	@echo "Please set ENV to beta first like: export ENV=beta"
	@exit 1
endif

.PHONY: is-prod-env
is-prod-env:
ifneq ($(ENV),prod)
	@echo "Please set ENV to prod first like: export ENV=prod"
	@exit 1
endif

.PHONY: is-dev-env
is-dev-env:
ifneq ($(ENV),dev)
	@echo "Please set ENV to dev first like: export ENV=dev"
	@exit 1
endif

# Targets common to Alpha/Beta/Pord/and some Dev

.PHONY: psql-vaultdb alpha-psql-vaultdb beta-psql-vaultdb prod-psql-vaultdb
psql-vaultdb:
	@echo "Connecting to VaultDB..."
	psql "$(VAULTDB_CREDENTIALS_SSL)"
alpha-psql-vaultdb: is-alpha-env psql-vaultdb
beta-psql-vaultdb: is-beta-env psql-vaultdb
prod-psql-vaultdb: is-prod-env psql-vaultdb

.PHONY: psql-qvarndb alpha-psql-qvarndb beta-psql-qvarndb prod-psql-qvarndb
psql-qvarndb:
	@echo "Connecting to QvarnDB..."
	@export PGPASSWORD="$(QVARNDB_PASSWORD)";\
	psql $(QVARNDB_CREDENTIALS)
alpha-psql-qvarndb: is-alpha-env psql-qvarndb
beta-psql-qvarndb: is-beta-env psql-qvarndb
prod-psql-qvarndb: is-prod-env psql-qvarndb

.PHONY: ssh-init alpha-ssh-init beta-ssh-init
ssh-init:
	@echo "Creating SSH tunnel to to QvarnDB..."
	ssh -N -f -L $(QVARNDB_PORT):$(QVARNDB_REMOTE_HOST):$(QVARNDB_REMOTE_PORT) $(USER)@$(QVARNDB_JUMP_HOST)
	@echo "Creating SSH tunnel to to VaultDB..."
	ssh -N -f -L $(VAULTDB_PORT):$(VAULTDB_REMOTE_HOST):$(VAULTDB_REMOTE_PORT) $(USER)@$(VAULTDB_JUMP_HOST)
alpha-ssh-init: is-alpha-env ssh-init
beta-ssh-init: is-beta-env ssh-init

.PHONY: init-schema alpha-init-schema beta-init-schema prod-init-schema
init-schema: directories
	@echo "VaultDB schema initialization $(DB_INIT_SCRIPT_LIVE)..."
	psql "$(VAULTDB_CREDENTIALS_SSL)" --file=$(DB_INIT_SCRIPT_LIVE)
	@echo "VaultDB tables initialization $(DB_TABLES_SCRIPT)..."
	psql "$(VAULTDB_CREDENTIALS_SSL)" --file=$(DB_TABLES_SCRIPT)
	@echo "VaultDB schema initialization finished"
alpha-init-schema: is-alpha-env init-schema
beta-init-schema: is-beta-env init-schema
prod-init-schema: is-prod-env init-schema

.PHONY: init-fk alpha-init-fk beta-init-fk prod-init-fk
init-fk: directories
	@echo "VaultDB schema initialization $(DB_INIT_FK_SCRIPT)..."
	psql "$(VAULTDB_CREDENTIALS_SSL)" --file=$(DB_INIT_FK_SCRIPT)
	@echo "VaultDB schema initialization finished"
alpha-init-fk: is-alpha-env init-fk
beta-init-fk: is-beta-env init-fk
prod-init-fk: is-prod-env init-fk

.PHONY: dev-init-fk
dev-init-fk: is-dev-env
	@echo "VaultDB schema initialization $(DB_INIT_FK_SCRIPT)..."
	psql "$(VAULTDB_CREDENTIALS_BASIC)" --file=$(DB_INIT_FK_SCRIPT)
	@echo "VaultDB schema initialization finished"

.PHONY: migrate-transform alpha-migrate-transform beta-migrate-transform prod-migrate-transform
migrate-transform: directories
	@echo "Transforming data..."
	@export PGPASSWORD="$(QVARNDB_PASSWORD)";\
	psql $(QVARNDB_CREDENTIALS) $(QVARN_INIT_SCHEMA_SCRIPT)
	@export PGPASSWORD="$(QVARNDB_PASSWORD)";\
	psql $(QVARNDB_CREDENTIALS) $(TRANSFORM_SCRIPTS)
	@echo "Transforming data finished"
alpha-migrate-transform: is-alpha-env migrate-transform
beta-migrate-transform: is-beta-env migrate-transform
prod-migrate-transform: is-prod-env migrate-transform
dev-migrate-transform: is-dev-env migrate-transform

.PHONY: migrate-validate alpha-migrate-validate beta-migrate-validate prod-migrate-validate
migrate-validate: directories
	@echo "Validating data..."
	@export PGPASSWORD="$(QVARNDB_PASSWORD)";\
	psql $(QVARNDB_CREDENTIALS) $(VALIDATE_SCRIPTS)
	@echo "Validating data finished"
alpha-migrate-validate: is-alpha-env migrate-validate
beta-migrate-validate: is-beta-env migrate-validate
prod-migrate-validate: is-prod-env migrate-validate
dev-migrate-validate: is-dev-env migrate-validate

.PHONY: migrate-pre-check alpha-migrate-pre-check beta-migrate-pre-check prod-migrate-pre-check
migrate-pre-check: directories
	@echo "Validating data..."
	@export PGPASSWORD="$(QVARNDB_PASSWORD)";\
	psql $(QVARNDB_CREDENTIALS) $(PRE_CHECK_SCRIPTS)
	@echo "Validating data finished"
alpha-migrate-pre-check: is-alpha-env migrate-pre-check
beta-migrate-pre-check: is-beta-env migrate-pre-check
prod-migrate-pre-check: is-prod-env migrate-pre-check
dev-migrate-pre-check: is-dev-env migrate-pre-check

.PHONY: migrate-dump alpha-migrate-dump beta-migrate-dump prod-migrate-dump
migrate-dump: directories
	@echo "Dumping data to $(DUMP_FILE)..."
	@export PGPASSWORD="$(QVARNDB_PASSWORD)";\
	pg_dump $(QVARNDB_CREDENTIALS) --format=plain --data-only --verbose --file=$(DUMP_FILE) $(WHAT_TO_DUMP)
	@echo "Dumping data finished to file $(DUMP_FILE)"
	$(MAKE) replace-target-schema
alpha-migrate-dump: is-alpha-env migrate-dump
beta-migrate-dump: is-beta-env migrate-dump
prod-migrate-dump: is-prod-env migrate-dump
dev-migrate-dump: is-dev-env migrate-dump

.PHONY: migrate-upload alpha-migrate-upload beta-migrate-upload prod-migrate-upload
migrate-upload: directories
	@echo "Uploading data from file $(DUMP_FILE)..."
	psql "$(VAULTDB_CREDENTIALS_SSL)" --file=$(DUMP_FILE)
	@echo "Uploading data finished."
alpha-migrate-upload: is-alpha-env migrate-upload
beta-migrate-upload: is-beta-env migrate-upload
prod-migrate-upload: is-prod-env migrate-upload

# Targets specific to dev

.PHONY: dev-init-schema
dev-init-schema: is-dev-env
	@echo "VaultDB schema initialization $(DB_INIT_SCRIPT_DEV)..."
	@export PGPASSWORD="$(VAULTDB_PASSWORD)";\
	psql "$(VAULTDB_CREDENTIALS_BASIC)" --file=$(DB_INIT_SCRIPT_DEV)
	@echo "VaultDB tables initialization $(DB_TABLES_SCRIPT)..."
	@export PGPASSWORD="$(VAULTDB_PASSWORD)";\
	psql "$(VAULTDB_CREDENTIALS_BASIC)" --file=$(DB_TABLES_SCRIPT)
	@echo "VaultDB schema initialization finished"

.PHONY: dev-migrate-upload
dev-migrate-upload: directories is-dev-env
	@echo "Uploading data from file $(DUMP_FILE)..."
	@export PGPASSWORD="$(VAULTDB_PASSWORD)";\
	psql "$(VAULTDB_CREDENTIALS_BASIC)" --file=$(DUMP_FILE)
	@echo "Uploading data finished."
