DROP TABLE IF EXISTS bol.validate_projects_errors;

-- XXX: Figure out how to persist inside the DO clause instead
CREATE TABLE bol.validate_projects_errors AS
  SELECT project.*
  FROM project
  LEFT OUTER JOIN bol.projects ON (project.id = bol.projects.external_id)
  WHERE
    project.country IS NULL AND -- Ignore STV-imported projects
    bol.projects.external_id IS NULL
;

DO
$do$
DECLARE
    errors_count INT DEFAULT 0;
    errors_array text[];
    error_text text;

    -- Count tests variables
    expected_projects_count INT DEFAULT 0;
    actual_projects_count INT;

    expected_internal_project_ids_count INT;
    actual_internal_project_ids_count INT;

    expected_tax_id_count INT;
    actual_tax_id_count INT;
BEGIN

    ----------------------------------------------------------------------------
    -- Do validation
    ----------------------------------------------------------------------------

    -- Get expected PROJECT numbers
    SELECT count(*) INTO expected_projects_count
    FROM project
    WHERE
        project.country IS NULL  -- Ignore STV-imported projects
    ;

    -- Validate migrated PROJECTS numbers
    SELECT count(*) INTO actual_projects_count FROM bol.projects;
    IF actual_projects_count <> expected_projects_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('PROJECTS count does not match: expected (%s), actual (%s)', expected_projects_count, actual_projects_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    -- Validate migrated PROJECT `tax_id` numbers
    SELECT count(*) INTO expected_tax_id_count
    FROM project_project_ids
    WHERE
        project_project_ids.project_id_type = 'tax_id'
    ;

    -- Validate migrated PROJECT `tax_id` numbers
    SELECT count(*) INTO actual_tax_id_count FROM bol.projects
    WHERE bol.projects.tax_id IS NOT NULL;
    IF actual_tax_id_count <> expected_tax_id_count THEN
        errors_count = errors_count + 1;
        SELECT FORMAT('PROJECT `tax_id` count does not match: expected (%s), actual (%s)', expected_tax_id_count, actual_tax_id_count) INTO error_text;
        errors_array := array_append(errors_array, error_text);
    END IF;

    -- Validate migrated INTERNAL_PROJECT_IDS uniqueness
    SELECT count(*) INTO expected_internal_project_ids_count
    FROM bol.internal_project_ids;
    SELECT COUNT(DISTINCT(project_id, company_id)) INTO actual_internal_project_ids_count
    FROM bol.internal_project_ids;

    IF actual_internal_project_ids_count <> expected_internal_project_ids_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('INTERNAL_PROJECT_IDS are not unique: expected (%s), actual (%s)', expected_internal_project_ids_count, actual_internal_project_ids_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    -- Validate migrated INTERNAL_PROJECT_IDS numbers
    SELECT count(*) INTO expected_internal_project_ids_count
    FROM project_project_ids
    WHERE
        project_project_ids.project_id_type = 'trafikverket_project_id'
    ;

    SELECT count(*) INTO actual_internal_project_ids_count FROM bol.internal_project_ids;
    IF actual_internal_project_ids_count <> expected_internal_project_ids_count THEN
        errors_count = errors_count + 1;
        SELECT FORMAT('INTERNAL_PROJECT_IDS count does not match: expected (%s), actual (%s)', expected_internal_project_ids_count, actual_internal_project_ids_count) INTO error_text;
        errors_array := array_append(errors_array, error_text);
    END IF;

    ----------------------------------------------------------------------------
    -- Raise exception if any validation error were found
    ----------------------------------------------------------------------------
    IF errors_count > 0 THEN

      RAISE NOTICE 'VALIDATION ERRORS:';
      FOREACH error_text IN ARRAY errors_array
      LOOP
        RAISE NOTICE '%', error_text;
      END LOOP;

    RAISE EXCEPTION '%', errors_array;

    END IF;

    -- Print the end of the script
    raise notice 'VALIDATION WAS SUCCESSFUL';
END
$do$;
