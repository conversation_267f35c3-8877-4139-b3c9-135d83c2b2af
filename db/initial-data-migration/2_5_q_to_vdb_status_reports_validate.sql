DROP TABLE IF EXISTS bol.validate_report_errors;

-- XXX: Figure out how to persist inside the DO clause instead
CREATE TABLE bol.validate_report_errors AS
SELECT report.*
FROM report
LEFT OUTER JOIN bol.status_reports ON (report.id = bol.status_reports.external_id)
LEFT OUTER JOIN report__path_pdf ON (report.id = report__path_pdf.id)
WHERE
  report.report_type='bolagsfakta.company_report' AND
  (
    bol.status_reports.external_id IS NULL OR
    report__path_pdf.id IS NULL
  )
;


DO
$do$
DECLARE
    errors_count INT DEFAULT 0;
    errors_array text[];
    error_text text;

    -- Count tests variables
    expected_status_reports_count INT DEFAULT 0;
    qvarn_status_reports_count INT DEFAULT 0;
    empty_status_reports_count INT DEFAULT 0;
    actual_status_reports_count INT;

BEGIN

    ----------------------------------------------------------------------------
    -- Do validation
    ----------------------------------------------------------------------------

    -- Get Qvarn STATUS_REPORT numbers
    SELECT count(*) INTO qvarn_status_reports_count
    FROM report
    WHERE report.report_type='bolagsfakta.company_report'
    ;

    -- Skip reports without PDFs as they are not useful
    SELECT count(*) INTO empty_status_reports_count
    FROM report
    LEFT OUTER JOIN report__path_pdf ON (report.id = report__path_pdf.id)
    WHERE
      report.report_type='bolagsfakta.company_report' AND
      report__path_pdf.id IS NULL
    ;

    expected_status_reports_count = qvarn_status_reports_count - empty_status_reports_count;

    -- Validate migrated STATUS_REPORTS numbers
    SELECT count(*) INTO actual_status_reports_count FROM bol.status_reports;
    IF actual_status_reports_count <> expected_status_reports_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('STATUS_REPORTS count does not match: expected (%s), actual (%s)', expected_status_reports_count, actual_status_reports_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;


    ----------------------------------------------------------------------------
    -- Raise exception if any validation error were found
    ----------------------------------------------------------------------------
    IF errors_count > 0 THEN

      RAISE NOTICE 'VALIDATION ERRORS:';
      FOREACH error_text IN ARRAY errors_array
      LOOP
        RAISE NOTICE '%', error_text;
      END LOOP;

    RAISE EXCEPTION '%', errors_array;

    END IF;

    -- Print the end of the script
    raise notice 'VALIDATION WAS SUCCESSFUL';
END
$do$;
