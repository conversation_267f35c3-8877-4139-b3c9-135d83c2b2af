DROP TABLE IF EXISTS bol.validate_suppliers_errors;

-- XXX: Figure out how to persist inside the DO clause instead
CREATE TABLE bol.validate_suppliers_errors AS
SELECT bol_supplier.*
FROM bol_supplier
LEFT OUTER JOIN bol.suppliers ON (bol_supplier.id = bol.suppliers.external_id)
LEFT OUTER JOIN project ON bol_supplier.project_resource_id = project.id
WHERE
  bol.suppliers.external_id IS NULL AND
  NOT project.id IS NULL
;

DO
$do$
DECLARE
    errors_count INT DEFAULT 0;
    errors_array text[];
    error_text text;

    -- Count tests variables
    qvarn_suppliers_count INT DEFAULT 0;
    no_project_suppliers_count INT DEFAULT 0;
    expected_suppliers_count INT DEFAULT 0;
    actual_suppliers_count INT;

BEGIN

    ----------------------------------------------------------------------------
    -- Do validation
    ----------------------------------------------------------------------------

    -- Get qvarn SUPPLIER numbers
    SELECT count(*)
    INTO qvarn_suppliers_count
    FROM bol_supplier
    ;

    -- Get SUPPLIER without projects numbers
    SELECT count(*)
    INTO no_project_suppliers_count
    FROM bol_supplier
    LEFT OUTER JOIN project ON bol_supplier.project_resource_id = project.id
    WHERE
      project.id IS NULL
    ;

    expected_suppliers_count = qvarn_suppliers_count - no_project_suppliers_count;

    -- Validate migrated SUPPLIERS numbers
    SELECT count(*)
    INTO actual_suppliers_count
    FROM bol.suppliers;

    IF actual_suppliers_count <> expected_suppliers_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('SUPPLIERS count does not match: expected (%s), actual (%s)', expected_suppliers_count, actual_suppliers_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    ----------------------------------------------------------------------------
    -- Raise exception if any validation error were found
    ----------------------------------------------------------------------------
    IF errors_count > 0 THEN

      RAISE NOTICE 'VALIDATION ERRORS:';
      FOREACH error_text IN ARRAY errors_array
      LOOP
        RAISE NOTICE '%', error_text;
      END LOOP;

    RAISE EXCEPTION '%', errors_array;

    END IF;

    -- Print the end of the script
    raise notice 'VALIDATION WAS SUCCESSFUL';
END
$do$;
