
# DESCRIPTION

## Purpose

Assist in migrating data from QvarnDB to current VaultDB schema.

#### 

Source of data is Qvarn PostgreSQL (QvarnDB). We assume the user can connect to the QvarnDB using `user`/`password` authentication.

Target is VaultIT PostgreSQL (VaultDB). We assume that the user can connect to VaultDB using SSL certificates (and an SSH tunnel if needed). 


## Process overview

Steps to execute:

1. Set up credentials for QvarnDB and VaultDB
2. Create SSH tunnel to both DBs
3. Check that SSH connections lead to correct databases
4. Initialize (thus wiping) BOL VaultDB
5. Pre-Check QvarnDB data within QvarnDB
6. Transform QvarnDB data into `bol.` schema within QvarnDB (crates `bol.`
   schema if not exists)
7. Validate the transformed schema withing QvarnDB
8. Dump the created `bol.` schema (data only) to local storage
9. Upload the dump to VaultDB
10. Create foreign keys
11. Remove all dumps and credentials
12. Send logs to BOL team

## Process with Copy/Paste examples

### 0. Make sure you work in the migration sub-folder of the project:

    cd db/initial-data-migration/

### 1. Set up credentials

Set up QvarnDB credentials in:

    ./server_configs/alpha/db_connection_parameters.cfg`

    # Similarly for Beta and Prod:
    ./server_configs/beta/db_connection_parameters.cfg`
    ./server_configs/prod/db_connection_parameters.cfg`

from the default values:

	QVARNDB_REMOTE_HOST=<qvarndb-floating-ip>
	QVARNDB_REMOTE_PORT=5432
	QVARNDB_JUMP_HOST=<ssh-jumphost-ip>
	QVARNDB_DB_NAME=qvarn
	QVARNDB_HOST=localhost
	QVARNDB_PORT=54327
	QVARNDB_USER=<YOUR USER>
	QVARNDB_PASSWORD=<YOUR PASSWORD>
    
Set up VaultDB credentials in:

    ./server_configs/alpha/db_connection_parameters.cfg`

    # Similarly for Beta and Prod:
    ./server_configs/beta/db_connection_parameters.cfg`
    ./server_configs/prod/db_connection_parameters.cfg`

from the default values:

	VAULTDB_REMOTE_HOST=<vaultdb-floating-ip>
	VAULTDB_REMOTE_PORT=5432
	VAULTDB_JUMP_HOST=<ssh-jumphost-ip>
	VAULTDB_DB_NAME=bol
	VAULTDB_HOST=localhost
	VAULTDB_PORT=54328
	VAULTDB_USER=<YOUR USER>

Add SSL credential files (the files are in .gitignore)

    super.key
    super.crt
    root.crt

for VaultDB database server to:

    `server_configs/alpha/`

    # Similarly for Beta and Prod:
    `server_configs/beta/`
    `server_configs/prod/`

You should get the IPs and credential files from your colleagues or DBAs.


### 2. Create SSH tunnel to both DBs

Create a tunnel to QvarnDB and VaultDB:

    make alpha-ssh-init
    #
    # this should print something like:
    #
    # Creating SSH tunnel to to QvarnDB...
    # ssh -N -f -L 54327:<qvarndb-floating-ip>:5432 <your_username>@<ssh-jumphost-ip>
    # Creating SSH tunnel to to VaultDB...
    # ssh -N -f -L 54328:<vaultdb-floating-ip>:5432 <your_username>@<ssh-jumphost-ip>

    # Similarly for Beta and Prod:
    make beta-ssh-init
    make prod-ssh-init


### 3. Check that SSH connections lead to correct databases

Connect to QvarnDB and VaultDB psql prompt:

    make alpha-psql-qvarndb
    # PSQL prompt should open
    SELECT current_database();
    # should return: qvarn

    make alpha-psql-vaultdb
    # PSQL prompt should open
    SELECT current_database();
    # should return: bol

    # Similarly for Beta:
    make beta-psql-qvarndb
    make beta-psql-vaultdb

    # Similarly for Prod:
    make prod-psql-qvarndb
    make prod-psql-vaultdb


### 4. Initialize (thus wiping) BOL VaultDB:

Run transform script on QvarnDB:

    make alpha-init-schema &> ./logs/alpha-init-schema.log

    # Similarly for Beta and Prod:
    make beta-init-schema &> ./logs/beta-init-schema.log
    make prod-init-schema &> ./logs/prod-init-schema.log

### 5. Pre-Check QvarnDB data within QvarnDB ####

Run check script:

    time make alpha-migrate-pre-check &> ./logs/alpha-migrate-check.log

    # Similarly for Beta and Prod:
    time make beta-migrate-pre-check &> ./logs/beta-migrate-check.log
    time make prod-migrate-pre-check &> ./logs/prod-migrate-check.log

### 6. Transform QvarnDB data into `bol.` schema within QvarnDB

This crates `bol.` schema if not exists. Run:

    time make alpha-migrate-transform &> ./logs/alpha-migrate-transform.log

    # Similarly for Beta and Prod:
    time make beta-migrate-transform &> ./logs/beta-migrate-transform.log
    time make prod-migrate-transform &> ./logs/prod-migrate-transform.log

### 7. Validate the transformed schema withing QvarnDB

Run validation scripts:

    time make alpha-migrate-validate &> ./logs/alpha-migrate-validate.log

    # Similarly for Beta and Prod:
    time make beta-migrate-validate &> ./logs/beta-migrate-validate.log
    time make prod-migrate-validate &> ./logs/prod-migrate-validate.log

### 8. Dump the created `bol.` schema to local storage

Dumps data-only. Run dump script:

    time make alpha-migrate-dump &> ./logs/alpha-migrate-dump.log

    # Similarly for Beta and Prod:
    time make beta-migrate-dump &> ./logs/beta-migrate-dump.log
    time make prod-migrate-dump &> ./logs/prod-migrate-dump.log

### 9. Upload the dump to VaultDB:

Run upload script to upload data to VaultDB:

    time make alpha-migrate-upload &> ./logs/alpha-migrate-upload.log

    # Similarly for Beta and Prod:
    time make beta-migrate-upload &> ./logs/beta-migrate-upload.log
    time make prod-migrate-upload &> ./logs/prod-migrate-upload.log

### 10. Create foreign keys in VaultDB

Run foreign key creation script in VaultDB:

    make alpha-init-fk &> ./logs/alpha-init-fk.log

    # Similarly for Beta and Prod:
    make beta-init-fk &> ./logs/beta-init-fk.log
    make prod-init-fk &> ./logs/prod-init-fk.log

### 11. Remove all dumps and credentials:

Run script to remove all dumps and credentials:

    make clean

### 12. Send logs to BOL team

Send folder `./logs` to BOL team for inspection
