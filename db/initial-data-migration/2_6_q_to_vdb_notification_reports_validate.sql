DROP TABLE IF EXISTS bol.validate_notification_report_errors;

-- XXX: Figure out how to persist inside the DO clause instead
CREATE TABLE bol.validate_notification_report_errors AS
SELECT report.*
FROM report
LEFT OUTER JOIN bol.notification_reports ON (report.id = bol.notification_reports.external_id)
LEFT OUTER JOIN report__path_pdf ON (report.id = report__path_pdf.id)
WHERE
  report.report_type='bolagsfakta.status_change_report' AND
  bol.notification_reports.external_id IS NULL AND
  NOT report__path_pdf.id IS NULL
;

DO
$do$
DECLARE
    errors_count INT DEFAULT 0;
    errors_array text[];
    error_text text;

    -- Count tests variables
    qvarn_notification_qvarn_reports_count INT DEFAULT 0;
    empty_notification_qvarn_reports_count INT DEFAULT 0;
    expected_notification_qvarn_reports_count INT DEFAULT 0;
    actual_notification_qvarn_reports_count INT;

    expected_notification_user_reports_count INT DEFAULT 0;
    actual_notification_user_reports_count INT;

BEGIN

    ----------------------------------------------------------------------------
    -- Do validation
    ----------------------------------------------------------------------------

    -- Get qvarn NOTIFICATION_REPORT qvarn_report numbers
    SELECT count(*) INTO qvarn_notification_qvarn_reports_count
    FROM report
    WHERE report.report_type='bolagsfakta.status_change_report'
    ;

    -- Skip reports without PDFs as they are not useful
    SELECT count(*) INTO empty_notification_qvarn_reports_count
    FROM report
    LEFT OUTER JOIN report__path_pdf ON (report.id = report__path_pdf.id)
    WHERE
      report.report_type='bolagsfakta.status_change_report' AND
      report__path_pdf.id IS NULL
    ;

    expected_notification_qvarn_reports_count = qvarn_notification_qvarn_reports_count - empty_notification_qvarn_reports_count;

    -- Validate migrated NOTIFICATION_REPORTS qvarn_report numbers
    SELECT count(DISTINCT external_id)
    INTO actual_notification_qvarn_reports_count
    FROM bol.notification_reports;

    IF actual_notification_qvarn_reports_count <> expected_notification_qvarn_reports_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT(
        'NOTIFICATION_REPORTS qvarn_report count does not match: expected (%s), actual (%s)',
        expected_notification_qvarn_reports_count,
        actual_notification_qvarn_reports_count
      )
      INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;


    -- Get expected NOTIFICATION_REPORT user_report numbers
    SELECT count(*) INTO expected_notification_user_reports_count
    FROM
        (
          SELECT
              convert_from(report__path_pdf.body, 'utf-8')::json AS report_json
          FROM report, report__path_pdf
          WHERE report.id=report__path_pdf.id AND report.report_type='bolagsfakta.status_change_report'
        ) report_meta
        -- Removed due to performance issues
        -- json_each(report_meta.report_json->'users_to_notify') AS user_reports
    ;

    -- Validate migrated NOTIFICATION_REPORTS user_report numbers

    SELECT count(*)
    INTO actual_notification_user_reports_count
    FROM bol.notification_reports;
    IF actual_notification_user_reports_count <> expected_notification_user_reports_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT(
        'NOTIFICATION_REPORTS user_report count does not match: expected (%s), actual (%s)',
        expected_notification_user_reports_count,
        actual_notification_user_reports_count
      )
      INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    ----------------------------------------------------------------------------
    -- Raise exception if any validation error were found
    ----------------------------------------------------------------------------
    IF errors_count > 0 THEN

      RAISE NOTICE 'VALIDATION ERRORS:';
      FOREACH error_text IN ARRAY errors_array
      LOOP
        RAISE NOTICE '%', error_text;
      END LOOP;

      RAISE EXCEPTION '%', errors_array;

    END IF;

    -- Print the end of the script
    raise notice 'VALIDATION WAS SUCCESSFUL';
END
$do$;
