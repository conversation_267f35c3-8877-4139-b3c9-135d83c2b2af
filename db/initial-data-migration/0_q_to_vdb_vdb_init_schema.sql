
\set db_schema 'public'
\set db_user 'bol_migration'


\set ON_ERROR_STOP on

DO
$$
BEGIN
  IF  current_database() <> 'bol'
    THEN RAISE 'Refusing to init DB, expected DB `bol` but got: %', current_database();
  END IF;
END
$$ LANGUAGE plpgsql;


----- EXTENSIONS

-- Extensions should be installed before we switch from superuser to a lesser
-- bol user.


CREATE EXTENSION IF NOT EXISTS citext;

-- This is to overcome error
--   ERROR:  function uuid_generate_v4() does not exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";


----- PERMISSIONS


-- We must set role to 'bol' for roles `bol-app` and `bol-migration` (set by
-- DB<PERSON> on the DB server manually) to work correctly.

SET ROLE 'bol';

CREATE SCHEMA IF NOT EXISTS :db_schema;

GRANT USAGE ON SCHEMA :db_schema TO public;

SET SCHEMA 'public';

ALTER DEFAULT PRIVILEGES GRANT SELECT ON TABLES TO bol_ro;
ALTER DEFAULT PRIVILEGES GRANT SELECT ON SEQUENCES TO bol_ro;
ALTER DEFAULT PRIVILEGES GRANT SELECT ON TABLES TO bol_rw;
ALTER DEFAULT PRIVILEGES GRANT INSERT ON TABLES TO bol_rw;
ALTER DEFAULT PRIVILEGES GRANT UPDATE ON TABLES TO bol_rw;
ALTER DEFAULT PRIVILEGES GRANT DELETE ON TABLES TO bol_rw;
ALTER DEFAULT PRIVILEGES GRANT TRUNCATE ON TABLES TO bol_rw;
ALTER DEFAULT PRIVILEGES GRANT SELECT ON SEQUENCES TO bol_rw;
ALTER DEFAULT PRIVILEGES GRANT USAGE ON SEQUENCES TO bol_rw;

\set ON_ERROR_STOP off

