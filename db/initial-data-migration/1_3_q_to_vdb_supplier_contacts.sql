DROP TABLE IF EXISTS bol.supplier_contacts CASCADE;

SELECT
  bol.suppliers.id AS supplier_id,
  supplier_contacts.supplier_contact_person_id AS person_id,
  supplier_contacts.supplier_contact_email AS person_email
INTO bol.supplier_contacts
FROM
  (
  bol_supplier
  JOIN
  bol_supplier_supplier_contacts AS supplier_contacts
  ON bol_supplier.id = supplier_contacts.id
  JOIN bol.suppliers ON bol.suppliers.external_id = bol_supplier.id
  LEFT OUTER JOIN project ON bol_supplier.project_resource_id = project.id
  )
WHERE
  project.id IS NOT NULL
;
