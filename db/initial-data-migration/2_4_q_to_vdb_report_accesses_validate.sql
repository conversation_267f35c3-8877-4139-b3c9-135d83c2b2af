DROP TABLE IF EXISTS bol.validate_report_accesses_errors;

-- XXX: Figure out how to persist inside the DO clause instead
CREATE TABLE bol.validate_report_accesses_errors AS
SELECT report_access.*
FROM report_access
LEFT OUTER JOIN bol.report_accesses ON (report_access.id = bol.report_accesses.external_id)
WHERE
  bol.report_accesses.external_id IS NULL
;

DO
$do$
DECLARE
    errors_count INT DEFAULT 0;
    errors_array text[];
    error_text text;

    -- Count tests variables
    expected_report_accesses_count INT DEFAULT 0;
    actual_report_accesses_count INT;

    expected_gov_org_ids_count INT DEFAULT 0;
    actual_gov_org_ids_count INT;

BEGIN

    ----------------------------------------------------------------------------
    -- Do validation
    ----------------------------------------------------------------------------

    -- Get expected REPORT_ACCESSES numbers
    SELECT count(*) INTO expected_report_accesses_count
    FROM report_access
    ;

    -- Validate migrated REPORT_ACCESSES numbers
    SELECT count(*) INTO actual_report_accesses_count FROM bol.report_accesses;
    IF actual_report_accesses_count <> expected_report_accesses_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('REPORT_ACCESSES count does not match: expected (%s), actual (%s)', expected_report_accesses_count, actual_report_accesses_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    -- Each report_access resource had an gov_org_ids attribute that was always
    -- either an empty list or a list with one item; new table has a nullable
    -- gov_org_id attribute instead.

    -- Get expected REPORT_ACCESSES gov_org_ids numbers
    SELECT count(*) INTO expected_gov_org_ids_count
    FROM report_access_gov_org_ids
    ;

    -- Validate migrated REPORT_ACCESSES gov_org_ids numbers
    SELECT count(*)
    INTO actual_gov_org_ids_count
    FROM bol.report_accesses
    WHERE company_gov_id IS NOT NULL;
    IF actual_gov_org_ids_count <> expected_gov_org_ids_count THEN
        errors_count = errors_count + 1;
        SELECT FORMAT(
        'REPORT_ACCESSES gov_org_ids count does not match: expected (%s), actual (%s)',
          expected_gov_org_ids_count,
          actual_gov_org_ids_count
        ) INTO error_text;
        errors_array := array_append(errors_array, error_text);
    END IF;


    ----------------------------------------------------------------------------
    -- Raise exception if any validation error were found
    ----------------------------------------------------------------------------
    IF errors_count > 0 THEN

      RAISE NOTICE 'VALIDATION ERRORS:';
      FOREACH error_text IN ARRAY errors_array
      LOOP
        RAISE NOTICE '%', error_text;
      END LOOP;

    RAISE EXCEPTION '%', errors_array;

    END IF;

    -- Print the end of the script
    raise notice 'VALIDATION WAS SUCCESSFUL';
END
$do$;
