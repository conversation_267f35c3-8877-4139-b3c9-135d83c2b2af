DROP TABLE IF EXISTS bol.check_supplier_duplicates;

CREATE TABLE bol.check_supplier_duplicates AS
  SELECT bol_supplier.id
  FROM bol_supplier
  GROUP BY bol_supplier.id
  HAVING COUNT(*) > 1
;


DROP TABLE IF EXISTS bol.check_project_duplicates;

CREATE TABLE bol.check_project_duplicates AS
  SELECT project.id
  FROM project
  GROUP BY project.id
  HAVING COUNT(*) > 1
;


DROP TABLE IF EXISTS bol.check_report_duplicates;

CREATE TABLE bol.check_report_duplicates AS
SELECT report.id
FROM report
GROUP BY report.id
HAVING COUNT(*) > 1
;


DROP TABLE IF EXISTS bol.check_report_access_duplicates;

CREATE TABLE bol.check_report_access_duplicates AS
SELECT report_access.id
FROM report_access
GROUP BY report_access.id
HAVING COUNT(*) > 1
;


DROP TABLE IF EXISTS bol.check_report_access_multiple_gov_org_ids;

CREATE TABLE bol.check_report_access_multiple_gov_org_ids AS
SELECT report_access.id AS report_access_id, report_access_gov_org_ids.id AS gov_org_id_id
FROM (
  report_access
  LEFT OUTER JOIN report_access_gov_org_ids
      ON report_access.id = report_access_gov_org_ids.id
)
WHERE report_access_gov_org_ids.list_pos = 1  -- more than 1 found
;


DO
$do$
DECLARE
    errors_count INT DEFAULT 0;
    errors_array text[];
    error_text text;

    -- Count tests variables
    duplicate_supplier_count INT DEFAULT 0;
    duplicate_project_count INT DEFAULT 0;
    duplicate_report_count INT DEFAULT 0;
    duplicate_report_access_count INT DEFAULT 0;
    report_access_count_multiple_gov_org_ids INT DEFAULT 0;
BEGIN

    ----------------------------------------------------------------------------
    -- Do validation
    ----------------------------------------------------------------------------

    -- Check SUPPLIER for duplicate IDs

    SELECT count(*)
    INTO duplicate_supplier_count
    FROM bol.check_supplier_duplicates
    ;

    -- Validate migrated SUPPLIER numbers

    IF duplicate_supplier_count > 0 THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('Duplicate ids found for SUPPLIER: %s', duplicate_supplier_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    -- Check PROJECT for duplicate IDs

    SELECT count(*)
    INTO duplicate_project_count
    FROM bol.check_project_duplicates
    ;

    -- Validate migrated PROJECT numbers

    IF duplicate_project_count > 0 THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('Duplicate ids found for PROJECT: %s', duplicate_project_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    -- Check REPORT for duplicate IDs

    SELECT count(*)
    INTO duplicate_report_count
    FROM bol.check_report_duplicates
    ;

    -- Validate migrated REPORT numbers

    IF duplicate_report_count > 0 THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('Duplicate ids found for REPORT: %s', duplicate_report_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    -- Check REPORT_ACCESS for duplicate IDs

    SELECT count(*)
    INTO duplicate_report_access_count
    FROM bol.check_report_access_duplicates
    ;

    -- Validate migrated REPORT_ACCESS numbers

    IF duplicate_report_access_count > 0 THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('Duplicate ids found for REPORT_ACCESS: %s', duplicate_report_access_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    -- Check for multiple REPORT_ACCESS gov_org_ids

    SELECT count(*)
    INTO report_access_count_multiple_gov_org_ids
    FROM bol.check_report_access_multiple_gov_org_ids
    ;

    IF report_access_count_multiple_gov_org_ids > 0 THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('Multiple ids found for REPORT_ACCESS: %s', report_access_count_multiple_gov_org_ids) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    ----------------------------------------------------------------------------
    -- Raise exception if any validation error were found
    ----------------------------------------------------------------------------
    IF errors_count > 0 THEN

      RAISE NOTICE 'DUPLICATE CHECK ERRORS:';
      FOREACH error_text IN ARRAY errors_array
      LOOP
        RAISE NOTICE '%', error_text;
      END LOOP;

    RAISE EXCEPTION '%', errors_array;

    END IF;

    -- Print the end of the script
    raise notice 'DUPLICATE CHECK SUCCESSFUL';
END
$do$;
