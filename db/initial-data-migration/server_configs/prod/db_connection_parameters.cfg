# Please update QvarnDB connection parameters and credentials.
# user/pass authentication assumed.
QVARNDB_REMOTE_HOST=<qvarndb-floating-ip>
QVARNDB_REMOTE_PORT=5432
QVARNDB_JUMP_HOST=<ssh-jumphost-ip>
QVARNDB_DB_NAME=qvarn
QVARNDB_HOST=localhost
QVARNDB_PORT=54327
QVARNDB_USER=
QVARNDB_PASSWORD=

# Please update VaultDB connection parameters and credentials.
# SSL authenticiation files should be added to this folder. Files to add: root.crt, super.crt, super.key
VAULTDB_REMOTE_HOST=<vaultdb-floating-ip>
VAULTDB_REMOTE_PORT=5432
VAULTDB_JUMP_HOST=<ssh-jumphost-ip>
VAULTDB_DB_NAME=bol
VAULTDB_HOST=localhost
VAULTDB_PORT=54328
VAULTDB_USER=super
