DROP TABLE IF EXISTS bol.suppliers CASCADE;

SELECT
  uuid_generate_v4() as id,
  bol_supplier.id AS external_id,
  bol_supplier.supplier_role AS role,
  bol_supplier.supplier_type AS type,
  bol_supplier.contract_start_date AS contract_start_date,
  bol_supplier.contract_end_date AS contract_end_date,
  ARRAY_AGG (
    bol_supplier_materialized_path.materialized_path
    ORDER BY bol_supplier_materialized_path.list_pos
  ) AS materialized_path,  -- list value
  bol.projects.id AS project_id,
  bol_supplier.parent_supplier_id AS parent_supplier_id,
  bol_supplier.parent_org_id AS parent_company_id,
  bol_supplier.supplier_org_id AS company_id,
  bol_supplier.revision AS revision
INTO bol.suppliers
FROM
  (
    bol_supplier
    LEFT OUTER JOIN
    bol_supplier_materialized_path
    ON
    bol_supplier.id = bol_supplier_materialized_path.id

    JOIN
    bol.projects
    ON
    bol_supplier.project_resource_id = bol.projects.external_id
  )
GROUP BY
  bol_supplier.id,
  bol_supplier.supplier_role,
  bol_supplier.supplier_type,
  bol_supplier.contract_start_date,
  bol_supplier.contract_end_date,
  bol_supplier.project_resource_id,
  bol_supplier.parent_supplier_id,
  bol_supplier.parent_org_id,
  bol_supplier.supplier_org_id,
  bol_supplier.revision,
  bol.projects.id
;
