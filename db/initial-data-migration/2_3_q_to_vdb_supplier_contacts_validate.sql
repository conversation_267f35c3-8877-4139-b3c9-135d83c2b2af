DO
$do$
DECLARE
    errors_count INT DEFAULT 0;
    errors_array text[];
    error_text text;

    -- Count tests variables
    qvarn_supplier_contacts_count INT DEFAULT 0;
    no_project_supplier_contacts_count INT DEFAULT 0;
    expected_supplier_contacts_count INT DEFAULT 0;
    actual_supplier_contacts_count INT;

BEGIN

    ----------------------------------------------------------------------------
    -- Do validation
    ----------------------------------------------------------------------------

    -- Get Qvarn SUPPLIER_CONTACT numbers
    SELECT count(*) INTO qvarn_supplier_contacts_count
    FROM bol_supplier_supplier_contacts
    ;

    -- Get SUPPLIER_CONTACT without projects numbers
    SELECT count(*)
    INTO no_project_supplier_contacts_count
    FROM bol_supplier_supplier_contacts
    JOIN bol_supplier ON bol_supplier_supplier_contacts.id = bol_supplier.id
    LEFT OUTER JOIN project ON bol_supplier.project_resource_id = project.id
    WHERE
      project.id IS NULL
    ;

    expected_supplier_contacts_count = qvarn_supplier_contacts_count - no_project_supplier_contacts_count;

    -- Validate migrated SUPPLIER_CONTACTS numbers
    SELECT count(*) INTO actual_supplier_contacts_count FROM bol.supplier_contacts;
    IF actual_supplier_contacts_count <> expected_supplier_contacts_count THEN
      errors_count = errors_count + 1;
      SELECT FORMAT('SUPPLIER_CONTACTS count does not match: expected (%s), actual (%s)', expected_supplier_contacts_count, actual_supplier_contacts_count) INTO error_text;
      errors_array := array_append(errors_array, error_text);
    END IF;

    ----------------------------------------------------------------------------
    -- Raise exception if any validation error were found
    ----------------------------------------------------------------------------
    IF errors_count > 0 THEN

      RAISE NOTICE 'VALIDATION ERRORS:';
      FOREACH error_text IN ARRAY errors_array
      LOOP
        RAISE NOTICE '%', error_text;
      END LOOP;

    RAISE EXCEPTION '%', errors_array;

    END IF;

    -- Print the end of the script
    raise notice 'VALIDATION WAS SUCCESSFUL';
END
$do$;
