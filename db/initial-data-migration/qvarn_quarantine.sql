-- Defunct

ALTER TABLE IF EXISTS bol_comp_list_map RENAME TO bol_comp_list_map_old;
ALTER TABLE IF EXISTS bol_comp_list_map__aux_listener RENAME TO bol_comp_list_map__aux_listener_old;
ALTER TABLE IF EXISTS bol_comp_list_map__aux_listener_listen_on RENAME TO bol_comp_list_map__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS bol_comp_list_map__aux_notification RENAME TO bol_comp_list_map__aux_notification_old;
ALTER TABLE IF EXISTS bol_comp_list_map__aux_versions RENAME TO bol_comp_list_map__aux_versions_old;
ALTER TABLE IF EXISTS bol_company_list RENAME TO bol_company_list_old;
ALTER TABLE IF EXISTS bol_company_list__aux_listener RENAME TO bol_company_list__aux_listener_old;
ALTER TABLE IF EXISTS bol_company_list__aux_listener_listen_on RENAME TO bol_company_list__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS bol_company_list__aux_notification RENAME TO bol_company_list__aux_notification_old;
ALTER TABLE IF EXISTS bol_company_list__aux_versions RENAME TO bol_company_list__aux_versions_old;
ALTER TABLE IF EXISTS bol_company_list_company_name_gov_org_id RENAME TO bol_company_list_company_name_gov_org_id_old;
ALTER TABLE IF EXISTS bol_company_list_roles RENAME TO bol_company_list_roles_old;
ALTER TABLE IF EXISTS bol_proj_list_map RENAME TO bol_proj_list_map_old;
ALTER TABLE IF EXISTS bol_proj_list_map__aux_listener RENAME TO bol_proj_list_map__aux_listener_old;
ALTER TABLE IF EXISTS bol_proj_list_map__aux_listener_listen_on RENAME TO bol_proj_list_map__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS bol_proj_list_map__aux_notification RENAME TO bol_proj_list_map__aux_notification_old;
ALTER TABLE IF EXISTS bol_proj_list_map__aux_versions RENAME TO bol_proj_list_map__aux_versions_old;
ALTER TABLE IF EXISTS bol_project_list RENAME TO bol_project_list_old;
ALTER TABLE IF EXISTS bol_project_list__aux_listener RENAME TO bol_project_list__aux_listener_old;
ALTER TABLE IF EXISTS bol_project_list__aux_listener_listen_on RENAME TO bol_project_list__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS bol_project_list__aux_notification RENAME TO bol_project_list__aux_notification_old;
ALTER TABLE IF EXISTS bol_project_list__aux_versions RENAME TO bol_project_list__aux_versions_old;
ALTER TABLE IF EXISTS bol_project_list_roles RENAME TO bol_project_list_roles_old;
ALTER TABLE IF EXISTS bol_project_list_roles_project_internal_id_name RENAME TO bol_project_list_roles_project_internal_id_name_old;
ALTER TABLE IF EXISTS bolagsfakta_supplier RENAME TO bolagsfakta_supplier_old;
ALTER TABLE IF EXISTS bolagsfakta_supplier__aux_listener RENAME TO bolagsfakta_supplier__aux_listener_old;
ALTER TABLE IF EXISTS bolagsfakta_supplier__aux_listener_listen_on RENAME TO bolagsfakta_supplier__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS bolagsfakta_supplier__aux_notification RENAME TO bolagsfakta_supplier__aux_notification_old;
ALTER TABLE IF EXISTS bolagsfakta_supplier__aux_versions RENAME TO bolagsfakta_supplier__aux_versions_old;
ALTER TABLE IF EXISTS bolagsfakta_supplier_materialized_path RENAME TO bolagsfakta_supplier_materialized_path_old;
ALTER TABLE IF EXISTS bolagsfakta_supplier_supplier_contacts RENAME TO bolagsfakta_supplier_supplier_contacts_old;

-- Current
ALTER TABLE IF EXISTS bol_supplier RENAME TO bol_supplier_old;
ALTER TABLE IF EXISTS bol_supplier__aux_listener RENAME TO bol_supplier__aux_listener_old;
ALTER TABLE IF EXISTS bol_supplier__aux_listener_listen_on RENAME TO bol_supplier__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS bol_supplier__aux_notification RENAME TO bol_supplier__aux_notification_old;
ALTER TABLE IF EXISTS bol_supplier__aux_versions RENAME TO bol_supplier__aux_versions_old;
ALTER TABLE IF EXISTS bol_supplier_materialized_path RENAME TO bol_supplier_materialized_path_old;
ALTER TABLE IF EXISTS bol_supplier_supplier_contacts RENAME TO bol_supplier_supplier_contacts_old;

ALTER TABLE IF EXISTS project RENAME TO project_old;
ALTER TABLE IF EXISTS project_project_ids RENAME TO project_project_ids_old;
ALTER TABLE IF EXISTS project__aux_listener RENAME TO project__aux_listener_old;
ALTER TABLE IF EXISTS project__aux_listener_listen_on RENAME TO project__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS project__aux_notification RENAME TO project__aux_notification_old;
ALTER TABLE IF EXISTS project__aux_versions RENAME TO project__aux_versions_old;
ALTER TABLE IF EXISTS project__path_sync RENAME TO project__path_sync_old;
ALTER TABLE IF EXISTS project__path_sync_sync_sources RENAME TO project__path_sync_sync_sources_old;
ALTER TABLE IF EXISTS project_names RENAME TO project_names_old;

ALTER TABLE IF EXISTS report RENAME TO report_old;
ALTER TABLE IF EXISTS report__aux_listener RENAME TO report__aux_listener_old;
ALTER TABLE IF EXISTS report__aux_listener_listen_on RENAME TO report__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS report__aux_notification RENAME TO report__aux_notification_old;
ALTER TABLE IF EXISTS report__aux_versions RENAME TO report__aux_versions_old;
ALTER TABLE IF EXISTS report__path_pdf RENAME TO report__path_pdf_old;
ALTER TABLE IF EXISTS report__path_sync RENAME TO report__path_sync_old;
ALTER TABLE IF EXISTS report__path_sync_sync_sources RENAME TO report__path_sync_sync_sources_old;
ALTER TABLE IF EXISTS report_certificates RENAME TO report_certificates_old;
ALTER TABLE IF EXISTS report_certificates_notes RENAME TO report_certificates_notes_old;
ALTER TABLE IF EXISTS report_company_information RENAME TO report_company_information_old;
ALTER TABLE IF EXISTS report_company_information_gov_org_ids RENAME TO report_company_information_gov_org_ids_old;
ALTER TABLE IF EXISTS report_company_information_names RENAME TO report_company_information_names_old;
ALTER TABLE IF EXISTS report_company_information_notes RENAME TO report_company_information_notes_old;
ALTER TABLE IF EXISTS report_company_relations RENAME TO report_company_relations_old;
ALTER TABLE IF EXISTS report_company_relations_notes RENAME TO report_company_relations_notes_old;
ALTER TABLE IF EXISTS report_company_relations_orgs RENAME TO report_company_relations_orgs_old;
ALTER TABLE IF EXISTS report_company_relations_persons RENAME TO report_company_relations_persons_old;
ALTER TABLE IF EXISTS report_notes RENAME TO report_notes_old;
ALTER TABLE IF EXISTS report_operating_licenses RENAME TO report_operating_licenses_old;
ALTER TABLE IF EXISTS report_operating_licenses_license_types RENAME TO report_operating_licenses_license_types_old;
ALTER TABLE IF EXISTS report_operating_licenses_licenses RENAME TO report_operating_licenses_licenses_old;
ALTER TABLE IF EXISTS report_ratings RENAME TO report_ratings_old;
ALTER TABLE IF EXISTS report_ratings_notes RENAME TO report_ratings_notes_old;
ALTER TABLE IF EXISTS report_registry_information RENAME TO report_registry_information_old;
ALTER TABLE IF EXISTS report_registry_information_notes RENAME TO report_registry_information_notes_old;
ALTER TABLE IF EXISTS report_registry_memberships RENAME TO report_registry_memberships_old;
ALTER TABLE IF EXISTS report_registry_memberships_registry_memberships RENAME TO report_registry_memberships_registry_memberships_old;
ALTER TABLE IF EXISTS report_reported_information RENAME TO report_reported_information_old;
ALTER TABLE IF EXISTS report_reported_information_notes RENAME TO report_reported_information_notes_old;
ALTER TABLE IF EXISTS report_reported_information_reported_data RENAME TO report_reported_information_reported_data_old;


ALTER TABLE IF EXISTS report_access RENAME TO report_access_old;
ALTER TABLE IF EXISTS report_access__aux_listener RENAME TO report_access__aux_listener_old;
ALTER TABLE IF EXISTS report_access__aux_listener_listen_on RENAME TO report_access__aux_listener_listen_on_old;
ALTER TABLE IF EXISTS report_access__aux_notification RENAME TO report_access__aux_notification_old;
ALTER TABLE IF EXISTS report_access__aux_versions RENAME TO report_access__aux_versions_old;
ALTER TABLE IF EXISTS report_access_gov_org_ids RENAME TO report_access_gov_org_ids_old;
