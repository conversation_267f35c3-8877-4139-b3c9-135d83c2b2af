variables:
  containerRegistryAlpha: crvaultitdevtest.azurecr.io
  containerRegistryBetaProd: crvaultitprod.azurecr.io
  
  dockerRegistryServiceConnectionAlpha: sc-acr-vaultit-private-devtest
  dockerRegistryServiceConnectionBetaProd: sc-acr-vaultit-private-prod

  imageRepository-bol: bol
  imageName-bol-data-api: bol-data-api
  namespace: bol

  imageUrlAlpha: $(containerRegistryAlpha)/$(imageRepository-bol)/$(imageName-bol-data-api)
  imageUrlBetaProd: $(containerRegistryBetaProd)/$(imageRepository-bol)/$(imageName-bol-data-api)
