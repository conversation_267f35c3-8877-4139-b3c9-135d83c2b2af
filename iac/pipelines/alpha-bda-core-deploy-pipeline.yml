parameters:
  - name: CustomImageTag
    type: string
    default: none
    displayName: Custom Image Tag

name: $(Date:yyyyMMdd)$(Rev:.r)-$(Build.BuildId)

trigger: none

pool: vaultit-agentpool-alpha-we-02

variables:
- template: variables/common.yml
- group: Docker-image-tags
- name: imageTag
  ${{ if eq(parameters.CustomImageTag, 'none') }}:
    value: $(bda_master_build_tag)
  ${{ else }}:
    value: ${{ parameters.CustomImageTag }}

stages:
- template: templates/check-image-tag-stage.yml
  parameters:
    imageTag: ${{ variables.imageTag }}
- stage: publish_manifests
  displayName: Publish manifests
  jobs:
  - job: publish
    steps:
    - task: PublishBuildArtifacts@1
      displayName: Publish manifests
      inputs:
        PathtoPublish: '$(System.DefaultWorkingDirectory)/iac/manifests'
        ArtifactName: 'manifests'
        publishLocation: 'Container'
- stage: Deploy
  variables:
  - template: variables/alpha.yml
  displayName: Deploy to ${{ variables.environment }}
  jobs:
  - deployment: deploy_${{ variables.environment }}
    displayName: Deploy to ${{ variables.environment }}
    environment: ${{ variables.environment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - template: templates/bda-deploy-steps.yml
            parameters:
              environment: ${{ variables.environment }}
              kubernetesServiceConnection: ${{ variables.kubernetesServiceConnection }}
              imageUrl: ${{ variables.imageUrlAlpha }}
              imageTag: ${{ variables.imageTag }}
              overlayFolder: 'overlays-core'
