name: $(Date:yyyyMMdd)$(Rev:.r)-$(Build.BuildId)

trigger:
  branches:
    include:
    - 'master'

pool: vaultit-agentpool-alpha-we-02

variables:
- template: variables/common.yml
- name: imageTag
  value: $(Build.BuildNumber)

stages:
- template: templates/build-stages.yml

- stage: Tag_docker_image
  displayName: Tag docker image
  # Run only this stage on master branch
  condition: contains(variables['build.sourceBranch'], 'refs/heads/master')
  jobs:
  - job: tag_docker_image_latest_acr
    steps:
    - task: Docker@2
      displayName: Login
      inputs:
        containerRegistry: $(dockerRegistryServiceConnectionAlpha)
        command: 'login'
    - task: CmdLine@2
      displayName: Tag docker image
      inputs:
        workingDirectory: $(System.DefaultWorkingDirectory)
        script: |
          docker pull --platform linux/amd64 $(imageUrlAlpha):$(imageTag)
          docker pull --platform linux/arm64 $(imageUrlAlpha):$(imageTag)-arm64
          docker manifest create $(imageUrlAlpha):latest $(imageUrlAlpha):$(imageTag) $(imageUrlAlpha):$(imageTag)-arm64
          docker manifest push $(imageUrlAlpha):latest
  # Update variable in the Azure variable group
  # so that we can use that in the deploy pipeline
  - job: update_bda_image_tag_variable
    variables:
    - group: Docker-image-tags
    steps:
    - bash: |
        az config set extension.use_dynamic_install=yes_without_prompt
        az pipelines variable-group variable update --group-id $(group_id) \
          --name bda_master_build_tag \
          --value $(imageTag) \
          --org https://dev.azure.com/vaultit/ \
          --project BOL
      displayName: 'Update bda master build docker tag'
      env:
        AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
