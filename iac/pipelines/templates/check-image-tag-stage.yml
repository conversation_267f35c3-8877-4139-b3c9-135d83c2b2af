parameters:
- name: imageTag
  type: string

stages:
  - stage: check_image_tag
    displayName: Check image tag in crvaultitdevtest.azurecr.io and echo it
    jobs:
    - job: check_image_tag
      steps:
        - task: Docker@2
          displayName: Login
          inputs:
            containerRegistry: $(dockerRegistryServiceConnectionAlpha)
            command: 'login'
        - task: CmdLine@2
          displayName: Check image tag and echo it
          inputs:
            script: |
              docker manifest inspect $(imageUrlAlpha):${{ parameters.imageTag }} >/dev/null 2>&1 || { echo "Image tag ${{ parameters.imageTag }} not found!"; exit 1; }
              echo Image tag ${{ parameters.imageTag }} found!
