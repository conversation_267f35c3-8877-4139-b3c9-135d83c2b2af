steps:
- task: Docker@2
  inputs:
    containerRegistry: $(dockerRegistryServiceConnectionAlpha)
    command: "login"
  displayName: Docker login crvaultitdevtest
- script: |
    docker pull $(imageUrlAlpha):$(imageTag)
  displayName: "Docker pull"
- script: |
    docker tag $(imageUrlAlpha):$(imageTag) $(imageUrlBetaProd):$(imageTag)
  displayName: "Docker tag"
- task: Docker@2
  inputs:
    containerRegistry: $(dockerRegistryServiceConnectionBetaProd)
    command: "login"
  displayName: Docker login crvaultitprod
- script: |
    docker push $(imageUrlBetaProd):$(imageTag)
  displayName: "Docker push"
