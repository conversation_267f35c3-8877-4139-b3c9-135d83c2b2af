parameters:
- name: environment
  type: string
- name: kubernetesServiceConnection
  type: string
- name: imageUrl
  type: string
- name: imageTag
  type: string
- name: overlayFolder
  type: string

steps:
- task: KubectlInstaller@0
  inputs:
    kubectlVersion: 'latest'

- task: KubernetesManifest@0
  displayName: Bake K8s manifests from kustomization path
  name: bake
  inputs:
    action: 'bake'
    namespace: '$(namespace)'
    renderType: 'kustomize'
    kustomizationPath: '$(Pipeline.Workspace)/manifests/bol-data-api/${{ parameters.overlayFolder }}/${{ parameters.environment }}'

- task: KubernetesManifest@0
  displayName: Deploy k8s manifests - $(namespace)
  inputs:
    kubernetesServiceConnection: '${{ parameters.kubernetesServiceConnection }}'
    action: 'deploy'
    namespace: '$(namespace)'
    manifests: $(bake.manifestsBundle)
    containers: '${{ parameters.imageUrl }}:${{ parameters.imageTag }}'
