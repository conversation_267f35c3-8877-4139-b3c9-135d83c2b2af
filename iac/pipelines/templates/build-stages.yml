stages:
- stage: test
  jobs:
  - job: test
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    container:
      image: python:3.11-bookworm
    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: bol
          POSTGRES_USER: dbuser
          POSTGRES_PASSWORD: dbpwd
    variables:
      # configuration for alembic (our env.py reads DATABASE_URL)
      DATABASE_URL: **************************************
      # configuraton for our test suite
      BOL_TEST_SQLA_URL: postgresql://postgres/bol
      BOL_TEST_SQLA_USERNAME: dbuser
      BOL_TEST_SQLA_PASSWORD: dbpwd
    steps:
    - script: |
        pip install tox -r db/alembic/requirements.txt
        export PATH="$HOME/.local/bin:$PATH"
        (cd db/alembic && PYTHONPATH=migrations alembic upgrade head)
        (cd server && python3 -m tox)
      displayName: test
  - job: test_alembic
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    steps:
    - task: UsePythonVersion@0
      displayName: use Python3.11
      inputs:
        versionSpec: '3.11'
    - script: |
        pip3 install tox
        (cd db/alembic && tox -e py3)
      displayName: test_alembic
  - job: build_git_version
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    container:
      image: python:3.11-bookworm
    steps:
    - script: |
        cd server
        pip install setuptools
        python3 setup.py --version | tee ../version.txt
    # https://learn.microsoft.com/en-us/azure/devops/pipelines/process/set-variables-scripts?view=azure-devops&tabs=bash#set-an-output-variable-for-use-in-future-stages
    - bash: echo "##vso[task.setvariable variable=val;isOutput=true]$(cat version.txt)"
      name: VersionVar

- stage: build_push_docker
  displayName: Docker build and push
  dependsOn: test
  variables:
    versionVar: $[stageDependencies.test.build_git_version.outputs['VersionVar.val']]
  jobs:
  - job: build_push_amd64
    steps:
    - task: Docker@2
      displayName: Login
      inputs:
        containerRegistry: $(dockerRegistryServiceConnectionAlpha)
        command: 'login'
    - task: CmdLine@2
      displayName: Build amd64 image & push to acr
      inputs:
        workingDirectory: $(System.DefaultWorkingDirectory)
        script: |
          docker build \
            --pull \
            --no-cache \
            -t $(imageUrlAlpha):$(imageTag) \
            --build-arg build=$(imageTag) \
            --build-arg VERSION=$(versionVar) \
            -f Dockerfile .
          docker push $(imageUrlAlpha):$(imageTag)
  - job: build_push_arm64
    variables:
      versionVar: $[stageDependencies.test.build_git_version.outputs['VersionVar.val']]
    steps:
    - task: Docker@2
      displayName: Login
      inputs:
        containerRegistry: $(dockerRegistryServiceConnectionAlpha)
        command: 'login'
    - task: CmdLine@2
      displayName: Make sure we have docker-buildx on the agent
      inputs:
        workingDirectory: $(System.DefaultWorkingDirectory)
        script: |
          if ! [ -e /usr/libexec/docker/cli-plugins/docker-buildx ]; then
            sudo apt-get update -qq
            sudo apt-get install -y docker-buildx
          fi
    - task: CmdLine@2
      displayName: Build arm64 image & push to acr
      inputs:
        workingDirectory: $(System.DefaultWorkingDirectory)
        script: |
          docker buildx create --use
          docker run --privileged --rm tonistiigi/binfmt --install arm64
          docker buildx build \
            --platform linux/arm64 \
            --provenance=false \
            --pull \
            --no-cache \
            -t $(imageUrlAlpha):$(imageTag)-arm64 \
            --push \
            --build-arg build=$(imageTag) \
            --build-arg VERSION=$(versionVar) \
            -f Dockerfile .
