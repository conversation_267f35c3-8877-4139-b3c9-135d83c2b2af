apiVersion: batch/v1
kind: Job
metadata:
  name: bol-data-api-db-migration-job
  labels:
    app: bol-data-api-db-migration
    cost-center: xxxx
    env: none  # Attn: Provided by the env patch.
    org: ID06
    vertical: BOL
spec:
  backoffLimit: 1
  template:
    metadata:
      labels:
        app: bol-data-api-db-migration
        cost-center: xxxx
        name: bol-data-api-db-migration-job
        org: ID06
        vertical: BOL
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: bol-workload-identity
      nodeSelector:
        agentpool: none # REQUIRED: provided by the env patch.
      restartPolicy: Never
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: bol-data-api-db-migration-job
          image: none # REQUIRED: provided by the env patch.
          command: ['/run.sh']
          # args: ['alembic', "history"]
          # args: ['alembic', "current"]
          # args: ["alembic", "stamp", "3a2fd1f4f0a9"]
          args: ["alembic", "upgrade", "head"]
          # args: ['alembic', "revision", "--autogenerate", "-m new"]
          resources:
            requests:
              memory: 256Mi
              cpu: 2m
            limits:
              memory: 2Gi
              cpu: 1000m
          imagePullPolicy: Always
          env:
            - name: application
              value: bol-data-api
            - name: BOLDATAAPI_DB_VAULT_DB_ROLE
              value: bol
            - name: BOLDATAAPI_DB_VAULT_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: bol-data-api-creds-migr-db
                  key: BOLDATAAPI_DB_VAULT_DB_MIG_USERNAME
            - name: BOLDATAAPI_DB_VAULT_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: bol-data-api-creds-migr-db
                  key: BOLDATAAPI_DB_VAULT_DB_MIG_PASSWORD
          envFrom:
            - configMapRef:
                name: bol-data-api-configmap
            - secretRef:
                name: bol-data-api-creds-migr-db
          volumeMounts:
            - name: secrets-store-inline-db
              mountPath: "/mnt/secrets-store-db"
              readOnly: true
            - name: ca-pemstore
              subPath: combined.pem
              mountPath: /etc/bolfak/certs/combined.pem
              readOnly: true
          securityContext:
            allowPrivilegeEscalation: false
      volumes:
        - name: secrets-store-inline-db
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: bol-data-api-spc-migr-db
        - name: ca-pemstore
          configMap:
            name: db-migration-configmap
            items:
              - key: combined.pem
                path: combined.pem
