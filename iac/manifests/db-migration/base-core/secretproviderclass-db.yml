apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: bol-data-api-core-spc-migr-db
  labels:
    cost-center: xxxx-xx
    org: ID06
    vertical: BOL
spec:
  provider: azure
  secretObjects:
  - secretName: bol-data-api-core-creds-migr-db
    data:
    - key: BOLDATAAPI_DB_VAULT_DB_MIG_USERNAME
      objectName: none                  # REQUIRED: provided by the env patch.
    - key: BOLDATAAPI_DB_VAULT_DB_MIG_PASSWORD
      objectName: none                  # REQUIRED: provided by the env patch.

    type: Opaque
  parameters:
    clientID: none                      # REQUIRED: provided by the env patch.
    usePodIdentity: "false"             # We're using a service account instead
    useVMManagedIdentity: "false"       # [OPTIONAL] if not provided, will default to "false". Set to "true" if using managed identities.
    userAssignedIdentityID: ""          # [OPTIONAL] If you're using managed identities, use the client id to specify which user-assigned managed identity to use. If the value is empty, it defaults to use the system-assigned identity on the VM
    keyvaultName: none                  # [REQUIRED] the name of the key vault (Attn: Provided by the env patch.)
    cloudName: ""                       # [OPTIONAL] if not provided, Azure environment will default to AzurePublicCloud
    objects: none                       # REQUIRED: provided by the env patch.

    tenantId: "9af535c0-cda2-481b-941c-43821cda329c"
