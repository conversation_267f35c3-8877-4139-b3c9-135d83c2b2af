- op: replace
  path: /spec/parameters/keyvaultName
  value: kv-psql-beta-we-01

- op: replace
  path: /spec/secretObjects/0/data/0/objectName
  value: psql-beta-bol-mig-user-we-01

- op: replace
  path: /spec/secretObjects/0/data/1/objectName
  value: psql-beta-bol-mig-pass-we-01

- op: replace
  path: /spec/parameters/clientID
  value: 7a68386e-a778-434e-bbeb-90dd8183c290

- op: add
  path: /spec/parameters/objects
  value:  |
      array:
        - |
          objectName: psql-beta-bol-mig-user-we-01
          objectType: secret
        - |
          objectName: psql-beta-bol-mig-pass-we-01
          objectType: secret
