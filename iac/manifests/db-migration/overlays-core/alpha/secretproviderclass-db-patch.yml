- op: replace
  path: /spec/parameters/keyvaultName
  value: kv-psql-alpha-we-01

- op: replace
  path: /spec/secretObjects/0/data/0/objectName
  value: psql-alpha-bol-mig-user-we-02

- op: replace
  path: /spec/secretObjects/0/data/1/objectName
  value: psql-alpha-bol-mig-pass-we-02

- op: replace
  path: /spec/parameters/clientID
  value: 61a3ca47-0049-41d0-a23b-fb17c0ba5f0e

- op: add
  path: /spec/parameters/objects
  value:  |
      array:
        - |
          objectName: psql-alpha-bol-mig-user-we-02
          objectType: secret
        - |
          objectName: psql-alpha-bol-mig-pass-we-02
          objectType: secret
