resources:
  - ../../base

patches:
- target:
    kind: ConfigMap
    name: bol-data-api-configmap
  path: configMap-patch.yml
- target:
    kind: Deployment
    name: bol-data-api-deploy
  path: deployment-patch.yml
- target:
    kind: SecretProviderClass
    name: bol-data-api-spc-db
  path: secretproviderclass-db-patch.yml
- target:
    kind: SecretProviderClass
    name: bol-data-api-spc
  path: secretproviderclass-patch.yml
- target:
    kind: Ingress
    name: bol-data-api-ingress-googledns
  path: ingress-googledns-patch.yml
