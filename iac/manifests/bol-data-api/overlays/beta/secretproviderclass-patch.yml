- op: replace
  path: /spec/parameters/keyvaultName
  value: kv-bol-beta-we-01

- op: replace
  path: /spec/secretObjects/0/data/0/objectName
  value: application-insight-beta-connection-string

- op: add
  path: /spec/secretObjects/0/data/-
  value:
    key: BOLDATAAPI_MAIN_SENTRY_DSN
    objectName: boldataapi-main-sentry-dsn

- op: replace
  path: /spec/parameters/clientID
  value: 7a68386e-a778-434e-bbeb-90dd8183c290

- op: add
  path: /spec/parameters/objects
  value:  |
      array:
        - |
          objectName: application-insight-beta-connection-string
          objectType: secret
        - |
          objectName: boldataapi-main-sentry-dsn
          objectType: secret

