apiVersion: apps/v1
kind: Deployment
metadata:
  name: bol-data-api-deploy
  labels:
    app: bol-data-api
    cost-center: xxxx
    env: none  # Attn: Provided by the env patch.
    org: ID06
    vertical: BOL
  annotations:
    # Enable https://github.com/stakater/Reloader/tree/master
    # Now we do not manually have to restart pods whenever we
    # update a secret in Azure Key Vault via the portal.
    # Note that also a label was added in the kubernetes namespace
    # reloader="enabled".
    reloader.stakater.com/auto: "true"
spec:
  progressDeadlineSeconds: 60
  replicas: 0  # Attn: Provided by the env patch.
  selector:
    matchLabels:
      name: bol-data-api-deploy
  minReadySeconds: 6
  revisionHistoryLimit: 0  # Attn: Provided by the env patch.
  template:
    metadata:
      labels:
        app: bol-data-api
        cost-center: xxxx
        name: bol-data-api-deploy
        org: ID06
        vertical: BOL
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: bol-workload-identity
      nodeSelector:
        agentpool: none # REQUIRED: provided by the env patch.
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        runAsNonRoot: true
      containers:
        - name: bol-data-api-container
          image: none # REQUIRED: provided by the env patch.
          imagePullPolicy: Always
          livenessProbe:
            httpGet:
              path: /api/v1/boldata/version
              port: 8000
            initialDelaySeconds: 60
            periodSeconds: 60
          resources:
            requests:
              memory: 512Mi
              cpu: 4m
            limits:
              memory: 2Gi
              cpu: 1000m
          envFrom:
            - configMapRef:
                name: bol-data-api-configmap
            - secretRef:
                name: bol-data-api-creds-db
            - secretRef:
                name: bol-data-api-creds
          volumeMounts:
            - name: secrets-store-inline-db
              mountPath: "/mnt/secrets-store-db"
              readOnly: true
            - name: secrets-store-inline
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: ca-pemstore
              subPath: combined.pem
              mountPath: /etc/bolfak/certs/combined.pem
              readOnly: true
          securityContext:
            allowPrivilegeEscalation: false
      volumes:
        - name: secrets-store-inline-db
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: bol-data-api-spc-db
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: bol-data-api-spc
        - name: ca-pemstore
          configMap:
            name: bol-data-api-configmap
