- op: replace
  path: /data/env
  value: beta
- op: replace
  path: /data/BOLDATAAPI_DB_SQLALCHEMY_URL
  value: 'postgresql://psql-bol-beta-we-01.postgres.database.azure.com/bolcore'
- op: replace
  path: /data/BOLDATAAPI_DB_SQLALCHEMY_USE_CLIENT_CERT
  value:  'false'
- op: replace
  path: /data/BOLDATAAPI_AUTH_GLUU_ADDRESSES
  value: 'https://auth-core.beta.id06.se'
- op: replace
  path: /data/BOLDATAAPI_AUTH_VERIFY_REQUESTS
  value: 'true'
- op: replace
  path: /data/BOLDATAAPI_FEATURE_FLAGS_VALIDATE_INPUT
  value: 'true'
- op: replace
  path: /data/BOLDATAAPI_FEATURE_FLAGS_FIRST_VISITED
  value: 'true'
- op: replace
  path: /data/BOLDATAAPI_FEATURE_FLAGS_SWAGGER_API
  value:  'false'
- op: replace
  path: /data/BOLDATAAPI_FEATURE_FLAGS_ON_AZURE
  value:  'true'
- op: replace
  path: /data/BOLDATAAPI_FEATURE_FLAGS_PERSON_ID_FOR_PROJECT_USERS
  value:  'true'
- op: replace
  path: /data/BOLDATAAPI_FEATURE_FLAGS_CORE
  value: 'true'
