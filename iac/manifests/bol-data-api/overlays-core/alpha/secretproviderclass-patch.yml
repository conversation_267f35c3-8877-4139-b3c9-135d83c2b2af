- op: replace
  path: /spec/parameters/keyvaultName
  value: kv-bol-alpha-we-01

- op: replace
  path: /spec/secretObjects/0/data/0/objectName
  value: application-insight-alpha-connection-string

- op: add
  path: /spec/secretObjects/0/data/-
  value:
    key: BOLDATAAPI_MAIN_SENTRY_DSN
    objectName: boldataapi-main-sentry-dsn

- op: replace
  path: /spec/parameters/clientID
  value: 61a3ca47-0049-41d0-a23b-fb17c0ba5f0e

- op: add
  path: /spec/parameters/objects
  value:  |
      array:
        - |
          objectName: application-insight-alpha-connection-string
          objectType: secret
        - |
          objectName: boldataapi-main-sentry-dsn
          objectType: secret

