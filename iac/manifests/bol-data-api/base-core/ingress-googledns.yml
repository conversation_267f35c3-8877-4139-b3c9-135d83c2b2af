apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bol-data-api-core-ingress-googledns
  annotations:
    appgw.ingress.kubernetes.io/appgw-ssl-certificate: none  # Attn: Provided by the env patch.
    appgw.ingress.kubernetes.io/waf-policy-for-path: none  # Attn: provided by the patch.
    appgw.ingress.kubernetes.io/health-probe-path: /api/v1/boldata/version
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
    appgw.ingress.kubernetes.io/request-timeout: "600"
    kubernetes.io/ingress.class: azure/application-gateway
  labels:
    cost-center: xxxx-xx
    org: ID06
    vertical: BOL
spec:
  rules:
    - host: none  #Attn: Provided by the env patch.
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: bol-data-api-core-service
                port:
                  number: 8000
