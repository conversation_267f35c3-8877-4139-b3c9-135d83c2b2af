version: "2.3"

services:
  db:
    build: ./db
    ports:
      - "54323:5432"
    environment:
      POSTGRES_DB: bol
      POSTGRES_USER: dbuser
      POSTGRES_PASSWORD: dbpwd
    healthcheck:
      test: "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"
    volumes:
      - bda-pg-data:/var/lib/postgresql/data
  bol-data-api:
    build:
      context: .
      args:
        - VERSION
    depends_on:
      - db
    environment:
      BOLDATAAPI_DB_SQLALCHEMY_URL: ********************************
      BOLDATAAPI_RUN_ALEMBIC_ON_STARTUP: 'true'
      # copy these from the shell environment for developer convenience
      BOLDATAAPI_FEATURE_FLAGS_AZURE_AI:
      APPLICATIONINSIGHTS_CONNECTION_STRING:
    ports:
      - "9700:8000"
    stdin_open: true  # so you can docker attach bol-data-api_bol-data-api_1 and use pdb
    tty: true         # so you can docker attach bol-data-api_bol-data-api_1 and use pdb
    volumes:
      - ./server/development.cfg:/opt/app/src/server/development.cfg
      - ./server/boldataapi:/opt/app/env/lib/python3.11/site-packages/boldataapi
      - ./db/alembic:/opt/app/alembic
    command: ["uwsgi", "--ini", "/etc/uwsgi/apps-enabled/bol-data-api-uwsgi.ini",
              "--py-autoreload", "1", "--honour-stdin", "--harakiri", "600",
              "--socket-timeout", "600", "--processes", "1",
              "--pyargv", "--config /opt/app/src/server/development.cfg"]
  swagger-ui:
    image: swaggerapi/swagger-ui
    depends_on:
      - bol-data-api
    environment:
      API_URL: http://localhost:9700/api/v1/boldata/swagger.json
    ports:
      - "8123:8080"
  alembic:
    build: ./db/alembic
    depends_on:
      - db-test-alembic
    command: "upgrade head"
    user: 1000:1000
    volumes:
      - ./db/alembic/migrations:/alembic/migrations:Z
    environment:
      POSTGRES_USER: dbuser
      POSTGRES_PASSWORD: dbpwd
      POSTGRES_HOST: db-test-alembic
      POSTGRES_DB: bol
      POSTGRES_PORT: 5432
    ports:
      - "5000:5000"
  db-test-alembic:
    image: postgres:11
    ports:
      - "54324:5432"
    environment:
      POSTGRES_PASSWORD: dbpwd
      POSTGRES_USER: dbuser
      POSTGRES_DB: bol
    healthcheck:
      test: "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"
    volumes:
      - bda-pg-test-alembic-data:/var/lib/postgresql/data
  db-test:
    build: ./db
    ports:
      - "54325:5432"
    environment:
      POSTGRES_PASSWORD: dbpwd
      POSTGRES_USER: dbuser
      POSTGRES_DB: bol
    healthcheck:
      test: "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"
    volumes:
      - bda-pg-test-data:/var/lib/postgresql/data
  db-admin:
    image: adminer
    restart: always
    ports:
      - "8090:8080"
volumes:
  bda-pg-data:
  bda-pg-test-data:
  bda-pg-test-alembic-data:
