[main]
app_name = bol-data-api
base_url = /api/v1/boldata
host = 0.0.0.0
port = 5555

[db]
sqlalchemy_url = postgresql://db:5432/bol
vault_db_username = dbuser
vault_db_password = dbpwd

[auth]
gluu_addresses = https://auth-azure-alpha.id06.se
verify_requests = true

[feature-flags]
validate_input = true
swagger_api = true
on_azure = false
person_id_for_project_users = true
core = false

#
# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[loggers]
keys = root, bda_access, urllib3

[logger_root]
handlers = console
level = DEBUG

[logger_bda_access]
qualname = bda-access
handlers = bda_access
propagate = 0

[logger_urllib3]
qualname = urllib3
handlers =
level = INFO

[handlers]
keys = console, bda_access

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = console
level = DEBUG

[handler_bda_access]
class = StreamHandler
args = (sys.stdout,)
formatter = bda_access

[formatters]
keys = console, bda_access

[formatter_console]
class = boldataapi.logging.ColorFormatter
format = {blue}%(asctime)s{reset} {green}[%(process)s:%(levelname)s:%(name)s]{reset} %(message)s
datefmt = %H:%M:%S.%f

[formatter_bda_access]
class = boldataapi.logging.ColorFormatter
format = {magenta}%(message)s{reset}
