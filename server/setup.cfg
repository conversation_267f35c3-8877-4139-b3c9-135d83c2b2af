[flake8]
exclude = .idea, __pycache__, log, __init__.py, env, qvarn, versions
max-line-length = 100
max-complexity = 10
filename = *.py
format = default
extend-ignore =
	# comparison to True should be 'if cond is True:' or 'if cond (it's needed for SQLAlchemy)
	E712

# flake8-import-order
application-import-names = boldataapi, tests, operations, models, config
import-order-style = smarkets

[coverage:run]
source = boldataapi
omit = boldataapi/storage/qvarn/*

[coverage:report]
exclude_lines =
    pragma: nocover
    pass
    if __name__ == .__main__.:

[mypy-sqlalchemy.*,psycopg2.*,bottle,pytest,webtest,freezegun,requests_mock,citext]
ignore_missing_imports = True

[mypy-boldataapi.storage.qvarn.*]
ignore_errors = True

[mypy-cerberus,expiringdict]
# I don't know why mypy fails to find cerberus or expiringdict!
ignore_missing_imports = True

[mypy-raven.*]
ignore_missing_imports = True

[mypy-bottleswagger.*]
ignore_missing_imports = True
