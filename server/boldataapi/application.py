import argparse
import functools
import logging
import sys
import traceback
from typing import Any, Dict, List, Optional

import bottle
from bottle import JSONPlugin
from raven.contrib.bottle.utils import get_data_from_request
from stv.azure.helpers import LoggerOptions
from typing_extensions import Protocol

import boldataapi
import boldataapi.controllers as controllers
from boldataapi.accesscontrol.access_control_plugin import AccessControlConfig, AccessControlPlugin
from boldataapi.accesscontrol.access_validator import AccessValidator
from boldataapi.featureflags import feature_active
from boldataapi.plugins import HTTPResponsePlugin, SQLAlchemyPlugin
from boldataapi.sentry import setup_sentry
from boldataapi.serialize import to_json_with_date
from boldataapi.storage.db import setup_db_client, SQLAlchemyEngine
from boldataapi.version import get_alembic_version


logger = logging.getLogger(__name__)

# Configure bottle application
application = bottle.app()
application.catchall = False

# Monkey-patch warning: increase the hardcoded limit for JSON data in POST request body.
# This is also used as a threshold for switching to actual on-disk temporary files when
# accessing bottle.request.body.
bottle.BaseRequest.MEMFILE_MAX = 5 * 1024 * 1024


# ----8<----
# Bottle bug workaround.
# https://github.com/bottlepy/bottle/issues/602#issuecomment-591434275
def pathinfo_adjust_wrapper(func):
    # A wrapper for _handle() method
    @functools.wraps(func)
    def _(environ):
        environ["PATH_INFO"] = environ["PATH_INFO"].encode("utf8").decode("latin1")
        return func(environ)
    return _


application._handle = pathinfo_adjust_wrapper(application._handle)
# ---->8----


class Controller(Protocol):
    def get_routes(self) -> List[Dict[str, Any]]:
        pass


def setup_application(
    *,
    catch_exceptions=True,
    db: SQLAlchemyEngine = None,
    add_auth_plugin: bool = True,
    add_access_log: bool = True,
) -> None:
    config = boldataapi.get_config()

    logger.info('BOL Data API setup...')

    if feature_active('on_azure') or feature_active('azure_ai'):
        logger.info('Initializing Application insights')
        api_application = boldataapi.CustomAzureBottle(
            application_insights_name='bol-data-api',
            catchall=catch_exceptions,
            autojson=False,
            logger_options=LoggerOptions(
                exclude_paths=[r'^/version$'],
            ),
        )
    else:
        api_application = boldataapi.CustomBottle(catchall=catch_exceptions, autojson=False)

    sentry_client = setup_sentry(config, tags={'service': 'wsgi'})

    def setup_sentry_context():
        sentry_client.context.clear()
        sentry_client.http_context(get_data_from_request(bottle.request)['request'])
        bottle.request.sentry_client = sentry_client

    if sentry_client:
        api_application.hook('before_request')(setup_sentry_context)

    if db is None:
        sqlalchemy_url = config.get('db', 'sqlalchemy_url')
        logger.info('Connecting to VaultDB at %s...', sqlalchemy_url)
        sqlalchemy_use_client_cert = config.getboolean(
            'db', 'sqlalchemy_use_client_cert')
        db = setup_db_client(sqlalchemy_url, sqlalchemy_use_client_cert)
    alembic_version_on_startup = get_alembic_version(db)

    api_application.catchall = False
    # Set up plugins
    if add_auth_plugin:
        access_validator = AccessValidator(logger)
        gluu_addresses = config.get('auth', 'gluu_addresses') or ''
        access_config = AccessControlConfig(
            verify_requests=config.getboolean('auth', 'verify_requests'),
            gluu_addresses=gluu_addresses.replace(',', ' ').split(),
        )
        api_application.install(
            AccessControlPlugin(logger, access_config, access_validator),
        )
    api_application.install(JSONPlugin(to_json_with_date))
    api_application.install(HTTPResponsePlugin(access_log_enabled=add_access_log))
    api_application.install(SQLAlchemyPlugin(db))

    # Set up controllers
    controller_list: List[Optional[Controller]] = [
        controllers.VersionController(logger),
        controllers.SwaggerController(logger) if feature_active('swagger_api') else None,
        controllers.BolSuppliersController(logger),
        controllers.ReportAccessesController(logger),
        controllers.ReportCacheController(logger),
        controllers.ProjectsController(logger),
        controllers.ProjectUsersController(logger),
        controllers.ProjectSupplierCommentsController(logger),
        controllers.ProjectCommentViewersController(logger),
        controllers.ReportsController(logger),
        controllers.CompaniesController(logger),
        controllers.BulkImportJobsController(logger),
        controllers.PreannouncementsController(logger),
        controllers.CreditsafeAccountsController(logger),
    ]

    # Set up routes
    for controller in controller_list:
        for route in controller.get_routes() if controller else ():
            api_application.route(**route)

    # Set up request hooks

    @api_application.hook('before_request')
    def setup_request():
        logger.debug('\n------------ started processing %s %s',
                     bottle.request.method, bottle.request.path)
        # With uWSGI bottle.request['wsgi.errors'] is a io.TextIOWrapper that
        # buffers all writes, which delays error messages, interleaves them
        # with regular log output, and loses some errors if you shut
        # down/restart the server process.  See:
        # https://github.com/bottlepy/bottle/pull/903
        # https://jira.tilaajavastuu.fi/browse/BOL-313
        bottle.request['wsgi.errors'] = sys.stderr
        bottle.request.db = db
        bottle.request.alembic_version_on_startup = alembic_version_on_startup

    # Set up application at the right URL prefix
    api_base_path = config.get('main', 'base_url')
    application.mount(api_base_path, api_application)

    logger.info('BOL Data API setup done')


def initialize_wsgi_application() -> None:
    # Read configuration
    parser = argparse.ArgumentParser(prog='uwsgi --pyargv')
    parser.add_argument(
        '--config',
        metavar='FILE',
        required=False,
        help='use FILE as configuration file')

    args = parser.parse_args()
    try:
        print("Loading configuration from %s" % args.config)
        boldataapi.set_config(args.config)
    except Exception:
        # set_config() is responsible for setting up logging, and it may have failed to do so!
        print("Failed to load configuration from %s!" % args.config, file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        print("bol-data-api application is *not* loaded! uwsgi will return 404 for all requests")
        sys.stderr.flush()  # in case we're in docker and somebody forgot the PYTHONUNBUFFERED=1
        # if we sys.exit(1) here, uwsgi will go into a respawn loop; if we
        # return None, we get a container running uwsgi with no apps loaded in
        # it, responding with an 404 error to heartbeat requests -- which will
        # cause Kubernetes to respawn after a while.
        return

    try:
        setup_application()
    except Exception:
        logging.fatal("failed to set up bol-data-api application!", exc_info=True)
        logging.warning("bol-data-api application is likely *not* loaded!"
                        " uwsgi will likely return 404 for all requests")
        sys.stderr.flush()  # in case we're in docker and somebody forgot the PYTHONUNBUFFERED=1
        # if we sys.exit(1) here, uwsgi will go into a respawn loop; if we
        # return None, we get a container running uwsgi with no apps loaded in
        # it, responding with an 404 error to heartbeat requests -- which will
        # cause Kubernetes to respawn after a while.
        return


try:
    import uwsgi  # noqa: will fail if not running under uwsgi
    initialize_wsgi_application()
except ImportError:
    logger.info('NOT RUNNING ON UWSGI')
