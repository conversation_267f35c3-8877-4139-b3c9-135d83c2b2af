import json

import bottle
from stv.azure.opentelemetry import AzureBottle


class ErrorHandlerMixin():

    def default_error_handler(self, res):
        bottle.response.content_type = 'application/json'
        return json.dumps({'error': res.body, 'status': res.status_code}, indent=4)


class CustomBottle(ErrorHandlerMixin, bottle.Bottle):
    pass


class CustomAzureBottle(ErrorHandlerMixin, AzureBottle):
    pass
