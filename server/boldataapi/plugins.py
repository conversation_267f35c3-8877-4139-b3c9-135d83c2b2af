import functools
import logging

import bottle

from boldataapi.accesscontrol.access_log import AccessLogger
from boldataapi.exceptions import Api<PERSON>rror, InternalServerError
from boldataapi.storage import qvarn


logger = logging.getLogger(__name__)
access_logger = logging.getLogger('bda-access')


class HTTPResponsePlugin(object):
    """Formats HTTP 200, 4xx, 5xx responses.

    This is a Bottle plugin.

    Catches exceptions and returns error as dict instead.

    Formats 20x HTTP responses.
    """

    name = 'http_response'
    api = 2

    def __init__(self, access_log_enabled=True) -> None:
        self.access_log_enabled = access_log_enabled

    def _prepare_for_success(self, route):
        if route.method == 'POST':
            status = 201
        else:
            status = 200
        bottle.response.status = status

    def _log_success(self, result):
        if self.access_log_enabled:
            AccessLogger(access_logger).log_access(bottle.request, result)

    def _process_success(self, result):
        return result

    def _process_qvarn_error(self, e):
        qvarn.log.log('exception', msg_text=str(e), exc_info=True)
        bottle.response.status = e.status_code
        return e.error

    def _process_api_error(self, e):
        if e.status_code >= 500:
            logger.exception(e)
        bottle.response.status = e.status_code
        return e.serialize()

    def _process_internal_error(self, e):
        logger.exception(e)
        exc = InternalServerError()
        bottle.response.status = exc.status_code
        return exc.serialize()

    def apply(self, callback, route):
        def wrapper(*args, **kwargs):
            try:
                self._prepare_for_success(route)
                result = callback(*args, **kwargs)
            except qvarn.QvarnException as e:
                return self._process_qvarn_error(e)
            except ApiError as e:
                return self._process_api_error(e)
            except bottle.HTTPError:
                # Don't convert 4xx HTTP errors into a 500 internal server error please
                raise
            except Exception as e:
                return self._process_internal_error(e)
            else:
                self._log_success(result)
                return self._process_success(result)
        return wrapper


class SQLAlchemyPlugin:
    """Commits or aborts the transaction when request is done processing.

    This is a Bottle plugin.
    """

    name = 'sqlalchemy'
    api = 2

    def __init__(self, db) -> None:
        self.db = db

    def apply(self, callback, route):

        @functools.wraps(callback)
        def wrapper(*args, **kwargs):
            try:
                retval = callback(*args, **kwargs)
                logger.debug('committing transaction')
                self.db.session.commit()
                return retval
            except Exception:
                logger.debug('aborting transaction')
                self.db.session.rollback()
                raise
            finally:
                self.db.session.remove()

        return wrapper
