import datetime
import uuid
from typing import Any, Dict

import sqlalchemy as sa

from boldataapi.schema import PA_STATUS_CREATED
from boldataapi.services.bol_suppliers import SUPPLIER_RESOURCE_TYPE
from boldataapi.services.project_supplier_comments import (
    create_project_supplier_comment,
    ProjectSupplierComment,
)
from boldataapi.services.projects import PROJECT_RESOURCE_TYPE
from boldataapi.services.report_accesses import REPORT_ACCESS_RESOURCE_TYPE
from boldataapi.services.reports import REPORT_RESOURCE_TYPE
from boldataapi.storage.db import (
    BULK_IMPORT_JOBS_TABLE,
    CREDITSAFE_ACCOUNT_HISTORY_TABLE,
    CREDITSAFE_ACCOUNT_TABLE,
    INTERNAL_PROJECT_IDS_TABLE,
    NOTIFICATION_REPORTS_TABLE,
    PREANNOUNCEMENT_FORMS_TABLE,
    PREANNOUNCEMENTS_TABLE,
    PROJECT_COMMENT_VIEWERS_TABLE,
    PROJECT_USERS_TABLE,
    PROJECTS_TABLE,
    REPORT_ACCESSES_TABLE,
    REPORT_CACHE_TABLE,
    STATUS_REPORTS_TABLE,
    SUPPLIER_CONTACTS_TABLE,
    SUPPLIERS_TABLE,
)
from boldataapi.storage.qvarn_helpers import generate_qvarn_id


FIXTURE_SUPPLIER: Dict[str, Any] = {
    'project_resource_id': str(uuid.uuid4()),
    'parent_org_id': 'any-parent-company-id',
    'parent_supplier_id': 'any-parent-supplier-id',
    'supplier_org_id': 'any-company-id',
    'supplier_role': 'supplier',
    'supplier_type': 'linked',
    'contract_start_date': datetime.date(2020, 1, 1).strftime('%Y-%m-%d'),
    'contract_end_date': datetime.date(2020, 12, 1).strftime('%Y-%m-%d'),
    'contract_type': None,
    'contract_work_areas': None,
    'materialized_path': ['item1', 'item2'],
    'internal_project_id': None,
    'first_visited': None,
    'last_visited': None,
    'visitor_type': None,
    'supplier_contacts': [
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>',
        }
    ],
}

FIXTURE_SUPPLIER_RESPONSE: Dict[str, Any] = {
    **FIXTURE_SUPPLIER,
    'pa_id': None,
    'pa_status': None,
    'bolagsfakta_status': None,
    'type': 'bol_supplier',
}


def create_supplier(db, *, external_id=None, project_id=None,
                    parent_company_id=None, company_id=None,
                    parent_supplier_id=None, role='supplier', type='linked',
                    contract_start_date=None, contract_end_date=None,
                    materialized_path=[], first_visited=None,
                    last_visited=None, contract_type=None,
                    contract_work_areas=None, is_one_man_company=None,
                    has_collective_agreement=None, collective_agreement_name=None):
    if not project_id:
        project_id = create_project(db)['id']
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]

    query = sa.insert(bol_suppliers).values(
        external_id=external_id or generate_qvarn_id(SUPPLIER_RESOURCE_TYPE.replace('qvarn_', '')),
        role=role,
        type=type,
        project_id=project_id,
        parent_supplier_id=parent_supplier_id,
        parent_company_id=parent_company_id,
        company_id=company_id,
        contract_start_date=contract_start_date.strftime('%Y-%m-%d')
        if contract_start_date else None,
        contract_end_date=contract_end_date.strftime('%Y-%m-%d')
        if contract_end_date else None,
        contract_type=contract_type,
        contract_work_areas=contract_work_areas,
        materialized_path=materialized_path,
        first_visited=first_visited,
        last_visited=last_visited,
        revision=str(uuid.uuid4()),
        is_one_man_company=is_one_man_company,
        has_collective_agreement=has_collective_agreement,
        collective_agreement_name=collective_agreement_name,
    )

    result = db.session.execute(query)
    query = (sa.select([bol_suppliers])
             .where(bol_suppliers.c.id == result.inserted_primary_key[0]))
    result = db.session.execute(query)
    return dict(result.fetchone())


def get_supplier(db, supplier_id):
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]

    qry = (
        sa.select([bol_suppliers])
        .where(bol_suppliers.c.id == supplier_id)
    )
    result = db.session.execute(qry)
    supplier = result.fetchone()
    return dict(supplier) if supplier else None


def create_supplier_contact(db, payload={}):
    supplier_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]

    qry = sa.insert(supplier_contacts).values(
        supplier_id=payload.get('supplier_id', 'fake_supplier_id'),
        person_id=payload.get('person_id', 'fake_person_id'),
        person_email=payload.get('person_email', '<EMAIL>'),
    )
    result = db.session.execute(qry)
    query = (sa.select([supplier_contacts])
             .where(supplier_contacts.c.id == result.inserted_primary_key[0]))
    result = db.session.execute(query)
    return dict(result.fetchone())


def get_supplier_contacts(db, supplier_id):
    supplier_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]

    qry = (
        sa.select([supplier_contacts])
        .where(supplier_contacts.c.supplier_id == supplier_id)
    )
    result = db.session.execute(qry)
    return [
        {
            'id': row[supplier_contacts.c.id],
            'supplier_id': row[supplier_contacts.c.supplier_id],
            'person_id': row[supplier_contacts.c.person_id],
            'person_email': row[supplier_contacts.c.person_email],
        } for row in result
    ]


def create_internal_project_id(db, payload={}):
    internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]

    qry = sa.insert(internal_project_ids).values(
        project_id=payload.get('project_id', 'any-project-id'),
        internal_project_id=payload.get('internal_project_id', 'any_internal_project_id'),
        company_id=payload.get('company_id')
    )
    result = db.session.execute(qry)
    query = (sa
             .select([internal_project_ids])
             .where(internal_project_ids.c.id == result.inserted_primary_key[0]))
    rez = db.session.execute(query)
    return dict(rez.fetchone())


def get_internal_project_ids(db, project_id):
    internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]

    qry = (
        sa.select([internal_project_ids])
        .where(internal_project_ids.c.project_id == project_id)
    )
    result = db.session.execute(qry)
    return [
        {
            'id': row[internal_project_ids.c.id],
            'internal_project_id': row[internal_project_ids.c.internal_project_id],
            'project_id': row[internal_project_ids.c.project_id],
            'company_id': row[internal_project_ids.c.company_id],
        } for row in result
    ]


# from boldataapi.fixtures.factories import fetch_all
def fetch_all(db, table_name):
    """Debugging helper. Use this to show all rows of a relation as seen by the
    API.
    """
    query = 'SELECT * FROM {}'.format(table_name)
    result = db.session.execute(query)
    return result.fetchall()


def create_report_access(
    db, *, customer_company_id, company_id, person_id, external_id=None,  status='active',
    access_time=None, arkisto_id=None, report_id=None, company_gov_id=None, language=None,
    company_gov_id_country=None, company_gov_id_type=None, template_version=None,
):
    tbl_report_accesses = db.meta.tables[REPORT_ACCESSES_TABLE]
    query = sa.insert(tbl_report_accesses).values(
        external_id=(
            external_id
            or generate_qvarn_id(REPORT_ACCESS_RESOURCE_TYPE.replace('qvarn_', ''))
        ),
        customer_company_id=customer_company_id,
        company_id=company_id,
        person_id=person_id,
        status=status,
        language=language,
        access_time=access_time,
        arkisto_id=arkisto_id,
        report_id=report_id,
        company_gov_id=company_gov_id,
        company_gov_id_country=company_gov_id_country,
        company_gov_id_type=company_gov_id_type,
        template_version=template_version,
    )
    result = db.session.execute(query)
    query = (
        sa.select([tbl_report_accesses])
        .where(tbl_report_accesses.c.id == result.inserted_primary_key[0])

    )
    rez = db.session.execute(query)
    return dict(rez.fetchone())


def get_report_access(db, id):
    tbl_report_accesses = db.meta.tables[REPORT_ACCESSES_TABLE]

    qry = (
        sa.select([tbl_report_accesses])
        .where(tbl_report_accesses.c.id == id)
    )
    rez = db.session.execute(qry)
    report_access = rez.fetchone()
    return dict(report_access) if report_access else None


def get_project(db, project_id):
    tbl_projects = db.meta.tables[PROJECTS_TABLE]

    qry = (
        sa.select([tbl_projects])
        .where(tbl_projects.c.id == project_id)
    )
    result = db.session.execute(qry)
    project = result.fetchone()
    return dict(project) if project else None


def create_project(db, *, name='Test project', external_id=None, tax_id=None,
                   client_company_id=None, client_contact_person_id=None,
                   client_contact_person_email=None, created_by_org_id=None,
                   start_date=None, end_date=None,
                   state='active', pa_form_enabled=False,
                   added_client_confirmed=False, project_creator_role=None,
                   added_client_can_view=False):

    tbl_projects = db.meta.tables[PROJECTS_TABLE]

    query = sa.insert(tbl_projects).values(
        external_id=external_id or generate_qvarn_id(PROJECT_RESOURCE_TYPE.replace('qvarn_', '')),
        name=name,
        tax_id=tax_id,
        client_company_id=client_company_id,
        client_contact_person_id=client_contact_person_id,
        client_contact_person_email=client_contact_person_email,
        created_by_org_id=created_by_org_id,
        start_date=start_date,
        end_date=end_date,
        state=state,
        pa_form_enabled=pa_form_enabled,
        added_client_confirmed=added_client_confirmed,
        project_creator_role=project_creator_role,
        added_client_can_view=added_client_can_view
    )
    rez = db.session.execute(query)
    project_id = rez.inserted_primary_key[0]

    query = (
        sa.select([tbl_projects])
        .where(tbl_projects.c.id == project_id)

    )
    rez = db.session.execute(query)
    return dict(rez.fetchone())


def create_project_user(db, *, external_id=None, project_id=None, **kwargs):
    if project_id is None:
        project_id = create_project(db)['id']
    project_users = db.meta.tables[PROJECT_USERS_TABLE]
    query = sa.insert(project_users).values(
        external_id=external_id or generate_qvarn_id('contracts'),
        project_id=project_id,
        **kwargs,
    )
    result = db.session.execute(query)
    return get_project_user(db, result.inserted_primary_key[0])


def get_project_user(db, project_user_id):
    project_users = db.meta.tables[PROJECT_USERS_TABLE]
    query = (
        sa.select([project_users])
        .where(project_users.c.id == project_user_id)
    )
    result = db.session.execute(query)
    project_user = result.fetchone()
    return dict(project_user) if project_user is not None else None


def create_status_report(db, *, external_id=None, status='500 OK',
                         generated_timestamp=None, interested_company_id=None,
                         company_id=None, json_=None, charge_reference=None,
                         used_providers=None):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]
    qry = (
        sa.insert(tbl_status_reports).values(
            external_id=(
                external_id
                or generate_qvarn_id(REPORT_RESOURCE_TYPE.replace('qvarn_', ''))
            ),
            status=status,
            generated_timestamp=generated_timestamp,
            interested_company_id=interested_company_id,
            company_id=company_id,
            charge_reference=charge_reference,
            used_providers=used_providers,
            json_=json_,
        )
    )
    result = db.session.execute(qry)
    query = (
        sa.select([tbl_status_reports])
        .where(tbl_status_reports.c.id == result.inserted_primary_key[0])

    )
    rez = db.session.execute(query)
    return dict(rez.fetchone())


def create_notification_report(
        db, *, external_id=None, generated_timestamp=None, period=None,
        to_timestamp=None, from_timestamp=None, statuses=None,
        company_ids=None, user_company_id=None, user_email=None,
        user_name=None, user_locale=None, user_report=None, qvarn_report=None,
):
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]

    qry = (
        sa.insert(tbl_notification_reports).values(
            external_id=(
                external_id or
                generate_qvarn_id(REPORT_RESOURCE_TYPE.replace('qvarn_', ''))
            ),
            generated_timestamp=generated_timestamp,
            period=period,
            to_timestamp=to_timestamp,
            from_timestamp=from_timestamp,
            statuses=statuses,
            company_ids=company_ids,
            user_company_id=user_company_id,
            user_email=user_email,
            user_name=user_name,
            user_locale=user_locale,
            user_report=user_report,
            qvarn_report=qvarn_report,
        )
    )

    rez = db.session.execute(qry)
    qry = (
        sa.select([tbl_notification_reports])
        .where(tbl_notification_reports.c.id == rez.inserted_primary_key[0])
    )
    rez = db.session.execute(qry)
    return dict(rez.fetchone())


def get_status_report(db, id):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    qry = (
        sa.select([tbl_status_reports])
        .where(tbl_status_reports.c.id == id)
    )

    rez = db.session.execute(qry)
    report = rez.fetchone()
    return dict(report) if report else None


def get_notification_report(db, id):
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]

    qry = (
        sa.select([tbl_notification_reports])
        .where(tbl_notification_reports.c.id == id)
    )

    rez = db.session.execute(qry)
    report = rez.fetchone()
    return dict(report) if report else None


def create_bulk_import_job(
    db, *, status='pending', project_id=None, interested_org_id=None, companies=(),
    imported=None, canceled=None, created_on=None, last_changed=None,
):
    if project_id is None:
        project_id = create_project(db)['id']
    bulk_import_jobs = db.meta.tables[BULK_IMPORT_JOBS_TABLE]
    query = sa.insert(bulk_import_jobs).values(
        status=status,
        project_id=project_id,
        interested_org_id=interested_org_id,
        companies=companies,
        imported=imported,
        canceled=canceled,
    )
    if created_on is not None:
        query = query.values(created_on=created_on)
    if last_changed is not None:
        query = query.values(last_changed=last_changed)
    result = db.session.execute(query)
    query = (
        sa.select([bulk_import_jobs])
        .where(bulk_import_jobs.c.id == result.inserted_primary_key[0])
    )
    result = db.session.execute(query)
    return dict(result.fetchone())


def create_preannouncement(db, *, status=PA_STATUS_CREATED,
                           created_by_supplier_id=None, for_supplier_id=None,
                           project_id=None, assigned_to_company_id=None,
                           assigned_to_supplier_id=None, assigned_to_time=None):

    tbl_preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]

    query = sa.insert(tbl_preannouncements).values(
        id=str(uuid.uuid4()),
        external_id=str(uuid.uuid4()),
        status=status,
        created_by_supplier_id=created_by_supplier_id,
        for_supplier_id=for_supplier_id,
        project_id=project_id,
        assigned_to_company_id=assigned_to_company_id,
        assigned_to_supplier_id=assigned_to_supplier_id,
        assigned_to_time=assigned_to_time,
    )
    rez = db.session.execute(query)
    preannouncement_id = rez.inserted_primary_key[0]

    query = (
        sa.select([tbl_preannouncements])
        .where(tbl_preannouncements.c.id == preannouncement_id)

    )
    rez = db.session.execute(query)
    return dict(rez.fetchone())


def create_pa_form(
    db, pa_id, *,
    buyer_gov_org_id='Some-buyer-id',
    buyer_id_type=None,
    buyer_country='N/A',
    company_gov_org_id='Some-company-id',
    company_id_type=None,
    company_country='N/A',
    has_permanent_establishment=None,
    is_one_man_company=None,
    has_collective_agreement=None,
    collective_agreement_name=None,
    foreman_is_on_site=None,
    foreman_first_name=None,
    foreman_last_name=None,
    foreman_phone_number=None,
    foreman_email=None,
    contract_type=None,
    contract_start_date=None,
    contract_end_date=None,
    contract_work_areas=None,
):
    tbl_pa_form = db.meta.tables[PREANNOUNCEMENT_FORMS_TABLE]
    tbl_preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]

    query = sa.insert(tbl_pa_form).values(
        id=str(uuid.uuid4()),
        external_id=str(uuid.uuid4()),
        pa_id=pa_id,
        company_gov_org_id=company_gov_org_id,
        company_id_type=company_id_type,
        company_country=company_country,
        has_permanent_establishment=has_permanent_establishment,
        is_one_man_company=is_one_man_company,
        has_collective_agreement=has_collective_agreement,
        collective_agreement_name=collective_agreement_name,
        buyer_gov_org_id=buyer_gov_org_id,
        buyer_id_type=buyer_id_type,
        buyer_country=buyer_country,
        foreman_is_on_site=foreman_is_on_site,
        foreman_first_name=foreman_first_name,
        foreman_last_name=foreman_last_name,
        foreman_phone_number=foreman_phone_number,
        foreman_email=foreman_email,
        contract_type=contract_type,
        contract_start_date=contract_start_date,
        contract_end_date=contract_end_date,
        contract_work_areas=contract_work_areas,
    )
    rez = db.session.execute(query)
    pa_form_id = rez.inserted_primary_key[0]

    query = (
        sa.select([tbl_pa_form])
        .where(tbl_pa_form.c.id == pa_form_id)
    )
    rez = db.session.execute(query)

    pa_update_query = (
        sa.update(tbl_preannouncements)
        .where(tbl_preannouncements.c.id == pa_id)
        .values({'active_pa_form': pa_form_id})
    )

    db.session.execute(pa_update_query)
    return dict(rez.fetchone())


def get_preannouncement(db, pa_id):
    tbl_preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]

    qry = (
        sa.select([tbl_preannouncements])
        .where(tbl_preannouncements.c.id == pa_id)
    )
    result = db.session.execute(qry)
    pa = result.fetchone()
    return dict(pa) if pa else None


def create_report_cache(
    db, *, key, value, type='statusreports_raw', expires_at=None,
    correlation_id=None, interested_org_id=None, provider=None,
):
    tbl_report_cache = db.meta.tables[REPORT_CACHE_TABLE]
    query = sa.insert(tbl_report_cache).values(
        external_id=str(uuid.uuid4()),
        key=key,
        value=value,
        type=type,
        expires_at=expires_at,
        correlation_id=correlation_id,
        interested_org_id=interested_org_id,
        provider=provider,
    )
    result = db.session.execute(query)
    query = (
        sa.select([tbl_report_cache])
        .where(tbl_report_cache.c.id == result.inserted_primary_key[0])

    )
    res = db.session.execute(query)
    return dict(res.fetchone())


def get_report_cache(db, id):
    tbl_report_cache = db.meta.tables[REPORT_CACHE_TABLE]

    qry = (
        sa.select([tbl_report_cache])
        .where(tbl_report_cache.c.id == id)
    )
    res = db.session.execute(qry)
    report_cache = res.fetchone()
    return dict(report_cache) if report_cache else None


def create_cs_account(db, org_id=None, person_id=None, username=None,
                      password=None, state='pending', comment=None, created_on=None):
    if org_id is None:
        org_id = generate_qvarn_id('orgs')
    cs_accounts = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]
    insert_params = dict(
        org_id=org_id,
        person_id=person_id,
        username=username,
        password=password,
        state=state,
    )
    if created_on:
        insert_params.update(dict(created_on=created_on))
    query = sa.insert(cs_accounts).values(insert_params)
    result = db.session.execute(query)
    cs_account_id = result.inserted_primary_key[0]
    create_cs_account_history(
        db=db,
        creditsafe_account_id=cs_account_id,
        state=state,
        org_id=org_id,
        person_id=person_id,
        changed_by_person_id=person_id,
        username=username,
        comment=comment,
        created_on=created_on,
    )
    query = (
        sa.select([cs_accounts])
        .where(cs_accounts.c.id == cs_account_id)
    )
    res = db.session.execute(query)
    return dict(res.fetchone())


def create_cs_account_history(
        db, creditsafe_account_id, state='pending', org_id=None, person_id=None,
        username=None, comment=None, changed_by_person_id=None, created_on=None):
    if org_id is None:
        org_id = generate_qvarn_id('orgs')
    cs_account_history = db.meta.tables[CREDITSAFE_ACCOUNT_HISTORY_TABLE]
    insert_params = dict(
        creditsafe_account_id=creditsafe_account_id,
        org_id=org_id,
        person_id=person_id,
        changed_by_person_id=changed_by_person_id,
        username=username,
        state=state,
        comment=comment,
    )
    if created_on:
        insert_params.update(dict(created_on=created_on))
    query = sa.insert(cs_account_history).values(insert_params)
    result = db.session.execute(query)
    cs_account_history_id = result.inserted_primary_key[0]

    query = (
        sa.select([cs_account_history])
        .where(cs_account_history.c.id == cs_account_history_id)

    )
    res = db.session.execute(query)
    return dict(res.fetchone())


def create_project_comment(
    db,
    project_id=None,
    supplier_id=None,
    org_id=None,
    comment="Test comment",
    created_by_org_id=None,
    created_by_person_id=None,
):
    # Create a project if not provided
    if project_id is None:
        project = create_project(db)
        project_id = project["id"]

    # Create a supplier if not provided
    if supplier_id is None:
        supplier = create_supplier(db, project_id=project_id)
        supplier_id = supplier["id"]

    if org_id is None:
        org_id = "f392-" + str(uuid.uuid4())

    if created_by_org_id is None:
        created_by_org_id = "f392-" + str(uuid.uuid4())

    if created_by_person_id is None:
        created_by_person_id = "0035-" + str(uuid.uuid4())

    # Insert into the project_supplier_comments table
    project_supplier_comments = db.meta.tables["project_supplier_comments"]
    query = project_supplier_comments.insert().values(
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        comment=comment,
        created_by_org_id=created_by_org_id,
        created_by_person_id=created_by_person_id,
    )
    result = db.session.execute(query)
    comment_id = result.inserted_primary_key[0]

    # Get the created comment
    query = project_supplier_comments.select().where(
        project_supplier_comments.c.id == comment_id
    )
    result = db.session.execute(query)
    return dict(result.fetchone())


def create_comment(
    db, project_id=None, supplier_id=None, **kwargs
) -> ProjectSupplierComment:
    if not project_id:
        project_id = create_project(db)["id"]
    if not supplier_id:
        supplier_id = create_supplier(db, project_id=project_id)["id"]

    comment = {
        "project_id": project_id,
        "supplier_id": supplier_id,
        "org_id": "test-org-id",
        "comment": "Test comment",
        "created_by_org_id": "test-org-id",
        "created_by_person_id": "test-person-id",
    }
    for key, value in kwargs.items():
        comment[key] = value
    return create_project_supplier_comment(db, comment)


def create_comment_viewer(db, comment_id, person_id):
    project_comment_viewers = db.meta.tables[PROJECT_COMMENT_VIEWERS_TABLE]
    query = project_comment_viewers.insert().values(
        project_comment_id=comment_id, read_by_person_id=person_id
    )
    result = db.session.execute(query)
    viewer_id = result.inserted_primary_key[0]

    # Get the created viewer
    query = project_comment_viewers.select().where(
        project_comment_viewers.c.id == viewer_id
    )
    result = db.session.execute(query)
    return dict(result.fetchone())
