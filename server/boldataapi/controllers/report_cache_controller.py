import json
from logging import Logger
from typing import List

from bottle import request

from boldataapi import exceptions
from boldataapi.schema import (
    ReportCacheValidator,
    schema_existing_report_cache,
    schema_new_report_cache,
)
from boldataapi.services.report_cache import (
    create_report_cache,
    delete_report_cache,
    get_report_cache,
    query_report_cache,
    update_report_cache,
)


class ReportCacheController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/report_cache/<external_id>',
                'method': 'GET',
                'callback': self.get_report_cache,
                'require_auth': True,
                'scopes': ['uapi_data_cache_get']
            },
            {
                'path': '/report_cache',
                'method': 'POST',
                'callback': self.post_report_cache,
                'require_auth': True,
                'scopes': ['uapi_data_cache_post'],
            },
            {
                'path': '/report_cache/query',
                'method': 'GET',
                'callback': self.query_report_cache,
                'require_auth': True,
                'scopes': ['uapi_data_cache_search_id_get'],
            },
            {
                'path': '/report_cache/<external_id>',
                'method': 'PUT',
                'callback': self.put_report_cache,
                'require_auth': True,
                'scopes': ['uapi_data_cache_id_put'],
            },
            {
                'path': '/report_cache/<external_id>',
                'method': 'DELETE',
                'callback': self.delete_report_cache,
                'require_auth': True,
                'scopes': ['uapi_data_cache_id_delete'],
            },
        ]

    def get_report_cache(self, external_id):
        """Get a report_cache entry by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/report_cache_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: report cache entry
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/report_cache'
        """
        self._logger.info('Getting report_cache %s', external_id)

        report_cache = get_report_cache(request.db, external_id=external_id)
        if not report_cache:
            raise exceptions.NotFound(external_id=external_id)

        report_cache_external_id = report_cache.pop('external_id')
        report_cache['id'] = report_cache_external_id
        return report_cache

    def post_report_cache(self):
        """Create a new report_cache entry.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_report_cache'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          201:
            description: the newly created report cache entry
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/report_cache'
        """
        self._logger.info('Creating a new report_cache')

        validator = ReportCacheValidator(schema_new_report_cache)
        if not validator.validate(request.json):
            self._logger.info('report_cache data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request.json)

        payload = {
            'correlation_id': data.get('correlation_id'),
            'expires_at': data.get('expires_at'),
            'interested_org_id': data.get('interested_org_id'),
            'key': data.get('key'),
            'provider': data.get('provider'),
            'type': data.get('type'),
            'value': data.get('value'),
        }
        report_cache = create_report_cache(request.db, payload)

        self._logger.info('New report_cache created %s', report_cache['external_id'])

        report_cache_external_id = report_cache.pop('external_id')
        report_cache['id'] = report_cache_external_id
        return report_cache

    def query_report_cache(self):
        """Return a list of report_cache entries that match a query.
        ---
        parameters:
          - name: q
            in: query
            description: |
              search query in URL-encoded JSON

              The keys of the query object are database column names optionally suffixed with an
              operator ("eq"/"ne"/"lt"/"gt"/"le"/"ge"/"any") following a "__".
              The values are search values for that field (or a list of search values for the
              "any" operator).  When searching with multiple conditions, they are ANDed together.

              Database columns you can search on are:
              - id (database ID aka external ID)
              - expires_at
              - correlation_id
              - interested_org_id
              - provider
              - key
              - value
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >
                        search by cache ID (aka external_id)
                    expires_at:
                      type: string
                      description: >
                        search by the UTC timestamp (in ISO-8601 format) of the time
                        when cache entry is set to expire
                    correlation_id:
                      type: string
                      description: >
                        search by request id used to relate cache entries created from a single call
                        Supposed to be used in the Middle Layer
                    interested_org_id:
                      type: string
                      description: >
                        search by org resource ID which retrieved the report
                    key:
                      type: string
                      description: >
                        search by cache key (org resource ID or report provider token)
                    provider:
                      type: string
                      description: >
                        search by report provider name
                    type:
                      type: string
                      description: >
                        search by type of cache `statusreports_raw` or `statusreports_parsed`
                    value:
                      type: string
                      description: >
                        search by report raw data string
        responses:
          200:
            description: List of search results
            content:
              application/json:
                schema:
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - report_cache
                      description: always "report_cache"
                    resources:
                      type: array
                      description: list of matching "report_cache" entries
                      items:
                        $ref: '#/components/schemas/report_cache'
                examples:
                  no_matches:
                    summary: Result when nothing matches
                    value:
                      {
                        "resource_type": "report_cache",
                        "resources": []
                      }
                  matches_exist:
                    summary: Result when something matches
                    value:
                      {
                        "resource_type": "report_cache",
                        "resources": [
                          {
                            "id": "597a17d9-d8a2-4224-aae0-c94bdc6e7985",
                            "expires_at": "2013-01-14T00:00:00",
                            "correlation_id":
                              "9606772e-09b4-4f01-9e5e-894e86f2a569",
                            "interested_org_id":
                              "f77b-b1d032a0281d024e1ad8e33db7a23ca1-2642fed5",
                            "key": "test-key-new",
                            "provider": "creditsafe_ggs",
                            "type": "statusreports_parsed",
                            "value": "{some key 1: some new value 12,
                                       some key 2: some new value 21,
                                       some key 3: some new value 23}"
                          }
                        ]
                      }
          400:
            description: Error in query (e.g. JSON parse error)
            content:
              application/json:
                example:
                  {
                    "message": "Could not parse search condition",
                    "error_code": "BadSearchCondition"
                  }
        """
        try:
            query = json.loads(request.query.q)
        except ValueError:
            raise exceptions.BadSearchCondition()
        result = query_report_cache(request.db, query)
        return {
            'resource_type': 'report_cache',
            'resources': result,
        }

    def delete_report_cache(self, external_id: str):
        """Delete a report_cache entry by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/report_cache_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info('Deleting report_cache %s', external_id)

        report_cache = get_report_cache(request.db, external_id=external_id)
        if not report_cache:
            raise exceptions.NotFound(external_id=external_id)
        delete_report_cache(request.db, report_cache['id'])

        return {}

    def put_report_cache(self, external_id: str): # noqa C901
        """Update a report_cache entry by ID.

        Fields that are omitted from the request will retain their old values.
        ---
        parameters:
          - $ref: '#/components/parameters/report_cache_external_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_report_cache'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the updated report cache entry
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/report_cache'
        """
        self._logger.info('Updating report_cache %s', external_id)

        # update resource by pk, not qvarn_id
        report_cache = get_report_cache(request.db, external_id=external_id)
        if not report_cache:
            self._logger.info('report_cache %s was not found.', external_id)
            raise exceptions.NotFound(external_id=external_id)

        validator = ReportCacheValidator(schema_existing_report_cache)
        if not validator.validate(request.json):
            self._logger.info('report_cache data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = request.json

        payload = {}

        if data.get('correlation_id'):
            payload['correlation_id'] = data['correlation_id']

        if data.get('expires_at'):
            payload['expires_at'] = data['expires_at']

        if data.get('interested_org_id'):
            payload['interested_org_id'] = data['interested_org_id']

        if data.get('key'):
            payload['key'] = data['key']

        if data.get('provider'):
            payload['provider'] = data['provider']

        if data.get('type'):
            payload['type'] = data['type']

        if data.get('value'):
            payload['value'] = data['value']

        update_report_cache(request.db, report_cache['id'], payload)

        updated_report_cache = get_report_cache(request.db,
                                                report_cache_id=report_cache['id'])
        report_cache_external_id = updated_report_cache.pop('external_id')
        updated_report_cache['id'] = report_cache_external_id
        return updated_report_cache
