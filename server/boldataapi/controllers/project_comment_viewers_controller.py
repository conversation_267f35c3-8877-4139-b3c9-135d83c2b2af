from logging import Logger
from typing import Any, Dict, List

from bottle import request

from boldataapi import exceptions
from boldataapi.schema import (
    is_valid_uuid,
    ProjectCommentViewerValidator,
    schema_new_project_comment_viewer,
)
from boldataapi.services.project_comment_viewers import (
    create_project_comment_viewer,
    delete_project_comment_viewer,
    get_project_comment_viewer_by_comment_id_and_person_id,
)
from boldataapi.services.project_supplier_comments import (
    get_project_supplier_comment,
)


class ProjectCommentViewersController:
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/project_comment_viewers/query',
                'method': 'GET',
                'callback': self.query_project_comment_viewers,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_get'],
            },
            {
                'path': '/project_comment_viewers',
                'method': 'POST',
                'callback': self.post_project_comment_viewer,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_post'],
            },
            {
                'path': '/project_comment_viewers/<viewer_id>',
                'method': 'DELETE',
                'callback': self.delete_project_comment_viewer,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_delete'],
            },
        ]

    def post_project_comment_viewer(self) -> Dict[str, Any]:
        """Add a new project comment viewer.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_project_comment_viewer'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          201:
            description: The newly created project comment viewer
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_comment_viewer'
        """
        self._logger.info("Creating a new project comment viewer")

        validator = ProjectCommentViewerValidator(schema_new_project_comment_viewer)

        if not validator.validate(request.json):
            self._logger.info("Project comment viewer data validation failed.")
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request.json)

        comment_id = data.get("comment_id")
        person_id = data.get("read_by_person_id")

        # Check if the comment exists
        comment = get_project_supplier_comment(request.db, comment_id)
        if not comment:
            self._logger.info("Project comment %s does not exist", comment_id)
            raise exceptions.ParameterValidationFailed(
                comment_id=["Comment does not exist"]
            )

        # Check if the comment is already viewed by this person
        existing_viewer = get_project_comment_viewer_by_comment_id_and_person_id(
            request.db,
            comment_id=comment_id,
            person_id=person_id,
        )

        if existing_viewer:
            self._logger.info(
                "Viewer record already exists for comment %s and person %s",
                comment_id,
                person_id,
            )
            raise exceptions.ParameterValidationFailed(
                comment_id=[
                    "A viewer record already exists for this comment and person"
                ]
            )

        viewer = create_project_comment_viewer(
            request.db,
            comment_id=comment_id,
            person_id=person_id,
        )
        self._logger.info("New project comment viewer created %s", viewer["id"])

        return viewer

    def delete_project_comment_viewer(self, viewer_id: str) -> Dict[str, Any]:
        """Delete a project comment viewer.

        ---
        parameters:
          - name: viewer_id
            in: path
            required: true
            schema:
              type: string
              format: uuid
            description: The ID of the project comment viewer
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info("Deleting project comment viewer %s", viewer_id)
        if not is_valid_uuid(viewer_id):
            raise exceptions.ParameterValidationFailed(
                viewer_id=f"{viewer_id} is not a UUID"
            )

        result = delete_project_comment_viewer(request.db, viewer_id)
        if not result:
            self._logger.info("Project comment viewer %s was not found.", viewer_id)
            raise exceptions.NotFound(viewer_id=viewer_id)

        self._logger.info("Successfully deleted project comment viewer %s", viewer_id)
        return {}

    def query_project_comment_viewers(self) -> Dict[str, Any]:
        """Query project comment viewers by comment_id and/or person_id. At least one needs to be
        provided.

        ---
        parameters:
          - name: comment_id
            in: query
            required: false
            schema:
              type: string
            description: Filter by project comment ID
          - name: person_id
            in: query
            required: false
            schema:
              type: string
              format: uuid
            description: Filter by person ID
        responses:
          200:
            description: List of project comment viewers matching the query
            content:
              application/json:
                schema:
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - project_comment_viewer
                      description: always "project_comment_viewer"
                    resources:
                      type: array
                      description: list of matching project users
                      items:
                        $ref: '#/components/schemas/project_comment_viewer'
          400:
            $ref: '#/components/responses/bad_request'
        """
        comment_id = request.query.get("comment_id")
        person_id = request.query.get("person_id")

        if not comment_id and not person_id:
            raise exceptions.ParameterValidationFailed(
                message="At least one of comment_id or person_id must be provided"
            )
        if comment_id and not is_valid_uuid(comment_id):
            raise exceptions.ParameterValidationFailed(
                comment_id=f"{comment_id} is not a UUID"
            )

        viewers = get_project_comment_viewer_by_comment_id_and_person_id(
            request.db,
            comment_id=comment_id,
            person_id=person_id,
        )
        return {"resource_type": "project_comment_viewer", "resources": viewers}
