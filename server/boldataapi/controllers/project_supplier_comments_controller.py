from logging import Logger
from typing import Any, Dict, List

from bottle import request

from boldataapi import exceptions
from boldataapi.schema import (
    is_valid_uuid,
    ProjectSupplierCommentValidator,
    schema_new_project_supplier_comment,
    schema_update_project_supplier_comment,
)
from boldataapi.services.bol_suppliers import get_supplier
from boldataapi.services.project_supplier_comments import (
    create_project_supplier_comment,
    delete_project_supplier_comment,
    get_project_supplier_comment,
    mark_comment_as_deleted,
    ProjectSupplierComment,
    update_project_supplier_comment,
)
from boldataapi.services.projects import get_project_id


class ProjectSupplierCommentsController:
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/project_supplier_comments/<comment_id>',
                'method': 'GET',
                'callback': self.get_project_supplier_comment,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_get'],
            },
            {
                'path': '/project_supplier_comments',
                'method': 'POST',
                'callback': self.post_project_supplier_comment,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_post'],
            },
            {
                'path': '/project_supplier_comments/<comment_id>',
                'method': 'PATCH',
                'callback': self.patch_project_supplier_comment,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_put'],
            },
            {
                'path': '/project_supplier_comments/<comment_id>',
                'method': 'DELETE',
                'callback': self.delete_project_supplier_comment,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_delete'],
            },
        ]

    def get_project_supplier_comment(self, comment_id: str) -> ProjectSupplierComment:
        """Get a project supplier comment by ID.

        ---
        parameters:
          - name: comment_id
            in: path
            required: true
            schema:
              type: string
              format: uuid
            description: The ID of the comment
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: The project supplier comment
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_supplier_comment'
        """
        if not is_valid_uuid(comment_id):
            raise exceptions.ParameterValidationFailed(
                comment_id=f"{comment_id} is not a UUID"
            )
        comment = get_project_supplier_comment(request.db, comment_id)
        if comment is None:
            raise exceptions.NotFound(comment_id=comment_id)
        return comment

    def post_project_supplier_comment(self) -> ProjectSupplierComment:
        """Create a new project supplier comment.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_project_supplier_comment'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            description: The provided project_id or supplier_id does not exist
            content:
              application/json:
                example:
                  {
                    "message": "Not found",
                    "error_code": "NotFound",
                    "error": {
                      "project_id": "a55f-7521303fbb16f824c9b9a2459bc2fedc-3cb8e412"
                    }
                  }
          201:
            description: The newly created project supplier comment
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_supplier_comment'
        """

        self._logger.info("Creating a new project supplier comment")

        validator = ProjectSupplierCommentValidator(schema_new_project_supplier_comment)
        if not validator.validate(request.json):
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request.json)

        # Validate project_id
        project_id = get_project_id(request.db, data["project_id"])
        if not project_id:
            raise exceptions.NotFound(project_id=data["project_id"])

        # Validate supplier_id
        supplier = get_supplier(request.db, external_id=data["supplier_id"])
        if not supplier:
            raise exceptions.NotFound(supplier_id=data["supplier_id"])
        if not supplier["project_resource_id"] == data["project_id"]:
            raise exceptions.BadRequest(
                project_id=["Supplier does not belong to the specified project"],
                supplier_id=["Supplier does not belong to the specified project"],
            )

        # Store the internal IDs (i.e. the ones used as foreign keys), not the external ones
        data["supplier_id"] = supplier["id"]
        data["project_id"] = project_id

        comment = create_project_supplier_comment(request.db, data)
        return comment

    def patch_project_supplier_comment(self, comment_id: str) -> ProjectSupplierComment:
        """Update a project supplier comment.

        At least one of comment or is_deleted must be provided.

        ---
        parameters:
          - name: comment_id
            in: path
            required: true
            schema:
              type: string
              format: uuid
            description: The ID of the comment
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_project_supplier_comment'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: The updated project supplier comment
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_supplier_comment'
        """
        self._logger.info("Updating project supplier comment %s", comment_id)
        if not is_valid_uuid(comment_id):
            raise exceptions.ParameterValidationFailed(
                comment_id=f"{comment_id} is not a UUID"
            )

        validator = ProjectSupplierCommentValidator(
            schema_update_project_supplier_comment
        )
        if not validator.validate(request.json):
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)

        if ("comment" in data) == ("is_deleted" in data):
            raise exceptions.BadRequest(
                comment=["Exactly one of comment and is_deleted must be provided"],
                is_deleted=["Exactly one of comment and is_deleted must be provided"],
            )

        comment = get_project_supplier_comment(request.db, comment_id)
        if comment is None:
            raise exceptions.NotFound(comment_id=comment_id)
        if comment["is_deleted"]:
            raise exceptions.BadRequest(
                comment_id="Cannot update comment marked as deleted"
            )

        if "comment" in data:
            comment = update_project_supplier_comment(
                request.db,
                comment["id"],
                data["comment"],
                data["updating_org_id"],
                data["updating_person_id"],
            )
        if "is_deleted" in data:
            comment = mark_comment_as_deleted(
                request.db,
                comment["id"],
                data["updating_org_id"],
                data["updating_person_id"],
            )
        return comment

    def delete_project_supplier_comment(self, comment_id: str) -> Dict[str, Any]:
        """Delete a project supplier comment.

        Note that this endpoint permanently deletes the comment from the database.\
        To mark a comment as deleted but keep it in the database, use the PUT endpoint\
        with is_deleted=true.

        ---
        parameters:
          - name: comment_id
            in: path
            required: true
            schema:
              type: string
              format: uuid
            description: The ID of the comment
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info("Deleting project supplier comment %s", comment_id)
        if not is_valid_uuid(comment_id):
            raise exceptions.ParameterValidationFailed(
                comment_id=f"{comment_id} is not a UUID"
            )

        comment = get_project_supplier_comment(request.db, comment_id)
        if comment is None:
            raise exceptions.NotFound(comment_id=comment_id)

        delete_project_supplier_comment(request.db, comment_id)
        return {}
