import json
from copy import deepcopy
from logging import Logger
from typing import Any, Dict, List

from bottle import request

from boldataapi import exceptions
from boldataapi.featureflags import feature_active
from boldataapi.schema import (
    ProjectUserValidator,
    schema_new_project_user,
    schema_update_project_user,
)
from boldataapi.services.project_users import (
    create_project_user,
    delete_project_user,
    get_project_user,
    query_project_users,
    update_project_user,
)
from boldataapi.services.projects import get_project_id


class ProjectUsersController:

    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/project_users/<external_id>',
                'method': 'GET',
                'callback': self.get_project_user,
                'require_auth': True,
                # in the days of yore, we stored project users as <PERSON>varn's contracts
                'scopes': ['bda_project_users_get'],
            },
            {
                'path': '/project_users',
                'method': 'POST',
                'callback': self.post_project_user,
                'require_auth': True,
                # in the days of yore, we stored project users as <PERSON><PERSON><PERSON>'s contracts
                'scopes': ['bda_project_users_post'],
            },
            {
                'path': '/project_users/<external_id>',
                'method': 'PUT',
                'callback': self.put_project_user,
                'require_auth': True,
                # in the days of yore, we stored project users as Qvarn's contracts
                'scopes': ['bda_project_users_put'],
            },
            {
                'path': '/project_users/<external_id>',
                'method': 'DELETE',
                'callback': self.delete_project_user,
                'require_auth': True,
                # in the days of yore, we stored project users as Qvarn's contracts
                'scopes': ['bda_project_users_delete'],
            },
            {
                'path': '/project_users/query',
                'method': 'GET',
                'callback': self.project_user_query,
                'require_auth': True,
                # in the days of yore, we stored project users as Qvarn's contracts
                'scopes': ['bda_project_users_search'],
            },
        ]

    def get_project_user(self, external_id: str) -> Dict[str, Any]:
        """Get a project user by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/project_user_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the project user
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_user'
        """
        self._logger.info('Getting project user %s', external_id)

        project_user = get_project_user(request.db, external_id=external_id)
        if project_user is None:
            raise exceptions.NotFound(external_id=external_id)

        project_user['id'] = project_user.pop('external_id')
        return project_user

    def post_project_user(self) -> Dict[str, Any]:
        """Create a new project user

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_project_user'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          201:
            description: the newly created project user
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_user'
        """
        self._logger.info('Creating a new project user')

        validator = ProjectUserValidator(schema_new_project_user)

        # Schema for a new project user varies with feature flag
        if feature_active("person_id_for_project_users"):
            schema_new_project_user_with_person_id = deepcopy(schema_new_project_user)
            del schema_new_project_user_with_person_id["user_account_id"]
            schema_new_project_user_with_person_id["person_id"]["required"] = True
            validator = ProjectUserValidator(schema_new_project_user_with_person_id)

        if not validator.validate(request.json):
            self._logger.info('Project user data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)

        if data.get('id') and get_project_user(request.db, external_id=data['id']) is not None:
            raise exceptions.ParameterValidationFailed(id=['already exists'])

        if not (data.get('person_id') or data.get('user_account_id')):
            raise exceptions.ParameterValidationFailed(
                person_id=['One of user_account_id and person_id must be provided'],
                user_account_id=['One of user_account_id and person_id must be provided']
            )

        project_id = get_project_id(request.db, data.get('project_id'))
        if project_id is None:
            # Other endpoints tend to use NotFound(project_id=project_id), which I don't like.
            # Returning 404 makes it look like the entire /project_user endpoint doesn't exist,
            # when the actual error is a bad value in the json data.
            raise exceptions.ParameterValidationFailed(project_id=['not found'])
        payload = {
            'external_id': data.get('id'),
            'project_id': project_id,
            'role': data.get('role'),
            'notify': data.get('notify'),
            'user_account_id': data.get('user_account_id'),
            'person_id': data.get('person_id'),
            'represented_company_id': data.get('represented_company_id'),
        }

        project_user = create_project_user(request.db, payload)
        self._logger.info('New project user created %s for project %s', project_user['id'],
                          data['project_id'])
        project_user['id'] = project_user.pop('external_id')
        return project_user

    def put_project_user(self, external_id: str) -> Dict[str, Any]:
        """Update a project user

        Fields that are omitted from the request will retain their old values.
        ---
        parameters:
          - $ref: '#/components/parameters/project_user_external_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_project_user'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the updated project user
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_user'
        """
        self._logger.info('Updating project user %s', external_id)

        project_user = get_project_user(request.db, external_id=external_id)
        if project_user is None:
            raise exceptions.NotFound(external_id=external_id)

        validator = ProjectUserValidator(schema_update_project_user)
        # Schema for update varies with feature flag
        if feature_active("person_id_for_project_users"):
            schema_update_project_user_with_person_id = deepcopy(
                schema_update_project_user
            )
            del schema_update_project_user_with_person_id["user_account_id"]
            validator = ProjectUserValidator(schema_update_project_user_with_person_id)

        if not validator.validate(request.json):
            self._logger.info('Project user data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)
        self._validate_read_only_fields(data, project_user)

        payload = {}
        if data.get('role'):
            payload['role'] = data['role']
        if data.get('notify') is not None:
            payload['notify'] = data['notify']
        # Remove this when we no longer need to run the person_id migration
        # so that person_id cannot be changed any longer
        if data.get("person_id") is not None:
            payload["person_id"] = data["person_id"]
        update_project_user(request.db, project_user['id'], payload)
        updated_project_user = get_project_user(request.db, project_user_id=project_user['id'])
        updated_project_user['id'] = updated_project_user.pop('external_id')
        return updated_project_user

    def _validate_read_only_fields(
        self, data: Dict[str, Any], existing_project_user: Dict[str, Any]
    ) -> None:
        errors = {}
        if "id" in data and data["id"] != existing_project_user["external_id"]:
            errors["id"] = ["read-only field"]
        for field in (
            "project_id",
            "user_account_id",
            "represented_company_id",
            # "person_id", add this when we no longer need to run the person_id migration
        ):
            if field in data and data[field] != existing_project_user[field]:
                errors[field] = ["read-only field"]
        if errors:
            raise exceptions.ParameterValidationFailed(**errors)
        return None

    def delete_project_user(self, external_id: str) -> Dict[str, Any]:
        """Delete a project user

        ---
        parameters:
          - $ref: '#/components/parameters/project_user_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info('Deleting project user %s', external_id)

        project_user = get_project_user(request.db, external_id=external_id)
        if project_user is None:
            raise exceptions.NotFound(external_id=external_id)
        delete_project_user(request.db, project_user['id'])
        return {}

    def project_user_query(self) -> Dict[str, Any]:
        """Return a list of project users that match a query.

        ---
        parameters:
          - name: q
            in: query
            description: |
              search query in URL-encoded JSON

              The keys of the query object are database column names optionally suffixed with an
              operator ("eq"/"ne"/"lt"/"gt"/"le"/"ge"/"any") following a "__".
              The values are search values for that field (or a list of search values for the
              "any" operator).  When searching with multiple conditions, they are ANDed together.

              Database columns you can search on are:
              - id (database ID aka external ID)
              - project_id (database ID aka external ID)
              - role (leader/manager/member)
              - notify (true/false)
              - user_account_id (database ID)
              - person_id (database ID from the User Account API)
              - represented_company_id (database ID)
            required: true
            content:
              application/json:
                # XXX: swagger-ui fails to show this schema anywhere, hence
                # it's essentially replicated in plain text in the description
                # field above
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >
                        search by project user database ID (aka external ID)

                        There's really no reason to search by ID instead of using a simple HTTP GET.
                    project_id:
                      type: string
                      description: search by project ID (database ID)
                    user_account_id:
                      type: string
                      description: search by user account ID (database ID of the user contract).\
Deprecated in favor of person_id.
                    person_id:
                      type: string
                      description: search by person ID (ID of the person provided by the User \
Account API)
                    represented_company_id:
                      type: string
                      description: >
                        search by user's represented company (database ID aka external ID)
                    role:
                      type: string
                      description: search by user's role
                      enum:
                        - member
                        - manager
                        - leader
                    notify:
                      type: boolean
                      description: search for users that have requested (or not) email notifications
            # XXX: editor.swagger.io insists that 'examples' is wrong here and it should be
            # indented further, into content["application/json"].schema.examples, but if I do that,
            # then swagger-ui fails to show the examples!
            examples:
              simple:
                summary: a simple query on one field
                value:
                  {"project_id": "68ba-8bd2e7c886df37cee70fd2aa6461662b-727bd523"}
                description: >
                  Searches for project users in a particular project.
              multiple_enum_values:
                summary: a simple query on a single field with multiple allowed values
                value:
                  {"role": ["manager", "leader"]}
                description: >
                  Searches for users who are either managers or leaders
        responses:
          200:
            description: List of search results
            content:
              application/json:
                schema:
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - project_user
                      description: always "project_user"
                    resources:
                      type: array
                      description: list of matching project users
                      items:
                        $ref: '#/components/schemas/project_user'
          400:
            description: Error in query (e.g. JSON parse error)
            content:
              application/json:
                example:
                  {
                    "message": "Could not parse search condition",
                    "error_code": "BadSearchCondition"
                  }
        """
        try:
            query = json.loads(request.query.q)
        except ValueError:
            raise exceptions.BadSearchCondition()
        result = query_project_users(request.db, query)
        return {
            'resource_type': 'project_user',
            'resources': [
                dict(project_user, id=project_user.pop('external_id'))
                for project_user in result
            ]
        }
