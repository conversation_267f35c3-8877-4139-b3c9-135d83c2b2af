import inspect
from logging import Logger
from typing import List

import bottleswagger
import yaml
from bottle import HTTPResponse, request, response
from bottleswagger.bottle_swagger import (
    BOTTLE_PATH_RX,
    convert_bottle_route_rule_to_swagger_path,
)

from boldataapi.services.bol_suppliers import (
    SWAGGER_NEW_SUPPLIER_SCHEMA,
    SWAG<PERSON>R_SUPPLIER_SCHEMA,
    SWAGGER_UPDATE_SUPPLIER_SCHEMA,
)
from boldataapi.services.bulk_import_jobs import (
    SWAGGER_BULK_IMPORT_JOB_EXAMPLE_DONE,
    SWAGGER_BULK_IMPORT_JOB_EXAMPLE_IN_PROGRESS,
    SWAGGER_BULK_IMPORT_JOB_EXAMPLE_PENDING,
    SWAGGER_BULK_IMPORT_JOB_SCHEMA,
    SWAGGER_NEW_BULK_IMPORT_JOB_SCHEMA,
    SWAGGER_UPDATE_BULK_IMPORT_JOB_SCHEMA,
)
from boldataapi.services.companies import SWAGGER_COMPANY_LIST_SCHEMA
from boldataapi.services.creditsafe_accounts import (
    SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITH_HISTORY,
    SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITHOUT_HISTORY,
    SWAGGER_NEW_CREDITSAFE_ACCOUNT_SCHEMA,
    SWAGGER_UPDATE_CREDITSAFE_ACCOUNT_SCHEMA,
)
from boldataapi.services.notification_reports import (
    SWAGGER_NEW_NOTIFICATION_REPORT_SCHEMA,
    SWAGGER_NOTIFICATION_REPORT_SCHEMA_QVARN_COMPAT,
    SWAGGER_UPDATE_NOTIFICATION_REPORT_SCHEMA,
)
from boldataapi.services.preannouncements import (
    SWAGGER_NEW_PREANNOUNCEMENT_SCHEMA,
    SWAGGER_PREANNOUNCEMENT_SCHEMA,
    SWAGGER_PREANNOUNCEMENT_SCHEMA_WITH_FORM_DATA,
    SWAGGER_PREANNOUNCEMENT_SCHEMA_WITHOUT_ACTIVE_PA_FORM,
    SWAGGER_UPDATE_PREANNOUNCEMENT_SCHEMA_WITH_NEW_FORM_DATA,
    SWAGGER_UPDATE_PREANNOUNCEMENT_SCHEMA_WITH_UPDATED_FORM_DATA,
)
from boldataapi.services.project_comment_viewers import (
    SWAGGER_NEW_PROJECT_COMMENT_VIEWER_SCHEMA,
    SWAGGER_PROJECT_COMMENT_VIEWER_SCHEMA,
)
from boldataapi.services.project_supplier_comments import (
    SWAGGER_NEW_PROJECT_SUPPLIER_COMMENT_SCHEMA,
    SWAGGER_PROJECT_SUPPLIER_COMMENT_SCHEMA,
    SWAGGER_UPDATE_PROJECT_SUPPLIER_COMMENT_SCHEMA,
)
from boldataapi.services.project_users import (
    SWAGGER_NEW_PROJECT_USER_SCHEMA,
    SWAGGER_PROJECT_USER_SCHEMA,
    SWAGGER_UPDATE_PROJECT_USER_SCHEMA,
)
from boldataapi.services.projects import (
    SWAGGER_NEW_PROJECT_SCHEMA,
    SWAGGER_PROJECT_LIST_SCHEMA,
    SWAGGER_PROJECT_SCHEMA,
    SWAGGER_PROJECT_SCHEMA_QVARN_COMPAT,
    SWAGGER_PROJECT_STATE_SCHEMA,
    SWAGGER_PROJECT_SUPPLIER_LIST_SCHEMA,
    SWAGGER_UPDATE_PROJECT_SCHEMA,
)
from boldataapi.services.report_accesses import (
    SWAGGER_NEW_REPORT_ACCESS_SCHEMA,
    SWAGGER_REPORT_ACCESS_SCHEMA,
    SWAGGER_UPDATE_REPORT_ACCESS_SCHEMA,
)
from boldataapi.services.report_cache import (
    SWAGGER_NEW_REPORT_CACHE_SCHEMA,
    SWAGGER_REPORT_CACHE_SCHEMA,
    SWAGGER_UPDATE_REPORT_CACHE_SCHEMA,
)
from boldataapi.services.status_reports import (
    SWAGGER_NEW_STATUS_REPORT_SCHEMA,
    SWAGGER_STATUS_REPORT_HISTORIC_SCHEMA,
    SWAGGER_STATUS_REPORT_JSON_SCHEMA,
    SWAGGER_STATUS_REPORT_SCHEMA,
    SWAGGER_STATUS_REPORT_SCHEMA_QVARN_COMPAT,
    SWAGGER_STATUS_REPORT_SCHEMA_WITH_JSON,
    SWAGGER_UPDATE_STATUS_REPORT_SCHEMA,
)
from boldataapi.version import get_bda_version


SWAGGER_TEMPLATE = yaml.safe_load("""
---
# We would like to use OAPI 3.1.0 for prefixItems in /bulk_import_jobs schemas,
# but Swagger UI doesn't support it yet:
# https://github.com/swagger-api/swagger-ui/issues/5891
##openapi: 3.1.0

servers:
  - url: "/api/v1/boldata/"
    description: This server
  - url: "http://localhost:8070/api/v1/boldata/"
    description: The BDA used by BOL's local development environment
  - url: "https://bol-data-api.alpha.vaultit.org/api/v1/boldata/"
    description: Alpha BDA
  - url: "https://bol-data-api.beta.vaultit.org/api/v1/boldata/"
    description: Beta BDA

components:

  parameters:
    supplier_external_id:
      name: external_id
      in: path
      schema:
        type: string
      required: true
      description: database ID (aka external ID) of the supplier
      example: bb72-749f75606c4331903b012118ed8c449d-0fe20015
    preannouncement_external_id:
      name: external_id
      in: path
      schema:
        type: string
      required: true
      description: database ID (aka external ID) of the preannouncement
      example: ********-f722-469f-aa57-44b2c6c4848a
    project_external_id:
      name: external_id
      in: path
      schema:
        type: string
      required: true
      description: database ID (aka external ID) of the project
      example: ef05-33cf1e02692fa377302dd109872038ea-c42ddd5c
    project_user_external_id:
      name: external_id
      in: path
      schema:
        type: string
      required: true
      description: database ID (aka external ID) of the project user
      example: 7432-9e644a23f8b08776b00f80e799e72648-17387178
    report_external_id:
      name: external_id
      in: path
      schema:
        type: string
      required: true
      description: database ID (aka external ID) of the report
      example: 22df-e9253049d4c29864588a957e1e0c7dab-36a27916
    report_access_external_id:
      name: external_id
      in: path
      schema:
        type: string
      required: true
      description: database ID (aka external ID) of the report access entry
      example: 904c-b3dd65dbcf9c7ad36cdf0b843c20e0d8-87dcd196
    report_cache_external_id:
      name: external_id
      in: path
      schema:
        type: string
      required: true
      description: database ID (aka external ID) of the report cache entry
      example: e636-eabe6fd31df5c0de1435877aa83b7550-f291cf03
    bulk_import_job_id:
      name: id
      in: path
      schema:
        type: string
      required: true
      description: database ID of the job
      example: edf07b2d-1409-4780-b5aa-4311a5932c05
    creditsafe_account_id:
      name: creditsafe_account_id
      in: path
      schema:
        type: string
      required: true
      description: database ID of the creditsafe account
      example: edf07b2d-1409-4780-b5aa-4311a5932c05
    project_supplier_comment_id:
      name: comment_id
      in: path
      schema:
        type: string
      required: true
      description: ID of the project supplier comment
      example: ********-f722-469f-aa57-44b2c6c4848a


  responses:
    # 400
    bad_request:
      description: bad request
      content:
        application/json:
          example:
            {
              "message": "Parameter validation failed",
              "error_code": "ParameterValidationFailed",
              "error": {
                "field_name": [
                  "actual error message"
                ]
              }
            }
    # 404
    not_found:
      description: resource not found
      content:
        application/json:
          example:
            {
              "message": "Not found",
              "error_code": "NotFound"
            }
    # 200
    delete_successful:
      description: delete successful
      content:
        application/json:
          schema:
            properties: {}

    # 409
    conflict:
      description: conflict with existing resource
      content:
        application/json:
          example:
            {
              "message": "Conflict",
              "error_code": "ConflictError",
              "error": {
                "org_id": "f392-749f75606c4331903b012118ed8c449d-0fe20015",
                "details": "An active Creditsafe account already exists for the org_id"
              }
            }

  schemas: {}

  examples: {}

  securitySchemes:
    token:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - token: []
""")

SWAGGER_TEMPLATE['components']['schemas'].update({
    'company_list': SWAGGER_COMPANY_LIST_SCHEMA,
    'project': SWAGGER_PROJECT_SCHEMA,
    'project_qvarn_compat': SWAGGER_PROJECT_SCHEMA_QVARN_COMPAT,
    'project_state': SWAGGER_PROJECT_STATE_SCHEMA,
    'project_list': SWAGGER_PROJECT_LIST_SCHEMA,
    'project_supplier_list': SWAGGER_PROJECT_SUPPLIER_LIST_SCHEMA,
    'new_project': SWAGGER_NEW_PROJECT_SCHEMA,
    'update_project': SWAGGER_UPDATE_PROJECT_SCHEMA,
    'project_user': SWAGGER_PROJECT_USER_SCHEMA,
    'new_project_user': SWAGGER_NEW_PROJECT_USER_SCHEMA,
    'update_project_user': SWAGGER_UPDATE_PROJECT_USER_SCHEMA,
    'status_report_historic': SWAGGER_STATUS_REPORT_HISTORIC_SCHEMA,
    'status_report_without_json': SWAGGER_STATUS_REPORT_SCHEMA,
    'status_report_qvarn_compat': SWAGGER_STATUS_REPORT_SCHEMA_QVARN_COMPAT,
    'status_report_json': SWAGGER_STATUS_REPORT_JSON_SCHEMA,
    'status_report_with_json': SWAGGER_STATUS_REPORT_SCHEMA_WITH_JSON,
    'notification_report_qvarn_compat': SWAGGER_NOTIFICATION_REPORT_SCHEMA_QVARN_COMPAT,
    'new_status_report': SWAGGER_NEW_STATUS_REPORT_SCHEMA,
    'new_notification_report': SWAGGER_NEW_NOTIFICATION_REPORT_SCHEMA,
    'update_status_report': SWAGGER_UPDATE_STATUS_REPORT_SCHEMA,
    'update_notification_report': SWAGGER_UPDATE_NOTIFICATION_REPORT_SCHEMA,
    'report_access': SWAGGER_REPORT_ACCESS_SCHEMA,
    'new_report_access': SWAGGER_NEW_REPORT_ACCESS_SCHEMA,
    'update_report_access': SWAGGER_UPDATE_REPORT_ACCESS_SCHEMA,
    'supplier': SWAGGER_SUPPLIER_SCHEMA,
    'new_supplier': SWAGGER_NEW_SUPPLIER_SCHEMA,
    'update_supplier': SWAGGER_UPDATE_SUPPLIER_SCHEMA,
    'preannouncement': SWAGGER_PREANNOUNCEMENT_SCHEMA,
    'preannouncement_with_form_data': SWAGGER_PREANNOUNCEMENT_SCHEMA_WITH_FORM_DATA,
    'preannouncement_without_active_pa_form': SWAGGER_PREANNOUNCEMENT_SCHEMA_WITHOUT_ACTIVE_PA_FORM,
    'new_preannouncement': SWAGGER_NEW_PREANNOUNCEMENT_SCHEMA,
    'update_preannouncement_with_new_form_data':
        SWAGGER_UPDATE_PREANNOUNCEMENT_SCHEMA_WITH_NEW_FORM_DATA,
    'update_preannouncement_with_updated_form_data':
        SWAGGER_UPDATE_PREANNOUNCEMENT_SCHEMA_WITH_UPDATED_FORM_DATA,
    'bulk_import_job': SWAGGER_BULK_IMPORT_JOB_SCHEMA,
    'new_bulk_import_job': SWAGGER_NEW_BULK_IMPORT_JOB_SCHEMA,
    'update_bulk_import_job': SWAGGER_UPDATE_BULK_IMPORT_JOB_SCHEMA,
    'report_cache': SWAGGER_REPORT_CACHE_SCHEMA,
    'new_report_cache': SWAGGER_NEW_REPORT_CACHE_SCHEMA,
    'update_report_cache': SWAGGER_UPDATE_REPORT_CACHE_SCHEMA,
    'creditsafe_account': SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITH_HISTORY,
    'creditsafe_account_without_history': SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITHOUT_HISTORY,
    'new_creditsafe_account': SWAGGER_NEW_CREDITSAFE_ACCOUNT_SCHEMA,
    'update_creditsafe_account': SWAGGER_UPDATE_CREDITSAFE_ACCOUNT_SCHEMA,
    'project_supplier_comment': SWAGGER_PROJECT_SUPPLIER_COMMENT_SCHEMA,
    'new_project_supplier_comment': SWAGGER_NEW_PROJECT_SUPPLIER_COMMENT_SCHEMA,
    'update_project_supplier_comment': SWAGGER_UPDATE_PROJECT_SUPPLIER_COMMENT_SCHEMA,
    'project_comment_viewer': SWAGGER_PROJECT_COMMENT_VIEWER_SCHEMA,
    'new_project_comment_viewer': SWAGGER_NEW_PROJECT_COMMENT_VIEWER_SCHEMA,
})

SWAGGER_TEMPLATE['components']['examples'].update({
    'bulk_import_job_pending': yaml.safe_load(SWAGGER_BULK_IMPORT_JOB_EXAMPLE_PENDING),
    'bulk_import_job_in_progress': yaml.safe_load(SWAGGER_BULK_IMPORT_JOB_EXAMPLE_IN_PROGRESS),
    'bulk_import_job_done': yaml.safe_load(SWAGGER_BULK_IMPORT_JOB_EXAMPLE_DONE),
})


class SwaggerController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                # NB: the OpenAPI spec version 3.0 recommends that this be named openapi.json
                # rather than swagger.json.  Other services (such as CompanyAPI) use
                # swagger.json, so I decided to be consistent with them.
                'path': '/swagger.json',
                'method': 'GET',
                'callback': self.get_swagger,
                'require_auth': False
            }
        ]

    def get_swagger(self) -> HTTPResponse:
        """Show machine-readable API documentation.

        This is automatically generated from view docstrings.  It may be incomplete
        if there are any views without the requisite Swagger markup.

        ---
        responses:
          200:
            description: >
              [OpenAPI](https://swagger.io/specification/) document describing the
              API of the Bol-Data-API service, in JSON format
        externalDocs:
          description: OpenAPI specification
          url: https://swagger.io/specification/
        """
        response.headers['Access-Control-Allow-Origin'] = '*'
        output = bottleswagger.swagger(
            request.app,
            title='Bolagsdeklaration Data API',
            version=get_bda_version(),
            template=SWAGGER_TEMPLATE,
        )
        output = add_automatic_tags(output, request.app)
        output = add_undocumented_views(output, request.app)
        output = add_scopes(output, request.app)
        return output


def get_path_parameters(rule):
    return [match[1] for match in BOTTLE_PATH_RX.findall(rule)]


def add_automatic_tags(swagger_doc, app):
    """Group API endpoints by source file.

    Note: will modify ``swagger_doc['paths']`` in place!

    The rest of ``swagger_doc`` is untouched, which is important.
    """
    paths = swagger_doc.get('paths', {})

    for route in app.routes:
        method = route.method.lower()
        path = convert_bottle_route_rule_to_swagger_path(route.rule)
        path_item = paths.setdefault(path, {})
        if method not in path_item:
            continue

        if not path_item[method].get('tags'):
            module = route.callback.__module__.rpartition('.')[-1]
            if module.endswith('_controller'):
                module = module[:-len('-controller')]
            path_item[method]['tags'] = [module]

    return swagger_doc


def add_undocumented_views(swagger_doc, app):
    """Add information about undocumented APIs.

    Note: will modify ``swagger_doc['paths']`` in place!

    The rest of ``swagger_doc`` is untouched, which is important.
    """

    paths = swagger_doc['paths']
    for route in app.routes:
        method = route.method.lower()
        path = convert_bottle_route_rule_to_swagger_path(route.rule)
        path_item = paths.setdefault(path, {})
        if method not in path_item:
            docstring = inspect.getdoc(route.callback) or ''
            summary, _nl, description = docstring.partition('\n')
            if summary:
                summary = '{} [UNDOCUMENTED]'.format(summary)
            else:
                summary = '[UNDOCUMENTED]'

            description = '**UNDOCUMENTED**'

            module = route.callback.__module__.rpartition('.')[-1]
            tags = [f'undocumented ({module})']

            path_item[method] = {
                'summary': summary,
                'description': description,
                'responses': {'default': {'description': '**UNDOCUMENTED**'}},
                'tags': tags,
            }

            params = []
            for param in get_path_parameters(route.rule):
                params.append({
                    'name': param,
                    'in': 'path',
                    'required': True,
                    'schema': {'type': 'string'},
                    'description': '**UNDOCUMENTED**',
                })

            # apparently there's software out there that doesn't like empty parameter lists,
            # but is fine if 'parameters' is omitted entirely
            if params:
                path_item[method]['parameters'] = params

    return swagger_doc


def add_scopes(swagger_doc, app):
    """Add required auth token scopes to each API endpoint.

    Note: will modify ``swagger_doc['paths']`` in place!

    The rest of ``swagger_doc`` is untouched, which is important.
    """
    paths = swagger_doc.get('paths', {})

    for route in app.routes:
        method = route.method.lower()
        path = convert_bottle_route_rule_to_swagger_path(route.rule)
        path_item = paths.setdefault(path, {})
        if method not in path_item:
            continue

        if not path_item[method].get('security'):
            if route.config['require_auth']:
                path_item[method]['security'] = [{'token': route.config['scopes']}]
            else:
                path_item[method]['security'] = []

    return swagger_doc
