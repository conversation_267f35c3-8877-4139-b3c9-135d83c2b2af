from logging import Logger
from typing import List

from bottle import request

from boldataapi import exceptions
from boldataapi.schema import (
    ReportAccessValidator,
    schema_existing_report_access,
    schema_new_report_access,
)
from boldataapi.services.report_accesses import (
    create_report_access,
    delete_report_access,
    get_report_access,
    get_report_accesses_list,
    search_report_accesses,
    update_report_access,
)


class ReportAccessesController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/report_accesses/<external_id>',
                'method': 'GET',
                'callback': self.get_report_access,
                'require_auth': True,
                'scopes': ['uapi_report_accesses_id_get']
            },
            {
                'path': '/report_accesses',
                'method': 'POST',
                'callback': self.post_report_access,
                'require_auth': True,
                'scopes': ['uapi_report_accesses_post'],
            },
            {
                'path': '/report_accesses/search/<path:path>',
                'method': 'GET',
                'callback': self.report_accesses_search,
                'require_auth': True,
                'scopes': ['uapi_report_accesses_search_id_get'],
            },
            {
                'path': '/report_accesses/<external_id>',
                'method': 'PUT',
                'callback': self.put_report_access,
                'require_auth': True,
                'scopes': ['uapi_report_accesses_id_put'],
            },
            {
                'path': '/report_accesses/<external_id>',
                'method': 'DELETE',
                'callback': self.delete_report_access,
                'require_auth': True,
                'scopes': ['uapi_report_accesses_id_delete'],
            },
            {
                'path': '/report_accesses/list',
                'method': 'GET',
                'callback': self.get_report_accesses_list,
                'require_auth': True,
                'scopes': ['uapi_ext_bolfin_report_accesses_company_list_get']
            },
        ]

    def get_report_access(self, external_id):
        """Get a report access entry by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/report_access_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: report access entry
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/report_access'
        """
        self._logger.info('Getting report_access %s', external_id)

        report_access = get_report_access(request.db, external_id=external_id)
        if not report_access:
            raise exceptions.NotFound(external_id=external_id)

        report_access_external_id = report_access.pop('external_id')
        report_access['id'] = report_access_external_id
        return report_access

    def post_report_access(self):
        """Create a new report access entry.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_report_access'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          201:
            description: the newly created report access entry
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/report_access'
        """
        self._logger.info('Creating a new report_access')

        validator = ReportAccessValidator(schema_new_report_access)

        if not validator.validate(request.json):
            self._logger.info('report_access data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request.json)
        gov_org_id = data['gov_org_ids'][0] if data.get('gov_org_ids') else None
        payload = {
            'customer_company_id': data.get('customer_org_id'),
            'company_id': data.get('org_id'),
            'person_id': data.get('person_id'),
            'status': data.get('status'),
            'access_time': data.get('access_time'),
            'arkisto_id': data.get('arkisto_id'),
            'report_id': data.get('report_id'),
            'language': data.get('language'),
            'template_version': data.get('template_version'),
            'company_gov_id': gov_org_id['gov_org_id'] if gov_org_id else None,
            'company_gov_id_country': gov_org_id['country'] if gov_org_id else None,
            'company_gov_id_type': gov_org_id['org_id_type'] if gov_org_id else None,
        }
        report_access = create_report_access(request.db, payload)

        self._logger.info('New report_access created %s', report_access['id'])

        report_access_external_id = report_access.pop('external_id')
        report_access['id'] = report_access_external_id
        return report_access

    def report_accesses_search(self, path: str):
        """Return a list of report accesses that match a query.
        ---
        parameters:
          - name: path
            in: path
            schema:
              type: string
            required: true
            # XXX: OpenAPI spec doesn't allow us to express that `path` can contain unescaped
            # slashes: https://github.com/OAI/OpenAPI-Specification/issues/892
            description: |
              Qvarn query

              Qvarn search queries consist of multiple path segments, separated
              with (unescaped) slashes.  Most of the segments describe search criteria,
              which consist of an operator followed by a field name, followed by a value.
              The operators are

              - exact
              - gt
              - ge
              - lt
              - le
              - ne
              - startswith
              - contains

              The values are plain strings.  You cannot match on `null`.

              An operator can be preceded by a special `any` segment, which affects it by
              allowing a match against any of the provided values.  In this
              case the value following the field name needs to be an
              URL-escaped JSON-encoded list.

              Multiple search criteria are ANDed together.

              There are also special operators that affect how the results are returned:

              - show/{field_name} - include {field_name} in the results
              - show_all - include all fields in the results
              - sort/{field_name} - sort by given field
              - rsort/{field_name} - reverse sort by given field
              - limit/{n} - limit to N results
              - offset/{n} - skip first N results

              If you want to specify offset or limit, you must also specify sorting.  Multiple
              sort parameters can be provided.

              If you don't specify any `show`/`show_all` operators, only the IDs will be returned.

              This method is deprecated, but we don't have a replacement yet.
            examples:
              one_field_exact_match:
                summary: exact match on customer_org_id
                value:
                  exact/customer_org_id/6991-79eb6caba29dc8db7d55d54a26bbdfd9-f47819c4
              any_match:
                summary: language is either "EN" or "FI"
                value:
                  any/exact/language/["EN","FI"]
              pagination:
                summary: arbitrary and unrealistic example of pagination
                value:
                  sort/org/sort/arkisto_id/offset/15/limit/5/show_all
        deprecated: true
        responses:
          200:
            description: query results
            content:
              application/json:
                schema:
                  properties:
                    resources:
                      type: array
                      items:
                        type: object
                        description: |
                          a subset of the report access resource, depending on which fields you've
                          requested in the query.

                          The `id` field is always returned.
                        properties:
                          id:
                            description: report access database ID
                            example: a13b-bea3897125af6f9bf4059ce8cfbc5ebd-373a1d9cv
                        additionalProperties: true
                        example:
                          {
                            "id": "a13b-bea3897125af6f9bf4059ce8cfbc5ebd-373a1d9cv"
                          }
        """
        result = search_report_accesses(request.db, path)
        return dict(result)

    def delete_report_access(self, external_id: str):
        """Delete a report access entry by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/report_access_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info(f'Deleting report_access {external_id}')

        report_access = get_report_access(request.db, external_id=external_id)
        if not report_access:
            raise exceptions.NotFound(external_id=external_id)
        delete_report_access(request.db, report_access['id'])

        return {}

    def put_report_access(self, external_id: str): # noqa C901
        """Update a report access entry by ID.

        Fields that are omitted from the request will retain their old values.
        ---
        parameters:
          - $ref: '#/components/parameters/report_access_external_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_report_access'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the updated report access entry
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/report_access'
        """
        self._logger.info(f'Updating report_access {external_id}')

        # update resource by pk, not qvarn_id
        report_access = get_report_access(request.db, external_id=external_id)
        if not report_access:
            self._logger.info('report_access %s was not found.', external_id)
            raise exceptions.NotFound(external_id=external_id)

        validator = ReportAccessValidator(schema_existing_report_access)
        if not validator.validate(request.json):
            self._logger.info('report_access data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = request.json

        payload = {}

        if data.get('customer_org_id'):
            payload['customer_company_id'] = data['customer_org_id']

        if data.get('org_id'):
            payload['company_id'] = data['org_id']

        if data.get('person_id'):
            payload['person_id'] = data['person_id']

        if data.get('status'):
            payload['status'] = data['status']

        if data.get('access_time'):
            payload['access_time'] = data['access_time']

        if data.get('arkisto_id'):
            payload['arkisto_id'] = data['arkisto_id']

        if data.get('report_id'):
            payload['report_id'] = data['report_id']

        if data.get('language'):
            payload['language'] = data['language']

        if data.get('template_version'):
            payload['template_version'] = data['template_version']

        gov_org_id = data['gov_org_ids'][0] if data.get('gov_org_ids') else None

        if gov_org_id:
            payload['company_gov_id'] = gov_org_id['gov_org_id']
            payload['company_gov_id_country'] = gov_org_id['country']
            payload['company_gov_id_type'] = gov_org_id['org_id_type']

        update_report_access(request.db, report_access['id'], payload)

        updated_report_access = get_report_access(request.db,
                                                  report_access_id=report_access['id'])
        report_access_external_id = updated_report_access.pop('external_id')
        updated_report_access['id'] = report_access_external_id
        return updated_report_access

    def get_report_accesses_list(self):
        """Return a list of report accesses for the logged in user.

        ---
        deprecated: true
        parameters:
          - name: user_active_org_id
            in: query
            required: true
            schema:
              type: string
              example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
            description: >
              the database ID of the active organization that the
              logged in user currently represents
          - name: limit
            in: query
            required: false
            schema:
              type: integer
            description: >
              maximum number of items to return (for pagination);
              missing or `0` means unlimited.
          - name: offset
            in: query
            required: false
            schema:
              type: integer
            description: >
              0-based index of the first item to return (for pagination);
              missing or `0` means start at the very beginning.
        responses:
          200:
            description: list of report accesses for the logged in user
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - "bol_report_accesses/bol_report_accesses_list"
                      description: >
                        always "bol_report_accesses/bol_report_accesses_list",
                        for Qvarn compatibility
                    resources:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: string
                            example: ce17-d3796d0e4d0772ce4c5d4802141ffd16-d124ed31
                            description: database ID (aka external ID) of the report access entry
                          gov_org_ids:
                            type: array
                            minItems: 1
                            maxItems: 1
                            items:
                              type: object
                              properties:
                                country:
                                  type: string
                                  example: FI
                                  description: 2-letter country code
                                org_id_type:
                                  type: string
                                  enum:
                                    - registration_number
                                  description: type of ID
                                gov_org_id:
                                  type: string
                                  example: 1234567-8
                                  description: |
                                    company registration number (when `org_id_type` is
                                    "registration_number")
        """
        self._logger.info('Getting list of report accesses')

        user_active_org_id = request.query.user_active_org_id
        limit = request.query.limit or None  # LIMIT NULL turns limit off
        offset = request.query.offset or 0

        report_accesses = get_report_accesses_list(
            request.db,
            user_active_org_id,
            limit=limit, offset=offset
        )
        return {
            'resource_type': 'bol_report_accesses/bol_report_accesses_list',
            'resources': report_accesses,
        }
