from logging import Logger
from typing import List

from bottle import request

from boldataapi import exceptions
from boldataapi.services.companies import get_companies_list, get_company_ids_batch


class CompaniesController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/companies/list',
                'method': 'POST',
                'callback': self.get_companies_list,
                'require_auth': True,
                'scopes': ['uapi_ext_bol_company_list_get'],
            },
            {
                'path': '/companies/all-ids',
                'method': 'GET',
                'callback': self.get_all_company_ids,
                'require_auth': True,
                'scopes': ['uapi_ext_bol_company_list_get'],
            },
        ]

    def get_companies_list(self):
        """Return a list of companies visible to the logged in user.

        Note that Bol-Data-API does not have company information in its
        database, so it cannot return things like company names or countries or
        registration numbers or VAT numbers.

        This endpoing is useful solely for quickly computing which companies
        should be visible to a user depending on project participation.
        ---
        requestBody:
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_active_org_id:
                    type: string
                    example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
                    description: >
                      the database ID of the active organization that the
                      logged in user currently represents
                  user_active_org_role:
                    type: string
                    enum:
                      - main
                      - basic
                    description: >
                      the logged in user's role in the organization
                  user_is_admin:
                    type: boolean
                    example: false
                    description: >
                      does the user have administrative privileges (i.e. can
                      see all the companies)?
                  user_projects_ids:
                    type: array
                    nullable: true
                    example: null
                    items:
                      type: string
                      description: database ID of a project
                      example: 957d-838e0098c0edb161f68d26a2e419f441-4aa836c6
                    description: >
                      limit the result to companies involved with the projects listed herein
                  filter.search:
                    type: string
                    example: ""
                    description: currently ignored
                  limit:
                    type: integer
                    nullable: true
                    description: >
                      maximum number of items to return (for pagination),
                      `null` of `0` means unlimited.
                  offset:
                    type: integer
                    nullable: true
                    description: >
                      0-based index of the first item to return (for pagination),
                      `null` or `0` means start at the very beginning.
                  ff_block_project_client:
                    type: boolean
                    example: true
                    description: >
                      Excludes the project from visible projects if the project attribute
                      `added_client_can_view` is set to `false`, while
                      the project creator's role is the main contractor, and the project's client
                      organization is the same as the active organization.
                required:
                  - user_active_org_id
                  - user_active_org_role
                  - user_is_admin
                  - filter.search
                  - limit
                  - offset
        responses:
          200:
            description: list of companies visible to the logged in user
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - "org/bol_company_list"
                      description: always "org/bol_company_list", for Qvarn compatibility
                    resources:
                      $ref: '#/components/schemas/company_list'
        """
        self._logger.info('Getting list of companies')

        user_active_org_id = request.json['user_active_org_id']
        user_active_org_role = request.json['user_active_org_role']
        user_is_admin = request.json['user_is_admin'] == True
        user_projects_ids = request.json.get('user_projects_ids')

        filter_search = request.json['filter.search']

        # ??? Pagination does not make sense as sorting is done on company name.
        limit = request.json['limit'] or None  # LIMIT NULL turns limit off
        offset = request.json['offset'] or 0

        ff_block_project_client = request.json.get('ff_block_project_client')

        companies = get_companies_list(
            request.db,
            user_active_org_id,
            user_active_org_role,
            user_is_admin,
            user_projects_ids,
            filter_search=filter_search,
            limit=limit,
            offset=offset,
            ff_block_project_client=ff_block_project_client,
        )
        return {
            'resource_type': 'org/bol_company_list',
            'resources': companies,
        }

    def get_all_company_ids(self):
        """Return all company IDs found across all database tables.

        This endpoint is designed for batch processing with efficient pagination
        to handle large datasets in production environments.
        ---
        parameters:
          - name: table
            in: query
            description: Specific table to query (all tables if not specified)
            required: false
            schema:
              type: string
              enum: [suppliers, projects, project_users, status_reports, status_reports_history,
                     bulk_import_jobs, internal_project_ids, creditsafe_account,
                     creditsafe_account_history, report_cache, notification_reports, all]
              default: all
          - name: batch_size
            in: query
            description: Number of results per batch
            required: false
            schema:
              type: integer
              default: 1000
          - name: last_id
            in: query
            description: Last company ID from previous batch (for cursor pagination)
            required: false
            schema:
              type: string
        responses:
          200:
            description: Batch of company IDs
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    company_ids:
                      type: array
                      items:
                        type: string
                    next_cursor:
                      type: string
                      description: Last ID in current batch, use as last_id for next batch
                    has_more:
                      type: boolean
                      description: Whether there are more results available
        """
        self._logger.info('Getting batch of company IDs')

        table = request.query.get('table', 'all')
        batch_size = request.query.get('batch_size', '1000')
        last_id = request.query.get('last_id', '')

        try:
            batch_size = int(batch_size)
            if batch_size <= 0 or batch_size > 1000:
                raise ValueError("Batch size must be between 1 and 1000")
        except ValueError as e:
            raise exceptions.ParameterValidationFailed(batch_size=str(e))

        valid_tables = ['suppliers', 'projects', 'project_users', 'status_reports',
                        'status_reports_history', 'bulk_import_jobs', 'internal_project_ids',
                        'creditsafe_account', 'creditsafe_account_history', 'report_cache',
                        'notification_reports', 'all']
        if table not in valid_tables:
            raise exceptions.ParameterValidationFailed(
                table=f"Invalid table name. Must be one of: {', '.join(valid_tables)}"
            )

        result = get_company_ids_batch(
            request.db,
            table=table,
            batch_size=batch_size,
            last_id=last_id
        )

        return result
