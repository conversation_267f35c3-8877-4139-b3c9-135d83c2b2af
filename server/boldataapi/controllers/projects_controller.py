import json
from logging import Logger
from typing import List

from bottle import request, response

from boldataapi import exceptions
from boldataapi.schema import (
    ProjectValidator,
    schema_existing_project,
    schema_new_project,
)
from boldataapi.serialize import asbool
from boldataapi.services.project_supplier_comments import (
    query_project_supplier_comments,
)
from boldataapi.services.projects import (
    create_project,
    delete_project,
    get_project,
    get_project_ids,
    get_project_suppliers,
    get_projects,
    get_projects_list,
    get_tax_id,
    is_filter_state_valid,
    is_filter_status_valid,
    query_projects,
    search_projects,
    update_project,
)


class ProjectsController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/projects',
                'method': 'GET',
                'callback': self.get_projects,
                'require_auth': True,
                'scopes': ['uapi_projects_get'],
            },
            {
                'path': '/projects/<external_id>',
                'method': 'GET',
                'callback': self.get_project,
                'require_auth': True,
                'scopes': ['uapi_projects_id_get'],
            },
            {  # Defunct, QAT compat
                'path': '/projects/<external_id>/sync',
                'method': 'GET',
                'callback': self.get_project_sub_resource,
                'require_auth': True,
                'scopes': ['uapi_projects_id_get'],
            },
            {
                'path': '/projects',
                'method': 'POST',
                'callback': self.post_project,
                'require_auth': True,
                'scopes': ['uapi_projects_post'],
            },
            {
                'path': '/projects/<external_id>',
                'method': 'PUT',
                'callback': self.put_project,
                'require_auth': True,
                'scopes': ['uapi_projects_id_put'],
            },
            {  # Defunct, QAT compat
                'path': '/projects/<external_id>/sync',
                'method': 'PUT',
                'callback': self.put_project_sub_resource,
                'require_auth': True,
                'scopes': ['uapi_projects_id_put'],
            },
            {
                # Deprecated Qvarn-compatible search API endpoint
                'path': '/projects/search/<path:path>',
                'method': 'GET',
                'callback': self.projects_search,
                'require_auth': True,
                'scopes': ['uapi_projects_search_id_get'],
            },
            {
                # New search API endpoint
                'path': '/projects/query',
                'method': 'GET',
                'callback': self.projects_query,
                'require_auth': True,
                'scopes': ['uapi_projects_search_id_get'],
            },
            {
                'path': '/projects/list',
                'method': 'GET',
                'callback': self.get_projects_list,
                'require_auth': True,
                'scopes': ['uapi_ext_bol_project_list_get'],
            },
            {
                'path': '/projects/list',
                'method': 'POST',
                'callback': self.post_projects_list,
                'require_auth': True,
                # do we need to use another scope with *_post?
                'scopes': ['uapi_ext_bol_project_list_get'],
            },
            {
                'path': '/projects/suppliers',
                'method': 'GET',
                'callback': self.get_project_suppliers,
                'require_auth': True,
                'scopes': ['uapi_ext_bol_project_suppliers_get'],
            },
            {
                'path': '/projects/<external_id>',
                'method': 'DELETE',
                'callback': self.delete_project,
                'require_auth': True,
                'scopes': ['uapi_projects_id_delete'],
            },
            {
                'path': '/projects/<external_id>/comments',
                'method': "GET",
                'callback': self.get_project_supplier_comments,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_get'],
            },
        ]

    def get_projects(self):
        """Return the IDs of all the projects.

        This is something that you should never use in production, because the
        side of the response grows unbounded with time as the number of
        projects in the database increases.  Consider using /projects/query
        instead.
        ---
        responses:
          200:
            description: list of all project IDs
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - project
                      description: >
                        always "project", for Qvarn compatibility
                    resources:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: project database ID (aka external ID)
                            example: 792e-e2e98ca021d768276825c422c2dd02c9-3cc0fe10
        """
        self._logger.info('Getting all project ids')
        projects = get_projects(request.db)
        return {
            'resource_type': 'project',
            'resources': projects,
        }

    def get_project(self, external_id):
        """Get a project by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/project_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: project information
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_qvarn_compat'
        """
        self._logger.info('Getting project %s', external_id)
        project = get_project(request.db, external_id=external_id)
        if not project:
            raise exceptions.NotFound(external_id=external_id)

        project_external_id = project.pop('external_id')
        project['id'] = project_external_id
        return project

    def get_project_sub_resource(self, external_id):
        """QAT compatibility endpoint.

        No real support for sub-resources.

        ---
        deprecated: true
        parameters:
          - $ref: '#/components/parameters/project_external_id'
        responses:
          200:
            description: always an empty object
            content:
              application/json:
                schema:
                  properties: {}
        """
        return {}

    def post_project(self):
        """Create a new project

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_project'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          201:
            description: the newly created project
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project_qvarn_compat'
        """
        self._logger.info('Creating a new project')

        validator = ProjectValidator(schema_new_project)
        if not validator.validate(request.json):
            self._logger.info('Project data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)
        payload = {
            'name': data['names'][0] if data.get('names') else None,
            'tax_id': get_tax_id(data.get('project_ids')),
            'client_company_id': data.get('project_responsible_org'),
            'client_contact_person_id': data.get('client_contact_person_id'),
            'client_contact_person_email': data.get('client_contact_person_email'),
            'created_by_org_id': data.get('created_by_org_id'),
            'state': data.get('state'),
            'start_date': data.get('start_date'),
            'end_date': data.get('end_date'),
            'project_ids': get_project_ids(data.get('project_ids')),
            'pa_form_enabled': data.get('pa_form_enabled'),
            'added_client_confirmed': data.get('added_client_confirmed'),
            'project_creator_role': data.get('project_creator_role'),
            'added_client_can_view': data.get('added_client_can_view'),
        }
        project = create_project(request.db, payload)
        self._logger.info('New project created %s', project['external_id'])

        project['id'] = project['external_id']
        project.pop('external_id')
        return project

    def prepare_put_project_payload(self, data, project_ids):  # noqa
        payload = {}
        if data.get('names'):
            payload['name'] = data['names'][0]
        if data.get('project_responsible_org'):
            payload['client_company_id'] = data['project_responsible_org']
        if data.get('client_contact_person_email') is not None:
            payload['client_contact_person_email'] = data['client_contact_person_email']
        if 'client_contact_person_email' in payload or data.get('client_contact_person_id'):
            # It must be possible to change client_contact_person_id to None
            # when client_contact_person_email changes.
            payload['client_contact_person_id'] = data.get('client_contact_person_id')
        if data.get('created_by_org_id'):
            payload['created_by_org_id'] = data['created_by_org_id']
        if 'start_date' in data:
            payload['start_date'] = data['start_date'] or None
        if 'end_date' in data:
            payload['end_date'] = data['end_date'] or None
        if data.get('state'):
            payload['state'] = data['state']
        if project_ids is not None:
            payload['project_ids'] = get_project_ids(project_ids)
            payload['tax_id'] = get_tax_id(project_ids)
        if data.get('pa_form_enabled') is not None:
            payload['pa_form_enabled'] = data['pa_form_enabled']
        if data.get('added_client_confirmed') is not None:
            payload['added_client_confirmed'] = data['added_client_confirmed']
        if data.get('added_client_can_view') is not None:
            payload['added_client_can_view'] = data['added_client_can_view']

        return payload

    def put_project(self, external_id):
        """Update a project.

        Fields that are omitted from the request or are set to `null` will
        retain their old values.  There's one exception: changing `client_contact_person_email`
        necessitates a change to `client_contact_person_id`, so it's possible
        to reset it to `null` when the email changes.  BTW to remove the client
        contact email, set it to an empty string rather than `null`.
        ---
        parameters:
          - $ref: '#/components/parameters/project_external_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_project'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the updated project
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/project'
        """
        self._logger.info('Updating project %s', external_id)

        project = get_project(request.db, external_id=external_id)
        if not project:
            raise exceptions.NotFound(external_id=external_id)

        validator = ProjectValidator(schema_existing_project)
        if not validator.validate(request.json):
            self._logger.info('Project data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request.json)
        project_ids = data.get('project_ids')
        payload = self.prepare_put_project_payload(data, project_ids)
        update_project(request.db, project['id'], payload)

        updated_project = get_project(request.db, project_id=project['id'])
        project_external_id = updated_project.pop('external_id')
        updated_project['id'] = project_external_id
        return updated_project

    def put_project_sub_resource(self, external_id):
        """QAT compatibility endpoint.

        As we have no real support for sub-resources, this does nothing.

        ---
        deprecated: true
        parameters:
          - $ref: '#/components/parameters/project_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: always an empty object
            content:
              application/json:
                schema:
                  properties:
                    revision:
                      type: string
                      nullable: true
                      example: null
                      description: |
                        always `null`, for Qvarn compatibility

                        This is intended to avoid update conflicts, but that is
                        not currently implemented.
        """
        project = get_project(request.db, external_id=external_id)
        if not project:
            raise exceptions.NotFound(external_id=external_id)
        # QAT expects updated sub-resource to contain field 'revision' that is
        # then saved as the 'revision' for the resource itself.
        project_sub_resource = {'revision': project['revision']}
        return project_sub_resource

    def delete_project(self, external_id):
        """Delete a project

        Deleting a project will also delete all the suppliers for that project.
        Any preannouncements created for suppliers in the project will remain,
        but their supplier and project internal references will be reset to `null`.
        ---
        parameters:
          - $ref: '#/components/parameters/project_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info('Deleting project %s', external_id)

        project = get_project(request.db, external_id=external_id)
        if not project:
            raise exceptions.NotFound(external_id=external_id)
        delete_project(request.db, project['id'])
        return {}

    def projects_search(self, path: str):
        """Return a list of projects that match a query.
        ---
        parameters:
          - name: path
            in: path
            schema:
              type: string
            required: true
            # XXX: OpenAPI spec doesn't allow us to express that `path` can contain unescaped
            # slashes: https://github.com/OAI/OpenAPI-Specification/issues/892
            description: |
              Qvarn query

              Qvarn search queries consist of multiple path segments, separated
              with (unescaped) slashes.  Most of the segments describe search criteria,
              which consist of an operator followed by a field name, followed by a value.
              The operators are

              - exact
              - gt
              - ge
              - lt
              - le
              - ne
              - startswith
              - contains

              The values are plain strings.  You cannot match on `null`.

              An operator can be preceded by a special `any` segment, which affects it by
              allowing a match against any of the provided values.  In this
              case the value following the field name needs to be an
              URL-escaped JSON-encoded list.

              Multiple search criteria are ANDed together.

              There are also special operators that affect how the results are returned:

              - show/{field_name} - include {field_name} in the results
              - show_all - include all fields in the results
              - sort/{field_name} - sort by given field
              - rsort/{field_name} - reverse sort by given field
              - limit/{n} - limit to N results
              - offset/{n} - skip first N results

              If you want to specify offset or limit, you must also specify sorting.  Multiple
              sort parameters can be provided.

              If you don't specify any `show`/`show_all` operators, only the IDs will be returned.

              **Note that only a subset of project fields can be used as search
              criteria or returned in the result list.**  Specifically, only those fields
              that existed in the old Qvarn project resource:

              - id
              - names
              - project_responsible_org
              - project_responsible_person (although it's always `null` so no point using it)
              - start_date
              - end_date
              - state
              - project_id
              - project_id_type
              - country (although it's always null so no point using it)
              - type (although it's always "project" so no point using it)
              - revision (although it's always null so no point using it)

              In particular you cannot filter (or see) `pa_form_enabled`.

              This method is deprecated, use the /projects/query API instead.
            examples:
              one_field_exact_match:
                summary: exact match on project_responsible_org
                value:
                  exact/project_responsible_org/f866-e8fcd27fdb7037f3bf4ce14803fb5538-00e242fd
              any_match:
                summary: status either active or draft
                value:
                  any/exact/status/["active","draft"]
              match on a subfield:
                summary: match on a project with a given project ID or tax ID
                value:
                  exact/project_id/TP-1001
              pagination:
                summary: arbitrary and unrealistic example of pagination
                value:
                  sort/id/offset/15/limit/5/show_all
        deprecated: true
        responses:
          200:
            description: query results
            content:
              application/json:
                schema:
                  properties:
                    resources:
                      type: array
                      items:
                        type: object
                        description: |
                          a subset of the project resource, depending on which fields you've
                          requested in the query.

                          The `id` field is always returned.
                        properties:
                          id:
                            description: project database ID
                            example: 7c10-349f092cb39181485bd811022c87771f-83e07ae3
                        additionalProperties: true
                        example:
                          {
                            "id": "7c10-349f092cb39181485bd811022c87771f-83e07ae3"
                          }
        """
        result = search_projects(request.db, path)
        return dict(result)

    def projects_query(self):
        """Return a list of projects that match a query.

        ---
        parameters:
          - name: q
            in: query
            description: |
              search query in URL-encoded JSON

              The keys of the query object are database column names optionally suffixed with an
              operator ("eq"/"ne"/"lt"/"gt"/"le"/"ge"/"any") following a "__".
              The values are search values for that field (or a list of search values for the
              "any" operator).  When searching with multiple conditions, they are ANDed together.

              Database columns you can search on are:
              - id (database ID aka external ID)
              - name
              - project_responsible_org (database ID)
              - state (active/draft/closed)
              - pa_form_enabled (true/false)
              - start_date (YYYY-MM-DD)
              - end_date (YYYY-MM-DD)
            required: true
            content:
              application/json:
                # XXX: swagger-ui fails to show this schema anywhere, hence
                # it's essentially replicated in plain text in the description
                # field above
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >
                        search by project database ID (aka external_id)

                        There's really no reason to search by ID instead of using a simple HTTP GET.
                    name:
                      type: string
                      description: search by project name
                    project_responsible_org:
                      type: string
                      description: search by project responsible organization's database ID
                    state:
                      type: string
                      description: search by project state
                      enum:
                        - active
                        - draft
                        - closed
                    pa_form_enabled:
                      type: boolean
                      description: search for projects that use (or don't) preannouncement forms
            # XXX: editor.swagger.io insists that 'examples' is wrong here and it should be
            # indented further, into content["application/json"].schema.examples, but if I do that,
            # then swagger-ui fails to show the examples!
            examples:
              simple:
                summary: a simple query on one field
                value:
                  {"name": "Test project"}
                description: >
                  Searches for projects named "Test project".  The name must
                  match exactly, including case.
              multiple_fields:
                summary: a simple query on multiple fields
                value:
                  {
                    "project_responsible_org":
                       "47b4-0265d36326dcf038befc6dc4cc2c34cb-f9da83bc",
                    "pa_form_enabled": true
                  }
                description: >
                  Searches for PA-enabled projects for which a certain
                  organization is responsible.
              multiple_enum_values:
                summary: a simple query on a single field with multiple allowed values
                value:
                  {"state__any": ["active", "draft"]}
                description: >
                  Searches for projects that are not closed
        responses:
          200:
            description: List of search results
            content:
              application/json:
                schema:
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - project
                      description: always "project"
                    resources:
                      type: array
                      description: list of matching projects
                      items:
                        $ref: '#/components/schemas/project'
                examples:
                  no_matches:
                    summary: Result when nothing matches
                    value:
                      {
                        "resource_type": "project",
                        "resources": []
                      }
                  matches_exist:
                    summary: Result when something matches
                    value:
                      {
                        "resource_type": "project",
                        "resources": [
                          {
                            "id": "45b1-0275ebe9f446fdc1cada25f57d545719-79aa723e",
                            "names": [
                              "Test project 1"
                            ],
                            "project_responsible_org":
                              "f392-1006b1d93fb333e3ad8f362960834e65-66463324",
                            "project_responsible_person": null,
                            "start_date": null,
                            "end_date": null,
                            "state": "active",
                            "project_ids": [
                              {
                                "project_id": "TP-1001",
                                "project_id_type": "trafikverket_project_id"
                              }
                            ],
                            "pa_form_enabled": null
                          }
                        ]
                      }
          400:
            description: Error in query (e.g. JSON parse error)
            content:
              application/json:
                example:
                  {
                    "message": "Could not parse search condition",
                    "error_code": "BadSearchCondition"
                  }
        """
        try:
            query = json.loads(request.query.q)
        except ValueError:
            raise exceptions.BadSearchCondition()
        result = query_projects(request.db, query)
        return {
            'resource_type': 'project',
            'resources': [
                dict(project, id=project.pop('external_id'))
                for project in result
            ]
        }

    def get_projects_list(self):
        """Return a list of projects visible to the logged in user.

        This is deprecated; you should use the POST version of this endpoint.
        ---
        deprecated: true
        parameters:
          - name: user_active_org_id
            in: query
            required: true
            schema:
              type: string
              example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
            description: >
              the database ID of the active organization that the
              logged in user currently represents
          - name: user_active_org_role
            in: query
            required: true
            schema:
              type: string
              enum:
                - main
                - basic
            description: >
              the logged in user's role in the organization
          - name: user_is_admin
            in: query
            required: false
            schema:
              type: boolean
              default: false
            description: >
              does the user have administrative privileges (i.e. can see all the companies)?
              (Valid values: True/true/1/False/false/0.)
          - name: include_active_org_projects
            in: query
            required: false
            schema:
              type: boolean
              default: false
            description: >
              include preannouncement-using projects where user_active_org_id is a supplier?
              (Valid values: True/true/1/False/false/0.)
          - name: filter.state
            in: query
            required: false
            schema:
              type: string
              enum:
                - active
                - closed
                - draft
                - "not:active"
                - "not:closed"
                - "not:draft"
            description: >
              list only those projects whose state matches (or does not match) the specified state
          - name: filter.search
            in: query
            required: false
            schema:
              type: string
            description: >
              list only those projects whose name or user-specific internal
              project ID matches this substring (case-insensitively)
          - name: filter.status
            in: query
            required: false
            schema:
              type: string
              enum:
                - stop
                - investigate
                - incomplete
                - attention
                - ok
                - "not:stop"
                - "not:investigate"
                - "not:incomplete"
                - "not:attention"
                - "not:ok"
            description: >
              list only those projects whose status matches (or does not match) the specified value
          - name: limit
            in: query
            required: false
            schema:
              type: integer
            description: >
              maximum number of items to return (for pagination);
              missing or `0` means unlimited.
          - name: offset
            in: query
            required: false
            schema:
              type: integer
            description: >
              0-based index of the first item to return (for pagination);
              missing or `0` means start at the very beginning.
        responses:
          200:
            description: list of projects visible to the logged in user
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - "bol_project_list"
                      description: always "bol_project_list", for Qvarn compatibility
                    resources:
                      $ref: '#/components/schemas/project_list'
        """
        self._logger.info('Getting list of projects')
        user_active_org_id = request.query.user_active_org_id
        user_active_org_role = request.query.user_active_org_role
        try:
            user_is_admin = asbool(request.query.user_is_admin, default=False)
        except ValueError as e:
            raise exceptions.ParameterValidationFailed(user_is_admin=str(e))

        try:
            include_active_org_projects = asbool(request.query.include_active_org_projects,
                                                 default=False)
        except ValueError as e:
            raise exceptions.ParameterValidationFailed(include_active_org_projects=str(e))

        filter_state = request.query.getunicode('filter.state')
        filter_search = request.query.getunicode('filter.search')
        filter_status = request.query.getunicode('filter.status')

        if filter_state and not is_filter_state_valid(filter_state):
            raise exceptions.ParameterValidationFailed(**{
                'filter.state': f'unallowed value {filter_state}'
            })
        if filter_status and not is_filter_status_valid(filter_status):
            raise exceptions.ParameterValidationFailed(**{
                'filter.status': f'unallowed value {filter_status}'
            })

        limit = request.query.limit or None  # LIMIT NULL turns limit off
        offset = request.query.offset or 0

        projects = get_projects_list(
            request.db,
            user_active_org_id,
            user_active_org_role,
            user_is_admin,
            include_active_org_projects=include_active_org_projects,
            filter_search=filter_search,
            filter_state=filter_state,
            filter_status=filter_status,
            limit=limit,
            offset=offset,
        )
        return {
            'resource_type': 'bol_project_list',
            'resources': projects,
        }

    def post_projects_list(self):
        """Return a list of projects visible to the logged in user.

        ---
        requestBody:
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_active_org_id:
                    type: string
                    example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
                    description: >
                      the database ID of the active organization that the
                      logged in user currently represents
                  user_active_org_role:
                    type: string
                    enum:
                      - main
                      - basic
                    default: basic
                    description: >
                      the logged in user's role in the organization
                  user_is_admin:
                    type: boolean
                    default: false
                    description: >
                      does the user have administrative privileges (i.e. can
                      see all the companies)?
                  user_projects_ids:
                    type: array
                    nullable: true
                    default: null
                    items:
                      type: string
                      description: database ID of a project
                      example: 957d-838e0098c0edb161f68d26a2e419f441-4aa836c6
                    description: >
                      limit the result to companies involved with the projects listed herein
                  include_active_org_projects:
                    type: boolean
                    default: false
                    description: >
                      include preannouncement-using projects where user_active_org_id is a supplier?
                  filter.state:
                    type: string
                    enum:
                      - active
                      - closed
                      - draft
                      - "not:active"
                      - "not:closed"
                      - "not:draft"
                    description: >
                      list only those projects whose state matches (or does not
                      match) the specified state
                  filter.search:
                    type: string
                    example: Test Project
                    description: >
                      list only those projects whose name or user-specific internal
                      project ID matches this substring (case-insensitively)
                  filter.status:
                    type: string
                    enum:
                      - stop
                      - investigate
                      - incomplete
                      - attention
                      - ok
                      - "not:stop"
                      - "not:investigate"
                      - "not:incomplete"
                      - "not:attention"
                      - "not:ok"
                    description: >
                      list only those projects whose status matches (or does
                      not match) the specified value
                  limit:
                    type: integer
                    nullable: true
                    description: >
                      maximum number of items to return (for pagination),
                      `null` of `0` means unlimited.
                  offset:
                    type: integer
                    nullable: true
                    description: >
                      0-based index of the first item to return (for pagination),
                      `null` or `0` means start at the very beginning.
                  ff_block_project_client:
                    type: boolean
                    example: true
                    description: >
                      Excludes the project from visible projects if the project attribute
                      `added_client_can_view` is set to `false`, while
                      the project creator's role is the main contractor, and the project's client
                      organization is the same as the active organization.
                required:
                  - user_active_org_id
        responses:
          200:
            description: list of projects visible to the logged in user
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - "bol_project_list"
                      description: always "bol_project_list", for Qvarn compatibility
                    resources:
                      $ref: '#/components/schemas/project_list'
        """
        self._logger.info('Getting list of projects')

        user_active_org_id = request.json['user_active_org_id']
        user_active_org_role = request.json.get('user_active_org_role') or 'basic'
        user_is_admin = request.json.get('user_is_admin') == True
        user_projects_ids = request.json.get('user_projects_ids')

        include_active_org_projects = request.json.get('include_active_org_projects')

        filter_state = request.json.get('filter.state')
        filter_search = request.json.get('filter.search')
        filter_status = request.json.get('filter.status')

        if filter_state and not is_filter_state_valid(filter_state):
            raise exceptions.ParameterValidationFailed(**{
                'filter.state': f'unallowed value {filter_state}'
            })
        if filter_status and not is_filter_status_valid(filter_status):
            raise exceptions.ParameterValidationFailed(**{
                'filter.status': f'unallowed value {filter_status}'
            })

        limit = request.json.get('limit') or None  # LIMIT NULL turns limit off
        offset = request.json.get('offset') or 0

        ff_block_project_client = request.json.get('ff_block_project_client')

        projects = get_projects_list(
            request.db,
            user_active_org_id,
            user_active_org_role,
            user_is_admin,
            user_projects_ids,
            include_active_org_projects=include_active_org_projects,
            filter_search=filter_search,
            filter_state=filter_state,
            filter_status=filter_status,
            limit=limit,
            offset=offset,
            ff_block_project_client=ff_block_project_client,
        )
        response.status = 200  # not 201, we're not creating anything
        return {
            'resource_type': 'bol_project_list',
            'resources': projects,
        }

    def get_project_suppliers(self):
        """Return a list of suppliers for a project.

        ---
        parameters:
          - name: project_id
            in: query
            required: true
            schema:
              type: string
              example: 5cb4-90d994047f5b505023d5439dbb646fc1-685c5a20
            description: >
              the database ID of the project
          - name: user_active_org_id
            in: query
            required: true
            schema:
              type: string
              example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
            description: >
              the database ID of the active organization that the
              logged in user currently represents
        responses:
          200:
            description: list of suppliers in the project visible to the logged in user
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - "bol_supplier/bol_project_suppliers"
                      description: >
                        always "bol_supplier/bol_project_suppliers", for Qvarn compatibility
                    resources:
                      $ref: '#/components/schemas/project_supplier_list'
        """
        self._logger.info('Getting project suppliers')

        external_id = request.query.project_id
        user_active_org_id = request.query.user_active_org_id

        suppliers = get_project_suppliers(request.db, external_id, user_active_org_id)

        return {
            'resource_type': 'bol_supplier/bol_project_suppliers',
            'resources': suppliers,
        }

    def get_project_supplier_comments(self, external_id):
        """Fetch all comments for a project.
        ---
        parameters:
          - name: external_id
            in: path
            required: true
            schema:
              type: string
            description: The ID of the project to fetch comments about
          - name: org_id
            in: query
            required: false
            schema:
              type: string
            description: The ID of the organization to fetch comments about
          - name: read_by
            in: query
            required: false
            schema:
              type: string
            description: Optional person_id to fetch read status of each comment
          - name: hide_deleted
            in: query
            required: false
            schema:
              type: string
            description: Whether to exclude comments marked as deleted. Defaults to false
        responses:
          200:
            description: List of project supplier comments matching the query
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      example: "project_supplier_comments"
                    resources:
                      type: array
                      items:
                        $ref: '#/components/schemas/project_supplier_comment'
          404:
            description: The project was not found
            content:
              application/json:
                example:
                  {
                    "message": "Not found",
                    "error_code": "NotFound",
                    "error": {
                      "external_id": "bb72-749f75606c4331903b012118ed8c449d-0fe20015"
                    }
                  }
        """
        org_id = request.query.org_id
        hide_deleted = request.query.hide_deleted
        read_by = request.query.read_by
        project = get_project(request.db, external_id=external_id)
        if not project:
            raise exceptions.NotFound(external_id=external_id)
        comments = query_project_supplier_comments(
            request.db,
            project_id=external_id,
            org_id=org_id,
            hide_deleted=hide_deleted,
            read_by_person_id=read_by,
        )
        return {
            "resource_type": "project_supplier_comments",
            "resources": comments,
        }
