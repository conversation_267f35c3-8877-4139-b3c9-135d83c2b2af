import json
from logging import Logger
from typing import List

from bottle import request

from boldataapi import exceptions
from boldataapi.featureflags import feature_active
from boldataapi.schema import (
    ALLOWED_SUPPLIERS_SHOW,
    BolSupplierValidator,
    schema_existing_bol_supplier,
    schema_new_bol_supplier,
)
from boldataapi.services.bol_suppliers import (
    create_supplier,
    delete_supplier,
    get_supplier,
    get_supplier_ids,
    query_suppliers,
    search_bol_suppliers,
    update_supplier,
    update_supplier_internal_project_id,
)
from boldataapi.services.project_supplier_comments import (
    query_project_supplier_comments,
)
from boldataapi.services.projects import (
    get_project_id,
)


class BolSuppliersController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/bol_suppliers',
                'method': 'GET',
                'callback': self.get_bol_suppliers,
                'require_auth': True,
                'scopes': ['uapi_bol_suppliers_get'],
            },
            {
                'path': '/bol_suppliers/<external_id>',
                'method': 'GET',
                'callback': self.get_bol_supplier,
                'require_auth': True,
                'scopes': ['uapi_bol_suppliers_id_get'],
            },
            {
                'path': '/bol_suppliers',
                'method': 'POST',
                'callback': self.post_bol_supplier,
                'require_auth': True,
                'scopes': ['uapi_bol_suppliers_post'],
            },
            {

                'path': '/bol_suppliers/<external_id>',
                'method': 'PUT',
                'callback': self.put_bol_supplier,
                'require_auth': True,
                'scopes': ['uapi_bol_suppliers_id_put'],
            },
            {

                'path': '/bol_suppliers/<external_id>',
                'method': 'DELETE',
                'callback': self.delete_bol_supplier,
                'require_auth': True,
                'scopes': ['uapi_bol_suppliers_id_delete'],
            },
            {
                'path': '/bol_suppliers/search/<path:path>',
                'method': 'GET',
                'callback': self.bol_suppliers_search,
                'require_auth': True,
                'scopes': ['uapi_bol_suppliers_search_id_get']
            },
            {
                'path': '/bol_suppliers/query',
                'method': 'GET',
                'callback': self.suppliers_query,
                'require_auth': True,
                'scopes': ['uapi_bol_suppliers_id_get'],
            },
            {
                'path': '/bol_suppliers/<external_id>/comments',
                'method': 'GET',
                'callback': self.get_supplier_comments,
                'require_auth': True,
                'scopes': ['bda_project_supplier_comments_get'],
            },
        ]

    def get_bol_suppliers(self):
        """Return the IDs of all the suppliers.

        This is something that you should never use.
        ---
        responses:
          200:
            description: list of all supplier IDs
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - bol_supplier
                      description: always 'bol_supplier', for Qvarn compatibility
                    resources:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: supplier database ID (aka external ID)
                            example: 5c5c-57f02e616420d8051846fea17189cadd-d7790c6f
        """
        self._logger.info('Getting all supplier ids')
        suppliers = get_supplier_ids(request.db)
        return {
            'resource_type': 'bol_supplier',
            'resources': suppliers,
        }

    def get_bol_supplier(self, external_id):
        """Get a supplier by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/supplier_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: supplier information
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/supplier'
        """
        self._logger.info('Getting supplier %s', external_id)
        supplier = get_supplier(request.db, external_id=external_id)
        if not supplier:
            raise exceptions.NotFound(external_id=external_id)

        external_supplier_id = supplier.pop('external_id')
        supplier['id'] = external_supplier_id
        return supplier

    def post_bol_supplier(self):
        """Create a new supplier.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_supplier'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            description: the provided `project_resource_id` does not exist in the database
            content:
              application/json:
                example:
                  {
                    "message": "Not found",
                    "error_code": "NotFound",
                    "error": {
                      "project_resource_id": "a55f-7521303fbb16f824c9b9a2459bc2fedc-3cb8e412"
                    }
                  }
          201:
            description: the newly created supplier
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/supplier'
        """
        self._logger.info('Creating a new supplier')

        validator = BolSupplierValidator(schema_new_bol_supplier)
        if not validator.validate(request.json):
            self._logger.info('Supplier data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)

        project_id = get_project_id(request.db, data.get('project_resource_id'))

        if not project_id:
            raise exceptions.NotFound(project_resource_id=data.get('project_resource_id'))

        payload = {
            'project_id': project_id,
            'parent_supplier_id': data.get('parent_supplier_id'),
            'company_id': data.get('supplier_org_id'),
            'parent_company_id': data.get('parent_org_id'),
            'role': data.get('supplier_role'),
            'type': data.get('supplier_type'),
            'contract_start_date': data.get('contract_start_date'),
            'contract_end_date': data.get('contract_end_date'),
            'contract_work_areas': data.get('contract_work_areas'),
            'materialized_path': data.get('materialized_path'),
            'first_visited': data.get('first_visited') if feature_active('first_visited') else None,
            'last_visited': data.get('last_visited'),
            'contract_type': data.get('contract_type'),
            'visitor_type': data.get('visitor_type'),
            'is_one_man_company': data.get('is_one_man_company'),
            'has_collective_agreement': data.get('has_collective_agreement'),
            'collective_agreement_name': data.get('collective_agreement_name'),

        }

        supplier = create_supplier(
            request.db, payload,
            supplier_contacts=data.get('supplier_contacts'),
            internal_project_id=data.get('internal_project_id'),
        )

        self._logger.info('New supplier created %s', supplier['id'])

        external_supplier_id = supplier.pop('external_id')
        supplier['id'] = external_supplier_id
        return supplier

    def put_bol_supplier(self, external_id):  # noqa C901
        """Update a supplier.

        Fields that are omitted from the request will retain their old values.
        ---
        parameters:
          - $ref: '#/components/parameters/supplier_external_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_supplier'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            description: >
              the provided supplier `external_id` OR the provided `project_resource_id` does not
              exist in the database
            content:
              application/json:
                examples:
                  bad_supplier_id:
                    summary: the provided supplier ID does not exist in the database
                    value:
                      {
                        "message": "Not found",
                        "error_code": "NotFound",
                        "error": {
                          "external_id": "bb72-749f75606c4331903b012118ed8c449d-0fe20015"
                        }
                      }
                  bad_project_resource_id:
                    summary: the provided `project_resource_id` does not exist in the database
                    value:
                      {
                        "message": "Not found",
                        "error_code": "NotFound",
                        "error": {
                          "project_resource_id": "a55f-7521303fbb16f824c9b9a2459bc2fedc-3cb8e412"
                        }
                      }
          200:
            description: the updated supplier
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/supplier'
        """
        self._logger.info('Updating supplier %s', external_id)
        supplier = get_supplier(request.db, external_id=external_id)

        if not supplier:
            self._logger.info('Supplier %s was not found.', external_id)
            raise exceptions.NotFound(external_id=external_id)

        validator = BolSupplierValidator(schema_existing_bol_supplier)
        if not validator.validate(request.json):
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)
        if supplier['revision'] != data['revision']:
            error = {'revision': ['Wrong supplier revision provided']}
            raise exceptions.ParameterValidationFailed(**error)
        payload = {}

        if data.get('project_resource_id'):
            project_id = get_project_id(request.db, data['project_resource_id'])
            if not project_id:
                raise exceptions.NotFound(project_resource_id=data['project_resource_id'])
            payload['project_id'] = project_id

        # None is a valid value for parent_supplier_id if data exists
        if 'parent_supplier_id' in data:
            payload['parent_supplier_id'] = data['parent_supplier_id']

        # None is a valid value for parent_company_id if data exists
        if 'parent_org_id' in data:
            payload['parent_company_id'] = data['parent_org_id']

        if data.get('supplier_org_id'):
            payload['company_id'] = data['supplier_org_id']

        if data.get('supplier_role'):
            payload['role'] = data['supplier_role']

        if data.get('supplier_type'):
            payload['type'] = data['supplier_type']

        if data.get('contract_start_date'):
            payload['contract_start_date'] = data['contract_start_date']

        if data.get('contract_end_date'):
            payload['contract_end_date'] = data['contract_end_date']

        if data.get('contract_work_areas') is not None:
            payload['contract_work_areas'] = data['contract_work_areas']

        if data.get('materialized_path'):
            payload['materialized_path'] = data['materialized_path']

        if data.get('last_visited'):
            payload['last_visited'] = data['last_visited']

        if feature_active('first_visited') and data.get('first_visited'):
            payload['first_visited'] = data['first_visited']

        if data.get('revision'):
            payload['revision'] = data['revision']

        if data.get('contract_type'):
            payload['contract_type'] = data['contract_type']

        if data.get('visitor_type'):
            payload['visitor_type'] = data['visitor_type']

        # None is a valid value for is_one_man_company if data exists
        if 'is_one_man_company' in data:
            payload['is_one_man_company'] = data['is_one_man_company']

        # None is a valid value for has_collective_agreement if data exists
        if 'has_collective_agreement' in data:
            payload['has_collective_agreement'] = data['has_collective_agreement']

        # None is a valid value for collective_agreement_name if data exists
        if 'collective_agreement_name' in data:
            payload['collective_agreement_name'] = data['collective_agreement_name']

        update_supplier(
            request.db, supplier['id'], payload,
            supplier_contacts=data.get('supplier_contacts'),
        )
        # internal_project_id can be ommited from request
        # or can have None set explicitly
        if 'internal_project_id' in data.keys():
            # take updated value of project_resource_id if exists
            project_resource_id = data.get('project_resource_id') or supplier['project_resource_id']
            supplier_company_id = data.get('supplier_org_id') or supplier['supplier_org_id']
            project_id = get_project_id(request.db, project_resource_id)

            update_supplier_internal_project_id(
                request.db,
                data['internal_project_id'],
                project_id,
                supplier_company_id,
            )

        updated_supplier = get_supplier(request.db, supplier_id=supplier['id'])
        external_supplier_id = updated_supplier.pop('external_id')
        updated_supplier['id'] = external_supplier_id
        return updated_supplier

    def delete_bol_supplier(self, external_id):
        """Delete a supplier.
        ---
        parameters:
          - $ref: '#/components/parameters/supplier_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """

        self._logger.info('Deleting supplier %s', external_id)

        supplier = get_supplier(request.db, external_id=external_id)
        if not supplier:
            self._logger.info('Supplier %s was not found.', external_id)
            raise exceptions.NotFound(supplier_id=external_id)
        delete_supplier(request.db, supplier['id'])
        return {}

    def bol_suppliers_search(self, path: str):
        """Return a list of suppliers that match a query.
        ---
        parameters:
          - name: path
            in: path
            schema:
              type: string
            required: true
            # XXX: OpenAPI spec doesn't allow us to express that `path` can contain unescaped
            # slashes: https://github.com/OAI/OpenAPI-Specification/issues/892
            description: |
              Qvarn query

              Qvarn search queries consist of multiple path segments, separated
              with (unescaped) slashes.  Most of the segments describe search criteria,
              which consist of an operator followed by a field name, followed by a value.
              The operators are

              - exact
              - gt
              - ge
              - lt
              - le
              - ne
              - startswith
              - contains

              The values are plain strings.  You cannot match on `null`.

              An operator can be preceded by a special `any` segment, which affects it by
              allowing a match against any of the provided values.  In this
              case the value following the field name needs to be an
              URL-escaped JSON-encoded list.

              Multiple search criteria are ANDed together.

              There are also special operators that affect how the results are returned:

              - show/{field_name} - include {field_name} in the results
              - show_all - include all fields in the results
              - sort/{field_name} - sort by given field
              - rsort/{field_name} - reverse sort by given field
              - limit/{n} - limit to N results
              - offset/{n} - skip first N results

              If you want to specify offset or limit, you must also specify sorting.  Multiple
              sort parameters can be provided.

              If you don't specify any `show`/`show_all` operators, only the IDs will be returned.

              This method is deprecated, but we don't have a replacement yet.
            examples:
              one_field_exact_match:
                summary: exact match on project_resource_id
                value:
                  exact/project_resource_id/d3f0-23ed685d507b98a5425dedc68d4fbc3a-94d9494b
              any_match:
                summary: supplier_role is either supervisor or main contractor
                value:
                  any/exact/supplier_role/["supervisor","main_contractor"]
              match on a subfield:
                summary: match on a supplier contact with a given email existing
                value:
                  exact/supplier_contact_email/<EMAIL>
              pagination:
                summary: arbitrary and unrealistic example of pagination
                value:
                  sort/project_resource_id/sort/supplier_org_id/offset/15/limit/5/show_all
        deprecated: true
        responses:
          200:
            description: query results
            content:
              application/json:
                schema:
                  properties:
                    resources:
                      type: array
                      items:
                        type: object
                        description: |
                          a subset of the supplier resource, depending on which fields you've
                          requested in the query.

                          The `id` field is always returned.
                        properties:
                          id:
                            description: supplier database ID
                            example: f158-543335c279f4a3ff7b08b1ba9794892d-277e40d7
                        additionalProperties: true
                        example:
                          {
                            "id": "804a-9819f78349496e76c993951197679386-8b8300ff"
                          }
        """
        result = search_bol_suppliers(request.db, path)
        return dict(result)

    def suppliers_query(self):
        """Return a list of suppliers that match a query.

        ---
        parameters:
          - name: q
            in: query
            description: |
              search query in URL-encoded JSON

              The keys of the query object are database column names optionally suffixed with an
              operator ("eq"/"ne"/"lt"/"gt"/"le"/"ge"/"any") following a "__".
              The values are search values for that field (or a list of search values for the
              "any" operator).  When searching with multiple conditions, they are ANDed together.

              Database columns you can search on are:
              - id (database ID aka external ID)
              - supplier_role
              - supplier_type
              - contract_start_date
              - contract_end_date
              - contract_type
              - contract_work_areas
              - visitor_type
              - project_resource_id
              - parent_supplier_id
              - parent_org_id
              - supplier_org_id
              - first_visited
              - last_visited
              - revision
              - pa_id
              - pa_status
              - is_one_man_company
              - has_collective_agreement
              - collective_agreement_name
            required: true
            content:
              application/json:
                # XXX: swagger-ui fails to show this schema anywhere, hence
                # it's essentially replicated in plain text in the description
                # field above
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >
                        search by supplier ID (aka external_id)

                        There's really no reason to search by ID instead of using a simple HTTP GET.
                    supplier_role:
                      type: string
                      description: search by supplier_role
                      enum:
                        - main_contractor
                        - supervisor
                        - supplier
                    supplier_type:
                      type: string
                      description: search by supplier_type
                      enum:
                        - linked
                        - unlinked
                        - visitor
                    contract_start_date:
                      type: string
                      description: search by the contract_start_date, e.g. "2022-01-01"
                    contract_end_date:
                      type: string
                      description: search by the contract_end_date, e.g. "2022-01-01"
                    contract_type:
                      type: string
                      description: search by contract_type, e.g. "consulting"
                    contract_type:
                      type: string
                      description: search by contract_type, e.g. "consulting"
                    visitor_type:
                      type: string
                      description: search by visitor_type
                      enum:
                        - nonpaed
                        - raw
                    project_resource_id:
                      type: string
                      description: search by project_resource_id (aka external_id)
                    project_resource_id:
                      type: string
                      description: search by project_resource_id (aka external_id)'
                    parent_supplier_id:
                      type: string
                      description: search by parent_supplier_id
                    supplier_org_id:
                      type: string
                      description: search by supplier_org_id
                    first_visited:
                      type: string
                      description: search by first_visited, e.g. "2022-01-01"
                    last_visited:
                      type: string
                      description: search by last_visited, e.g. "2022-01-01"
                    revision:
                      type: string
                      description: search by revision id.
                    pa_id:
                      type: string
                      description: search by pa_id (preannouncement id)
                    pa_status:
                      type: string
                      description: search by pa_status
                      enum:
                        - created
                        - registered
                        - confirmed
                        - rejected
                    is_one_man_company:
                      type: boolean
                      description: search by is_one_man_company
                    has_collective_agreement:
                      type: boolean
                      description: search by has_collective_agreement
                    collective_agreement_name:
                      type: string
                      description: search by collective_agreement_name

            # XXX: editor.swagger.io insists that 'examples' is wrong here and it should be
            # indented further, into content["application/json"].schema.examples, but if I do that,
            # then swagger-ui fails to show the examples!
            examples:
              simple:
                summary: a simple query on one field
                value:
                  {"id": "c4d7-860d817a9bc6f3f2e31538dbac0bc2a8-cbbd9557"}
                description: >
                  Searches for suppliers with the specified id.
              multiple_fields:
                summary: a simple query on multiple fields
                value:
                  {
                    "project_resource_id": "d53f-46d8e3de8c0ce571b7455f8744e13014-c2992b4f",
                    "visitor_type": "raw"
                  }
                description: >
                  Searches for visitor suppliers with type raw in a specific project.
              multiple_enum_values:
                summary: a simple query on a single field with multiple allowed values
                value:
                  {"pa_status__any": ["confirmed", "rejected"]}
                description: >
                  Searches for suppliers that have pa status either confirmed or rejected
        responses:
          200:
            description: List of search results
            content:
              application/json:
                schema:
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - suppliers
                      description: always "suppliers"
                    resources:
                      type: array
                      description: list of matching suppliers
                      items:
                        $ref: '#/components/schemas/supplier'
                examples:
                  no_matches:
                    summary: Result when nothing matches
                    value:
                      {
                        "resource_type": "suppliers",
                        "resources": []
                      }
                  matches_exist:
                    summary: Result when something matches
                    value:
                      {
                        "resource_type": "suppliers",
                        "resources": [
                          {
                            "id": "c732b47b-9358-49d2-8dc1-9167b685e389",
                            "revision": "ab32b47b-9358-49d2-8dc1-9167b685e389",
                            "supplier_role": "supplier",
                            "supplier_type": "linked",
                            "contract_start_date": "2020-01-01",
                            "contract_end_date": "2020-12-01",
                            "contract_type": None,
                            "contract_work_areas": None,
                            "materialized_path": ["item1", "item2"],
                            "project_resource_id": "external_id",
                            "pa_id": None,
                            "pa_status": None,
                            "internal_project_id": "Test Internal Project ID",
                            "parent_supplier_id": "any-parent-supplier-id",
                            "parent_org_id": "any-parent-id",
                            "supplier_org_id": "any-company-id",
                            "supplier_contacts": [
                                {
                                    "supplier_contact_email": "<EMAIL>",
                                    "supplier_contact_person_id": "fake_person_id"
                                },
                                {
                                    "supplier_contact_person_id": "fake_person_id2",
                                    "supplier_contact_email": "<EMAIL>"
                                }
                            ],
                            "bolagsfakta_status": None,
                            "type": "bol_supplier",
                            "first_visited": None,
                            "last_visited": None,
                            "visitor_type": None,
                            "is_one_man_company": None,
                            "has_collective_agreement": None,
                            "collective_agreement_name": None
                          }
                        ]
                      }
          400:
            description: Error in query (e.g. JSON parse error)
            content:
              application/json:
                example:
                  {
                    "message": "Could not parse search condition",
                    "error_code": "BadSearchCondition"
                  }
        """
        try:
            query = json.loads(request.query.q)
        except ValueError:
            raise exceptions.BadSearchCondition()

        show = []
        if request.query.show:
            try:
                show = json.loads(request.query.show)
                if not isinstance(show, list):
                    error = {'show': 'Must be a list'}
                    raise exceptions.ParameterValidationFailed(**error)
                if not all(field in ALLOWED_SUPPLIERS_SHOW for field in show):
                    error = {'show': f'Allowed values in list: {", ".join(ALLOWED_SUPPLIERS_SHOW)}'}
                    raise exceptions.ParameterValidationFailed(**error)
            except ValueError:
                raise exceptions.BadShowCondition()

        result = query_suppliers(request.db, query)

        if show:
            suppliers_show = _select_show_fields(result, show)

            return {
                'resource_type': 'suppliers',
                'resources': suppliers_show
            }
        else:
            return {
              'resource_type': 'suppliers',
              'resources': [
                  dict(s, id=s.pop('external_id'))
                  for s in result
              ]
            }

    def get_supplier_comments(self, external_id):
        """Fetch all comments for a supplier.
        ---
        parameters:
          - name: external_id
            in: path
            required: true
            schema:
              type: string
            description: The ID of the supplier to fetch comments about
          - name: hide_deleted
            in: query
            required: false
            schema:
              type: string
            description: Whether to include comments marked as deleted. Defaults to false
          - name: read_by
            in: query
            required: false
            schema:
              type: string
            description: Optional person_id to fetch read status of each comment
        responses:
          200:
            description: List of project supplier comments matching the query
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    resource_type:
                      type: string
                      example: "project_supplier_comments"
                    resources:
                      type: array
                      items:
                        $ref: '#/components/schemas/project_supplier_comment'
          404:
            description: The supplier was not found
            content:
              application/json:
                example:
                  {
                    "message": "Not found",
                    "error_code": "NotFound",
                    "error": {
                      "external_id": "bb72-749f75606c4331903b012118ed8c449d-0fe20015"
                    }
                  }
        """
        supplier = get_supplier(request.db, external_id=external_id)
        if not supplier:
            raise exceptions.NotFound(external_id=external_id)
        hide_deleted = request.query.hide_deleted
        read_by = request.query.read_by
        comments = query_project_supplier_comments(
            request.db,
            supplier_id=external_id,
            hide_deleted=hide_deleted,
            read_by_person_id=read_by,
        )
        return {
            "resource_type": "project_supplier_comments",
            "resources": comments,
        }


def _select_show_fields(suppliers, show):
    suppliers_show_fields = []
    for s in suppliers:
        supplier_to_be_added = {'id': s.pop('external_id')}
        for field in show:
            supplier_to_be_added[field] = s[field]
        suppliers_show_fields.append(supplier_to_be_added)
    return suppliers_show_fields
