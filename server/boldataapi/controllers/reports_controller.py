import datetime
from logging import Logger
from typing import List

from bottle import request

from boldataapi import exceptions
from boldataapi.schema import (
    ALLOWED_PROVIDERS,
    REPORT_TYPE_STATUS_REPORT,
    ReportValidator,
    schema_existing_notification_report,
    schema_existing_status_report,
    schema_move_report_to_history,
    schema_new_base_report,
    schema_new_notification_report,
    schema_new_status_report,
    schema_reports_pdf,
)
from boldataapi.serialize import asbool
from boldataapi.services.notification_reports import (
    create_notification_report,
    delete_notification_report,
    get_notification_report,
    update_notification_report,
    update_notification_report_pdf,
)
from boldataapi.services.reports import search_reports
from boldataapi.services.status_reports import (
    create_status_report,
    delete_status_report,
    get_all_status_report_intrstd_cmpny_id_to_cmpny_id,
    get_historic_status_report,
    get_latest_status_report_for_company,
    get_status_report,
    get_status_reports,
    move_old_status_reports_to_history,
    move_status_reports_to_history,
    update_status_report,
    update_status_report_pdf,
)


CONFLICTING_IDS_ERROR_MSG = ("cannot process this request due to conflict "
                             "of status_reports and notification_reports ids")


class ReportsController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/reports/<external_id>',
                'method': 'GET',
                'callback': self.get_report,
                'require_auth': True,
                'scopes': ['uapi_reports_id_get'],
            },
            {  # Defunct, QAT compat
                'path': '/reports/<external_id>/sync',
                'method': 'GET',
                'callback': self.get_report_sub_resource,
                'require_auth': True,
                'scopes': ['uapi_reports_id_get'],
            },
            {
                'path': '/reports',
                'method': 'GET',
                'callback': self.get_reports,
                'require_auth': True,
                'scopes': ['uapi_reports_search_id_get']
            },
            {
                'path': '/reports',
                'method': 'POST',
                'callback': self.post_report,
                'require_auth': True,
                'scopes': ['uapi_reports_post']
            },
            {
                'path': '/reports/<external_id>',
                'method': 'PUT',
                'callback': self.put_report,
                'require_auth': True,
                'scopes': ['uapi_reports_id_put'],
            },
            {  # Defunct, QAT compat
                'path': '/reports/<external_id>/sync',
                'method': 'PUT',
                'callback': self.put_report_sub_resource,
                'require_auth': True,
                'scopes': ['uapi_reports_id_put'],
            },
            {  # Deprecated, Qvarn compat
                'path': '/reports/search/<path:path>',
                'method': 'GET',
                'callback': self.reports_search,
                'require_auth': True,
                'scopes': ['uapi_reports_search_id_get']
            },
            {
                'path': '/reports/by_company',
                'method': 'GET',
                'callback': self.get_report_by_company,
                'require_auth': True,
                'scopes': ['uapi_reports_search_id_get']
            },
            {
                'path': '/reports/<external_id>/pdf',
                'method': 'GET',
                'callback': self.get_report_pdf,
                'require_auth': True,
                'scopes': ['uapi_reports_id_pdf_get'],
            },
            {
                'path': '/reports/<external_id>/pdf',
                'method': 'PUT',
                'callback': self.put_report_pdf,
                'require_auth': True,
                'scopes': ['uapi_reports_id_pdf_put'],
            },
            {
                'path': '/reports/<external_id>',
                'method': 'DELETE',
                'callback': self.delete_report,
                'require_auth': True,
                'scopes': ['uapi_reports_id_delete'],
            },
            {
                'path': '/reports/interested_company_ids_to_company_ids',
                'method': 'GET',
                'callback': self.get_all_interested_company_id_to_company_id,
                'require_auth': True,
                'scopes': ['uapi_reports_search_id_get']
            },
            {
                'path': '/reports/move_old_to_history',
                'method': 'POST',
                'callback': self.move_old_to_history,
                'require_auth': True,
                'scopes': ['uapi_reports_post']
            },
            {
                'path': '/reports/move_to_history',
                'method': 'POST',
                'callback': self.move_to_history,
                'require_auth': True,
                'scopes': ['uapi_reports_post']
            },
            {
                'path': '/reports/historic/<external_id>',
                'method': 'GET',
                'callback': self.get_historic_report,
                'require_auth': True,
                'scopes': ['uapi_reports_id_get'],
            },
        ]

    def get_report(self, external_id: str):
        """Get a report by ID.

        Returns metadata only; use GET /reports/{external_id}/pdf to get the
        contents of the report.
        ---
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: report metadata
            content:
              application/json:
                schema:
                  oneOf:
                    - $ref: '#/components/schemas/status_report_qvarn_compat'
                    - $ref: '#/components/schemas/notification_report_qvarn_compat'
        """
        self._logger.info('Getting report %s', external_id)

        status_report, notification_report = _get_reports(request.db, external_id)
        report = status_report or notification_report

        report_external_id = report.pop('external_id')
        report['id'] = report_external_id
        report.pop('pdf')
        return report

    def get_all_interested_company_id_to_company_id(self):
        """
        Get all interested_company_id to company_id mappings
        ---
        parameters:
          - name: start_date
            in: query
            description: start date of report mappings
            required: true
            schema:
              type: string
              format: date
          - name: end_date
            in: query
            description: end date of report mappings (inclusive)
            required: true
            schema:
              type: string
              format: date
        responses:
          200:
            description: list of interested_company_id to company_id mappings
            content:
              application/json:
                schema:
                  type: array
                  items:
                    type: object
                    properties:
                      interested_company_id:
                        type: string
                      company_id:
                        type: string
          400:
            $ref: '#/components/responses/bad_request'
        """
        self._logger.info('Getting interested company ids to company ids')

        start_date = request.query.start_date
        end_date = request.query.end_date

        if start_date:
            try:
                start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                error = {'start_date': 'Must be in YYYY-MM-DD format'}
                raise exceptions.ParameterValidationFailed(**error)
        else:
            error = {'start_date': 'Must be specified'}
            raise exceptions.ParameterValidationFailed(**error)

        if end_date:
            try:
                end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                error = {'end_date': 'Must be in YYYY-MM-DD format'}
                raise exceptions.ParameterValidationFailed(**error)
        else:
            error = {'end_date': 'Must be specified'}
            raise exceptions.ParameterValidationFailed(**error)

        if start_date > end_date:
            error = {'end_date': 'Must be greater than or equal to start_date'}
            raise exceptions.ParameterValidationFailed(**error)

        start_timestamp = start_date
        end_timestamp = end_date + datetime.timedelta(1)
        res = get_all_status_report_intrstd_cmpny_id_to_cmpny_id(
            request.db, start_timestamp, end_timestamp)

        returning_list = []
        for intrst_cmpny_id, cmpny_id in res:
            returning_list.append({
                'interested_company_id': intrst_cmpny_id,
                'company_id': cmpny_id
            })

        return {'interested_company_ids_to_company_ids': returning_list}

    def move_old_to_history(self):
        """
        Move all old reports between start_date and end_date to history
        ---
        parameters:
          - name: start_date
            in: query
            description: start date of reports to move
            required: true
            schema:
              type: string
              format: date
          - name: end_date
            in: query
            description: end date of reports to move (inclusive)
            required: true
            schema:
              type: string
              format: date
        responses:
          200:
            description: number of moved reports
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    moved:
                      type: integer
          400:
            $ref: '#/components/responses/bad_request'
        """
        self._logger.info('Archiving old reports')

        start_date = request.query.start_date
        end_date = request.query.end_date

        if start_date:
            try:
                start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                error = {'start_date': 'Must be in YYYY-MM-DD format'}
                raise exceptions.ParameterValidationFailed(**error)
        else:
            error = {'start_date': 'Must be specified'}
            raise exceptions.ParameterValidationFailed(**error)

        if end_date:
            try:
                end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                error = {'end_date': 'Must be in YYYY-MM-DD format'}
                raise exceptions.ParameterValidationFailed(**error)
        else:
            error = {'end_date': 'Must be specified'}
            raise exceptions.ParameterValidationFailed(**error)

        if start_date > end_date:
            error = {'end_date': 'Must be greater than or equal to start_date'}
            raise exceptions.ParameterValidationFailed(**error)

        start_timestamp = start_date
        end_timestamp = end_date + datetime.timedelta(1)
        res = move_old_status_reports_to_history(request.db, start_timestamp, end_timestamp)

        return {'moved': res}

    def move_to_history(self):
        """
        Move reports to history by interested_company_id to company_id mappings
        ---
        requestBody:
          content:
            application/json:
              schema:
                required:
                  - interested_company_ids_to_company_ids
                properties:
                  interested_company_ids_to_company_ids:
                    description: interested_company_id to company_id mappings
                    type: array
                    items:
                      type: object
                      properties:
                        interested_company_id:
                          type: string
                        company_id:
                          type: string
        responses:
          200:
            description: number of moved reports
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    moved:
                      type: integer
          400:
            $ref: '#/components/responses/bad_request'
        """
        self._logger.info('Moving reports to history')

        validator = ReportValidator(schema_move_report_to_history)
        if not validator.validate(request.json):
            self._logger.info('Report data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        intrstd_cmpny_ids_to_cmpny_ids = request.json.get('interested_company_ids_to_company_ids')
        if not intrstd_cmpny_ids_to_cmpny_ids:
            return {'moved': 0}

        nr_moved = move_status_reports_to_history(request.db, intrstd_cmpny_ids_to_cmpny_ids)

        return {'moved': nr_moved}

    def get_report_sub_resource(self, external_id):
        """QAT compatibility endpoint.

        No real support for sub-resources.

        ---
        deprecated: true
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        responses:
          200:
            description: always an empty object
            content:
              application/json:
                schema:
                  properties: {}
        """
        return {}

    def post_report(self):
        """Create a new report

        ---
        requestBody:
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/new_status_report'
                  - $ref: '#/components/schemas/new_notification_report'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          201:
            description: the newly created report metadata
            content:
              application/json:
                schema:
                  oneOf:
                    - $ref: '#/components/schemas/status_report_qvarn_compat'
                    - $ref: '#/components/schemas/notification_report_qvarn_compat'
        """
        self._logger.info('Creating a new report')

        # validate base fields for both types or reports
        # ignore rest fields, which will be validated in futher steps
        validator = ReportValidator(schema_new_base_report, allow_unknown=True)
        if not validator.validate(request.json):
            self._logger.info('Report data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)
        base_data = validator.normalized(request.json)

        if base_data['report_type'] == REPORT_TYPE_STATUS_REPORT:
            validator = ReportValidator(schema_new_status_report)
            if not validator.validate(request.json):
                self._logger.info('Report data validation failed.')
                raise exceptions.ParameterValidationFailed(**validator.errors)

        else:
            validator = ReportValidator(schema_new_notification_report)
            if not validator.validate(request.json):
                self._logger.info('Report data validation failed.')
                raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request.json)
        pdf = data.get('pdf')
        payload = {
            'status': data.get('tilaajavastuu_status'),
            'generated_timestamp': data.get('generated_timestamp'),
            'interested_company_id': data.get('interested_org_id'),
            'company_id': data.get('org'),
            'charge_reference': data.get('charge_reference'),
            'used_providers': data.get('used_providers'),
            'json_': pdf['body'] if pdf else None,
        }
        if data['report_type'] == 'bolagsfakta.company_report':
            report = create_status_report(request.db, payload)
        else:
            report = create_notification_report(request.db, payload)

        report['id'] = report['external_id']
        report.pop('external_id')
        report.pop('pdf')
        return report

    def put_report(self, external_id: str):
        """Update a report by ID.

        This allows both metadata and content updates.

        Note that you cannot change a report's type; if the `report_type` field is provided,
        it must match the existing value.

        Fields that are omitted from the request will retain their old values.
        ---
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        requestBody:
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/update_status_report'
                  - $ref: '#/components/schemas/update_notification_report'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: report contents
            content:
              application/json:
                schema:
                  oneOf:
                    - $ref: '#/components/schemas/status_report_qvarn_compat'
                    - $ref: '#/components/schemas/notification_report_qvarn_compat'
        """
        self._logger.info('Updating report %s', external_id)

        status_report, notification_report = _get_reports(request.db, external_id)
        if status_report:
            validator = ReportValidator(schema_existing_status_report)
        else:
            validator = ReportValidator(schema_existing_notification_report)

        if not validator.validate(request.json):
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)
        if status_report:
            payload = _prepare_data_update_status_report(data)
            updated = update_status_report(request.db, status_report['id'], payload)
        if notification_report:
            payload = _prepare_data_update_notification_report(data)
            updated = update_notification_report(
                request.db, notification_report['external_id'], payload)

        report_external_id = updated.pop('external_id')
        updated['id'] = report_external_id
        updated.pop('pdf')
        return updated

    def put_report_sub_resource(self, external_id: str):
        """QAT compatibility endpoint.

        As we have no real support for sub-resources, this does nothing.

        ---
        deprecated: true
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: always an empty object
            content:
              application/json:
                schema:
                  properties:
                    revision:
                      type: string
                      nullable: true
                      example: null
                      description: |
                        always `null`, for Qvarn compatibility

                        This is intended to avoid update conflicts, but that is
                        not currently implemented.
        """
        status_report, notification_report = _get_reports(request.db, external_id)
        report = status_report or notification_report
        if not report:
            raise exceptions.NotFound(external_id=external_id)

        # QAT expects updated sub-resource to contain field 'revision' that is
        # then saved as the 'revision' for the resource itself.
        report_sub_resource = {'revision': report['revision']}
        return report_sub_resource

    def get_report_by_company(self):
        """Look up the latest status report by company ID

        ---
        parameters:
          - name: company_id
            in: query
            required: true
            schema:
              type: string
            example: 6144-db63de8f6fc6c0e451ddfc509eb74556-10cd5cb5
            description: database ID (aka external ID) of the company
          - name: interested_company_id
            in: query
            required: true
            schema:
              type: string
              minLength: 0
            example: 7cb6-b96a26d20444d6569faed214d214af41-f1e6532c
            description: |
              database ID (aka external ID) of the interested company

              This field is required, but its value can be blank.

              Unfortunately I was unable to convince Swagger UI to allow blank values
              and put them in the query string as &interested_org_id=
          - name: provider
            in: query
            required: false
            schema:
              type: string
            example: creditsafe_v2
            description: |
              restrict reports to those generated with this report provider;
              omit or use blank value for no restriction
          - name: include_json
            in: query
            required: false
            schema:
              type: boolean
              default: true
            description: |
              include the full report json in the response?
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the latest report for this company
            content:
              application/json:
                schema:
                  oneOf:
                    - $ref: '#/components/schemas/status_report_without_json'
                    - $ref: '#/components/schemas/status_report_with_json'
        """

        company_id = request.query.company_id
        interested_company_id = request.query.interested_company_id
        provider = request.query.provider
        try:
            include_json = asbool(request.query.include_json, default=True)
        except ValueError as e:
            raise exceptions.ParameterValidationFailed(include_json=str(e))

        errors = {}
        if not company_id:
            errors['company_id'] = 'parameter is required'
        # Bottle request.query.foo returns '' when foo is missing. We want an
        # error when the parameter is missing, but also we want to accept blank
        # values without error.
        if 'interested_company_id' not in request.query:
            errors['interested_company_id'] = 'parameter is required'
        if errors:
            raise exceptions.ParameterValidationFailed(**errors)

        report = get_latest_status_report_for_company(
            request.db,
            company_id=company_id,
            interested_company_id=interested_company_id,
            provider=provider,
            include_json=include_json,
        )
        if not report:
            params = dict(company_id=company_id,
                          interested_company_id=interested_company_id)
            if provider:
                params['provider'] = provider
            raise exceptions.NotFound(**params)
        report['id'] = report.pop('external_id')
        return report

    def get_reports(self):  # noqa: C901
        """Look up the status reports by date range

        Includes historical reports.
        ---
        parameters:
          - name: start_date
            in: query
            required: false
            schema:
              type: string
              format: date
            example: 2019-01-01
            description: |
              restrict reports to those generated on or after this date;
              format is YYYY-MM-DD
          - name: end_date
            in: query
            required: false
            schema:
              type: string
              format: date
            example: 2019-01-31
            description: |
              restrict reports to those generated on or before this date;
              format is YYYY-MM-DD
          - name: provider
            in: query
            required: false
            schema:
              type: string
            example: creditsafe_connect
            description: |
              restrict reports to given report provider type
          - name: company_ids
            in: query
            required: false
            schema:
              type: string
            example: 123,456
            description: |
              restrict reports to given company IDs, separated by commas
          - name: interested_company_id
            in: query
            required: false
            schema:
              type: string
            example: 789
            description: |
              restrict reports to given interested company ID
          - name: limit
            in: query
            required: false
            schema:
              type: integer
              default: null
            description: |
              Maximum number of reports to return. If not specified, returns all reports.
          - name: cursor
            in: query
            required: false
            schema:
              type: string
            example: test-report-id-123
            description: |
              cursor for pagination, use the next_cursor from previous response
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: all the reports
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    reports:
                      type: array
                      items:
                        $ref: '#/components/schemas/status_report_with_json'
                    pagination:
                      type: object
                      properties:
                        limit:
                          type: integer
                          description: Maximum number of reports returned
                        has_more:
                          type: boolean
                          description: Indicates if more results are available
                        next_cursor:
                          type: string
                          description: Cursor for fetching next page of results
        """
        start_date = request.query.get('start_date')
        end_date = request.query.get('end_date')
        provider = request.query.get('provider')
        company_ids = request.query.get('company_ids')
        interested_company_id = request.query.get('interested_company_id')

        # Pagination parameters
        limit = request.query.get('limit')
        cursor = request.query.get('cursor')

        # Convert limit to integer if provided
        limit = int(limit) if limit else None

        if start_date:
            try:
                start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                error = {'start_date': 'Must be in YYYY-MM-DD format'}
                raise exceptions.ParameterValidationFailed(**error)

        if end_date:
            try:
                end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                error = {'end_date': 'Must be in YYYY-MM-DD format'}
                raise exceptions.ParameterValidationFailed(**error)

        # Validate date range
        if start_date and end_date and start_date > end_date:
            error = {'end_date': 'Must be greater than or equal to start_date'}
            raise exceptions.ParameterValidationFailed(**error)

        # Validate provider
        if provider and provider not in ALLOWED_PROVIDERS:
            error = {'provider': f'Allowed values: {", ".join(ALLOWED_PROVIDERS)}'}
            raise exceptions.ParameterValidationFailed(**error)

        # Parse company IDs
        company_ids = company_ids.split(',') if company_ids else []

        # Get reports
        result = get_status_reports(
            request.db,
            start_timestamp=start_date,
            end_timestamp=end_date + datetime.timedelta(1) if end_date else None,
            provider=provider,
            company_ids=company_ids,
            interested_company_id=interested_company_id,
            limit=limit,
            cursor=cursor
        )

        reports = result['reports']
        for report in reports:
            report['id'] = report.pop('external_id')

        return {
            'reports': reports,
            'pagination': result['pagination']
        }

    def reports_search(self, path: str):
        """Return a list of reports that match a query.
        ---
        parameters:
          - name: path
            in: path
            schema:
              type: string
            required: true
            # XXX: OpenAPI spec doesn't allow us to express that `path` can contain unescaped
            # slashes: https://github.com/OAI/OpenAPI-Specification/issues/892
            description: |
              Qvarn query

              Qvarn search queries consist of multiple path segments, separated
              with (unescaped) slashes.  Most of the segments describe search criteria,
              which consist of an operator followed by a field name, followed by a value.
              The operators are

              - exact
              - gt
              - ge
              - lt
              - le
              - ne
              - startswith
              - contains

              The values are plain strings.  You cannot match on `null`.

              An operator can be preceded by a special `any` segment, which affects it by
              allowing a match against any of the provided values.  In this
              case the value following the field name needs to be an
              URL-escaped JSON-encoded list.

              Multiple search criteria are ANDed together.

              There are also special operators that affect how the results are returned:

              - show/{field_name} - include {field_name} in the results
              - show_all - include all fields in the results
              - sort/{field_name} - sort by given field
              - rsort/{field_name} - reverse sort by given field
              - limit/{n} - limit to N results
              - offset/{n} - skip first N results

              If you want to specify offset or limit, you must also specify sorting.  Multiple
              sort parameters can be provided.

              If you don't specify any `show`/`show_all` operators, only the IDs will be returned.

              Note that you cannot search for, or return report contents.  Only metadata.

              This method is deprecated, but we don't have a replacement yet.
            examples:
              one_field_exact_match:
                summary: exact match on org
                value:
                  exact/org/6991-79eb6caba29dc8db7d55d54a26bbdfd9-f47819c4
              any_match:
                summary: bolagsfakta_status is either "100 STOP" or "200 INVESTIGATE"
                value:
                  any/exact/bolagsfakta_status/%5B100%20STOP","200%20INVESTIGATE"%5D
              pagination:
                summary: arbitrary and unrealistic example of pagination
                value:
                  sort/org/sort/generated_timestamp/offset/15/limit/5/show_all
        deprecated: true
        responses:
          200:
            description: query results
            content:
              application/json:
                schema:
                  properties:
                    resources:
                      type: array
                      items:
                        type: object
                        description: |
                          a subset of the report resource, depending on which fields you've
                          requested in the query.

                          The `id` field is always returned.
                        properties:
                          id:
                            description: report database ID
                            example: dfd1-f09efa38d2f1ebc92e1cc188ae7cebd4-26ca5f5c
                        additionalProperties: true
                        example:
                          {
                            "id": "dfd1-f09efa38d2f1ebc92e1cc188ae7cebd4-26ca5f5c"
                          }
        """
        result = search_reports(request.db, path)
        return dict(result)

    def delete_report(self, external_id: str):
        """Delete a report by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info(f'Deleting report {external_id}')

        status_report, notification_report = _get_reports(request.db, external_id)
        if status_report:
            delete_status_report(request.db, status_report['id'])
        elif notification_report:
            delete_notification_report(request.db, notification_report['external_id'])
        return {}

    def get_report_pdf(self, external_id: str):
        """Get a report's contents by ID.

        Returns contents only; use GET /reports/{external_id} to get the
        metadata of the report.
        ---
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: report contents
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/status_report_json'
        """
        self._logger.info('Getting report %s pdf', external_id)

        status_report, notification_report = _get_reports(request.db, external_id)

        report = status_report or notification_report

        if not report['pdf']:
            raise exceptions.NotFound(external_id=external_id)

        return report['pdf']

    def get_historic_report(self, external_id: str):
        """Get a historic report's contents by ID.
        ---
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: report contents
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/status_report_historic'
        """
        self._logger.info('Getting historic report %s', external_id)

        historic_status_report = get_historic_status_report(request.db, external_id=external_id)
        if not historic_status_report:
            raise exceptions.NotFound(external_id=external_id)

        report_external_id = historic_status_report.pop('external_id')
        historic_status_report['id'] = report_external_id
        return historic_status_report

    def put_report_pdf(self, external_id: str):
        """Update a report's contents by ID.

        Updates contents only; use PUT /reports/{external_id} to update the
        metadata of the report.
        ---
        parameters:
          - $ref: '#/components/parameters/report_external_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/status_report_json'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: report contents
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/status_report_json'
        """
        self._logger.info('Updating report %s pdf', external_id)

        validator = ReportValidator(schema_reports_pdf)
        # we cannot validate field without naming it in cerberus
        if not validator.validate({'pdf': request.json}):
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized({'pdf': request.json})
        updated = (
            update_status_report_pdf(request.db, external_id, data['pdf'])
            or
            update_notification_report_pdf(request.db, external_id, data['pdf'])
        )
        if not updated:
            raise exceptions.NotFound(external_id=external_id)
        return updated


def _prepare_data_update_status_report(raw_data):
    payload = {}
    pdf = raw_data.get('pdf')

    if raw_data.get('generated_timestamp'):
        payload['generated_timestamp'] = raw_data['generated_timestamp']
    if raw_data.get('tilaajavastuu_status'):
        payload['status'] = raw_data['tilaajavastuu_status']
    if raw_data.get('interested_org_id'):
        payload['interested_company_id'] = raw_data['interested_org_id']
    if raw_data.get('org'):
        payload['company_id'] = raw_data['org']
    if pdf:
        payload['json_'] = pdf['body']
    if raw_data.get('charge_reference'):
        payload['charge_reference'] = raw_data['charge_reference']
    if raw_data.get('used_providers'):
        payload['used_providers'] = raw_data['used_providers']
    return payload


def _prepare_data_update_notification_report(raw_data):
    payload = {}
    pdf = raw_data.get('pdf')

    if raw_data.get('generated_timestamp'):
        payload['generated_timestamp'] = raw_data['generated_timestamp']
    if pdf:
        payload['qvarn_report'] = pdf['body']
    return payload


def _get_reports(db, external_id):
    status_report = get_status_report(db, external_id=external_id)
    notification_report = get_notification_report(db, external_id=external_id)

    if not any([status_report, notification_report]):
        raise exceptions.NotFound(external_id=external_id)
    if status_report and notification_report:
        error = {
            'external_id': [CONFLICTING_IDS_ERROR_MSG]
        }
        raise exceptions.InternalServerError(**error)
    return status_report, notification_report
