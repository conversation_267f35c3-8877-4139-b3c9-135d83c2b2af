import json
from logging import Logger
from typing import List

from bottle import request

from boldataapi import exceptions
from boldataapi.schema import (
    CreditsafeAccountValidator,
    is_valid_uuid,
    schema_new_creditsafe_account,
    schema_update_creditsafe_account,
)
from boldataapi.serialize import asbool
from boldataapi.services.creditsafe_accounts import (
    create_creditsafe_account,
    delete_creditsafe_account,
    get_creditsafe_account,
    query_creditsafe_accounts,
    update_creditsafe_account,
)


class CreditsafeAccountsController:
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/creditsafe_accounts/<creditsafe_account_id>',
                'method': 'GET',
                'callback': self.get_creditsafe_account,
                'require_auth': True,
                'scopes': ['bda_creditsafe_account_get'],
            },
            {
                'path': '/creditsafe_accounts',
                'method': 'POST',
                'callback': self.post_creditsafe_account,
                'require_auth': True,
                'scopes': ['bda_creditsafe_account_post'],
            },
            {
                'path': '/creditsafe_accounts/<creditsafe_account_id>',
                'method': 'PUT',
                'callback': self.put_creditsafe_account,
                'require_auth': True,
                'scopes': ['bda_creditsafe_account_put'],
            },
            {
                'path': '/creditsafe_accounts/<creditsafe_account_id>',
                'method': 'DELETE',
                'callback': self.delete_creditsafe_account,
                'require_auth': True,
                'scopes': ['bda_creditsafe_account_delete'],
            },
            {
                'path': '/creditsafe_accounts/query',
                'method': 'GET',
                'callback': self.query_creditsafe_account,
                'require_auth': True,
                'scopes': ['bda_creditsafe_account_search'],
            },
        ]

    def get_creditsafe_account(self, creditsafe_account_id):
        """Get a Creditsafe account by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/creditsafe_account_id'
          - name: include_history
            in: query
            schema:
              type: boolean
              default: true
            description: include account history in the response?
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: Creditsafe account data by ID
            content:
              application/json:
                schema:
                  oneOf:
                    - $ref: '#/components/schemas/creditsafe_account'
                    - $ref: '#/components/schemas/creditsafe_account_without_history'
        """
        self._logger.info('Getting Creditsafe account %s', creditsafe_account_id)

        try:
            include_history = asbool(request.query.include_history, default=True)
        except ValueError as e:
            raise exceptions.ParameterValidationFailed(include_history=[str(e)])

        if not is_valid_uuid(creditsafe_account_id):
            raise exceptions.NotFound(external_id=creditsafe_account_id)

        creditsafe_account = get_creditsafe_account(
            request.db, creditsafe_account_id, include_history=include_history
        )
        if creditsafe_account is None:
            raise exceptions.NotFound(external_id=creditsafe_account_id)

        return creditsafe_account

    def post_creditsafe_account(self):
        """Create a new Creditsafe account.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_creditsafe_account'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          409:
            description: An active Creditsafe account already exists for this organization
            content:
              application/json:
                schema:
                  $ref: '#/components/responses/conflict'
          201:
            description: the newly created Creditsafe account
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/creditsafe_account'
        """
        validator = CreditsafeAccountValidator(schema_new_creditsafe_account)
        if not validator.validate(request.json):
            self._logger.info('Creditsafe account data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)
        payload = {
            'person_id': data.get('person_id'),
            'org_id': data.get('org_id'),
            'username': data.get('username'),
            'password': data.get('password'),
            'state': data.get('state'),
            'changed_by_person_id': data.get(
                'changed_by_person_id') or data.get('person_id'),
            'comment': data.get('comment')
        }
        migration_attrs = [
            'id',
            'history',
            'created_on',
            'last_changed'
        ]
        for a in migration_attrs:
            if a in data:
                payload[a] = data[a]

        return create_creditsafe_account(request.db, payload)

    def put_creditsafe_account(self, creditsafe_account_id):
        """Update a Creditsafe account.

        Fields that are omitted from the request will retain their old values.
        ---
        parameters:
          - $ref: '#/components/parameters/creditsafe_account_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_creditsafe_account'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the updated Creditsafe account
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/creditsafe_account'
        """
        self._logger.info('Updating Creditsafe account %s', creditsafe_account_id)

        if not is_valid_uuid(creditsafe_account_id):
            raise exceptions.NotFound(external_id=creditsafe_account_id)

        creditsafe_account = get_creditsafe_account(request.db, creditsafe_account_id)
        if not creditsafe_account:
            self._logger.info(
                'Creditsafe account %s was not found.',
                creditsafe_account_id
            )
            raise exceptions.NotFound(external_id=creditsafe_account_id)

        validator = CreditsafeAccountValidator(schema_update_creditsafe_account)
        if not validator.validate(request.json):
            self._logger.info('Creditsafe account data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request.json)
        keys = [
            'person_id',
            'org_id',
            'username',
            'password',
            'state',
            'comment'
        ]
        payload = {'creditsafe_account_id': creditsafe_account_id}
        for key in keys:
            if key in data:
                payload[key] = data[key]
        payload['changed_by_person_id'] = data.get(
            'changed_by_person_id') or data.get('person_id')
        return update_creditsafe_account(request.db, payload)

    def delete_creditsafe_account(self, creditsafe_account_id):
        """Delete a Creditsafe account.

        ---
        parameters:
          - $ref: '#/components/parameters/creditsafe_account_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info('Deleting Creditsafe account %s', creditsafe_account_id)

        if not is_valid_uuid(creditsafe_account_id):
            raise exceptions.NotFound(external_id=creditsafe_account_id)

        creditsafe_account = get_creditsafe_account(
            request.db, creditsafe_account_id, include_history=False
        )
        if not creditsafe_account:
            raise exceptions.NotFound(external_id=creditsafe_account_id)
        delete_creditsafe_account(request.db, creditsafe_account_id)
        return {}

    def query_creditsafe_account(self):
        """Return a list of creditsafe_account entries that match a query.
        ---
        parameters:
          - name: q
            in: query
            description: |
              search query in URL-encoded JSON

              The keys of the query object are database column names optionally suffixed with an
              operator ("eq"/"ne"/"lt"/"gt"/"le"/"ge"/"any") following a "__".
              The values are search values for that field (or a list of search values for the
              "any" operator).  When searching with multiple conditions, they are ANDed together.

              Database columns you can search on are:
              - id
              - org_id
              - person_id
              - username
              - state
              - created_on
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >
                        search by Creditsafe account ID
                    org_id:
                      type: string
                      description: >
                        search by organization database ID
                    person_id:
                      type: string
                      description: >
                        search by person ID of the user
                    username:
                      type: string
                      description: >
                        search by Creditsafe service username
                    state:
                      type: string
                      description: >
                        search by Creditsafe account state
                    created_on:
                      type: string
                      description: >
                        search by creation timestamp (ISO-8601 format)
        responses:
          200:
            description: List of search results
            content:
              application/json:
                schema:
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - creditsafe_account
                      description: always "creditsafe_account"
                    resources:
                      type: array
                      description: list of matching "creditsafe_account" entries
                      items:
                        $ref: '#/components/schemas/creditsafe_account'
                examples:
                  no_matches:
                    summary: Result when nothing matches
                    value:
                      {
                        "resource_type": "creditsafe_account",
                        "resources": []
                      }
                  matches_exist:
                    summary: Result when something matches
                    value:
                      {
                        "resource_type": "creditsafe_account",
                        "resources": [
                          {
                            "created_on": "2024-03-25T07:30:55.252666+00:00",
                            "history": [
                                          {
                                            "changed_by_person_id": "0035-test",
                                            "comment": "new account",
                                            "created_on": "2024-03-25T07:30:55.252666+00:00",
                                            "org_id": "f392-test",
                                            "person_id": "0035-test",
                                            "state": "pending",
                                            "username": "cs_user"
                                          }
                                        ],
                            "id": "f5671f3d-1810-46d4-b207-99e95ad479a8",
                            "org_id": "f392-test",
                            "password": "cs_pass",
                            "person_id": "0035-test",
                            "state": "pending",
                            "username": "cs_user"
                          }
                        ]
                      }
          400:
            description: Error in query (e.g. JSON parse error)
            content:
              application/json:
                example:
                  {
                    "message": "Could not parse search condition",
                    "error_code": "BadSearchCondition"
                  }
        """
        try:
            query = json.loads(request.query.q)
        except ValueError:
            raise exceptions.BadSearchCondition()
        result = query_creditsafe_accounts(request.db, query)
        return {
            'resource_type': 'creditsafe_account',
            'resources': result,
        }
