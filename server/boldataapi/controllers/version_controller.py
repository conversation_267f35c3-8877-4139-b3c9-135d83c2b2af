import sys
from logging import Logger
from typing import List

from bottle import HTTPResponse, request

from boldataapi.config import get_config
from boldataapi.version import get_alembic_version, get_bda_version


class VersionController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/version',
                'method': 'GET',
                'callback': self.get_version,
                'require_auth': False
            }
        ]

    def get_version(self) -> HTTPResponse:
        r"""Return service version.

        ---
        responses:
          200:
            description: Version information
            content:
              application/json:
                example:
                  {
                    "version": "0.10.2",
                    "python_version":
                      "3.7.3 (default, Jan 22 2021, 20:04:44) \n[GCC 8.3.0]",
                    "docker_tag": "109044",
                    "alembic_version": "73af9b771387"
                  }
                schema:
                  properties:
                    version:
                      description: >
                        Bol-Data-API version number (git tag, optionally
                        followed by number of commits since then, plus a suffix
                        containing a truncated git commit hash)
                      type: string
                    docker_tag:
                      description: >
                        Docker image tag of this build (currently this is our
                        GitLab CI pipeline number), only present when running in a
                        Docker image
                      type: string
                    python_version:
                      description: Python interpreter version, as returned by `sys.version`
                      type: string
                    alembic_version:
                      description: Alembic schema version from the database
                      type: string
                    alembic_version_on_startup:
                      description: >
                        Alembic schema version from the database during
                        startup.  If this doesn't match alembic_version, then
                        BOL-Data-API should be restarted ASAP so that
                        SQLAlchemy reflection will know the current schema.
                      type: string
        """
        self._logger.debug('preparing version response')
        config = get_config()
        response = {
            'version': get_bda_version(),
            'python_version': sys.version,
            'docker_tag': config.get('main', 'docker_tag', fallback=None),
            'alembic_version': get_alembic_version(request.db),
            'alembic_version_on_startup': request.alembic_version_on_startup,
        }
        return HTTPResponse(status=200, body=response)
