import json
from logging import Logger
from typing import List

from bottle import request

from boldataapi import exceptions
from boldataapi.schema import (
    pa_form_fields,
    pa_form_new_fields,
    pa_form_update_fields,
    preannouncement_update_fields,
    PreannouncementValidator,
    schema_new_pa_form,
    schema_new_preannouncement,
    schema_update_pa_form,
    schema_update_preannouncement,
)
from boldataapi.services.bol_suppliers import get_supplier, get_supplier_id
from boldataapi.services.preannouncements import (
    create_preannouncement,
    delete_preannouncement,
    get_preannouncement,
    get_preannouncement_form,
    get_preannouncement_form_data,
    get_preannouncement_form_id,
    query_preannouncements,
    update_preannouncement,
    upsert_pa_form,
)
from boldataapi.services.projects import get_project_id


class PreannouncementsController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/preannouncements/<external_id>',
                'method': 'GET',
                'callback': self.get_preannouncement,
                'require_auth': True,
                'scopes': ['bda_preannouncement_get'],
            },
            {
                'path': '/preannouncements',
                'method': 'POST',
                'callback': self.post_preannouncement,
                'require_auth': True,
                'scopes': ['bda_preannouncement_post'],
            },
            {
                'path': '/preannouncements/<external_id>',
                'method': 'PUT',
                'callback': self.put_preannouncement,
                'require_auth': True,
                'scopes': ['bda_preannouncement_put'],
            },
            {
                'path': '/preannouncements/<external_id>',
                'method': 'DELETE',
                'callback': self.delete_preannouncement,
                'require_auth': True,
                'scopes': ['bda_preannouncement_delete'],
            },
            {
                'path': '/preannouncements/query',
                'method': 'GET',
                'callback': self.preannouncements_query,
                'require_auth': True,
                'scopes': ['uapi_projects_search_id_get'],
            },
        ]

    def get_preannouncement(self, external_id):
        """Get a preannouncement by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/preannouncement_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: preannouncement information
            content:
              application/json:
                schema:
                  oneOf:
                    - $ref: '#/components/schemas/preannouncement'
                    - $ref: '#/components/schemas/preannouncement_with_form_data'
        """
        self._logger.info('Getting preannouncement %s', external_id)
        preannouncement = get_preannouncement(request.db,
                                              external_id=external_id)
        if not preannouncement:
            raise exceptions.NotFound(external_id=external_id)

        pa_form = get_preannouncement_form_data(request.db, pa=preannouncement)
        preannouncement.update(pa_form)

        # Translate ids to external_ids
        preannouncement['id'] = preannouncement.pop('external_id')
        return preannouncement

    def post_preannouncement(self):
        """Create a new preannouncement.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_preannouncement'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            description: one of the provided IDs does does not exist in the database
            content:
              application/json:
                examples:
                  project:
                    summary: bad `project_id`
                    value:
                      {
                        "message": "Not found",
                        "error_code": "NotFound",
                        "error": {
                          "project_id": "a55f-7521303fbb16f824c9b9a2459bc2fedc-3cb8e412"
                        }
                      }
                  created_by_supplier_id:
                    summary: bad `created_by_supplier_id`
                    value:
                      {
                        "message": "Not found",
                        "error_code": "NotFound",
                        "error": {
                          "created_by_supplier_id": "4650-ea2f872e9f6cb69fb3b28f00b45806f6-cb82052b"
                        }
                      }
                  for_supplier_id:
                    summary: bad `for_supplier_id`
                    value:
                      {
                        "message": "Not found",
                        "error_code": "NotFound",
                        "error": {
                          "for_supplier_id": "4650-ea2f872e9f6cb69fb3b28f00b45806f6-cb82052b"
                        }
                      }
                  assigned_to_supplier_id:
                    summary: bad `assigned_to_supplier_id`
                    value:
                      {
                        "message": "Not found",
                        "error_code": "NotFound",
                        "error": {
                          "assigned_to_supplier_id":
                            "4650-ea2f872e9f6cb69fb3b28f00b45806f6-cb82052b"
                        }
                      }
          201:
            description: the newly created preannouncement
            content:
              application/json:
                schema:
                  oneOf:
                    - $ref: '#/components/schemas/preannouncement'
                    - $ref: '#/components/schemas/preannouncement_with_form_data'
        """
        self._logger.info('Creating a new preannouncement')

        validator = PreannouncementValidator(schema_new_preannouncement)
        if not validator.validate(request.json):
            self._logger.info('Preannouncement data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)
        data = validator.normalized(request.json)

        # Translate external_ids to ids for optional fields
        created_by_supplier = None
        created_by_supplier_external_id = data.get('created_by_supplier_id')
        if created_by_supplier_external_id:
            created_by_supplier = get_supplier(
                request.db, external_id=created_by_supplier_external_id)
            if created_by_supplier is None:
                raise exceptions.NotFound(created_by_supplier_id=created_by_supplier_external_id)

        assigned_to_supplier = None
        assigned_to_supplier_external_id = data.get('assigned_to_supplier_id')
        if assigned_to_supplier_external_id:
            assigned_to_supplier = get_supplier(
                request.db, external_id=assigned_to_supplier_external_id)
            if assigned_to_supplier is None:
                raise exceptions.NotFound(assigned_to_supplier_id=assigned_to_supplier_external_id)

        # Translate external_ids to ids for mandatory fields
        for_supplier_external_id = data.get('for_supplier_id')
        for_supplier = get_supplier(
            request.db, external_id=for_supplier_external_id)
        if for_supplier is None:
            raise exceptions.NotFound(for_supplier_id=for_supplier_external_id)

        project_external_id = data.get('project_id')
        project_id = get_project_id(request.db, project_external_id)
        if project_id is None:
            raise exceptions.NotFound(project_id=project_external_id)

        # Create preannouncement
        payload = {
            'status': data.get('status'),
            'project_id': project_id,
            'created_by_supplier_id': created_by_supplier['id'] if created_by_supplier else None,
            'for_supplier_id': for_supplier['id'],
            'assigned_to_company_id': data.get('assigned_to_company_id'),
            'assigned_to_supplier_id': assigned_to_supplier['id'] if assigned_to_supplier else None,
            'assigned_to_time': data.get('assigned_to_time'),
        }
        preannouncement = create_preannouncement(request.db, payload)
        self._logger.info('New preannouncement created %s',
                          preannouncement['external_id'])

        new = get_preannouncement(request.db,
                                  preannouncement_id=preannouncement['id'])
        new['id'] = new.pop('external_id')
        return new

    def _get_internal_id(self, db, external_id, id_getter, field_name):
        """Translate external_id to internal id"""
        id = id_getter(db, external_id)
        if id is None:
            raise exceptions.NotFound(**{field_name: external_id})
        return id

    def _update_preannouncement(self, request, external_id):  # noqa: C901
        """Update only preannouncement object"""
        db = request.db

        request_preannouncement_data = {k: v
                                        for k, v in request.json.items()
                                        if k in preannouncement_update_fields}

        if not request_preannouncement_data:
            return get_preannouncement(db, external_id=external_id)

        validator = PreannouncementValidator(schema_update_preannouncement)
        if not validator.validate(request_preannouncement_data):
            self._logger.info('Preannouncement data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        data = validator.normalized(request_preannouncement_data)

        pa_payload = {}
        if data.get('status'):
            pa_payload['status'] = data['status']
        if 'assigned_to_company_id' in data:
            pa_payload['assigned_to_company_id'] = data['assigned_to_company_id']
        if 'assigned_to_time' in data:
            pa_payload['assigned_to_time'] = data['assigned_to_time']
        if 'assigned_to_supplier_id' in data:
            if data['assigned_to_supplier_id'] is None:
                id = None
            else:
                id = self._get_internal_id(
                    db, data['assigned_to_supplier_id'], get_supplier_id, 'assigned_to_supplier_id')
            pa_payload['assigned_to_supplier_id'] = id
        if data.get('project_id'):  # Mandatory field
            pa_payload['project_id'] = self._get_internal_id(
                db, data['project_id'], get_project_id, 'project_id')
        if data.get('created_by_supplier_id'):  # Mandatory field
            pa_payload['created_by_supplier_id'] = self._get_internal_id(
                db, data['created_by_supplier_id'], get_supplier_id, 'created_by_supplier_id')
        if data.get('for_supplier_id'):  # Mandatory field
            pa_payload['for_supplier_id'] = self._get_internal_id(
                db, data['for_supplier_id'], get_supplier_id, 'for_supplier_id')
        if 'active_pa_form' in data:
            if data['active_pa_form'] is None:
                active_pa_form = None
            else:
                active_pa_form = self._get_internal_id(
                    db, data['active_pa_form'], get_preannouncement_form_id, 'active_pa_form'
                )
            pa_payload['active_pa_form'] = active_pa_form

        update_preannouncement(db, external_id, pa_payload)

        return get_preannouncement(db, external_id=external_id)

    def _create_pa_form(self, request, preannouncement):
        request_pa_form_data = {k: v
                                for k, v in request.json.items()
                                if k in pa_form_new_fields}

        if not request_pa_form_data:
            return {}

        validator = PreannouncementValidator(schema_new_pa_form)
        if not validator.validate(request_pa_form_data):
            self._logger.info(
                'Preannouncement creating new pa_form data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        pa_form_payload = validator.normalized(request_pa_form_data)

        if pa_form_payload.get('last_assigned_to_supplier'):
            pa_form_payload['last_assigned_to_supplier'] = self._get_internal_id(
                request.db, pa_form_payload['last_assigned_to_supplier'], get_supplier_id,
                'last_assigned_to_supplier')

        updated_pa_form = {}
        if pa_form_payload:
            updated_pa_form = upsert_pa_form(request.db, pa_form_payload, preannouncement)

        return updated_pa_form

    def _update_pa_form(self, request, preannouncement):
        request_pa_form_data = {k: v
                                for k, v in request.json.items()
                                if k in pa_form_update_fields}

        if not request_pa_form_data:
            return {}

        validator = PreannouncementValidator(schema_update_pa_form)
        if not validator.validate(request_pa_form_data):
            self._logger.info(
                'Preannouncement updating pa_form data validation failed.')
            raise exceptions.ParameterValidationFailed(**validator.errors)

        pa_form_payload = validator.normalized(request_pa_form_data)

        if pa_form_payload.get('last_assigned_to_supplier'):
            pa_form_payload['last_assigned_to_supplier'] = self._get_internal_id(
                request.db, pa_form_payload['last_assigned_to_supplier'], get_supplier_id,
                'last_assigned_to_supplier')

        updated_pa_form = {}
        if pa_form_payload:
            updated_pa_form = upsert_pa_form(request.db, pa_form_payload, preannouncement)

        return updated_pa_form

    def put_preannouncement(self, external_id):  # noqa: C901
        """Update a preannouncement

        This endpoint is a bit tricky: it has to support:

        - change of existing preannouncement.status (no pa_form data)

        - registration/new pa_form for an existing preannouncement
          (both pa_form and preannouncement data)

        - final confirm/reject of pa_form (preannouncement and pa_form data)

        Fields that are omitted from the request will retain their old values.
        ---
        parameters:
          - $ref: '#/components/parameters/preannouncement_external_id'
        requestBody:
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/update_preannouncement_with_new_form_data'
                  - $ref: '#/components/schemas/update_preannouncement_with_updated_form_data'
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: the updated preannouncement
            content:
              application/json:
                schema:
                  oneOf:
                  - $ref: '#/components/schemas/preannouncement'
                  - $ref: '#/components/schemas/preannouncement_with_form_data'
        """
        self._logger.info('Updating preannouncement %s', external_id)

        preannouncement = get_preannouncement(request.db, external_id=external_id)
        if not preannouncement:
            raise exceptions.NotFound(external_id=external_id)

        # Validate for rogue fields

        allowed_fields = preannouncement_update_fields.union(pa_form_fields)
        request_fields = set(request.json.keys())
        errors = {}
        for unknown_field in request_fields.difference(allowed_fields):
            errors[unknown_field] = ['unknown field']
        if errors:
            raise exceptions.ParameterValidationFailed(**errors)

        # Process preannouncement fields

        updated_preannouncement = self._update_preannouncement(request, external_id)

        # Process pa_form fields

        pa_form = get_preannouncement_form(request.db, pa=preannouncement)
        if pa_form:
            # Process pa_form update
            updated_pa_form = self._update_pa_form(request, preannouncement)
        else:
            # Process pa_form create
            updated_pa_form = self._create_pa_form(request, preannouncement)
        updated_preannouncement.update(updated_pa_form)

        updated_preannouncement['id'] = updated_preannouncement.pop('external_id')
        return updated_preannouncement

    def delete_preannouncement(self, external_id):
        """Delete a preannouncement by ID.

        Deleting a preannouncement keeps preannouncement forms in the database,
        but their pa_id field is reset to `null`.
        ---
        parameters:
          - $ref: '#/components/parameters/preannouncement_external_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            $ref: '#/components/responses/delete_successful'
        """
        self._logger.info('Deleting preannouncement %s', external_id)

        preannouncement = get_preannouncement(request.db, external_id=external_id)
        if not preannouncement:
            raise exceptions.NotFound(external_id=external_id)
        delete_preannouncement(request.db, external_id)
        return {}

    def preannouncements_query(self):
        """Return a list of preannouncements that match a query.

        ---
        parameters:
          - name: q
            in: query
            description: |
              search query in URL-encoded JSON

              The keys of the query object are database column names optionally suffixed with an
              operator ("eq"/"ne"/"lt"/"gt"/"le"/"ge"/"any") following a "__".
              The values are search values for that field (or a list of search values for the
              "any" operator).  When searching with multiple conditions, they are ANDed together.

              Database columns you can search on are:
              - id (database ID aka external ID)
              - status
              - created_by_supplier_id
              - for_supplier_id
              - project_id (database ID)
              - assigned_to_company_id
              - assigned_to_supplier_id
              - assigned_to_time
            required: true
            content:
              application/json:
                # XXX: swagger-ui fails to show this schema anywhere, hence
                # it's essentially replicated in plain text in the description
                # field above
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >
                        search by preannouncement ID (aka external_id)

                        There's really no reason to search by ID instead of using a simple HTTP GET.
                    status:
                      type: string
                      description: search by preannoucement status
                      enum:
                        - created
                        - registered
                        - confirmed
                        - rejected
                    created_by_supplier_id:
                      type: string
                      description: >
                        search by the database ID of the supplier that created
                        the preannouncement (also known as buyer)
                    for_supplier_id:
                      type: string
                      description: >
                        search by the database ID of the supplier that this
                        preannouncement is about
                    project_id:
                      type: string
                      description: search by project database ID
                    assigned_to_company_id:
                      type: string
                      description: >
                        search by the database ID of the company that should
                        take the next review step
                    assigned_to_supplier_id:
                      type: string
                      description: >
                        search by the database ID of the supplier that should
                        take the next review step
                    assigned_to_time:
                      type: string
                      description: >
                        search by the UTC timestamp (in ISO-8601 format) of the time
                        when the assigned_to_... fields were last modified

                        I'm not sure why you'd ever use this, especially since
                        lt/gt/le/ge comparisons are not implemented yet.
            # XXX: editor.swagger.io insists that 'examples' is wrong here and it should be
            # indented further, into content["application/json"].schema.examples, but if I do that,
            # then swagger-ui fails to show the examples!
            examples:
              simple:
                summary: a simple query on one field
                value:
                  {"for_supplier_id": "c4d7-860d817a9bc6f3f2e31538dbac0bc2a8-cbbd9557"}
                description: >
                  Searches for preannouncements created for a specific supplier.
              multiple_fields:
                summary: a simple query on multiple fields
                value:
                  {
                    "project_id": "d53f-46d8e3de8c0ce571b7455f8744e13014-c2992b4f",
                    "status": "rejected"
                  }
                description: >
                  Searches for rejected preannouncements in a specific project.
              multiple_enum_values:
                summary: a simple query on a single field with multiple allowed values
                value:
                  {"status__any": ["confirmed", "rejected"]}
                description: >
                  Searches for preannouncements that are either confirmed or rejected
        responses:
          200:
            description: List of search results
            content:
              application/json:
                schema:
                  properties:
                    resource_type:
                      type: string
                      enum:
                        - preannouncement
                      description: always "preannouncement"
                    resources:
                      type: array
                      description: list of matching preannouncements
                      items:
                        $ref: '#/components/schemas/preannouncement_without_active_pa_form'
                examples:
                  no_matches:
                    summary: Result when nothing matches
                    value:
                      {
                        "resource_type": "preannouncement",
                        "resources": []
                      }
                  matches_exist:
                    summary: Result when something matches
                    value:
                      {
                        "resource_type": "preannouncement",
                        "resources": [
                          {
                            "id": "c732b47b-9358-49d2-8dc1-9167b685e389",
                            "status": "created",
                            "created_by_supplier_id":
                              "e5f3-fa28cf983eeda185b96532813d248fcc-9487d60e",
                            "for_supplier_id":
                              "f77b-b1d032a0281d024e1ad8e33db7a23ca1-2642fed5",
                            "project_id": "c620-26100fb1dd4d22ccd6f8d549116c9ace-347576d9",
                            "assigned_to_company_id":
                              "099d-6ac9330e9f0f64c342b6b21888ce70fc-5d8a4a2e",
                            "assigned_to_supplier_id":
                              "4cb3-8d1857571e2eddb3dd5b9862a0b50a08-7286cc57",
                            "assigned_to_time": "2022-09-14T11:33:54.392579"
                          }
                        ]
                      }
          400:
            description: Error in query (e.g. JSON parse error)
            content:
              application/json:
                example:
                  {
                    "message": "Could not parse search condition",
                    "error_code": "BadSearchCondition"
                  }
        """
        try:
            query = json.loads(request.query.q)
        except ValueError:
            raise exceptions.BadSearchCondition()
        result = query_preannouncements(request.db, query)
        return {
            'resource_type': 'preannouncement',
            'resources': [
                dict(pa, id=pa.pop('external_id'))
                for pa in result
            ]
        }
