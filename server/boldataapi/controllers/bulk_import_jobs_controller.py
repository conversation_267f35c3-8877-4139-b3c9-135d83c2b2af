from logging import Logger
from typing import Any, Dict, List

from bottle import request, response

from boldataapi import exceptions
from boldataapi.featureflags import feature_active
from boldataapi.schema import (
    BulkImportJobValidator,
    schema_new_bulk_import_job,
    schema_new_bulk_import_job_core,
    schema_update_bulk_import_job,
    schema_update_bulk_import_job_core,
)
from boldataapi.services.bulk_import_jobs import (
    create_bulk_import_job,
    get_bulk_import_job,
    get_last_bulk_import_job,
    update_bulk_import_job,
)


class BulkImportJobsController(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def get_routes(self) -> List:
        return [
            {
                'path': '/bulk_import_jobs/<id>',
                'method': 'GET',
                'callback': self.get_bulk_import_job,
                'require_auth': True,
                'scopes': ['uapi_jobs_id_get'],
            },
            {
                'path': '/bulk_import_jobs/last/<project_id>',
                'method': 'GET',
                'callback': self.get_last_bulk_import_job,
                'require_auth': True,
                'scopes': ['uapi_jobs_search_id_get'],
            },
            {
                'path': '/bulk_import_jobs',
                'method': 'POST',
                'callback': self.create_bulk_import_job,
                'require_auth': True,
                'scopes': ['uapi_jobs_post'],
            },
            {
                'path': '/bulk_import_jobs/<id>',
                'method': 'POST',
                'callback': self.update_bulk_import_job,
                'require_auth': True,
                'scopes': ['uapi_jobs_id_put'],
            },
        ]

    def get_bulk_import_job(self, id: str) -> Dict[str, Any]:
        """Get a bulk import job by ID.

        ---
        parameters:
          - $ref: '#/components/parameters/bulk_import_job_id'
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: bulk import job
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/bulk_import_job'
                examples:
                  pending:
                    $ref: '#/components/examples/bulk_import_job_pending'
                  in_progress:
                    $ref: '#/components/examples/bulk_import_job_in_progress'
                  finished:
                    $ref: '#/components/examples/bulk_import_job_done'
        """
        self._logger.info('Getting bulk import job %s', id)
        job = get_bulk_import_job(request.db, id)
        if job is None:
            raise exceptions.NotFound(id=id)
        return job

    def get_last_bulk_import_job(self, project_id: str) -> Dict[str, Any]:
        """Get the most recent bulk import job for a project

        ---
        parameters:
          - name: project_id
            in: path
            schema:
              type: string
            required: true
            description: database ID of the project
            example: 7111-e877470a0594ff214b893ca71111d8d5-bf11d345
        responses:
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: bulk import job
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/bulk_import_job'
                examples:
                  pending:
                    $ref: '#/components/examples/bulk_import_job_pending'
                  in_progress:
                    $ref: '#/components/examples/bulk_import_job_in_progress'
                  finished:
                    $ref: '#/components/examples/bulk_import_job_done'
        """
        self._logger.info('Getting last bulk import job %s', project_id)
        job = get_last_bulk_import_job(request.db, project_id)
        if job is None:
            raise exceptions.NotFound(project_id=project_id)
        return job

    def create_bulk_import_job(self) -> Dict[str, Any]:
        """Create a new bulk import job.

        Bulk import jobs are used to fetch company information given a list of
        company registration numbers, prior to adding those companies as
        suppliers to a project.

        ---
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new_bulk_import_job'
              example:
                {
                  "project_id": "7111-e877470a0594ff214b893ca71111d8d5-bf11d345",
                  "interested_org_id": "1cf7-a7ed03f70e65733eb3772b91a309a2c4-f3254db7",
                  "companies": [
                    ["38-4521384", null, "pending", null, null, null, "2022-08-30T11:33:10", null,
                      "12345121324"],
                    ["41-1985581", null, "pending", null, null, null, "2022-08-30T11:33:10", null,
                      null]
                  ]
                }
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          201:
            description: bulk import job
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/bulk_import_job'
                examples:
                  pending:
                    $ref: '#/components/examples/bulk_import_job_pending'
        """
        self._logger.info('Creating a new bulk import job')

        core_validator = BulkImportJobValidator(schema_new_bulk_import_job_core)
        legacy_validator = BulkImportJobValidator(schema_new_bulk_import_job)
        if feature_active('core'):
            payload = _validate_and_normalize_payload(request.json, [core_validator])
        else:
            # If core is not active, accept both core and legacy payloads (core has a ninth element)
            payload = _validate_and_normalize_payload(
                request.json, [core_validator, legacy_validator])
        job = create_bulk_import_job(request.db, payload)
        return job

    def update_bulk_import_job(self, id: str) -> Dict[str, Any]:
        """Update a bulk import job.

        This is intended for the background bulk import job processor (Celery).

        Only those properties that are explicitly included in the request body will be updated;
        other properties will be left at their existing values.

        ---
        parameters:
          - $ref: '#/components/parameters/bulk_import_job_id'
        requestBody:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_bulk_import_job'
              examples:
                status_only:
                  summary: update the status only
                  value:
                    {
                      "status": "in_progress",
                    }
                companies:
                  summary: update the per-company import status
                  value:
                    {
                      "companies": [
                        ["38-4521384", "3e78-f7060fb06fdeaaef5e2d41fcb625dbb0-02055120", "done",
                         "CreditSafe", null, "add", "2022-08-30T11:37:03", "2022-08-30T11:33:37",
                         "12345121324"],
                        ["41-1985581", null, "pending", null, null, null, "2022-08-30T11:33:10",
                         null, null]
                      ]
                    }
                cancel:
                  summary: cancel a job that is pending or in progress
                  value:
                    {
                      "canceled": "2022-08-30T15:20:30.000000",
                    }
        responses:
          400:
            $ref: '#/components/responses/bad_request'
          404:
            $ref: '#/components/responses/not_found'
          200:
            description: bulk import job was updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/bulk_import_job'
                examples:
                  pending:
                    $ref: '#/components/examples/bulk_import_job_pending'
                  in_progress:
                    $ref: '#/components/examples/bulk_import_job_in_progress'
                  finished:
                    $ref: '#/components/examples/bulk_import_job_done'
        """
        self._logger.info('Updating bulk import job %s', id)

        core_validator = BulkImportJobValidator(schema_update_bulk_import_job_core)
        legacy_validator = BulkImportJobValidator(schema_update_bulk_import_job)
        if feature_active('core'):
            payload = _validate_and_normalize_payload(request.json, [core_validator])
        else:
            # If core is not active, accept both core and legacy payloads (core has a ninth element)
            payload = _validate_and_normalize_payload(
                request.json, [core_validator, legacy_validator])
        job = update_bulk_import_job(request.db, id, payload)
        if job is None:
            raise exceptions.NotFound(id=id)
        response.status = 200  # not 201, we're not creating anything
        return job


def _validate_and_normalize_payload(
        payload: Dict[str, Any],
        validators: List[BulkImportJobValidator]) -> Dict[str, Any]:
    """Uses the first validator that passes to normalize the payload, or raises
    an exception based on the last validator that failed.
    """
    for validator in validators:
        if validator.validate(payload):
            return validator.normalized(payload)
    raise exceptions.ParameterValidationFailed(**validators[-1].errors)
