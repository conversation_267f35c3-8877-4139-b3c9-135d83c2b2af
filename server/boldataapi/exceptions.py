# Parts copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

from abc import ABC
from typing import Dict

import boldataapi.accesscontrol.constants as constants


# Based on https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27


class ApiError(ABC, Exception):
    status_code = 0
    message = ""

    def __init__(self, **kwargs) -> None:
        self.message = self.message  # copy class attribute into instance dict
        super().__init__(self.message)
        self.error_code = self.__class__.__name__
        self.status_code = self.status_code  # copy class attr into instance dict
        self.error = kwargs

    def serialize(self) -> Dict:
        return {
            key: value
            for key, value in self.__dict__.items()
            if key != "status_code" and value
        }


class InvalidTokenError(ApiError):
    status_code = 401
    message = constants.EXC_INVALID_TOKEN_HEADER


class InvalidTokenIssuer(ApiError):
    status_code = 401
    message = constants.EXC_INVALID_TOKEN_ISSUER


class ExpiredTokenError(ApiError):
    status_code = 403
    message = constants.EXC_EXPIRED_TOKEN


class InsufficientScopeError(ApiError):
    status_code = 403
    message = constants.EXC_INSUFFICIENT_SCOPES


class ClientError(ApiError):
    status_code = 400
    message = "Bad request"


class BadSearchCondition(ClientError):
    message = "Could not parse search condition"

    def __init__(self, message: str = None):
        self.message = message or self.message
        super().__init__()


# Original bol-data-api exceptions


class InternalServerError(ApiError):
    status_code = 500
    message = 'Internal Server Error'

    def __init__(self, **kwargs) -> None:
        self.message = kwargs.get('msg') or self.__class__.message
        super().__init__(**kwargs)
        # we want str(self) to be with all the extra details, but we don't want
        # self.message to contain them
        self.message = self.__class__.message


class BadRequest(ApiError):
    status_code = 400
    message = 'Bad request'


class ParameterValidationFailed(ApiError):
    status_code = 400
    message = 'Failed to validate parameters'

    def __init__(self, **kwargs) -> None:
        self.message = self.__class__.message
        for k, v in kwargs.items():
            self.message += f'\n  - {k}: {v}'
        super().__init__(**kwargs)
        # we want str(self) to be with all the extra details, but we don't want
        # self.message to contain them
        self.message = self.__class__.message


class NotFound(ApiError):
    status_code = 404
    message = 'Not found'


class ConflictError(ApiError):
    status_code = 409
    message = 'Conflict'


class BadShowCondition(ClientError):
    message = "Could not parse show condition"

    def __init__(self, message: str = None):
        self.message = message or self.message
        super().__init__()
