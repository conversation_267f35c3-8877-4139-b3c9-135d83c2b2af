import pkg_resources
import sqlalchemy as sa

from boldataapi.storage.db import ALEMBIC_VERSION_TABLE


def get_bda_version() -> str:
    try:
        return pkg_resources.get_distribution('bol-data-api').version
    except pkg_resources.DistributionNotFound:  # pragma: nocover
        return '(not installed)'


def get_alembic_version(db) -> str:
    try:
        tbl_alembic_version = db.meta.tables[ALEMBIC_VERSION_TABLE]
        qry = (
            sa.select([tbl_alembic_version.c.version_num])
            .order_by(tbl_alembic_version.c.version_num)
        )
        rez = db.session.execute(qry)
        return ' '.join(ver for (ver,) in rez.fetchall())
    except Exception as e:
        return str(e)
