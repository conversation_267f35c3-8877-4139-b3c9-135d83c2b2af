import webtest


class TestApp(webtest.TestApp):

    def do_request(self, req, *args, **kwargs):
        # <PERSON>var<PERSON> uses REQUEST_URI to parse search URI before decoding, see
        # qvarn.list_resource.ListResource.get_matching_items.
        #
        # But REQUEST_URI is optional and webtest does not add it to request
        # environ, see https://github.com/Pylons/webtest/issues/1.
        path = req.environ['PATH_INFO']
        qs = req.environ['QUERY_STRING']
        req.environ['REQUEST_URI'] = path + ('?' + qs if qs else '')
        return super().do_request(req, *args, **kwargs)
