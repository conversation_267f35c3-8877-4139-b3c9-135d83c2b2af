# Copied from https://git.vaultit.org/Foretagsdeklaration/foretagsdeklaration/blob/c5d63ec80934f40cbade09e60e60e294c03929b5/server/bolfak/logging.py # noqa
import collections
import copy
import json
import logging
import os
import re
import sys

import bottle


program_name = os.path.basename(sys.argv[0])


def traverse(data, key=None):
    """Recursively iterate over dicts and lists of given data object

    This iterator gives you reference of each list and dict in data object together with key. Having
    this you can easily iterator over all values in data object recursively and modify data object
    in place if needed.

    For example, iterate over all values recursively:

        >>> [node[key] for node, key in traverse([1, {'a': 2}])]
        [1, 2]

    Also you can iterate and modify data in place:

        >>> data = [1, ['a': 2]]
        >>> for node, key in traverse(data):
        ...     node[key] += 1
        >>> data
        [2, ['a', 3]]

    """
    node = data if key is None else data[key]
    if isinstance(node, dict):
        for key in node.keys():
            yield from traverse(node, key)
    elif isinstance(node, list):
        for key in range(len(data)):
            yield from traverse(node, key)
    else:
        yield data, key


def get_request_attr(name, default=None):
    try:
        return getattr(bottle.request, name, default)
    except (RuntimeError, TypeError, KeyError, AttributeError):
        # Bottle request might be not yet initialized. Depending on
        # initialisation state, Bottle can raise any of the above errors.
        return default


class JsonStyle(object):
    default_format = ['asctime', 'levelname', {'message': 'message'}]

    def __init__(self, fmt):
        self._fmt = fmt or self.default_format

    def usesTime(self):
        for node, key in traverse(self._fmt):
            if node[key] == 'asctime':
                return True
        return False

    def format(self, record):
        fmt = copy.deepcopy(self._fmt)
        for node, key in traverse(fmt):
            node[key] = getattr(record, node[key])
        return json.dumps(fmt)


class Formatter(logging.Formatter):

    def __init__(self, fmt=None, datefmt=None, style='%'):
        if style == 'json':
            fmt = json.loads(fmt, object_pairs_hook=collections.OrderedDict) if fmt else None
            self._style = JsonStyle(fmt)
            self._fmt = self._style._fmt
            self.datefmt = datefmt
        else:
            super().__init__(fmt, datefmt, style)

    def formatTime(self, record, datefmt=None):
        # time.strftime() doesn't support '%f', and the way time.strftime() handles unknown
        # format specifiers differs between platforms -- MacOS strips the % and
        # leaves just an 'f', Linux keeps the entire '%f'.
        if datefmt and '%f' in datefmt:
            datefmt = datefmt.replace('%f', '%03d' % record.msecs)
        s = super(Formatter, self).formatTime(record, datefmt)
        return s

    def getRequestId(self):
        return get_request_attr('request_id')

    def getUser(self):
        return get_request_attr('user_id')

    def format(self, record):
        record.requestId = self.getRequestId() or ''
        record.user = self.getUser() or ''
        record.programName = program_name or ''
        return super().format(record)


class JsonFormatter(Formatter):

    def __init__(self, fmt=None, datefmt=None, style='%'):
        super().__init__(fmt, datefmt, style='json')


class ColorFormatter(Formatter):

    colors = {
        'red': '\033[31m',
        'green': '\033[32m',
        'brown': '\033[33m',
        'blue': '\033[34m',
        'magenta': '\033[35m',
        'cyan': '\033[36m',
        'grey': '\033[37m',
        'reset': '\033[m',
    }

    def __init__(self, fmt=None, datefmt=None, style='%'):
        fmt = re.sub('{(%s)}' % '|'.join(self.colors), lambda m: self.colors[m.group(1)], fmt)
        super().__init__(fmt, datefmt, style)

    def formatMessage(self, record):
        if hasattr(record, 'status_color'):
            record.status_color = self.colors.get(record.status_color)
        return super().formatMessage(record)
