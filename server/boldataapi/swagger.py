import copy
import functools
from typing import Any, Dict, Union

import yaml


DELETE_MARKER = '<DELETED>'


def schema_variant(
    original: Union[str, Dict],
    *deltas: Union[str, Dict],
    delete_marker: str = DELETE_MARKER,
) -> Dict:
    """Produce a variant OpenAPI schema with some changes.

    Usage example::

        SCHEMA_CONTACT = yaml.safe_load('''
        properties:
          id:
            type: string
          name:
            type: string
          email:
            type: string
        ''')

        SCHEMA_NEW_CONTACT = schema_variant(SCHEMA_CONTACT, '''
        required:
          - name
          - email
          - verify_email
        properties:
          id: <DELETED>
          verify_email:
            type: string
            description: second copy for typo checking
        ''')

    """

    def merge_recursively(dest, delta):
        if delta is None:
            # happens a lot if you use the bottle autoreloaded
            # and save incomplete edits
            return
        for k, v in delta.items():
            if v == delete_marker:
                dest.pop(k)
            elif isinstance(dest.get(k), dict):
                merge_recursively(dest[k], v)
            else:
                dest[k] = v

    if isinstance(original, str):
        original = yaml.safe_load(original)
    assert isinstance(original, dict)
    schema = copy.deepcopy(original)
    for delta in deltas:
        if isinstance(delta, str):
            delta = yaml.safe_load(delta)
        merge_recursively(schema, delta)
    return schema


def qvarn_compat_schema(compat_fields: Dict[str, Any], for_writing: bool):
    """Convert a dictionary with default values used for Qvarn compatibility to Swagger schema."""
    schema: Dict[str, Dict[str, Dict[str, Any]]] = dict(properties={})
    for field, value in compat_fields.items():
        if value is None:
            schema['properties'][field] = dict(
                type="string",
                nullable=True,
                example=None,
                description="always `null` for Qvarn compatibility"
            )
        elif value == []:
            schema['properties'][field] = dict(
                type="array",
                example=[],
                items={},
                description="always `[]` for Qvarn compatibility"
            )
        elif value == {}:
            schema['properties'][field] = dict(
                type="object",
                example={},
                properties={},
                description="always `{}` for Qvarn compatibility"
            )
        elif isinstance(value, str):
            schema['properties'][field] = dict(
                type="string",
                enum=[value],
                example=value,
                description=f'always "{value}" for Qvarn compatibility',
            )
        else:
            raise ValueError("don't know how to describe %r" % value)
        if for_writing:
            schema['properties'][field].update(dict(
                nullable=True,
                description="ignored (for Qvarn compatibility)"
            ))
            # Let's preserve constant string examples
            if not isinstance(value, str):
                schema['properties'][field].update(dict(
                    example=None,
                ))
    return schema


qvarn_compat_schema_for_reading = functools.partial(qvarn_compat_schema, for_writing=False)
qvarn_compat_schema_for_writing = functools.partial(qvarn_compat_schema, for_writing=True)
