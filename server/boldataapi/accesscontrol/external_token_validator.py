# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

from logging import Logger

import requests
from expiringdict import ExpiringDict

import boldataapi.accesscontrol.constants as constants


class ExternalTokenValidator(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger
        self._token_cache = ExpiringDict(max_len=100, max_age_seconds=600)

    def validate(
        self, token: str, token_issuer: str, verify_requests: bool
    ) -> bool:
        try:
            if self._token_cache.get(token):
                self._logger.debug("token was cached before")
                return True

            validation_response = self._send_validation_request(
                token, token_issuer, verify_requests
            )

            validation_response_status_code = validation_response.status_code
            self._logger.debug(
                "token check response is %s", validation_response_status_code
            )
            is_valid_token = validation_response_status_code == 200
            if is_valid_token:
                self._token_cache[token] = token[0:10]
            else:
                self._logger.warning(
                    "provided token is invalid: %s", validation_response.text
                )
            return is_valid_token

        except Exception as err:
            self._logger.error("error occurred while validating token: %s", err)
            raise

    def _send_validation_request(
        self, token: str, token_issuer: str, verify_requests: bool
    ) -> requests.Response:
        self._logger.debug("checking token externally")
        address = token_issuer + constants.URL_AUTH_TOKEN_VALIDATION
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }
        data = "access_token={}".format(token)
        self._logger.debug("sending request to %s", address)
        response = requests.post(
            address,
            data,
            verify=verify_requests,
            headers=headers,
            timeout=constants.TIMEOUT_EXTERNAL_API,
        )
        return response
