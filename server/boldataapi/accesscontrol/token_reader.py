# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

from logging import Logger
from typing import Dict, Tuple

import jwt

import boldataapi.accesscontrol.constants as constants
import boldataapi.exceptions as exceptions


# PyJWT strongly encourages us to list all the algorithms that we want to accept.
# I don't know which algorithms <PERSON><PERSON><PERSON> may produce; the default seems to be RS256.
# Let's list all public key crypto algorithms supported by PyJWT, just in case.
# And let's omit the obvious security holes like the "None" algorithm.
ALGORITHMS = [
    "RS256",
    "RS384",
    "RS512",
    "ES256",
    "ES384",
    "ES521",  # Not a typo!  It uses the P-521 elliptic curve and SHA-512
    "PS256",
    "PS384",
    "PS512",
    "EdDSA",
]


class TokenReader(object):
    def __init__(self, logger: Logger):
        self._logger = logger

    def read_and_parse_token(self, headers: Dict) -> Tuple[str, Dict]:
        encoded_token = self._read_token_from_headers(headers)
        decoded_token = self._parse_token(encoded_token)
        return encoded_token, decoded_token

    def _read_token_from_headers(self, headers: Dict) -> str:
        self._logger.debug("starting to read token from headers")
        if constants.AUTH_HEADER_NAME not in headers:
            self._logger.debug("no token in HTTP headers")
            raise exceptions.InvalidTokenError()

        authorization_header_value = headers[constants.AUTH_HEADER_NAME]
        authorization_header_values = authorization_header_value.split(" ")
        if (
            len(authorization_header_values) != 2
            or authorization_header_values[0].lower() != "bearer"
        ):
            self._logger.debug("bad token type in HTTP headers")
            raise exceptions.InvalidTokenError()

        access_token = authorization_header_values[1]
        return access_token

    def _parse_token(self, access_token: str) -> Dict:
        self._logger.debug("starting to validate client token")
        try:
            payload = jwt.decode(
                access_token,
                algorithms=ALGORITHMS,
                options={
                    "verify_aud": False,
                    "verify_signature": False,  # it's okay, ExternalTokenValidator validates it
                    "verify_exp": True,
                },
            )
        except jwt.ExpiredSignatureError:
            raise exceptions.ExpiredTokenError()
        except jwt.InvalidTokenError:
            raise exceptions.InvalidTokenError()
        self._logger.debug("client token is valid")
        return payload
