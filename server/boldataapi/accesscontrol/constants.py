# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

AUTH_HEADER_NAME = "Authorization"
EXC_INVALID_TOKEN_HEADER = "Could not find token in header"  # nosec
EXC_EXPIRED_TOKEN = "Expired token"  # nosec
EXC_INSUFFICIENT_SCOPES = "Bearer error='insufficient_scope'"
EXC_INVALID_TOKEN_ISSUER = "Invalid token issuer"  # nosec
URL_AUTH_TOKEN_VALIDATION = "/oxauth/restv1/clientinfo"
TIMEOUT_EXTERNAL_API = 10
