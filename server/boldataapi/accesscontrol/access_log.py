# Copied from https://git.vaultit.org/qvarn/qvarn/blob/master/qvarn/access_log.py

import datetime
import json
import re

import jwt

from boldataapi.accesscontrol.token_reader import ALGORITHMS


QVARN_ID_OR_UUID_RE = re.compile(
    '^(?:'
    '[0-9a-fA-F]{4}-[0-9a-fA-F]{32}-[0-9a-fA-F]{8}'
    '|'
    '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}'
    ')$'
)


class AccessLogger(object):

    def __init__(self, logger, entry_chunk_size=None):
        self.logger = logger
        self.entry_chunk_size = entry_chunk_size or 300

    def log_access(self, request, data):
        rtype = self._get_rtype(request, data)
        op = self._get_op(request)

        ids, revision = self._get_ids_and_revision(request, data)
        if not ids:
            # we might be returning response that doesn't look like
            # a resource or a resource list (/version is one example)
            # can't do anything about it here.
            return

        ahead = request.get_header('Authorization', '')
        qhead = request.get_header('Qvarn-Token', '')
        ohead = request.get_header('Qvarn-Access-By', '')
        whead = request.get_header('Qvarn-Why', None)

        token_headers = ahead
        if qhead:
            token_headers = ', '.join([ahead, qhead])
        encoded_tokens = re.split(r'(?:\A|,\s*)Bearer ', token_headers)[1:]
        tokens = [
            jwt.decode(
                t,
                options={'verify_signature': False},
                algorithms=ALGORITHMS
            )
            for t in encoded_tokens
        ]

        persons = [
            {
                'accessor_id': t['sub'],
                'accessor_type': 'person',
            }
            for t in tokens]
        clients = [
            {
                'accessor_id': t['aud'],
                'accessor_type': 'client',
            }
            for t in tokens]
        orgs = [
            {
                'accessor_id': t,
                'accessor_type': 'org',
            }
            for t in re.findall(r',?\s*Org (.+?)(?:,|\Z)', ohead) if t]
        others = [
            {
                'accessor_id': t,
                'accessor_type': 'other',
            }
            for t in re.findall(r',?\s*Other (.+?)(?:,|\Z)', ohead) if t]

        ip_address = request.headers.get('X-Forwarded-For')

        for some_ids in self._split(self.entry_chunk_size, ids):
            # room for improvement: replace `msg_type `with custom log level ACCESS:
            # https://vaultit.atlassian.net/wiki/spaces/AT/pages/926547986/Kubernetes+and+centralized+access+logging
            entry = {
                'msg_type': 'access_log',
                'operation': op,
                'resource_type': rtype,
                'resource_ids': some_ids,
                'resource_revision': revision,
                'accessors': persons + clients + orgs + others,
                'reason': whead,
                'ip_address': ip_address,
                'timestamp': datetime.datetime.utcnow().isoformat() + 'Z',
            }
            self.logger.info('%s', json.dumps(entry), extra=entry)

    def _get_ids_and_revision(self, request, data):
        if not data or not isinstance(data, dict):
            # In case we are returning a file or it's a delete request
            # extract ID from path
            path_parts = request.path.split('/')
            if len(path_parts) > 2 and QVARN_ID_OR_UUID_RE.match(path_parts[2]):
                ids = [path_parts[2]]
            else:
                ids = []
            revision = None
        else:
            if 'resources' in data:
                resources = data['resources']
                if isinstance(resources, list):
                    # Standard format: resources is a list of objects with 'id' field
                    ids = [r['id'] for r in resources]
                elif isinstance(resources, dict):
                    # Batch format: resources is a dict, use project_ids if available
                    ids = data.get('project_ids', list(resources.keys()))
                else:
                    ids = []
                revision = None
            elif 'id' in data:
                ids = [data['id']]
                revision = data.get('revision')
            else:
                ids, revision = [], None
        # make the logging deterministic
        ids.sort()
        return ids, revision

    def _get_rtype(self, request, data):
        # If data has `resource_type` field, we use it as resource type
        if isinstance(data, dict) and 'resource_type' in data:
            return data['resource_type']

        # Othewise we try to guess it from request URL.
        path_parts = request.path.split('/')
        if len(path_parts) >= 2:
            rpart = path_parts[1]
            return rpart[:-1] if rpart[-1] == 's' else rpart

    def _get_op(self, request):
        if request.method == 'GET':
            path_parts = request.path.split('/')
            if len(path_parts) == 2:
                return 'LIST'
            elif 'search' in path_parts:
                return 'SEARCH'
            else:
                return 'GET'
        else:
            return request.method

    def _split(self, n, ids):
        while len(ids) > n:
            yield ids[:n]
            ids = ids[n:]
        yield ids
