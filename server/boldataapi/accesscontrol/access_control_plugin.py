# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

import json
from logging import Logger
from typing import List, NamedTuple

import bottle

from boldataapi import exceptions
from boldataapi.accesscontrol.access_validator import AccessValidator


class AccessControlConfig(NamedTuple):
    gluu_addresses: List[str]
    verify_requests: bool


class AccessControlPlugin:

    name = 'access_control'
    api = 2

    def __init__(
        self,
        logger: Logger,
        config: AccessControlConfig,
        access_validator: AccessValidator,
    ) -> None:
        self._logger = logger
        self._config = config
        self._access_validator = access_validator

    def apply(self, callback, route: bottle.Route):
        def wrapper(*args, **kwargs):
            if self._requires_auth(route):
                self._logger.debug("the call requires authorization")
                self._check_auth(route)
            else:
                self._logger.debug("the call does not require authorization")
            return callback(*args, **kwargs)

        return wrapper

    @staticmethod
    def _requires_auth(route: bottle.Route) -> bool:
        context_config = route.config
        if "require_auth" not in context_config:
            return True
        return context_config["require_auth"]

    def _check_auth(self, route: bottle.Route) -> None:
        self._logger.debug("starting to validate token and scope")
        headers = bottle.request.headers
        context_config = route.config
        required_scopes = context_config.get("scopes", [])
        verify_requests = self._config.verify_requests
        gluu_addresses = self._config.gluu_addresses
        assert gluu_addresses, "BOLDATAAPI_AUTH_GLUU_ADDRESSES is not specified!"
        try:
            client_id = self._access_validator.validate(
                headers, required_scopes, gluu_addresses, verify_requests
            )
            self._logger.info(
                "access to endpoint: %s by clientId: %s",
                bottle.request.fullpath,
                client_id,
            )
        except exceptions.ApiError as ex:
            self._return_error_response(ex)

    def _return_error_response(self, error: exceptions.ApiError) -> None:
        error_body = error.serialize()
        self._logger.error("exception occurred: %s", error_body)
        raise bottle.HTTPResponse(
            status=error.status_code,
            body=json.dumps(error_body),
            headers={"Content-Type": "application/json"},
        )
