# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

from logging import Logger
from typing import Dict, List

from .external_token_validator import ExternalTokenValidator
from .token_reader import Token<PERSON>eader
from .. import exceptions


class AccessValidator(object):
    def __init__(self, logger: Logger) -> None:
        self._logger = logger
        self._external_validator = ExternalTokenValidator(logger)

    def validate(
        self,
        headers: Dict,
        required_scopes: List[str],
        identity_managers: List[str],
        verify_requests: bool,
    ) -> str:
        self._logger.debug("started to validate coarse grained access control")
        token_reader = TokenReader(self._logger)
        encoded_token, decoded_token = token_reader.read_and_parse_token(headers)
        self._validate_client_token(
            encoded_token, decoded_token, identity_managers, verify_requests
        )

        client_scopes = decoded_token["scope"].split(" ")
        client_id = decoded_token["aud"]
        if not required_scopes:
            self._logger.debug("scopes are not required for this operation")
            return client_id

        self._logger.debug("starting to validate client scopes")
        for required_scope in required_scopes:
            if required_scope not in client_scopes:
                self._logger.debug(
                    "required scope %s not in client scopes", required_scope,
                )
                raise exceptions.InsufficientScopeError()

        self._logger.debug("client scopes are valid")
        return client_id

    def _validate_client_token(
        self,
        encoded_token: str,
        decoded_token: Dict,
        identity_managers: List[str],
        verify_requests: bool,
    ) -> None:
        self._logger.debug("starting to validate client token")
        token_fields = ["scope", "aud", "iss"]
        for field in token_fields:
            if field not in decoded_token:
                self._logger.debug("required field %s not in token", field)
                raise exceptions.InvalidTokenError()

        token_issuer = decoded_token["iss"]
        if token_issuer not in identity_managers:
            self._logger.error("token issuer %s not accepted", token_issuer)
            raise exceptions.InvalidTokenIssuer()

        if not self._external_validator.validate(
            encoded_token, token_issuer, verify_requests
        ):
            raise exceptions.InvalidTokenError()

        self._logger.debug("client token is valid")
