import uuid

import sqlalchemy as sa

from boldataapi.storage.db import (
    INTERNAL_PROJECT_IDS_TABLE,
    PROJECTS_TABLE,
)


def update_internal_project_id(db, internal_project_id, db_internal_project_id):
    table_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]
    qry = (
        sa.update(table_internal_project_ids)
        .where(table_internal_project_ids.c.id == db_internal_project_id)
        .values(internal_project_id=internal_project_id)
    )
    db.session.execute(qry)


def delete_internal_project_id(db, project_id, company_id):
    tbl_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]
    qry = (
        sa.delete(tbl_internal_project_ids)
        .where(sa.and_(
            tbl_internal_project_ids.c.project_id == project_id,
            tbl_internal_project_ids.c.company_id == company_id,
        ))
    )
    db.session.execute(qry)


def delete_internal_project_ids(db, project_id):
    tbl_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]
    qry = (
        sa.delete(tbl_internal_project_ids)
        .where(tbl_internal_project_ids.c.project_id == project_id)
    )
    db.session.execute(qry)


def create_internal_project_id(db, project_id, internal_project_id, company_id):
    table_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]

    query = sa.insert(table_internal_project_ids).values(
        id=str(uuid.uuid4()),
        project_id=project_id,
        internal_project_id=internal_project_id,
        company_id=company_id,
    )
    db.session.execute(query)


def get_internal_project_id(db, project_id, company_id):
    projects = db.meta.tables[PROJECTS_TABLE]

    table_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]

    qry = (
        sa.select([
            table_internal_project_ids
        ]).
        select_from(
            projects.
            join(
                table_internal_project_ids,
                projects.c.id == table_internal_project_ids.c.project_id,
            )
        ).
        where(sa.and_(
            projects.c.id == project_id,
            table_internal_project_ids.c.company_id == company_id,
        ))
    )

    qry_result = db.session.execute(qry)
    internal_project_id = qry_result.fetchone()
    return dict(internal_project_id) if internal_project_id else None


def get_internal_project_ids(db, project_company_id_tuples):
    projects = db.meta.tables[PROJECTS_TABLE]

    table_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]

    qry = (
        sa.select([
            table_internal_project_ids
        ]).
        select_from(
            projects.
            join(
                table_internal_project_ids,
                projects.c.id == table_internal_project_ids.c.project_id,
            )
        ).
        where(
            sa.tuple_(
                projects.c.id, table_internal_project_ids.c.company_id
            ).in_(project_company_id_tuples)
        )
    )
    qry_result = db.session.execute(qry)

    result = {}
    for row in qry_result if qry_result.returns_rows else []:
        project_id = row[table_internal_project_ids.c.project_id]
        company_id = row[table_internal_project_ids.c.company_id]
        key = (project_id, company_id)
        internal_project_id = row[table_internal_project_ids.c.internal_project_id]
        result[key] = internal_project_id

    return result
