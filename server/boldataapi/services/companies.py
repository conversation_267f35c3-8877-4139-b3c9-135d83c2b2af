import logging

import sqlalchemy as sa
import yaml

from boldataapi.services.projects import (
    _get_visible_projects,
    decode_status_db_repr,
    NOT_CLOSED,
)
from boldataapi.storage.db import (
    BULK_IMPORT_JOBS_TABLE,
    CREDITSAFE_ACCOUNT_HISTORY_TABLE,
    CREDITSAFE_ACCOUNT_TABLE,
    INTERNAL_PROJECT_IDS_TABLE,
    NOTIFICATION_REPORTS_TABLE,
    PROJECT_USERS_TABLE,
    PROJECTS_TABLE,
    REPORT_CACHE_TABLE,
    STATUS_REPORTS_HISTORY_TABLE,
    STATUS_REPORTS_TABLE,
    SUPPLIERS_TABLE,
)

logger = logging.getLogger(__name__)


def get_companies_list(
    db,
    user_active_org_id,
    user_active_org_role,
    user_is_admin=False,
    user_projects_ids=None,
    filter_search='',
    limit=None,
    offset=0,
    ff_block_project_client=False,
):
    visible_projects = (
        _get_visible_projects(db, user_active_org_id,
                              user_active_org_role=user_active_org_role,
                              user_is_admin=user_is_admin,
                              user_projects_ids=user_projects_ids,
                              filter_state=NOT_CLOSED,
                              include_only_confirmed_added_clients=True,
                              ff_block_project_client=ff_block_project_client
                              )
    ).cte('visible_projects')

    visible_orgs = (
        _get_visible_orgs(
            db, visible_projects, user_active_org_id, user_is_admin)
    ).cte('visible_orgs')

    latest_reports = (
        _get_latest_reports(db, user_active_org_id, companies=visible_orgs)
    ).cte('latest_reports')

    qry = (
        sa.select([
            visible_orgs.c.company_id,
            visible_orgs.c.project_count,
            latest_reports.c.last_report_status
        ]).
        select_from(
            visible_orgs.
            outerjoin(latest_reports,
                      visible_orgs.c.company_id == latest_reports.c.company_id)
        ).
        limit(limit).
        offset(offset)
    )

    qry_result = db.session.execute(qry)
    result = [
        {
            'id': row[visible_orgs.c.company_id],
            'name': None,
            'gov_org_id': None,
            'vat_number': None,
            'country': None,
            'project_count': row[visible_orgs.c.project_count] or 0,
            'latest_report_status': (
                decode_status_db_repr(row[latest_reports.c.last_report_status])
                if row[latest_reports.c.last_report_status]
                else None),
            'report_available': True if row[latest_reports.c.last_report_status] else False,
        }
        for row in (qry_result if qry_result.returns_rows else [])
    ]

    return result


SWAGGER_COMPANY_LIST_SCHEMA = yaml.safe_load("""
type: array
items:
  type: object
  properties:
    id:
      type: string
      example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
      description: database ID
    name:
      type: string
      nullable: true
      example: null
      description: always `null`
    gov_org_id:
      type: string
      nullable: true
      example: null
      description: always `null`
    vat_number:
      type: string
      nullable: true
      example: null
      description: always `null`
    country:
      type: string
      nullable: true
      example: null
      description: always `null`
    project_count:
      type: integer
      description: number of projects where the company is participating
    latest_report_status:
      type: string
      enum:
        - stop
        - investigate
        - incomplete
        - attention
        - ok
      nullable: true
      description: >
        company status according to the latest status report, if it exists;
        `null` if no status reports exist for this company
    report_available:
      type: boolean
      description: >
        do we have any status reports for this company?
""")


def _get_visible_orgs(db, visible_projects,
                      user_active_org_id, user_is_admin=False):
    projects = db.meta.tables[PROJECTS_TABLE]
    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    # Own company always included
    own_org = (
        sa.select([
            sa.literal(user_active_org_id),
            # why own company has 0 project count?
            sa.literal(None)
        ])
    )

    if user_is_admin:
        # All suppliers from *all* projects
        supplier_orgs = (
            sa.select([
                suppliers.c.company_id,
                suppliers.c.project_id
            ])
        )
        # All responsible_orgs of projects
        responsible_orgs = (
            sa.select([
                projects.c.client_company_id,
                None
            ]).
            where(projects.c.client_company_id.isnot(None))
        )
        org_projects = sa.union(supplier_orgs, responsible_orgs)

    else:
        # All suppliers from *visible* projects
        org_projects = (
            sa.select([
                suppliers.c.company_id,
                suppliers.c.project_id
            ]).
            select_from(
                # Supplier orgs
                suppliers.
                # Use INNER JOIN to filter out projects non-visible to given
                # user.
                join(visible_projects, (
                    suppliers.c.project_id ==
                    visible_projects.c.project_id
                ))
            )
        )

    org_projects = sa.union(org_projects, own_org).alias('org_projects')

    qry = (
        sa.select([
            org_projects.c.company_id,
            sa.func.count(
                sa.distinct(org_projects.c.project_id)
            ).label('project_count'),
        ]).
        group_by(org_projects.c.company_id)
    )

    return qry


def _get_latest_reports(db, user_active_org_id, companies=None):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    companies_predicate = True
    if companies is not None:
        companies_predicate = (tbl_status_reports.c.company_id == companies.c.company_id)

    latest_report = (
        sa.select([
            tbl_status_reports.c.company_id,
            sa.func.max(tbl_status_reports.c.generated_timestamp).label('ts'),
        ]).
        where(
            sa.and_(
                tbl_status_reports.c.interested_company_id == user_active_org_id,
                companies_predicate
            )
        ).
        group_by(tbl_status_reports.c.company_id)
    ).cte('latest_report')

    latest_report_with_status = (
        sa.select([
            latest_report.c.company_id,
            latest_report.c.ts,
            sa.func.min(tbl_status_reports.c.status).label('last_report_status'),
        ]).
        select_from(
            tbl_status_reports.join(latest_report, sa.and_(
                tbl_status_reports.c.company_id == latest_report.c.company_id,
                tbl_status_reports.c.generated_timestamp == latest_report.c.ts
            ))
        ).
        group_by(latest_report.c.company_id, latest_report.c.ts)
    )

    return latest_report_with_status


def get_company_ids_batch(db, table='all', batch_size=1000, last_id=''):
    """Retrieve a batch of company IDs from specified tables.

    This function is optimized for production environments with large datasets:
    - Uses cursor-based pagination for efficiency
    - Allows querying specific tables to reduce load
    - Processes one table at a time to minimize memory usage

    Args:
        db: Database connection
        table: Specific table to query or 'all' for all tables
        batch_size: Number of results to return
        last_id: Last company ID from previous batch

    Returns:
        Dictionary with company_ids, next_cursor and has_more flag
    """
    # Map of table names to their query functions
    table_queries = {
        'suppliers': _get_supplier_company_ids,
        'projects': _get_project_company_ids,
        'project_users': _get_project_users_company_ids,
        'status_reports': _get_status_reports_company_ids,
        'status_reports_history': _get_status_reports_company_ids,  # Uses the same function
        'bulk_import_jobs': _get_bulk_import_jobs_company_ids,
        'internal_project_ids': _get_internal_project_ids_company_ids,
        'creditsafe_account': _get_creditsafe_account_company_ids,
        'creditsafe_account_history': _get_creditsafe_account_history_company_ids,
        'report_cache': _get_report_cache_company_ids,
        'notification_reports': _get_notification_reports_company_ids,
    }

    # If querying all tables, we need to fetch batch_size from each table first,
    # then merge and sort to get the correct batch_size results
    if table == 'all':
        all_company_ids = set()
        any_table_has_more = False

        # Query each table to get batch_size results from each
        for current_table in table_queries.keys():
            table_func = table_queries[current_table]

            # Get batch_size results from current table
            result = table_func(db, batch_size, last_id)
            table_ids = result['company_ids']

            # Add new IDs to our set (avoiding duplicates)
            all_company_ids.update(id for id in table_ids if id > last_id)

            # Track if any table has more results
            if result['has_more']:
                any_table_has_more = True

        # Convert to sorted list
        sorted_company_ids = sorted(list(all_company_ids))

        # Take only the first batch_size IDs
        company_ids = sorted_company_ids[:batch_size]

        # Determine if there are more results:
        # 1. If we have more IDs than batch_size, there are definitely more
        # 2. If any table reported has_more, there might be more beyond our current batch
        has_more = len(sorted_company_ids) > batch_size or any_table_has_more
        next_cursor = company_ids[-1] if company_ids else last_id

    else:
        # Query just the specified table
        table_func = table_queries[table]
        result = table_func(db, batch_size + 1, last_id)
        company_ids = result['company_ids'][:batch_size]
        has_more = result['has_more']
        next_cursor = company_ids[-1] if company_ids else last_id

    return {
        'company_ids': company_ids,
        'next_cursor': next_cursor,
        'has_more': has_more
    }


def _get_supplier_direct_ids(db, limit, last_id):
    """Get company IDs directly from the suppliers table (not from materialized_path)."""
    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    # Query company_id
    company_id_query = (
        sa.select([suppliers.c.company_id])
        .where(suppliers.c.company_id > last_id)
        .where(suppliers.c.company_id.isnot(None))
    )

    # Query parent_company_id if it exists in the table
    if hasattr(suppliers.c, 'parent_company_id'):
        parent_id_query = (
            sa.select([suppliers.c.parent_company_id])
            .where(suppliers.c.parent_company_id > last_id)
            .where(suppliers.c.parent_company_id.isnot(None))
        )
        union_query = sa.union(company_id_query, parent_id_query).alias('supplier_ids')
    else:
        union_query = company_id_query.alias('supplier_ids')

    # Final query with ordering and limit
    final_query = (
        sa.select([union_query.c.company_id])
        .order_by(union_query.c.company_id)
        .limit(limit)
    )

    # Execute query
    result = db.session.execute(final_query)
    company_ids = [row[0] for row in result]

    # Check if there are more results beyond what we've fetched
    has_more = False
    if company_ids:
        # Get the total count of company IDs in the table
        count_query = (
            sa.select([sa.func.count(union_query.c.company_id)])
            .where(union_query.c.company_id > last_id)
        )
        count_result = db.session.execute(count_query)
        total_count = count_result.scalar()

        has_more = total_count > len(company_ids)

    return company_ids, has_more, union_query


def _extract_path_ids_from_batch(rows, last_id):
    """Extract company IDs from materialized_path entries.

    Args:
        rows: Database result rows containing materialized_path arrays
        last_id: Last company ID from previous batch

    Returns:
        List of company IDs from materialized_path fields
    """
    path_ids = []
    for row in rows:
        if row[0]:  # materialized_path is not None
            for path_id in row[0]:
                if path_id and path_id > last_id:
                    path_ids.append(path_id)
    return path_ids


def _fetch_materialized_path_batch(db, suppliers, offset, batch_size):
    """Fetch a batch of materialized_path entries from the database.

    Args:
        db: Database connection
        suppliers: Suppliers table object
        offset: Offset for pagination
        batch_size: Maximum number of rows to fetch

    Returns:
        List of rows containing materialized_path arrays
    """
    path_query = (
        sa.select([suppliers.c.materialized_path, suppliers.c.id])
        .where(suppliers.c.materialized_path.isnot(None))
        .order_by(suppliers.c.id)
        .limit(batch_size)
        .offset(offset)
    )
    path_result = db.session.execute(path_query)
    return path_result.fetchall()


def _get_supplier_materialized_path_ids(db, last_id, batch_size=1000):
    """Get company IDs from the materialized_path field in the suppliers table.

    Uses batched processing to handle large datasets efficiently.

    Args:
        db: Database connection
        last_id: Last company ID from previous batch
        batch_size: Maximum number of rows to process in one batch

    Returns:
        List of company IDs from materialized_path fields
    """

    suppliers = db.meta.tables[SUPPLIERS_TABLE]
    path_ids = []

    if hasattr(suppliers.c, 'materialized_path'):
        try:
            # Process materialized_path entries in batches
            offset = 0
            while True:
                # Get a batch of materialized_path entries
                rows = _fetch_materialized_path_batch(db, suppliers, offset, batch_size)
                if not rows:
                    break  # No more rows to process

                # Extract path IDs from this batch
                batch_ids = _extract_path_ids_from_batch(rows, last_id)
                path_ids.extend(batch_ids)

                # Move to next batch
                offset += batch_size

                # If we've collected enough IDs, we can stop
                if len(path_ids) > batch_size * 2:
                    break

        except Exception as e:
            # If unnest fails, continue without it
            logger.error(f"Error processing materialized_path: {str(e)}")

    return path_ids


def _get_supplier_company_ids(db, limit, last_id):
    """Get company IDs from suppliers table."""

    # Get direct IDs from the suppliers table
    company_ids, has_more_main, _ = _get_supplier_direct_ids(db, limit, last_id)

    # Get IDs from materialized_path - use a reasonable batch size
    # We use limit*2 to ensure we get enough IDs to determine if there are more
    path_ids = _get_supplier_materialized_path_ids(db, last_id, batch_size=limit*2)

    # Combine the IDs if we have path IDs
    if path_ids:
        # Sort and limit the combined results
        all_ids = sorted(set(company_ids + path_ids))
        # Check if there are more results beyond the limit
        has_more = len(all_ids) > limit or has_more_main
        # Limit to requested batch size
        company_ids = all_ids[:limit]
    else:
        has_more = has_more_main

    return {
        'company_ids': company_ids,
        'has_more': has_more
    }


def _get_project_company_ids(db, limit, last_id):
    """Get company IDs from projects table."""
    projects = db.meta.tables[PROJECTS_TABLE]

    # Query client_company_id
    client_id_query = (
        sa.select([projects.c.client_company_id.label('company_id')])
        .where(projects.c.client_company_id > last_id)
        .where(projects.c.client_company_id.isnot(None))
    )

    # Query created_by_org_id if it exists
    if hasattr(projects.c, 'created_by_org_id'):
        created_by_query = (
            sa.select([projects.c.created_by_org_id.label('company_id')])
            .where(projects.c.created_by_org_id > last_id)
            .where(projects.c.created_by_org_id.isnot(None))
        )
        union_query = sa.union(client_id_query, created_by_query).alias('project_ids')
    else:
        union_query = client_id_query.alias('project_ids')

    # Final query with ordering and limit
    final_query = (
        sa.select([union_query.c.company_id])
        .order_by(union_query.c.company_id)
        .limit(limit)
    )

    # Execute query
    result = db.session.execute(final_query)
    company_ids = [row[0] for row in result]

    # Check if there are more results
    has_more = len(company_ids) >= limit

    return {
        'company_ids': company_ids,
        'has_more': has_more
    }


def _get_project_users_company_ids(db, limit, last_id):
    """Get company IDs from project_users table."""
    project_users = db.meta.tables[PROJECT_USERS_TABLE]

    query = (
        sa.select([sa.distinct(project_users.c.represented_company_id).label('company_id')])
        .where(project_users.c.represented_company_id > last_id)
        .where(project_users.c.represented_company_id.isnot(None))
        .order_by('company_id')
        .limit(limit)
    )

    result = db.session.execute(query)
    company_ids = [row[0] for row in result]

    return {
        'company_ids': company_ids,
        'has_more': len(company_ids) >= limit
    }


def _get_status_reports_company_ids(db, limit, last_id):
    """Get company IDs from status_reports and status_reports_history tables."""
    status_reports = db.meta.tables[STATUS_REPORTS_TABLE]
    status_reports_history = db.meta.tables[STATUS_REPORTS_HISTORY_TABLE]

    # Query from status_reports
    sr_interested_query = (
        sa.select([status_reports.c.interested_company_id.label('company_id')])
        .where(status_reports.c.interested_company_id > last_id)
        .where(status_reports.c.interested_company_id.isnot(None))
    )

    sr_company_query = (
        sa.select([status_reports.c.company_id.label('company_id')])
        .where(status_reports.c.company_id > last_id)
        .where(status_reports.c.company_id.isnot(None))
    )

    # Query from status_reports_history
    srh_interested_query = (
        sa.select([status_reports_history.c.interested_company_id.label('company_id')])
        .where(status_reports_history.c.interested_company_id > last_id)
        .where(status_reports_history.c.interested_company_id.isnot(None))
    )

    srh_company_query = (
        sa.select([status_reports_history.c.company_id.label('company_id')])
        .where(status_reports_history.c.company_id > last_id)
        .where(status_reports_history.c.company_id.isnot(None))
    )

    # Combine queries
    union_query = sa.union(
        sr_interested_query,
        sr_company_query,
        srh_interested_query,
        srh_company_query
    ).alias('status_report_ids')

    # Final query with ordering and limit
    final_query = (
        sa.select([union_query.c.company_id])
        .order_by(union_query.c.company_id)
        .limit(limit)
    )

    # Execute query
    result = db.session.execute(final_query)
    company_ids = [row[0] for row in result]

    return {
        'company_ids': company_ids,
        'has_more': len(company_ids) >= limit
    }


def _get_bulk_import_jobs_company_ids(db, limit, last_id):
    """Get company IDs from bulk_import_jobs table."""
    bulk_import_jobs = db.meta.tables[BULK_IMPORT_JOBS_TABLE]

    query = (
        sa.select([sa.distinct(bulk_import_jobs.c.interested_org_id).label('company_id')])
        .where(bulk_import_jobs.c.interested_org_id > last_id)
        .where(bulk_import_jobs.c.interested_org_id.isnot(None))
        .order_by('company_id')
        .limit(limit)
    )

    result = db.session.execute(query)
    company_ids = [row[0] for row in result]

    return {
        'company_ids': company_ids,
        'has_more': len(company_ids) >= limit
    }


def _get_internal_project_ids_company_ids(db, limit, last_id):
    """Get company IDs from internal_project_ids table."""
    internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]

    query = (
        sa.select([sa.distinct(internal_project_ids.c.company_id).label('company_id')])
        .where(internal_project_ids.c.company_id > last_id)
        .where(internal_project_ids.c.company_id.isnot(None))
        .order_by('company_id')
        .limit(limit)
    )

    result = db.session.execute(query)
    company_ids = [row[0] for row in result]

    return {
        'company_ids': company_ids,
        'has_more': len(company_ids) >= limit
    }


def _get_creditsafe_account_company_ids(db, limit, last_id):
    """Get company IDs from creditsafe_account table."""
    creditsafe_account = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]

    # Check if org_id column exists in the table
    if not hasattr(creditsafe_account.c, 'org_id'):
        return {
            'company_ids': [],
            'has_more': False
        }

    query = (
        sa.select([sa.distinct(creditsafe_account.c.org_id).label('company_id')])
        .where(creditsafe_account.c.org_id > last_id)
        .where(creditsafe_account.c.org_id.isnot(None))
        .order_by('company_id')
        .limit(limit)
    )

    result = db.session.execute(query)
    company_ids = [row[0] for row in result]

    return {
        'company_ids': company_ids,
        'has_more': len(company_ids) >= limit
    }


def _get_creditsafe_account_history_company_ids(db, limit, last_id):
    """Get company IDs from creditsafe_account_history table."""
    creditsafe_account_history = db.meta.tables[CREDITSAFE_ACCOUNT_HISTORY_TABLE]

    # Check if org_id column exists in the table
    if not hasattr(creditsafe_account_history.c, 'org_id'):
        return {
            'company_ids': [],
            'has_more': False
        }

    query = (
        sa.select([sa.distinct(creditsafe_account_history.c.org_id).label('company_id')])
        .where(creditsafe_account_history.c.org_id > last_id)
        .where(creditsafe_account_history.c.org_id.isnot(None))
        .order_by('company_id')
        .limit(limit)
    )

    result = db.session.execute(query)
    company_ids = [row[0] for row in result]

    return {
        'company_ids': company_ids,
        'has_more': len(company_ids) >= limit
    }


def _get_report_cache_company_ids(db, limit, last_id):
    """Get company IDs from report_cache table."""
    report_cache = db.meta.tables[REPORT_CACHE_TABLE]

    # Check if interested_org_id column exists in the table
    if not hasattr(report_cache.c, 'interested_org_id'):
        return {
            'company_ids': [],
            'has_more': False
        }

    query = (
        sa.select([sa.distinct(report_cache.c.interested_org_id).label('company_id')])
        .where(report_cache.c.interested_org_id > last_id)
        .where(report_cache.c.interested_org_id.isnot(None))
        .order_by('company_id')
        .limit(limit)
    )

    result = db.session.execute(query)
    company_ids = [row[0] for row in result]

    return {
        'company_ids': company_ids,
        'has_more': len(company_ids) >= limit
    }


def _extract_org_ids_from_qvarn_report(qvarn_report, last_id):
    """Extract org_ids from the qvarn_report JSON field."""
    org_ids = set()

    if not qvarn_report:
        return org_ids

    # Extract org_ids array
    if 'org_ids' in qvarn_report and isinstance(qvarn_report['org_ids'], list):
        for org_id in qvarn_report['org_ids']:
            if org_id and org_id > last_id:
                org_ids.add(org_id)

    return org_ids


def _extract_user_org_ids_from_qvarn_report(qvarn_report, last_id):
    """Extract user_org_id from each entry in users_to_notify."""
    org_ids = set()

    if not qvarn_report:
        return org_ids

    users_to_notify = qvarn_report.get('users_to_notify', {})
    if not isinstance(users_to_notify, dict):
        return org_ids

    for user_key, user_data in users_to_notify.items():
        if user_data and 'user_org_id' in user_data:
            user_org_id = user_data['user_org_id']
            if user_org_id and user_org_id > last_id:
                org_ids.add(user_org_id)

    return org_ids


def _extract_company_ids_from_project(project_data, last_id):
    """Extract company IDs from a single project."""
    company_ids = set()

    if not project_data:
        return company_ids

    companies = project_data.get('companies', {})
    if not isinstance(companies, dict):
        return company_ids

    for company_id in companies.keys():
        if company_id and company_id > last_id:
            company_ids.add(company_id)

    return company_ids


def _extract_company_ids_from_user_projects(user_data, last_id):
    """Extract company IDs from all projects for a single user."""
    company_ids = set()

    if not user_data:
        return company_ids

    projects = user_data.get('projects', {})
    if not isinstance(projects, dict):
        return company_ids

    for project_id, project_data in projects.items():
        company_ids.update(_extract_company_ids_from_project(project_data, last_id))

    return company_ids


def _extract_company_ids_from_projects(qvarn_report, last_id):
    """Extract company IDs from the companies field in each project."""
    company_ids = set()

    if not qvarn_report:
        return company_ids

    users_to_notify = qvarn_report.get('users_to_notify', {})
    if not isinstance(users_to_notify, dict):
        return company_ids

    for user_key, user_data in users_to_notify.items():
        company_ids.update(_extract_company_ids_from_user_projects(user_data, last_id))

    return company_ids


def _get_notification_reports_company_ids(db, limit, last_id):
    """Get company IDs from notification_reports table.

    This function extracts company IDs from the notification_reports table by:
    1. Extracting org_ids from the qvarn_report JSON field
    2. Extracting user_org_id from each entry in users_to_notify
    3. Extracting company IDs from the companies field in each project

    The function implements batch-based processing to handle large datasets efficiently.
    """
    notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]

    # For small batch sizes, we need to process enough reports to ensure we get the correct IDs
    # This is because the company IDs are embedded in the JSON and not directly queryable

    # Use a large batch size for processing to ensure we get enough IDs
    # but not so large that we cause memory issues
    processing_batch_size = max(1000, limit * 2)

    # Track all company IDs found
    all_company_ids = set()

    # Process reports in batches to avoid memory issues
    offset = 0
    max_batches = 10  # Safety limit to prevent infinite loops
    batch_count = 0

    while batch_count < max_batches:
        # Get a batch of notification reports
        query = (
            sa.select([notification_reports.c.qvarn_report])
            .where(notification_reports.c.qvarn_report.isnot(None))
            .order_by(notification_reports.c.id)
            .limit(processing_batch_size)
            .offset(offset)
        )

        result = db.session.execute(query)
        rows = result.fetchall()

        # If no more rows, we're done
        if not rows:
            break

        # Process each report to extract company IDs
        for row in rows:
            qvarn_report = row[0]

            # Extract company IDs from different parts of the qvarn_report
            org_ids = _extract_org_ids_from_qvarn_report(qvarn_report, last_id)
            user_org_ids = _extract_user_org_ids_from_qvarn_report(qvarn_report, last_id)
            company_ids = _extract_company_ids_from_projects(qvarn_report, last_id)

            # Add all extracted IDs to our set
            all_company_ids.update(org_ids)
            all_company_ids.update(user_org_ids)
            all_company_ids.update(company_ids)

        # If we have enough IDs (at least twice the limit), we can stop
        # This ensures we have enough IDs to determine if there are more results
        if len(all_company_ids) >= limit * 2:
            break

        # Move to the next batch
        offset += processing_batch_size
        batch_count += 1

        # If we processed fewer rows than the batch size, we've reached the end
        if len(rows) < processing_batch_size:
            break

    # Sort all IDs for consistent ordering
    sorted_ids = sorted(list(all_company_ids))

    # Apply the limit
    company_ids = sorted_ids[:limit]

    # Determine if there are more results
    has_more = len(sorted_ids) > limit

    return {
        'company_ids': company_ids,
        'has_more': has_more
    }
