import sqlalchemy as sa

from boldataapi import exceptions
from boldataapi.storage.db import (
    PROJECTS_TABLE,
    REPORT_CACHE_TABLE,
)


Scalar = (float, str, bool, type(None))

# Fake table names when querying over subselects

PROJECT_USERS_DECORATED_TABLE = 'project_users_decorated'
PREANNOUNCEMENTS_DECORATED_TABLE = 'preannouncements_decorated'
SUPPLIERS_DECORATED_TABLE = 'suppliers_decorated'
CREDITSAFE_ACCOUNT_DECORATED_TABLE = 'creditsafe_account_decorated'

# Supported query operators

QUERY_OPERATORS = {
    'eq': (Scalar, lambda column, value: column == value),
    'ne': (Scalar, lambda column, value: column != value),
    'lt': (Scalar, lambda column, value: column < value),
    'gt': (Scalar, lambda column, value: column > value),
    'le': (Scalar, lambda column, value: column <= value),
    'ge': (Scalar, lambda column, value: column >= value),
    # should be List[Scalar], but then you need to replace isinstance() checks with something saner
    'any': (list, lambda column, value: column.in_(value)),
}

# Supported query fields (per table) - mapping from query field name to table
# field name

QUERY_FIELDS_PROJECTS_TABLE = {
    'id': 'external_id',
    'name': 'name',
    'project_responsible_org': 'client_company_id',
    'state': 'state',
    'pa_form_enabled': 'pa_form_enabled',
    'start_date': 'start_date',
    'end_date': 'end_date',
    'project_creator_role': 'project_creator_role',
    'added_client_confirmed': 'added_client_confirmed',
    'added_client_can_view': 'added_client_can_view',
}

QUERY_FIELDS_PROJECT_USERS_TABLE = {
    'id': 'id',
    'project_id': 'project_id',
    'role': 'role',
    'notify': 'notify',
    'user_account_id': 'user_account_id',
    'person_id': 'person_id',
    'represented_company_id': 'represented_company_id',
}

QUERY_FIELDS_PREANNOUNCEMNTS_TABLE = {
    'id': 'external_id',
    'status': 'status',
    'created_by_supplier_id': 'created_by_supplier_external_id',
    'for_supplier_id': 'for_supplier_external_id',
    'project_id': 'project_external_id',
    'assigned_to_company_id': 'assigned_to_company_id',
    'assigned_to_supplier_id': 'assigned_to_supplier_external_id',
    'assigned_to_time': 'assigned_to_time',
}

QUERY_FIELDS_REPORT_CACHE_TABLE = {
    'id': 'external_id',
    'correlation_id': 'correlation_id',
    'expires_at': 'expires_at',
    'interested_org_id': 'interested_org_id',
    'key': 'key',
    'provider': 'provider',
    'type': 'type',
    'value': 'value',
}

QUERY_FIELDS_SUPPLIERS_TABLE = {
    'id': 'external_id',
    'project_resource_id': 'project_external_id',
    'supplier_role': 'role',
    'supplier_type': 'type',
    'contract_start_date': 'contract_start_date',
    'contract_end_date': 'contract_end_date',
    'contract_type': 'contract_type',
    'contract_work_areas': 'contract_work_areas',
    'visitor_type': 'visitor_type',
    'parent_supplier_id': 'parent_supplier_id',
    'parent_org_id': 'parent_company_id',
    'supplier_org_id': 'company_id',
    'first_visited': 'first_visited',
    'last_visited': 'last_visited',
    'revision': 'revision',
    'pa_id': 'pa_external_id',
    'pa_status': 'pa_status',
    'is_one_man_company': 'is_one_man_company',
    'has_collective_agreement': 'has_collective_agreement',
    'collective_agreement_name': 'collective_agreement_name',
}

QUERY_FIELDS_CREDITSAFE_ACCOUNT_TABLE = {
    'id': 'cs_id',
    'org_id': 'cs_org_id',
    'person_id': 'cs_person_id',
    'username': 'cs_username',
    'state': 'cs_state',
    'created_on': 'cs_created_on',
}

QUERY_FIELDS_BY_TABLE = {
    PROJECTS_TABLE: QUERY_FIELDS_PROJECTS_TABLE,
    PROJECT_USERS_DECORATED_TABLE: QUERY_FIELDS_PROJECT_USERS_TABLE,
    PREANNOUNCEMENTS_DECORATED_TABLE: QUERY_FIELDS_PREANNOUNCEMNTS_TABLE,
    REPORT_CACHE_TABLE: QUERY_FIELDS_REPORT_CACHE_TABLE,
    SUPPLIERS_DECORATED_TABLE: QUERY_FIELDS_SUPPLIERS_TABLE,
    CREDITSAFE_ACCOUNT_DECORATED_TABLE: QUERY_FIELDS_CREDITSAFE_ACCOUNT_TABLE,
}


def query_table(db, query, table):
    """
    Query table/table_name using JSON formatted query

    query - JSON of ``field__opperator: <searched_value>``
    table_name - name of the table to query
    table - sqlalchemy table-like object

    NB: ``table_name`` and ``table`` parameters are mutually exclusive
    """
    if not isinstance(query, dict):
        raise exceptions.BadSearchCondition('Bad search query structure')

    table_name = table.name
    fields = QUERY_FIELDS_BY_TABLE[table_name]

    qry = sa.select([table])
    clauses = []
    for k, v in query.items():
        name, __, operator = k.partition('__')
        operator = operator or 'eq'
        try:
            field = fields[name]
        except KeyError:
            raise exceptions.BadSearchCondition(
                f"Unsupported search field for {table.name}: {name}")
        try:
            type_, op = QUERY_OPERATORS[operator]
        except KeyError:
            raise exceptions.BadSearchCondition(f"Unsupported search operator: {operator}")
        if not isinstance(v, type_):
            raise exceptions.BadSearchCondition(f"Bad value type for {operator}")
        clauses.append(op(table.c[field], v))
    qry = qry.where(sa.and_(*clauses))

    try:
        qry_result = db.session.execute(qry)
    except (sa.exc.DataError, sa.exc.ProgrammingError) as e:
        raise exceptions.BadSearchCondition(str(getattr(e, 'orig', None)))

    return [
        row
        for row in (qry_result if qry_result.returns_rows else [])
    ]


def query_table_by_name(db, query, table_name):
    """See documentation for query_table()"""
    table = db.meta.tables[table_name]
    return query_table(db, query, table)
