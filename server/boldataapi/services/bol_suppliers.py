import json
import uuid
from collections import defaultdict

import sqlalchemy as sa
import yaml

from boldataapi import exceptions
from boldataapi.featureflags import feature_active
from boldataapi.schema import (
    LINKED,
    MAIN_CONTRACTOR_ROLE,
    PA_STATUSES,
    SUPERVISOR_ROLE,
    SUPPLIER_CONTRACT_TYPES,
    SUPPLIER_CONTRACT_WORK_AREAS,
    SUPPLIER_ROLES,
    SUPPLIER_TYPES,
    UNLINKED,
    VISITOR,
    VISITOR_TYPES,
)
from boldataapi.services.internal_project_ids import (
    create_internal_project_id,
    delete_internal_project_id,
    get_internal_project_id,
    get_internal_project_ids,
    update_internal_project_id,
)
from boldataapi.services.shared import query_table, SUPPLIERS_DECORATED_TABLE
from boldataapi.storage import qvarn
from boldataapi.storage.db import (
    PREANNOUNCEMENTS_TABLE,
    PROJECTS_TABLE,
    SUPPLIER_CONTACTS_TABLE,
    SUPPLIERS_TABLE,
)
from boldataapi.storage.qvarn.resources import get_resource
from boldataapi.storage.qvarn_helpers import generate_qvarn_id
from boldataapi.swagger import DELETE_MARKER, schema_variant


SUPPLIER_RESOURCE_NAME = 'bol_suppliers'
SUPPLIER_RESOURCE = get_resource(SUPPLIER_RESOURCE_NAME)
SUPPLIER_RESOURCE_TYPE = SUPPLIER_RESOURCE.type
SUPPLIER_RESOURCE_PROTOTYPE = SUPPLIER_RESOURCE.schema


def get_supplier_ids(db):
    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    qry = (
        sa.select([suppliers.c.external_id])
    )
    rez = db.session.execute(qry)
    rows = [row for row in rez]
    return [{'id': x[0]} for x in rows]


def get_supplier_id(db, external_id):
    if external_id is None:
        return None

    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    qry = (
        sa.select([suppliers.c.id])
        .where(suppliers.c.external_id == external_id)
    )
    rez = db.session.execute(qry)
    ids = rez.fetchone()
    return ids[0] if ids else None


def query_suppliers(db, query):
    # Decorate supplier table with external_ids for project
    bol_projects = db.meta.tables[PROJECTS_TABLE]
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]
    preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]

    sa_table = (
        sa.select([
            bol_suppliers,
            bol_projects.c.external_id.label('project_external_id'),
            preannouncements.c.external_id.label('pa_external_id'),
            preannouncements.c.status.label('pa_status'),

        ])
        .select_from(
            bol_suppliers
            .join(bol_projects, bol_projects.c.id == bol_suppliers.c.project_id)
            .outerjoin(preannouncements, preannouncements.c.for_supplier_id == bol_suppliers.c.id)
        )
    ).alias(SUPPLIERS_DECORATED_TABLE)

    rows = query_table(db, query, table=sa_table)

    # Get supplier contact and internal project ids in bulk
    supplier_ids = []
    project_company_id_tuples = []
    for s in rows:
        supplier_ids.append(s[bol_suppliers.c.id])
        t = (s[bol_suppliers.c.project_id], s[bol_suppliers.c.company_id])
        project_company_id_tuples.append(t)

    supplier_contacts = get_supplier_contacts_by_supp_ids(db, supplier_ids)
    internal_project_ids = get_internal_project_ids(db, project_company_id_tuples)

    retrieved_suppliers = []
    for s in rows:
        internal_project_id = None
        key = (s[bol_suppliers.c.project_id], s[bol_suppliers.c.company_id])
        if key in internal_project_ids:
            internal_project_id = internal_project_ids[key]

        # This should match the OpenAPI schema documented in SWAGGER_SUPPLIER_SCHEMA, except
        # we never expose the internal ID; instead we rename external_id to id.
        retrieved_supplier = {
            'id': s[bol_suppliers.c.id],
            'external_id': s[bol_suppliers.c.external_id],
            'supplier_role': s[bol_suppliers.c.role],
            'supplier_type': s[bol_suppliers.c.type],
            'contract_start_date': s[bol_suppliers.c.contract_start_date],
            'contract_end_date': s[bol_suppliers.c.contract_end_date],
            'contract_type': s[bol_suppliers.c.contract_type],
            'contract_work_areas': s[bol_suppliers.c.contract_work_areas],
            'materialized_path': s[bol_suppliers.c.materialized_path],
            'visitor_type': s[bol_suppliers.c.visitor_type],
            'project_resource_id': s[sa_table.c.project_external_id],
            'parent_supplier_id': s[bol_suppliers.c.parent_supplier_id],
            'parent_org_id': s[bol_suppliers.c.parent_company_id],
            'supplier_org_id': s[bol_suppliers.c.company_id],
            'internal_project_id': internal_project_id,
            'first_visited': get_visited(db, s)['first'],
            'last_visited': get_visited(db, s)['last'],
            'revision': s[bol_suppliers.c.revision],
            'supplier_contacts': supplier_contacts[s[bol_suppliers.c.id]],
            'pa_id': s[sa_table.c.pa_external_id],
            'pa_status': s[sa_table.c.pa_status],
            'is_one_man_company': s[bol_suppliers.c.is_one_man_company],
            'has_collective_agreement': s[bol_suppliers.c.has_collective_agreement],
            'collective_agreement_name': s[bol_suppliers.c.collective_agreement_name],
        }
        retrieved_suppliers.append(retrieved_supplier)

    return retrieved_suppliers


def get_supplier(db, *, external_id=None, supplier_id=None):
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]
    bol_projects = db.meta.tables[PROJECTS_TABLE]
    preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]

    params = [external_id, supplier_id]

    if not any(params) or all(params):
        raise exceptions.InternalServerError(
            msg="One of parameters must be provided: external_id or supplier_id"
        )

    search_predicate = (
        bol_suppliers.c.external_id == external_id
    )
    if supplier_id:
        search_predicate = (
            bol_suppliers.c.id == supplier_id
        )
    query = (
        sa.select([
            bol_suppliers,
            bol_projects.c.external_id,
            preannouncements.c.external_id,
            preannouncements.c.status,
        ])
        .select_from(
            bol_suppliers
            .join(bol_projects, bol_projects.c.id == bol_suppliers.c.project_id)
            .outerjoin(preannouncements, preannouncements.c.for_supplier_id == bol_suppliers.c.id)
        )
        .where(search_predicate)
    )
    rez = db.session.execute(query)
    supplier = rez.fetchone()
    if not supplier:
        return None

    internal_project_id = None
    db_internal_project_id = get_internal_project_id(
        db,
        supplier[bol_suppliers.c.project_id],
        supplier[bol_suppliers.c.company_id],
    )
    if db_internal_project_id:
        internal_project_id = db_internal_project_id['internal_project_id']
    # This should match the OpenAPI schema documented in SWAGGER_SUPPLIER_SCHEMA, except
    # we never expose the internal ID; instead we rename external_id to id.
    retrieved_supplier = {
        'id': supplier[bol_suppliers.c.id],
        'external_id': supplier[bol_suppliers.c.external_id],
        'supplier_role': supplier[bol_suppliers.c.role],
        'supplier_type': supplier[bol_suppliers.c.type],
        'contract_start_date': supplier[bol_suppliers.c.contract_start_date],
        'contract_end_date': supplier[bol_suppliers.c.contract_end_date],
        'contract_type': supplier[bol_suppliers.c.contract_type],
        'contract_work_areas': supplier[bol_suppliers.c.contract_work_areas],
        'materialized_path': supplier[bol_suppliers.c.materialized_path],
        'visitor_type': supplier[bol_suppliers.c.visitor_type],
        'project_resource_id': supplier[bol_projects.c.external_id],
        'parent_supplier_id': supplier[bol_suppliers.c.parent_supplier_id],
        'parent_org_id': supplier[bol_suppliers.c.parent_company_id],
        'supplier_org_id': supplier[bol_suppliers.c.company_id],
        'is_one_man_company': supplier[bol_suppliers.c.is_one_man_company],
        'has_collective_agreement': supplier[bol_suppliers.c.has_collective_agreement],
        'collective_agreement_name': supplier[bol_suppliers.c.collective_agreement_name],
        'internal_project_id': internal_project_id,
        'first_visited': get_visited(db, supplier)['first'],
        'last_visited': get_visited(db, supplier)['last'],
        'revision': supplier[bol_suppliers.c.revision],
        'supplier_contacts': get_supplier_contacts(db, supplier[bol_suppliers.c.id]),
        'type': 'bol_supplier',  # Qvarn back compat
        'bolagsfakta_status': None,  # Qvarn back compat
        'pa_id': supplier[preannouncements.c.external_id],
        'pa_status': supplier[preannouncements.c.status],
    }
    return retrieved_supplier


SWAGGER_SUPPLIER_SCHEMA = yaml.safe_load(f"""
type: object
properties:
  id:
    type: string
    example: 4d0a-885d700e61bc75360fb967a7b6f6cad1-2504940e
    description: database ID (aka external ID)
  supplier_role:
    type: string
    enum: {json.dumps(SUPPLIER_ROLES)}
    description: |
      supplier role in the project

      Usually only top-level suppliers have "{SUPERVISOR_ROLE}" or "{MAIN_CONTRACTOR_ROLE}" roles.
  supplier_type:
    type: string
    enum: {json.dumps(SUPPLIER_TYPES)}
    description: |
      supplier type

      "{LINKED}" suppliers form the project tree.  "{UNLINKED}" suppliers
      participate in the project, but their position in the project tree is not yet known.
      "{VISITOR}" are possible suppliers who had employees visiting the
      construction site according to STAMP data, despite not being added as a
      supplier.
  supplier_org_id:
    type: string
    example: b8cd-10e9bab8c160372c0ea89b954acec9da-d8629c6d
    description: |
      database ID of the supplier organization

      The same organization can appear as a supplier multiple times in the same
      project, possibly at different levels of the tree.  It will have multiple
      supplier resources sharing the same `supplier_org_id`.
  project_resource_id:
    type: string
    example: 1d32-06c3d72fa6a8319618719e6e3722b282-1f5916b5
    description: database ID of the project
  parent_supplier_id:
    type: string
    nullable: true
    example: 2141-c83ebbbc3862fbad6a964f113df4955c-001f600c
    description: |
      database ID of the parent supplier

      Suppliers form a tree.  Top-level suppliers have a `parent_supplier_id` of `null`, i.e.
      the root node of the tree does not have a bol_supplier resource in the database.
  parent_org_id:
    type: string
    nullable: true
    example: 1944-adf740eb271250c985fcf63e84efbd30-664f4ff3
    description: |
      database ID of the parent supplier's organization

      This should always match the `supplier_org_id` of the supplier identified
      by `parent_supplier_id`.
  materialized_path:
    type: array
    minItems: 2
    description:
      A list of organization database IDs from the project tree root (the
      project's responsible organization) down to this supplier.  The last item
      in the list should always match the `supplier_org_id`.  The penultimate
      item in the list should match `parent_org_id`, if `parent_org_id` is not
      `null`.
    example:
      - 52c7-b6ef5c50c5082e229069d6d10284bbda-3c0a8d06
      - 1944-adf740eb271250c985fcf63e84efbd30-664f4ff3
      - b8cd-10e9bab8c160372c0ea89b954acec9da-d8629c6d
    items:
      type: string
      example: 02e4-74b2ec86567be75ef89f4915de72d3c7-bb82cef3
      description: |
        database ID of the supplier organization, except for position 0 which
        contains the database ID of the project's responsible organization
  contract_start_date:
    type: string
    format: date
    nullable: true
    description: contract start date (YYYY-MM-DD)
  contract_end_date:
    type: string
    format: date
    nullable: true
    description: contract end date (YYYY-MM-DD)
  contract_type:
    type: string
    nullable: true
    enum: {json.dumps(SUPPLIER_CONTRACT_TYPES)}
    description: type of work for the contract
  contract_work_areas:
    type: array
    description: list of work areas according to the contract
    items:
      type: string
      enum: {json.dumps(SUPPLIER_CONTRACT_WORK_AREAS)}
  internal_project_id:
    type: string
    nullable: true
    example: TP-1003
    description: an arbitrary project ID that the supplier company uses for this project
  first_visited:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    nullable: true
    description: earliest recorded site visit according to STAMP (ISO-8601)
  last_visited:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    nullable: true
    description: latest recorded site visit according to STAMP (ISO-8601)
  visitor_type:
    type: string
    nullable: true
    enum: {json.dumps(VISITOR_TYPES)}
    description: type of visitor of the supplier
  supplier_contacts:
    type: array
    description: list of supplier contact persons
    items:
      properties:
        supplier_contact_person_id:
          type: string
          example: 9a73-ec67ffedab8cb852988b2c29029e0271-c8ace3f6
          nullable: true
          description: database ID of the supplier contact person, if one exists in the database
        supplier_contact_email:
          type: string
          nullable: true
          format: email
          description: supplier contact email address
  bolagsfakta_status:
    type: string
    nullable: true
    example: null
    description: always `null`, for Qvarn compatibility
  pa_id:
    type: string
    nullable: true
    example: 66211b7c-3848-4f75-ab8f-a9ecd98a611b
    description: database ID of the preannouncement for this supplier, if one exists
  pa_status:
    type: string
    nullable: true
    enum: {json.dumps(PA_STATUSES)}
    description: preannouncement status, if a preannouncement exists
  type:
    type: string
    enum:
      - bol_supplier
    description: always "bol_supplier", for Qvarn compatibility
  revision:
    type: string
    example: 3b0cd997-1af1-4e80-a77c-dd27bdd557b6
    description: |
      revision number of the supplier record

      This is intended to avoid update conflicts, but that is not currently fully implemented.
  is_one_man_company:
    description: Indicator for one man company
    type: boolean
  has_collective_agreement:
    description: Indicator for if supplier has colletive agreement parameter
    type: boolean
  collective_agreement_name:
    description: ignored
    type: string
""")


SWAGGER_NEW_SUPPLIER_SCHEMA = schema_variant(SWAGGER_SUPPLIER_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_new_bol_supplier
required:
  - supplier_role
  - supplier_type
  - materialized_path
  - project_resource_id
  - supplier_org_id
properties:
  id: {DELETE_MARKER}
  bolagsfakta_status: {DELETE_MARKER}
  contract_work_areas:
    nullable: true
  supplier_contacts:
    description: |
      list of supplier contact persons

      For each item either `supplier_contact_person_id` or
      `supplier_contact_email` must be non-null.

      Projects using PA forms should have at least one contact, but this is
      not enforced at the BDA level.
  pa_id:
    description: ignored
  pa_status:
    description: ignored
  type:
    description: ignored
  revision:
    description: ignored
  is_one_man_company:
    description: ignored
  has_collective_agreement:
    description: ignored
  collective_agreement_name:
    description: ignored
""")


SWAGGER_UPDATE_SUPPLIER_SCHEMA = schema_variant(SWAGGER_SUPPLIER_SCHEMA, """
# This is basically the same as SWAGGER_NEW_SUPPLIER_SCHEMA, with the following differences:
# - no fields are required, except for revision, which must match the current revision
# - id is allowed (and ignored)
# - bolagsfakta_status is allowed (and ignored)
# - supplier contacts are allowed to have entries with no data
# Note that despite the above list being a diff against SWAGGER_NEW_SUPPLIER_SCHEMA,
# we're defining the YAML as a diff against SWAGGER_SUPPLIER_SCHEMA, for
# reasons of simplicity (easier to mentally apply one delta rather than two)
# and for preserving property order (if you remove `id` and add it back, it'll
# end up last in the list.)
x-bolfak-schema: boldataapi.schema.schema_existing_bol_supplier
required:
  - revision
properties:
  id:
    type: string
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  contract_work_areas:
    nullable: true
  supplier_contacts:
    type: array
    nullable: true
    description: |
      list of supplier contact persons

      Omit it or set to `null` to keep the old list of contacts.  Specify `[]`
      to remove all contacts.  Specify a new list of contacts to replace the
      entire list of contacts.

      Note that there's no longer a requirement for either
      `supplier_contact_person_id` or `supplier_contact_email` to be
      non-null in each list item.  I'm not sure why.

      Projects using PA forms should have at least one contact, but this is
      not enforced at the BDA level.
  bolagsfakta_status:
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  pa_id:
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  pa_status:
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  type:
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  revision:
    description: |
      must match the current revision of the supplier, or the update will fail

      This is intended as a mechanism to prevent update conflicts losing data,
      but it's not fully implemented yet (the revision should change
      automatically after each PUT, but this is not yet implemented).
""")


def get_visited(db, supplier):
    """Get visited information.


    Entry point to modify if visitor information location changes.
    """
    visited = {'first': None, 'last': None}

    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]
    visited['last'] = supplier[bol_suppliers.c.last_visited]

    if feature_active('first_visited'):
        visited['first'] = supplier[bol_suppliers.c.first_visited]

    return visited


def create_supplier(db, data: dict, *, supplier_contacts=None, internal_project_id=None) -> dict:

    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]

    insert_values = dict(
        id=str(uuid.uuid4()),
        external_id=generate_qvarn_id(SUPPLIER_RESOURCE_TYPE.replace('qvarn_', '')),
        role=data.get('role'),
        type=data.get('type'),
        project_id=data.get('project_id'),
        parent_supplier_id=data.get('parent_supplier_id'),
        parent_company_id=data.get('parent_company_id'),
        company_id=data.get('company_id'),
        contract_start_date=data.get('contract_start_date'),
        contract_end_date=data.get('contract_end_date'),
        contract_work_areas=data.get('contract_work_areas'),
        materialized_path=data.get('materialized_path'),
        last_visited=data.get('last_visited'),
        revision=str(uuid.uuid4()),
        contract_type=data.get('contract_type'),
        visitor_type=data.get('visitor_type'),
        is_one_man_company=data.get('is_one_man_company'),
        has_collective_agreement=data.get('has_collective_agreement'),
        collective_agreement_name=data.get('collective_agreement_name'),
    )
    if feature_active('first_visited'):
        insert_values['first_visited'] = data.get('first_visited')
    else:
        pass

    query = sa.insert(bol_suppliers).values(**insert_values)
    result = db.session.execute(query)
    supplier_id = result.inserted_primary_key[0]
    supplier = _get_supplier(db, supplier_id)

    if supplier_contacts:
        _create_supplier_contacts(db, supplier_id, supplier_contacts)

    if internal_project_id:
        existing_project_id = get_internal_project_id(
            db,
            supplier['project_id'],
            supplier['company_id']
        )
        if not existing_project_id:
            create_internal_project_id(
                db,
                supplier['project_id'],
                internal_project_id,
                supplier['company_id'],
            )
    return get_supplier(db, supplier_id=supplier_id)


def update_supplier(db, supplier_id, data, *, supplier_contacts=None):
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]

    query = sa.update(bol_suppliers) \
        .where(sa.and_(
            bol_suppliers.c.id == supplier_id,
            bol_suppliers.c.revision == data['revision']
        )).values(data)
    db.session.execute(query)

    if supplier_contacts is not None:
        _update_supplier_contacts(db, supplier_id, supplier_contacts)
    return get_supplier(db, supplier_id=supplier_id)


def _get_supplier(db, supplier_id):
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]
    qry = (
        sa.select([
            bol_suppliers.c.project_id,
            bol_suppliers.c.company_id,
        ])
        .where(bol_suppliers.c.id == supplier_id)
    )
    rez = db.session.execute(qry)
    supplier = rez.fetchone()
    return dict(supplier) if supplier else None


def _update_supplier_contacts(db, supplier_id, supplier_contacts):
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]

    qry_all = (
        sa.select([s_contacts.c.id])
        .where(s_contacts.c.supplier_id == supplier_id)
    )
    rez = db.session.execute(qry_all)

    all_contacts_ids = [row['id'] for row in rez]
    contacts_ids_to_remain = []
    contacts_to_add = []

    for supplier_contact in supplier_contacts:
        qry = (
            sa.select([s_contacts.c.id])
            .select_from(s_contacts.
                         join(
                             bol_suppliers,
                             bol_suppliers.c.id == s_contacts.c.supplier_id))
            .where(sa.and_(
                s_contacts.c.supplier_id == supplier_id,
                s_contacts.c.person_id == supplier_contact['supplier_contact_person_id'],
                s_contacts.c.person_email == supplier_contact['supplier_contact_email'],
            ))
        )
        rez = db.session.execute(qry)
        contact = rez.fetchone()
        if contact:
            contacts_ids_to_remain.append(dict(contact)['id'])
        else:
            contacts_to_add.append(supplier_contact)
    contacts_ids_to_be_deleted = set(all_contacts_ids) - set(contacts_ids_to_remain)
    if contacts_ids_to_be_deleted:
        _delete_supplier_contacts(db, supplier_id, list(contacts_ids_to_be_deleted))
    if contacts_to_add:
        _create_supplier_contacts(db, supplier_id, contacts_to_add)
    return


def search_bol_suppliers(db, path):
    # Set up Qvarn DB connection
    dbconn = qvarn.DatabaseConnection()
    dbconn.set_sql(qvarn.PostgresAdapterSimplified(db))

    # Set up ListResource
    resource = qvarn.ListResource()
    resource.set_path(SUPPLIER_RESOURCE_TYPE)
    resource.set_item_type(SUPPLIER_RESOURCE_TYPE)
    resource.set_item_prototype(SUPPLIER_RESOURCE_PROTOTYPE)
    resource.prepare_resource(dbconn)

    result = resource.get_matching_items(path)
    return result


def delete_supplier(db, supplier_id):

    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]

    supplier = _get_supplier(db, supplier_id)

    # get all contacts by supplier
    qry = (
        sa.select([s_contacts.c.id])
        .where(s_contacts.c.supplier_id == supplier_id)
    )
    result = db.session.execute(qry)
    supplier_contacts_ids = [row[s_contacts.c.id] for row in result]

    if supplier_contacts_ids:
        _delete_supplier_contacts(db, supplier_id, supplier_contacts_ids)

    query = (
        sa.delete(bol_suppliers)
        .where(bol_suppliers.c.id == supplier_id)
    )
    db.session.execute(query)

    #  delete internal project id if supplier exists
    # and is last supplier in a project
    # and supplier company is not project's client
    if get_internal_project_id(
            db,
            supplier['project_id'],
            supplier['company_id']
    ) and not _has_company_suppliers_in_project(
        db, supplier['company_id'], supplier['project_id'],
    ) and not _is_company_project_responsible_org(
        db, supplier['company_id'], supplier['project_id']
    ):
        delete_internal_project_id(db, supplier['project_id'], supplier['company_id'])
    return


def get_supplier_contacts(db, supplier_id):
    bol_supplier = db.meta.tables[SUPPLIERS_TABLE]
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]

    qry = (
        sa.select([
            bol_supplier.c.id,
            s_contacts.c.person_id,
            s_contacts.c.person_email,
        ]).
        select_from(
            bol_supplier.
            join(
                s_contacts,
                bol_supplier.c.id == s_contacts.c.supplier_id,
            )
        ).
        where(bol_supplier.c.id == supplier_id)
    )

    qry_result = db.session.execute(qry)

    result = []
    for row in qry_result if qry_result.returns_rows else []:
        result.append({
            'supplier_contact_person_id': row[s_contacts.c.person_id],
            'supplier_contact_email': row[s_contacts.c.person_email],
        })

    return result


def get_supplier_contacts_by_supp_ids(db, supplier_ids):
    bol_supplier = db.meta.tables[SUPPLIERS_TABLE]
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]

    qry = (
        sa.select([
            bol_supplier.c.id,
            s_contacts.c.person_id,
            s_contacts.c.person_email,
        ]).
        select_from(
            bol_supplier.
            join(
                s_contacts,
                bol_supplier.c.id == s_contacts.c.supplier_id,
            )
        ).
        where(bol_supplier.c.id.in_(supplier_ids))
    )

    qry_result = db.session.execute(qry)

    result = defaultdict(list)
    for row in qry_result if qry_result.returns_rows else []:

        result[row[bol_supplier.c.id]].append({
            'supplier_contact_person_id': row[s_contacts.c.person_id],
            'supplier_contact_email': row[s_contacts.c.person_email],
        })

    return result


def _create_supplier_contacts(db, supplier_id, supplier_contacts):
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]
    for contact in supplier_contacts:
        query = sa.insert(s_contacts).values(
            id=str(uuid.uuid4()),
            supplier_id=supplier_id,
            person_id=contact['supplier_contact_person_id'],
            person_email=contact['supplier_contact_email'],
        )
        db.session.execute(query)


def _delete_supplier_contacts(db, supplier_id, contact_ids):
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]

    qry = (
        sa.delete(s_contacts)
        .where(sa.and_(
            s_contacts.c.supplier_id == supplier_id,
            s_contacts.c.id.in_(contact_ids)
        ))
    )
    db.session.execute(qry)


def _has_company_suppliers_in_project(db, company_id, project_id):
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]
    projects = db.meta.tables[PROJECTS_TABLE]

    qry = (
        sa.select([bol_suppliers.c.id])
        .select_from(
            bol_suppliers.
            join(projects, projects.c.id == bol_suppliers.c.project_id))
        .where(sa.and_(
            bol_suppliers.c.project_id == project_id,
            bol_suppliers.c.company_id == company_id
        ))
    )
    rez = db.session.execute(qry)
    rows = [row for row in rez]
    return True if len(rows) else False


def _is_company_project_responsible_org(db, company_id, project_id):
    projects = db.meta.tables[PROJECTS_TABLE]
    qry = (
        sa.select([projects.c.id])
        .where(sa.and_(
            projects.c.id == project_id,
            projects.c.client_company_id == company_id,
        ))
    )
    rez = db.session.execute(qry)
    rows = [row for row in rez]
    return True if len(rows) else False


def update_supplier_internal_project_id(
        db,
        internal_project_id,
        supplier_project_id,
        supplier_company_id
):
    existing_internal_project_id = get_internal_project_id(
        db,
        supplier_project_id,
        supplier_company_id,
    )
    if existing_internal_project_id and internal_project_id:
        update_internal_project_id(
            db,
            internal_project_id,
            existing_internal_project_id['id'],
        )
    elif existing_internal_project_id:
        delete_internal_project_id(
            db,
            supplier_project_id,
            supplier_company_id,
        )
    elif internal_project_id:
        create_internal_project_id(
            db,
            supplier_project_id,
            internal_project_id,
            supplier_company_id,
        )
