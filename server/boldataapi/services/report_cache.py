import uuid

import sqlalchemy as sa

from boldataapi import exceptions
from boldataapi.services.shared import query_table
from boldataapi.storage.db import REPORT_CACHE_TABLE
from boldataapi.swagger import DELETE_MARKER, schema_variant


def get_report_cache(db, *, external_id=None, report_cache_id=None):
    tbl_report_cache = db.meta.tables[REPORT_CACHE_TABLE]

    params = [external_id, report_cache_id]

    if not any(params) or all(params):
        raise exceptions.InternalServerError(
            msg="One of parameters must be provided: external_id or report_cache_id"
        )

    query = (
        sa.select([tbl_report_cache])
        .where(tbl_report_cache.c.external_id == external_id)
    )
    if report_cache_id:
        query = (
            sa.select([tbl_report_cache])
            .where(tbl_report_cache.c.id == report_cache_id)
        )

    res = db.session.execute(query)
    report_cache = res.fetchone()

    if not report_cache:
        return None

    retrieved_report_cache = {
        'id': report_cache[tbl_report_cache.c.id],
        'external_id': report_cache[tbl_report_cache.c.external_id],
        'correlation_id': report_cache[tbl_report_cache.c.correlation_id],
        'expires_at': report_cache[tbl_report_cache.c.expires_at],
        'interested_org_id': report_cache[tbl_report_cache.c.interested_org_id],
        'key': report_cache[tbl_report_cache.c.key],
        'provider': report_cache[tbl_report_cache.c.provider],
        'type': report_cache[tbl_report_cache.c.type],
        'value': report_cache[tbl_report_cache.c.value],
    }
    return retrieved_report_cache


def create_report_cache(db, data):
    tbl_report_cache = db.meta.tables[REPORT_CACHE_TABLE]

    query = sa.insert(tbl_report_cache).values(
        id=str(uuid.uuid4()),
        external_id=str(uuid.uuid4()),
        correlation_id=data.get('correlation_id'),
        expires_at=data.get('expires_at'),
        interested_org_id=data.get('interested_org_id'),
        key=data.get('key'),
        provider=data.get('provider'),
        type=data.get('type'),
        value=data.get('value'),
    )
    result = db.session.execute(query)
    return get_report_cache(db, report_cache_id=result.inserted_primary_key[0])


def update_report_cache(db, report_cache_id, data):
    tbl_report_cache = db.meta.tables[REPORT_CACHE_TABLE]

    query = (
        sa.update(tbl_report_cache)
        .where(tbl_report_cache.c.id == report_cache_id)
        .values(data)
    )
    db.session.execute(query)
    return get_report_cache(db, report_cache_id=report_cache_id)


def delete_report_cache(db, report_cache_id):
    tbl_report_cache = db.meta.tables[REPORT_CACHE_TABLE]

    qry = (
        sa.delete(tbl_report_cache)
        .where(tbl_report_cache.c.id == report_cache_id)
    )
    db.session.execute(qry)


def query_report_cache(db, query):
    report_cache = db.meta.tables[REPORT_CACHE_TABLE]
    rows = query_table(db, query, table=report_cache)
    report_caches = [
        {
            'id': rc[report_cache.c.external_id],
            'correlation_id': rc[report_cache.c.correlation_id],
            'expires_at': rc[report_cache.c.expires_at],
            'interested_org_id': rc[report_cache.c.interested_org_id],
            'key': rc[report_cache.c.key],
            'provider': rc[report_cache.c.provider],
            'type': rc[report_cache.c.type],
            'value': rc[report_cache.c.value],
        }
        for rc in rows
    ]
    return report_caches


SWAGGER_REPORT_CACHE_SCHEMA = schema_variant("""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID)
    example: b52dc07e-3f40-4de4-b9d0-2a9adedd436e
  expires_at:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54
    description: ISO 8601 timestamp (in UTC) of when the report was accessed
  correlation_id:
    type: string
    example: 97742793-8285-48bb-9413-fc4b9db80a42
    description: request ID used to relate cache entries created
        from a single call to the middle layer
  interested_org_id:
    type: string
    example: 095b-8bacfe95354e84a42355760e46487769-55c8fd8a
    description: org resource ID which retrieved the report
  key:
    type: string
    example: 095b-8bacfe95354e84a42355760e46487769-55c8fd8a:CS_BLOCK_NAME
    description: org resource identifier or provider token in the form of "token:cs_block_name"
  provider:
    type: string
    enum:
      - "creditsafe"
      - "creditsafe_v2"
      - "creditsafe_ggs"
      - "creditsafe_connect"
      - "creditsafe_connect_core"
      - "bisnode"
      - "skatteverket"
    example: creditsafe_connect
    description: report provider name
  type:
    type: string
    enum:
      - "statusreports_raw"
      - "statusreports_parsed"
    example: statusreports_parsed, statusreports_raw
    description: type of cache
        "statusreports_raw" - status report data retrieved from provider as is
        "statusreports_parsed" - parsed statusreport data from specific provider used to combine
        a report from multiple providers data.
  value:
    type: string
    example: {some key 1: some new value 12}
    description: report raw data if type is set to statusreports_raw or parsed data
        in key-value form from a specific report provider if type is set to statusreports_parsed
""")

SWAGGER_NEW_REPORT_CACHE_SCHEMA = schema_variant(SWAGGER_REPORT_CACHE_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_new_report_cache
required:
  - key
  - provider
  - value
  - type
properties:
  id: {DELETE_MARKER}
  correlation_id:
  expires_at:
  interested_org_id:
""")

SWAGGER_UPDATE_REPORT_CACHE_SCHEMA = schema_variant(SWAGGER_REPORT_CACHE_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_existing_report_cache
properties:
  id: {DELETE_MARKER}
""")
