import uuid

import sqlalchemy as sa
import yaml

from boldataapi import exceptions
from boldataapi.schema import QVARN_COMPAT_FIELDS_FOR_REPORTS
from boldataapi.services.reports import REPORT_RESOURCE_TYPE
from boldataapi.storage.db import NOTIFICATION_REPORTS_TABLE
from boldataapi.storage.qvarn_helpers import generate_qvarn_id
from boldataapi.swagger import (
    DELETE_MARKER,
    qvarn_compat_schema_for_reading,
    qvarn_compat_schema_for_writing,
    schema_variant,
)


def get_notification_report(db, *, external_id=None, report_id=None):
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]

    params = [external_id, report_id]

    if not any(params) or all(params):
        raise exceptions.InternalServerError(
            msg="One of parameters must be provided: external_id or report_id"
        )

    search_predicate = (
        tbl_notification_reports.c.external_id == external_id
    )
    if report_id:
        search_predicate = (
            tbl_notification_reports.c.id == report_id
        )

    qry = (
        sa.select([tbl_notification_reports])
        .where(search_predicate)
    )

    rez = db.session.execute(qry)
    db_notification_report = rez.fetchone()
    if db_notification_report is None:
        return None
    r = db_notification_report
    notification_report = {
        'id': db_notification_report[tbl_notification_reports.c.id],
        'external_id': db_notification_report[tbl_notification_reports.c.external_id],
        'generated_timestamp': r[tbl_notification_reports.c.generated_timestamp],
        'pdf': r[tbl_notification_reports.c.qvarn_report],
        'tilaajavastuu_status': None,  # Qvarn back compat
        'interested_org_id': None,  # Qvarn back compat
        'org': None,  # Qvarn back compat
        'report_type': 'bolagsfakta.status_change_report',  # Qvarn back compat
        'type': 'report',  # Qvarn back compat
    }
    notification_report.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)

    return notification_report


SWAGGER_NOTIFICATION_REPORT_SCHEMA = yaml.safe_load("""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID)
    example: 4c29-e2ced4332b3f01801173ec4505f8bb11-aa9062c2
  report_type:
    type: string
    enum:
      - bolagsfakta.status_change_report
    description: always `bolagsfakta.status_change_report`, for Qvarn compatibility
  org:
    type: string
    nullable: true
    example: null
    description: always `null` for Qvarn compatibility
  interested_org_id:
    type: string
    nullable: true
    example: null
    description: always `null` for Qvarn compatibility
  tilaajavastuu_status:
    type: string
    nullable: true
    example: null
    description: always `null` for Qvarn compatibility
  generated_timestamp:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: ISO 8601 timestamp (in UTC) of when the report was generated
  # the 'pdf' field is not always present
  # and sometimes it's renamed to 'json'
  # so I'm not going to document it here
  type:
    type: string
    enum:
      - report
    description: always "report", for Qvarn compatibility
  revision:
    type: string
    nullable: true
    example: null
    description: always `null`, for Qvarn compatibility
""")


SWAGGER_NOTIFICATION_REPORT_SCHEMA_QVARN_COMPAT = schema_variant(
    SWAGGER_NOTIFICATION_REPORT_SCHEMA,
    qvarn_compat_schema_for_reading(QVARN_COMPAT_FIELDS_FOR_REPORTS))


SWAGGER_NEW_NOTIFICATION_REPORT_SCHEMA = schema_variant(SWAGGER_NOTIFICATION_REPORT_SCHEMA, """
x-bolfak-schema: boldataapi.schema.schema_new_notification_report
required:
  - type
  - report_type
  - generated_timestamp
properties:
  id:
    nullable: true
    description: ignored (for Qvarn compatibility)
  type:
    description: ignored (for Qvarn compatibility)
  tilaajavastuu_status:
    enum:
      - "100 STOP"
      - "200 INVESTIGATE"
      - "300 INCOMPLETE"
      - "400 ATTENTION"
      - "500 OK"
  pdf:
    type: object
    required:
      - content_type
      - body
    properties:
      content_type:
        type: string
        enum:
          - "application/json"
        description: always "application/json"
      body:
        type: string
        description: content of the report, as a string containing JSON
        example: "{}"
""", qvarn_compat_schema_for_writing(QVARN_COMPAT_FIELDS_FOR_REPORTS))


SWAGGER_UPDATE_NOTIFICATION_REPORT_SCHEMA = schema_variant(SWAGGER_NEW_NOTIFICATION_REPORT_SCHEMA,
                                                           f"""
x-bolfak-schema: boldataapi.schema.schema_existing_notification_report
required: {DELETE_MARKER}
properties:
  pdf:
    required: {DELETE_MARKER}
  org:
    nullable: true
  interested_org_id:
    nullable: true
""")


def create_notification_report(db, data):
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]

    qry = (
        sa.insert(tbl_notification_reports).values(
            id=str(uuid.uuid4()),
            external_id=generate_qvarn_id(REPORT_RESOURCE_TYPE.replace('qvarn_', '')),
            generated_timestamp=data['generated_timestamp'],
            qvarn_report=data['json_'],
        )
    )
    rez = db.session.execute(qry)
    notification_report_id = rez.inserted_primary_key[0]
    notification_report = get_notification_report(db, report_id=notification_report_id)
    return notification_report


def update_notification_report(db, external_id, data):
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]
    qry = (
        sa.update(tbl_notification_reports)
        .where(tbl_notification_reports.c.external_id == external_id)
        .values(**data)
    )
    db.session.execute(qry)
    return get_notification_report(db, external_id=external_id)


def delete_notification_report(db, external_id):
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]
    qry = (
        sa.delete(tbl_notification_reports)
        .where(tbl_notification_reports.c.external_id == external_id)
    )
    db.session.execute(qry)


def update_notification_report_pdf(db, external_id, report_data):
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]

    qry = (
        sa.update(tbl_notification_reports)
        .where(tbl_notification_reports.c.external_id == external_id)
        .values(qvarn_report=report_data)
    )
    db.session.execute(qry)
    return get_notification_report(db, external_id=external_id)
