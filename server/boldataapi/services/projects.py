import json
import uuid

import sqlalchemy as sa
import yaml

from boldataapi import exceptions
from boldataapi.featureflags import feature_active
from boldataapi.schema import (
    ADMIN_ROLE,
    BOL_STATUS_INCOMPLETE,
    BOL_STATUSES,
    CLIENT_ROLE,
    LINKED,
    MAIN_CONTRACTOR_ROLE,
    PROJECT_ID_TYPE,
    REPORT_STATUSES,
    STATUS_INCOMPLETE,
    STATUS_OK,
    SUPPLIER_ROLE,
    TAX_ID,
    TOP_LEVEL_SUPPLIER_ROLE,
    TOP_LEVEL_SUPPLIER_ROLES,
    UNLINKED,
)
from boldataapi.services.bol_suppliers import SWAGGER_SUPPLIER_SCHEMA
from boldataapi.services.internal_project_ids import (
    create_internal_project_id,
    delete_internal_project_id,
    delete_internal_project_ids,
    get_internal_project_id,
    update_internal_project_id,
)
from boldataapi.services.shared import query_table_by_name
from boldataapi.storage import qvarn
from boldataapi.storage.db import (
    INTERNAL_PROJECT_IDS_TABLE,
    PROJECTS_TABLE,
    STATUS_REPORTS_TABLE,
    SUPPLIER_CONTACTS_TABLE,
    SUPPLIERS_TABLE,
)
from boldataapi.storage.db.schema import PREANNOUNCEMENTS_TABLE
from boldataapi.storage.qvarn.resources import get_resource
from boldataapi.storage.qvarn_helpers import generate_qvarn_id
from boldataapi.swagger import DELETE_MARKER, schema_variant


BOL_STATUS_NOT = 'not:'

ACTIVE = 'active'
CLOSED = 'closed'
DRAFT = 'draft'
NOT_CLOSED = 'not:closed'
PROJECT_STATES = (
    ACTIVE,
    CLOSED,
    DRAFT,
)

DEFAULT_STATUS = BOL_STATUS_INCOMPLETE

PROJECT_RESOURCE_NAME = 'projects'
PROJECT_RESOURCE = get_resource(PROJECT_RESOURCE_NAME)
PROJECT_RESOURCE_TYPE = PROJECT_RESOURCE.type
PROJECT_RESOURCE_PROTOTYPE = PROJECT_RESOURCE.schema


def get_project_id(db, external_id):
    if external_id is None:
        return None

    projects = db.meta.tables[PROJECTS_TABLE]

    qry = (
        sa.select([projects.c.id])
        .where(projects.c.external_id == external_id)
    )
    rez = db.session.execute(qry)
    project_ids = rez.fetchone()
    return project_ids[0] if project_ids else None


def get_projects(db):
    projects = db.meta.tables[PROJECTS_TABLE]

    qry = (
        sa.select([projects.c.external_id])
    )
    rez = db.session.execute(qry)
    rows = [row for row in rez]
    return [{'id': x[0]} for x in rows]


def get_project(db, *, external_id=None, project_id=None):
    projects = db.meta.tables[PROJECTS_TABLE]

    params = [external_id, project_id]

    if not any(params) or all(params):
        raise exceptions.InternalServerError(
            msg="One of parameters must be provided: external_id or project_id"
        )

    search_predicate = (projects.c.external_id == external_id)
    if project_id:
        search_predicate = (projects.c.id == project_id)

    qry = (
        sa.select([projects])
        .where(search_predicate)
    )
    rez = db.session.execute(qry)
    project = rez.fetchone()

    if not project:
        return None

    retrieved_project = _project_as_json(db, project)
    retrieved_project.update({
        'type': 'project',  # Qvarn back compat
        'country': None,  # Qvarn back compat
        'revision': None,  # Qvarn back compat
        'sync': {},  # QAT back compat
    })
    return retrieved_project


def _project_as_json(db, project):
    projects = db.meta.tables[PROJECTS_TABLE]
    project_ids = []
    if project[projects.c.tax_id]:
        project_ids.append(
            {
                'project_id_type': TAX_ID,
                'project_id': project[projects.c.tax_id],
            }
        )
    internal_project_id = get_internal_project_id(
        db,
        project[projects.c.id],
        project[projects.c.client_company_id]
    )
    if internal_project_id:
        project_ids.append(
            {
                'project_id': internal_project_id['internal_project_id'],
                'project_id_type': PROJECT_ID_TYPE,
            }
        )
    # This should match the OpenAPI schema documented in SWAGGER_PROJECT_SCHEMA, except
    # we never expose the internal ID; instead we rename external_id to id.
    return {
        'id': project[projects.c.id],
        'external_id': project[projects.c.external_id],
        'names': [project[projects.c.name]],
        'project_responsible_org': project[projects.c.client_company_id],
        'project_responsible_person': None,
        'start_date': project[projects.c.start_date],
        'end_date': project[projects.c.end_date],
        'state': project[projects.c.state],
        'project_ids': project_ids,
        'pa_form_enabled': project[projects.c.pa_form_enabled],
        'created_by_org_id': project[projects.c.created_by_org_id],
        'client_contact_person_id': project[projects.c.client_contact_person_id],
        'client_contact_person_email': project[projects.c.client_contact_person_email],
        'added_client_confirmed': project[projects.c.added_client_confirmed],
        'project_creator_role': project[projects.c.project_creator_role],
        'added_client_can_view': project[projects.c.added_client_can_view],
    }


SWAGGER_PROJECT_SCHEMA = yaml.safe_load(f"""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID)
    example: ef05-33cf1e02692fa377302dd109872038ea-c42ddd5c
  client_contact_person_id:
    type: string
    nullable: true
    description: |
      specifies contact project client person resource id
  client_contact_person_email:
    type: string
    nullable: true
    description: |
      specifies contact project client person email
  created_by_org_id:
    type: string
    nullable: true
    description: |
      allows to track if a project has been created by the client or the main contractor
  names:
    type: array
    description: |
      list of project names

      It always contains exactly one item.  It's a list for Qvarn compatibility.
    minItems: 1
    maxItems: 1
    items:
      type: string
      example: Test Project
  project_responsible_org:
    type: string
    description: database ID of the project responsible organization
    example: 2632-cc5b91f132c1321432f0b5d06e7f86f8-8f4ebb6eyy
  project_responsible_person:
    type: string
    nullable: true
    description: always `null`
    example: null
  start_date:
    type: string
    format: date
    nullable: true
    description: project start date in ISO-8601 format (YYYY-MM-DD)
  end_date:
    type: string
    format: date
    nullable: true
    description: project end date in ISO-8601 format (YYYY-MM-DD)
  state:
    $ref: '#/components/schemas/project_state'
  project_ids:
    type: array
    description: business-oriented project identifiers (nothing to do with database IDs)
    items:
      type: object
      properties:
        project_id:
          type: string
          description: project identifier, specific to the logged in user
          example: TP-1001
        project_id_type:
          type: string
          enum:
            - {TAX_ID}
            - {PROJECT_ID_TYPE}
          example: {PROJECT_ID_TYPE}
          description: |
            - {TAX_ID} is used for the single identifier for tax purposes
            - {PROJECT_ID_TYPE} is used for customer internal project
              identifiers and may have different values depending on the logged
              in user
  pa_form_enabled:
    type: boolean
    description: is this a construction project that uses preannouncement forms?
  added_client_confirmed:
    type: boolean
    description: should added client has access to this project before reports are fetched?
  project_creator_role:
    type: string
    enum:
      - {CLIENT_ROLE}
      - {MAIN_CONTRACTOR_ROLE}
    example: {MAIN_CONTRACTOR_ROLE}
    description: indicates who has created the project - Client or Main Contractor
  added_client_can_view:
    type: boolean
    description: should the added client have view permission for all current projects?
""")


SWAGGER_PROJECT_STATE_SCHEMA = yaml.safe_load(f"""
type: string
description: project state
enum: {json.dumps(PROJECT_STATES)}
""")


SWAGGER_PROJECT_SCHEMA_QVARN_COMPAT = schema_variant(SWAGGER_PROJECT_SCHEMA, """
properties:
  country:
    type: string
    nullable: true
    example: null
    description: always `null`, for Qvarn compatibility
  sync:
    type: object
    example: {}
    description: always `{}`, for Qvarn compatibility
  type:
    type: string
    enum:
      - project
    description: always "project", for Qvarn compatibility
  revision:
    type: string
    nullable: true
    example: null
    description: |
      always `null`, for Qvarn compatibility

      This is intended to avoid update conflicts, but that is not currently implemented.
""")


SWAGGER_NEW_PROJECT_SCHEMA = schema_variant(SWAGGER_PROJECT_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_new_project
required:
  - names
  - project_responsible_org
  - state
properties:
  id: {DELETE_MARKER}
  client_contact_person_id:
    type: string
    nullable: true
    description: |
      specifies contact project client person resource id
  client_contact_person_email:
    type: string
    nullable: true
    description: |
      specifies contact project client person email
  created_by_org_id:
    type: string
    nullable: true
    description: |
      allows to track if a project has been created by the client or the main contractor
  names:
    description: |
      list of project names

      Any items beyond the first one will be ignored.
    minItems: 1
    maxItems: {DELETE_MARKER}
  pa_form_enabled:
    type: boolean
    nullable: true
    description: is this a construction project that uses preannouncement forms?
  sync:
    type: object
    nullable: true
    example: null
    description: ignored (for Qvarn compatibility)
  project_responsible_person:
    description: ignored (for Qvarn compatibility)
""")


SWAGGER_UPDATE_PROJECT_SCHEMA = schema_variant(SWAGGER_PROJECT_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_existing_project
# There's actually a subtle difference here where project_ids[].project_id
# either can or cannot be an empty string, depending on the value of
# the sibling project_id_type, but it's a pain to describe in
# OpenAPI schema language, and I don't think it would be helpful to
# human readers.
properties:
  client_contact_person_id:
    type: string
    nullable: true
    description: |
      specifies contact project client person resource id
  client_contact_person_email:
    type: string
    nullable: true
    description: |
      specifies contact project client person email
  created_by_org_id:
    type: string
    nullable: true
    description: |
      allows to track if a project has been created by the client or the main contractor
  names:
    description: |
      list of project names

      Any items beyond the first one will be ignored.
    minItems: 1
    maxItems: {DELETE_MARKER}
  type:
    type: string
    nullable: true
    example: "project"
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  revision:
    type: string
    nullable: true
    example: null
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  country:
    type: string
    nullable: true
    example: null
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  project_responsible_person:
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  sync:
    type: object
    nullable: true
    example: {{}}
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
""")


def create_project(db, payload):
    projects = db.meta.tables[PROJECTS_TABLE]

    query = sa.insert(projects).values(
        id=str(uuid.uuid4()),
        external_id=generate_qvarn_id(PROJECT_RESOURCE_TYPE.replace('qvarn_', '')),
        name=payload.get('name'),
        tax_id=payload.get('tax_id'),
        client_company_id=payload.get('client_company_id'),
        client_contact_person_id=payload.get('client_contact_person_id'),
        client_contact_person_email=payload.get('client_contact_person_email'),
        created_by_org_id=payload.get('created_by_org_id'),
        start_date=payload.get('start_date'),
        end_date=payload.get('end_date'),
        state=payload.get('state'),
        pa_form_enabled=payload.get('pa_form_enabled'),
        added_client_confirmed=payload.get('added_client_confirmed'),
        project_creator_role=payload.get('project_creator_role'),
        added_client_can_view=payload.get('added_client_can_view'),
    )
    rez = db.session.execute(query)
    project_id = rez.inserted_primary_key[0]

    project_ids = payload.get('project_ids')
    company_id = (payload['created_by_org_id']
                  if payload.get('created_by_org_id')
                  else payload['client_company_id'])
    if project_ids:
        create_internal_project_id(
            db, project_id, project_ids[0], company_id
        )
    return get_project(db, project_id=project_id)


def update_project(db, project_id, data):
    projects = db.meta.tables[PROJECTS_TABLE]
    try:
        project_ids = data.pop('project_ids')
        project_ids_present = True
    except KeyError:
        project_ids_present = False

    if data:
        query = (
            sa.update(projects)
            .where(projects.c.id == project_id)
            .values(data)
        )
        db.session.execute(query)

    if project_ids_present:
        qry = (
            sa.select([projects.c.client_company_id])
            .where(projects.c.id == project_id)
        )
        rez = db.session.execute(qry)
        client_company_id = dict(rez.fetchone())['client_company_id']
        internal_project_id = project_ids[0] if project_ids else None

        if internal_project_id:
            db_internal_project_id = get_internal_project_id(db, project_id, client_company_id)
            if not db_internal_project_id:
                create_internal_project_id(db, project_id, internal_project_id, client_company_id)
            else:
                update_internal_project_id(db, internal_project_id, db_internal_project_id['id'])
        else:
            delete_internal_project_id(db, project_id, client_company_id)


def delete_project(db, project_id):
    projects = db.meta.tables[PROJECTS_TABLE]
    delete_internal_project_ids(db, project_id)
    query = (
        sa.delete(projects)
        .where(projects.c.id == project_id)
    )
    db.session.execute(query)


def get_project_ids(project_ids):
    if not project_ids:
        return project_ids
    return [
        project_id['project_id']
        for project_id in project_ids
        if project_id['project_id_type'] != TAX_ID
    ]


def get_tax_id(project_ids):
    if not project_ids:
        return None
    project_ids_tax = [
        project_id['project_id']
        for project_id in project_ids
        if project_id['project_id_type'] == 'tax_id'
    ]
    return project_ids_tax[0] if project_ids_tax else None


def search_projects(db, path):
    # Set up Qvarn DB connection
    dbconn = qvarn.DatabaseConnection()
    dbconn.set_sql(qvarn.PostgresAdapterSimplified(db))

    # Set up ListResource
    resource = qvarn.ListResource()
    resource.set_path(PROJECT_RESOURCE_TYPE)
    resource.set_item_type(PROJECT_RESOURCE_TYPE)
    resource.set_item_prototype(PROJECT_RESOURCE_PROTOTYPE)
    resource.prepare_resource(dbconn)

    result = resource.get_matching_items(path)
    return result


def query_projects(db, query):
    rows = query_table_by_name(db, query, PROJECTS_TABLE)
    return [_project_as_json(db, row) for row in rows]


def is_filter_state_valid(filter_state):
    if filter_state.startswith(BOL_STATUS_NOT):
        filter_state = filter_state[len(BOL_STATUS_NOT):]
    return filter_state in PROJECT_STATES


def is_filter_status_valid(filter_status):
    if filter_status.startswith(BOL_STATUS_NOT):
        filter_status = filter_status[len(BOL_STATUS_NOT):]
    return filter_status in BOL_STATUSES


def get_projects_list(
    db,
    user_active_org_id,
    user_active_org_role,
    user_is_admin=False,
    user_projects_ids=None,
    include_active_org_projects=False,
    filter_search='',
    filter_state='',
    filter_status='',
    limit=None,
    offset=0,
    ff_block_project_client=False,
):
    projects = db.meta.tables[PROJECTS_TABLE]
    table_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]
    visible_projects = (
        _get_visible_projects(db, user_active_org_id,
                              user_active_org_role=user_active_org_role,
                              user_is_admin=user_is_admin,
                              user_projects_ids=user_projects_ids,
                              include_active_org_projects=include_active_org_projects,
                              filter_state=filter_state,
                              ff_block_project_client=ff_block_project_client)
    ).cte('visible_projects')

    # do we have any projects?
    if not hasattr(visible_projects.c, 'project_id'):
        return []

    decorated_projects = (
        _get_decorated_projects(db, visible_projects, user_active_org_id,
                                include_active_org_projects=include_active_org_projects)
    ).cte('decorated_projects')

    search_predicate = True
    if filter_search:
        search_predicate = sa.or_(
            table_internal_project_ids.c.internal_project_id.ilike(
                '%' + filter_search + '%'),
            projects.c.name.ilike('%' + filter_search + '%')
        )

    status_predicate = decorated_projects.c.status.in_(REPORT_STATUSES)
    if filter_status:
        if filter_status.startswith(BOL_STATUS_NOT):
            filter_status = filter_status[len(BOL_STATUS_NOT):]
            status_predicate = (
                decorated_projects.c.status !=
                encode_status_db_repr(filter_status)
            )
        else:
            status_predicate = (
                decorated_projects.c.status ==
                encode_status_db_repr(filter_status)
            )

    qry = (
        sa.select([
            projects.c.external_id,
            projects.c.name,
            projects.c.state,
            table_internal_project_ids.c.internal_project_id,
            decorated_projects.c.supplier_count,
            decorated_projects.c.status,
            projects.c.project_creator_role,
            projects.c.added_client_confirmed,
            projects.c.client_company_id,
            projects.c.added_client_can_view,
        ]).
        select_from(
            decorated_projects.
            join(projects, projects.c.id == decorated_projects.c.project_id).
            outerjoin(table_internal_project_ids, sa.and_(
                projects.c.id == table_internal_project_ids.c.project_id,
                table_internal_project_ids.c.company_id == user_active_org_id
            ))
        ).
        where(search_predicate).
        where(status_predicate).
        order_by(
            projects.c.name,
            table_internal_project_ids.c.internal_project_id,
        ).
        limit(limit).
        offset(offset)
    )
    qry_result = db.session.execute(qry)
    return [
        {
            'id': row[projects.c.external_id],
            'name': row[projects.c.name],
            'state': row[projects.c.state],
            'project_id': row[table_internal_project_ids.c.internal_project_id],
            'supplier_count': row[decorated_projects.c.supplier_count],
            'status': row[decorated_projects.c.status],
            'project_creator_role': row[projects.c.project_creator_role],
            'added_client_confirmed': row[projects.c.added_client_confirmed],
            'project_responsible_org': row[projects.c.client_company_id],
            'added_client_can_view': row[projects.c.added_client_can_view],
        }
        for row in (qry_result if qry_result.returns_rows else [])
    ]


SWAGGER_PROJECT_LIST_SCHEMA = yaml.safe_load("""
type: array
items:
  type: object
  properties:
    id:
      type: string
      example: 0c85-0744763281b7fea637f929ec9a3ce2e6-19e80696
      description: database ID
    name:
      type: string
      example: Test Project
      description: project name
    state:
      $ref: '#/components/schemas/project_state'
    project_id:
      type: string
      description: project identifier, specific to the logged in user
      example: TP-1001
    supplier_count:
      type: integer
      description: number of suppliers participating in the project
      example: 3
    project_responsible_org:
      type: string
      description: database ID of the project responsible organization
      example: 2632-cc5b91f132c1321432f0b5d06e7f86f8-8f4ebb6eyy
    project_creator_role:
      type: string
      enum:
        - {CLIENT_ROLE}
        - {MAIN_CONTRACTOR_ROLE}
      example: {MAIN_CONTRACTOR_ROLE}
      description: indicates who has created the project - Client or Main Contractor
    added_client_confirmed:
      type: boolean
      nullable: true
      description: should added client has access to this project before reports are fetched?
    status:
      type: string
      enum:
        - stop
        - investigate
        - incomplete
        - attention
        - ok
      nullable: false
      description: |
        the worst status of all suppliers in this project

        For projects that have no suppliers at all, the status is "ok".

        For projects that have suppliers, but none of the supplier
        companies have reports, the status is "incomplete".
""")


def get_project_suppliers(db, external_id, user_active_org_id):
    bol_supplier = db.meta.tables[SUPPLIERS_TABLE]
    projects = db.meta.tables[PROJECTS_TABLE]
    preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]
    table_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    latest_report = (
        sa.select([
            tbl_status_reports.c.company_id,
            sa.func.max(tbl_status_reports.c.generated_timestamp).label('ts'),
        ]).
        where(
            tbl_status_reports.c.interested_company_id == user_active_org_id,
        ).
        group_by(tbl_status_reports.c.company_id)
    ).cte('latest_report')

    latest_report_with_status = (
        sa.select([
            latest_report.c.company_id,
            latest_report.c.ts,
            sa.func.min(tbl_status_reports.c.status).label('last_report_status'),
        ]).
        select_from(
            tbl_status_reports.join(latest_report, sa.and_(
                tbl_status_reports.c.company_id == latest_report.c.company_id,
                tbl_status_reports.c.generated_timestamp == latest_report.c.ts
            ))
        ).
        group_by(latest_report.c.company_id, latest_report.c.ts)
    ).cte('latest_report_with_status')

    select_cols = [
        bol_supplier.c.id,
        latest_report_with_status.c.last_report_status,
        latest_report_with_status.c.ts,
        bol_supplier.c.external_id,
        bol_supplier.c.contract_end_date,
        bol_supplier.c.contract_start_date,
        bol_supplier.c.parent_company_id,
        bol_supplier.c.parent_supplier_id,
        bol_supplier.c.materialized_path,
        bol_supplier.c.project_id,
        bol_supplier.c.revision,
        bol_supplier.c.company_id,
        bol_supplier.c.role,
        bol_supplier.c.contract_type,
        bol_supplier.c.contract_work_areas,
        bol_supplier.c.last_visited,
        bol_supplier.c.visitor_type,
        bol_supplier.c.type,
        bol_supplier.c.is_one_man_company,
        bol_supplier.c.has_collective_agreement,
        bol_supplier.c.collective_agreement_name,
        projects.c.external_id,
        table_internal_project_ids.c.internal_project_id,
        preannouncements.c.status,
        preannouncements.c.external_id,
        preannouncements.c.assigned_to_company_id,
    ]
    if feature_active('first_visited'):
        select_cols.append(bol_supplier.c.first_visited)

    qry = (
        sa.select(select_cols).
        select_from(
            bol_supplier.
            join(projects, projects.c.id == bol_supplier.c.project_id).
            outerjoin(
                table_internal_project_ids, sa.and_(
                    projects.c.id == table_internal_project_ids.c.project_id,
                    bol_supplier.c.company_id == table_internal_project_ids.c.company_id,
                )).
            outerjoin(
                latest_report_with_status,
                bol_supplier.c.company_id == latest_report_with_status.c.company_id
            ).outerjoin(preannouncements, preannouncements.c.for_supplier_id == bol_supplier.c.id)
        ).
        where(projects.c.external_id == external_id).
        order_by(latest_report_with_status.c.last_report_status)
    )
    qry_result = db.session.execute(qry)
    resources = []
    supp_ids = []
    for row in qry_result if qry_result.returns_rows else []:
        supp_ids.append(row[bol_supplier.c.id])
        resource = {
            'id': row[bol_supplier.c.external_id],
            'bolagsfakta_status':
                decode_status_db_repr(row[latest_report_with_status.c.last_report_status])
                if row[latest_report_with_status.c.last_report_status]
                else DEFAULT_STATUS,
            'latest_report_timestamp': row[latest_report_with_status.c.ts],
            'contract_type': row[bol_supplier.c.contract_type],
            'contract_end_date': row[bol_supplier.c.contract_end_date],
            'contract_start_date': row[bol_supplier.c.contract_start_date],
            'contract_work_areas': row[bol_supplier.c.contract_work_areas],
            'internal_project_id': row[table_internal_project_ids.c.internal_project_id],
            'materialized_path': row[bol_supplier.c.materialized_path],
            'parent_org_id': row[bol_supplier.c.parent_company_id],
            'parent_supplier_id': row[bol_supplier.c.parent_supplier_id],
            'project_resource_id': row[projects.c.external_id],
            'revision': row[bol_supplier.c.revision],
            'supplier_contacts': [],
            'supplier_org': None,
            'supplier_org_id': row[bol_supplier.c.company_id],
            'supplier_role': row[bol_supplier.c.role],
            'supplier_type': row[bol_supplier.c.type],
            'last_visited': row[bol_supplier.c.last_visited],
            'visitor_type': row[bol_supplier.c.visitor_type],
            'is_one_man_company': row[bol_supplier.c.is_one_man_company],
            'has_collective_agreement': row[bol_supplier.c.has_collective_agreement],
            'collective_agreement_name': row[bol_supplier.c.collective_agreement_name],
            'type': 'bol_supplier',
            'pa_status': row[preannouncements.c.status],
            'pa_id': row[preannouncements.c.external_id],
            'pa_assigned_to_company_id': row[preannouncements.c.assigned_to_company_id]
        }
        if feature_active('first_visited'):
            resource['first_visited'] = row[bol_supplier.c.first_visited]
        else:
            resource['first_visited'] = None

        resources.append(resource)

    supplier_contacts = _get_supplier_contacts_by_suppliers_ids(
        db, supplier_ids=supp_ids
    )

    # Decorate resources with sub-resources
    for r in resources:
        r['supplier_contacts'] = supplier_contacts.get(r['id'], [])

    return resources


SWAGGER_PROJECT_SUPPLIER_LIST_SCHEMA = dict(
    type="array",
    items=schema_variant(SWAGGER_SUPPLIER_SCHEMA, f"""
description: |
  This differs from the supplier schema returned by GET /suppliers/{{external_id}} in the
  following ways:

  - missing field: `project_resource_id`
  - updated field: `bolagsfakta_status` actually contains a meaningful value
  - new field: `latest_report_timestamp`
  - new field: `pa_assigned_to_company_id`
  - new field: `supplier_org`
properties:
  project_resource_id: {DELETE_MARKER}
  bolagsfakta_status:
    type: string
    enum:
      - stop
      - investigate
      - incomplete
      - attention
      - ok
    nullable: false
    example: ok
    description: >
      supplier company status according to the latest status report, if it exists;
      `{DEFAULT_STATUS}` if no status reports exist for this company
  latest_report_timestamp:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    nullable: true
    description: >
      timestamp of the latest report for the supplier company (`null` if no report is available)
  pa_assigned_to_company_id:
    type: string
    nullable: true
    example: 94df-d75564713d954d41fffc648c9006a829-411367c5
    description: >
      database ID (aka external ID) of the company that the preannouncement of this supplier
      is currently assigned to
  supplier_org:
    # I would like to know why we have this field at all
    type: object
    nullable: true
    example: null
    description: always `null`
"""),
)


def _get_supplier_contacts_by_suppliers_ids(db, supplier_ids):

    bol_supplier = db.meta.tables[SUPPLIERS_TABLE]
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]

    qry = (
        sa.select([
            bol_supplier.c.external_id,
            s_contacts.c.person_id,
            s_contacts.c.person_email,
        ]).
        select_from(
            bol_supplier.
            # Connect supplier contracts
            join(
                s_contacts,
                bol_supplier.c.id == s_contacts.c.supplier_id,
            )
        ).
        where(bol_supplier.c.id.in_(supplier_ids)).
        order_by(
            bol_supplier.c.external_id,
        )
    )

    qry_result = db.session.execute(qry)

    result = {}
    for row in qry_result if qry_result.returns_rows else []:

        s_contact = {
            'supplier_contact_person_id':
                row[s_contacts.c.person_id],
            'supplier_contact_email':
                row[s_contacts.c.person_email],
        }

        supplier_id = row[bol_supplier.c.external_id]
        result.setdefault(supplier_id, [])
        result[supplier_id].append(s_contact)
    return result


def _get_visible_projects(db, user_active_org_id, *,
                          user_active_org_role='basic', user_is_admin=False,
                          user_projects_ids=None,  filter_state='',
                          include_active_org_projects=False,
                          include_only_confirmed_added_clients=False,
                          ff_block_project_client=False):

    projects = db.meta.tables[PROJECTS_TABLE]
    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]

    if user_active_org_role == 'basic' and not user_projects_ids and not user_is_admin:
        # return intentionally empty result
        # column name project_id is still needed for some cases
        return (
            sa.select(
                [
                    projects.c.id.label('project_id'),
                    projects.c.pa_form_enabled.label('pa_form_enabled'),
                    sa.literal('').label('user_company_role')
                ],
                distinct=True).
            where(projects.c.id.in_([]))
        )

    state_predicate = True
    if filter_state:
        if filter_state.startswith(BOL_STATUS_NOT):
            filter_state = filter_state[len(BOL_STATUS_NOT):]
            state_predicate = (sa.or_(
                projects.c.state.in_(
                    [x for x in PROJECT_STATES if x != filter_state]),
                projects.c.state.is_(None)
            ))
        else:
            state_predicate = (projects.c.state == filter_state)

    projects_ids_predicate = True
    if user_projects_ids:
        projects_ids_predicate = projects.c.external_id.in_(user_projects_ids)

    if include_only_confirmed_added_clients:
        added_client_confirmed_predicate = sa.not_(sa.and_(
            projects.c.added_client_confirmed == False,
            projects.c.created_by_org_id.isnot(None),
            projects.c.created_by_org_id != projects.c.client_company_id,
            projects.c.client_company_id == user_active_org_id,
        ))
    else:
        added_client_confirmed_predicate = True

    if ff_block_project_client:
        # NB: some of these columns can be NULL, which can get confusing.  A reminder
        # that an SQL NULL approximately means "don't know", which means NULL = FALSE
        # evaluates to NULL, NULL AND NULL evaluates to NULL, NOT NULL evaluates to NULL,
        # and WHERE NULL filters out the row.  Use COALESCE() to convert NULL
        # to something that is != FALSE here so that rows with null added_client_can_view
        # will not be excluded.  FALSE AND NULL is, luckily, FALSE, so we don't
        # need COALESCE() for the other two columns.
        added_client_can_view_predicate = sa.not_(sa.and_(
            sa.func.coalesce(projects.c.added_client_can_view, True) == False,
            projects.c.project_creator_role == MAIN_CONTRACTOR_ROLE,
            projects.c.client_company_id == user_active_org_id
        ))
    else:
        added_client_can_view_predicate = True

    # User company roles
    #
    # Company of the user can have different roles in different projects and
    # different roles in the same project. Roles are:
    # * Admin
    # * Client
    # * Main contractor or a Supervisor - top-level supplier
    # * Ordinary supplier - active_org supplier
    #
    # We decorate visible_projects with each project's user company role. This
    # comes handy in other queries, e.g., to determine visible
    # suppliers/companies.

    # Admin user
    #
    # Admin user gets a special treatment - show all relevant projects
    if user_is_admin:
        # All projects that have suppliers
        has_suppliers_projects = (
            sa.select(
                [
                    bol_suppliers.c.project_id.label('project_id'),
                    projects.c.pa_form_enabled.label('pa_form_enabled'),
                    sa.literal(ADMIN_ROLE).label('user_company_role')
                ],
                distinct=True).
            select_from(
                bol_suppliers.join(
                    projects, projects.c.id == bol_suppliers.c.project_id)
            ).
            where(state_predicate)
        )
        # All projects that have a responsible_org (should capture empty
        # projects)
        responsible_org_projects = (
            sa.select([projects.c.id.label('project_id'),
                       projects.c.pa_form_enabled.label('pa_form_enabled'),
                       sa.literal(ADMIN_ROLE).label('user_company_role')]).
            where(projects.c.client_company_id.isnot(None)).
            where(state_predicate)
        )

        return sa.union(has_suppliers_projects, responsible_org_projects)

    visible_projects = []

    # Non-admins
    #
    # A non-admin company can be:
    # * a Client,
    # * a Main contractor or a Supervisor - top-level supplier
    # * an ordinary supplier - activ
    #
    # We need to support these types of projects simultaneously. These sets of
    # projects can overlap. We prevent overlap and include a project in the
    # 'most privileged' (where user can see more suppliers) set of projects,
    # e.g., if a company is a client and a supplier in a project the project
    # will be included in client_projects as these are 'more_privileged'
    # projects.

    # Client
    client_projects = (
        sa.select([projects.c.id.label('project_id'),
                   projects.c.pa_form_enabled.label('pa_form_enabled'),
                   sa.literal(CLIENT_ROLE).label('user_company_role')]).
        where(projects.c.client_company_id == user_active_org_id)
    )
    visible_projects.append(client_projects)

    # Supervisor or main contractor
    supervisor_or_main_contractor_projects = (
        sa.select([bol_suppliers.c.project_id.label('project_id'),
                   projects.c.pa_form_enabled.label('pa_form_enabled'),
                   sa.literal(TOP_LEVEL_SUPPLIER_ROLE).label('user_company_role')]).
        where(bol_suppliers.c.company_id == user_active_org_id).
        where(bol_suppliers.c.role.in_(TOP_LEVEL_SUPPLIER_ROLES)).
        where(
            bol_suppliers.c.project_id.notin_(
                # subquery inside notin_() needs an alias
                sa.select([client_projects.alias('C_projects').c.project_id])
            )
        )
    )
    visible_projects.append(supervisor_or_main_contractor_projects)

    # Supplier projects - where project tree contains a node with active_org_id
    # (only for PA projects)

    if include_active_org_projects:
        active_org_projects = (
            sa.select([bol_suppliers.c.project_id.label('project_id'),
                       projects.c.pa_form_enabled.label('pa_form_enabled'),
                       sa.literal(SUPPLIER_ROLE).label('user_company_role')],
                      distinct=True).
            select_from(bol_suppliers.join(projects,
                                           projects.c.id == bol_suppliers.c.project_id)).
            where(projects.c.pa_form_enabled == True).
            where(bol_suppliers.c.company_id == user_active_org_id).
            where(bol_suppliers.c.type == LINKED).
            where(
                bol_suppliers.c.project_id.notin_(
                    sa.select([
                        # subquery inside notin_() needs an alias
                        supervisor_or_main_contractor_projects.alias('SOMC_projects').c.project_id
                    ])
                )
            )
        )
        visible_projects.append(active_org_projects)

    # We are interested in unique projects.
    visible_projects = sa.union(*visible_projects).alias('visible_projects')

    visible_projects = (
        sa.select([visible_projects.c.project_id,
                   visible_projects.c.user_company_role,
                   visible_projects.c.pa_form_enabled]).
        select_from(
            visible_projects.join(
                projects, projects.c.id == visible_projects.c.project_id)
        ).
        where(state_predicate).
        where(projects_ids_predicate).
        where(added_client_confirmed_predicate).
        where(added_client_can_view_predicate)
    )
    return visible_projects


def _get_decorated_projects(db, visible_projects, user_active_org_id,
                            include_active_org_projects=False):
    """Get supplier count"""

    bol_suppliers = db.meta.tables[SUPPLIERS_TABLE]
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]
    latest_report = (
        sa.select([
            tbl_status_reports.c.company_id,
            sa.func.max(tbl_status_reports.c.generated_timestamp).label('ts'),
        ]).
        where(
            tbl_status_reports.c.interested_company_id == user_active_org_id,
        ).
        group_by(tbl_status_reports.c.company_id)
    ).cte('latest_report')

    # we need status of a last report
    # not the worst status of all company reports
    latest_report_with_status = (
        sa.select([
            latest_report.c.company_id,
            latest_report.c.ts,
            # take worst status of same company reports with same ts
            sa.func.min(tbl_status_reports.c.status).label('last_report_status'),
        ]).
        select_from(
            tbl_status_reports.join(latest_report, sa.and_(
                tbl_status_reports.c.company_id == latest_report.c.company_id,
                tbl_status_reports.c.generated_timestamp == latest_report.c.ts
            ))
        ).
        group_by(latest_report.c.company_id, latest_report.c.ts)
    ).cte('latest_report_with_status')

    visible_suppliers_for_supplier = sa.select([
        bol_suppliers.c.company_id,
        visible_projects.c.project_id,
    ]).select_from(
        visible_projects
        .outerjoin(bol_suppliers,
                   visible_projects.c.project_id == bol_suppliers.c.project_id
                   )).where(sa.and_(
                       visible_projects.c.pa_form_enabled == True,
                       visible_projects.c.user_company_role.in_(
                           [SUPPLIER_ROLE]),
                       bol_suppliers.c.type.in_(
                           [LINKED, UNLINKED]),
                       bol_suppliers.c.role.in_(
                           [SUPPLIER_ROLE]),
                       bol_suppliers.c.materialized_path.contains([user_active_org_id]))

    ).alias("visible_suppliers_for_supplier")

    visible_suppliers_for_privileged = sa.select([
        bol_suppliers.c.company_id,
        visible_projects.c.project_id,
    ]).select_from(
        visible_projects
        .outerjoin(bol_suppliers,
                   visible_projects.c.project_id == bol_suppliers.c.project_id
                   )).where(sa.and_(
                       visible_projects.c.user_company_role.in_(
                           [ADMIN_ROLE, TOP_LEVEL_SUPPLIER_ROLE, CLIENT_ROLE]),
                       bol_suppliers.c.type.in_([LINKED, UNLINKED])
                   )
    ).alias("visible_suppliers_for_privileged")

    visible_suppliers = (
        sa.union(
            visible_suppliers_for_privileged.select(),
            visible_suppliers_for_supplier.select()
        ).cte('visible_suppliers')
    )

    # Supplier counts:
    # * Client and top-level projects should count all suppliers.
    # * Supplier-only projects should count only suppliers that have
    #   user_active_org in their materialized path.

    # Add supplier counts to top-level projects
    supplier_counts_for_top_level = (
        sa.select([
            visible_projects.c.project_id,
            sa.func.count(
                sa.distinct(bol_suppliers.c.company_id)
            ).label('supplier_count'),
        ]).
        select_from(
            visible_projects.
            outerjoin(bol_suppliers, sa.and_(
                bol_suppliers.c.project_id == visible_projects.c.project_id,
                bol_suppliers.c.type.in_([LINKED, UNLINKED]),
            ))
        ).
        where(visible_projects.c.user_company_role.in_([CLIENT_ROLE,
                                                        TOP_LEVEL_SUPPLIER_ROLE,
                                                        ADMIN_ROLE])).
        group_by(visible_projects.c.project_id)
    ).cte('supplier_counts_for_top_level')

    # Add supplier counts to supplier projects
    if include_active_org_projects:
        supplier_counts_for_suppliers = (
            sa.select([
                visible_projects.c.project_id,
                sa.func.count(
                    sa.distinct(bol_suppliers.c.company_id)
                ).label('supplier_count'),
            ]).
            select_from(
                visible_projects.
                outerjoin(bol_suppliers, sa.and_(
                    bol_suppliers.c.project_id == visible_projects.c.project_id,
                    bol_suppliers.c.type.in_([LINKED, UNLINKED]),
                    bol_suppliers.c.materialized_path.contains([user_active_org_id])
                ))
            ).
            where(visible_projects.c.user_company_role.in_([SUPPLIER_ROLE])).
            group_by(visible_projects.c.project_id)
        ).cte('supplier_counts_for_suppliers')

        supplier_counts = (
            sa.union(
                supplier_counts_for_top_level.select(),
                supplier_counts_for_suppliers.select()
            ).cte('supplier_counts')
        )
    else:
        supplier_counts = supplier_counts_for_top_level.select().cte('supplier_counts')

    decorated = (
        sa.select([
            visible_projects.c.project_id.label('project_id'),
            supplier_counts.c.supplier_count,
            sa.func.min(latest_report_with_status.c.last_report_status)
            .label('last_report_status'),
        ])
        .select_from(
            # Supplier projects
            visible_projects.
            # Use LEFT OUTER JOIN to include all projects but filter out
            # suppliers of non-visible projects to given user.
            outerjoin(supplier_counts, (
                supplier_counts.c.project_id ==
                visible_projects.c.project_id
            )).
            outerjoin(visible_suppliers, (
                visible_suppliers.c.project_id ==
                visible_projects.c.project_id
            )).
            outerjoin(latest_report_with_status, sa.and_(
                visible_suppliers.c.company_id == latest_report_with_status.c.company_id
            ))
        )
        .group_by(visible_projects.c.project_id, supplier_counts.c.supplier_count)
    ).alias('decorated')

    return (
        sa.select([
            decorated.c.project_id,
            decorated.c.supplier_count,
            decorated.c.last_report_status,
            sa.case(
                [
                    # Non-empty projects without reports should be INCOMPLETE
                    (sa.and_(
                        decorated.c.last_report_status.is_(None),
                        decorated.c.supplier_count > 0),
                     STATUS_INCOMPLETE),
                    # Empty projects should be OK
                    (sa.and_(
                        decorated.c.last_report_status.is_(None),
                        decorated.c.supplier_count == 0),
                     STATUS_OK),
                ],
                else_=decorated.c.last_report_status
            ).label('status')
        ]).
        select_from(
            decorated,
        )
    )


def encode_status_db_repr(status):
    num = (BOL_STATUSES.index(status.lower()) + 1) * 100
    return '%03d %s' % (num, status.upper())


def decode_status_db_repr(status):
    status = status.split()[-1].lower()
    assert status in BOL_STATUSES
    return status
