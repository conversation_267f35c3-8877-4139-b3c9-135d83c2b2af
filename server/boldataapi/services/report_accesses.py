import uuid

import sqlalchemy as sa

from boldataapi import exceptions
from boldataapi.storage import qvarn
from boldataapi.storage.db import REPORT_ACCESSES_TABLE
from boldataapi.storage.qvarn.resources import get_resource
from boldataapi.storage.qvarn_helpers import generate_qvarn_id
from boldataapi.swagger import (
    DELETE_MARKER,
    qvarn_compat_schema_for_reading,
    qvarn_compat_schema_for_writing,
    schema_variant,
)


REPORT_ACCESS_RESOURCE_NAME = 'report_accesses'
REPORT_ACCESS_RESOURCE = get_resource(REPORT_ACCESS_RESOURCE_NAME)
REPORT_ACCESS_RESOURCE_TYPE = REPORT_ACCESS_RESOURCE.type
REPORT_ACCESS_RESOURCE_PROTOTYPE = REPORT_ACCESS_RESOURCE.schema

HIDDEN_STATUS = 'hidden'


def get_report_access(db, *, external_id=None, report_access_id=None):
    tbl_report_accesses = db.meta.tables[REPORT_ACCESSES_TABLE]

    params = [external_id, report_access_id]

    if not any(params) or all(params):
        raise exceptions.InternalServerError(
            msg="One of parameters must be provided: external_id or report_access_id"
        )

    query = (
        sa.select([tbl_report_accesses])
        .where(tbl_report_accesses.c.external_id == external_id)
    )
    if report_access_id:
        query = (
            sa.select([tbl_report_accesses])
            .where(tbl_report_accesses.c.id == report_access_id)
        )

    rez = db.session.execute(query)
    report_access = rez.fetchone()
    gov_org_ids = []
    if report_access and report_access[tbl_report_accesses.c.company_gov_id]:
        gov_org_ids.append(
            {
                'country': report_access[tbl_report_accesses.c.company_gov_id_country],
                'org_id_type': report_access[tbl_report_accesses.c.company_gov_id_type],
                'gov_org_id': report_access[tbl_report_accesses.c.company_gov_id],
            }
        )
    if not report_access:
        return None
    retrieved_report_access = {
        'id': report_access[tbl_report_accesses.c.id],
        'external_id': report_access[tbl_report_accesses.c.external_id],
        'access_time': report_access[tbl_report_accesses.c.access_time],
        'arkisto_id': report_access[tbl_report_accesses.c.arkisto_id],
        'report_id': report_access[tbl_report_accesses.c.report_id],
        'status': report_access[tbl_report_accesses.c.status],
        'language': report_access[tbl_report_accesses.c.language],
        'template_version': report_access[tbl_report_accesses.c.template_version],
        'customer_org_id': report_access[tbl_report_accesses.c.customer_company_id],
        'org_id': report_access[tbl_report_accesses.c.company_id],
        'person_id': report_access[tbl_report_accesses.c.person_id],
        'gov_org_ids': gov_org_ids,
        'client_id': None,  # Qvarn back compat
        'type': 'report_access',  # Qvarn back compat
        'revision': None,   # Qvarn compat
    }
    return retrieved_report_access


SWAGGER_REPORT_ACCESS_SCHEMA = schema_variant("""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID)
    example: 095b-8bacfe95354e84a42355760e46487769-55c8fd8a
  access_time:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: ISO 8601 timestamp (in UTC) of when the report was accessed
  arkisto_id:
    type: string
    example: EE112267651642411977827
    description: archival ID in the arkisto service
  report_id:
    type: string
    nullable: true
    example: null
    description: unused
  status:
    type: string
    enum:
      - active
      - hidden
    description: is this report access visible (active) or deleted (hidden)?
  language:
    type: string
    example: FI
    description: language of the report (2-letter language code)
  template_version:
    type: string
    example: "1.0"
    nullable: true
    description: >
      report template version that was used to generate the web reports
      (`null` for PDF reports)
  customer_org_id:
    type: string
    description: database ID of the company of the user who viewed the report
    example: 8ed8-a81c6bfba328427dc28b9d20aaf90f70-d9df1fc4
  org_id:
    type: string
    description: database ID of the company that the report was about
    example: 4833-ec57387bc57983cad1c638458db5cc6b-4094bfe1
    nullable: true
  person_id:
    type: string
    description: database ID of the user who viewed the report
    example: 4833-ec57387bc57983cad1c638458db5cc6b-4094bfe1
    nullable: true
  gov_org_ids:
    type: array
    minItems: 0
    maxItems: 1
    description: registration number of the company that the report was about
    items:
      type: object
      properties:
        country:
          type: string
          enum:
            - FI
            - EE
          example: FI
          description: 2-letter country code
        org_id_type:
          type: string
          enum:
            - registration_number
          description: type of ID
        gov_org_id:
          type: string
          example: 1234567-8
          description: |
            company registration number (when `org_id_type` is "registration_number")
""", qvarn_compat_schema_for_reading(dict(
    client_id=None,
    type="report_access",
    revision=None,
)))


SWAGGER_NEW_REPORT_ACCESS_SCHEMA = schema_variant(SWAGGER_REPORT_ACCESS_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_new_report_access
required:
  - customer_org_id
  - status
  - access_time
  - arkisto_id
  - gov_org_ids
properties:
  id: {DELETE_MARKER}
  type: {DELETE_MARKER}
  revision: {DELETE_MARKER}
  gov_org_ids:
    minItems: 1
    items:
      required:
        - gov_org_id
        - org_id_type
        - country
""", qvarn_compat_schema_for_writing(dict(
    client_id=None,
    report_id=None,
)))


SWAGGER_UPDATE_REPORT_ACCESS_SCHEMA = schema_variant(SWAGGER_REPORT_ACCESS_SCHEMA, """
x-bolfak-schema: boldataapi.schema.schema_existing_report_access
properties:
  id:
    description: >
      ignored, it's here so that you could round-trip and PUT the same data you received from a GET
  gov_org_ids:
    items:
      required:
        - gov_org_id
        - org_id_type
        - country
""", qvarn_compat_schema_for_writing(dict(
    client_id=None,
    report_id=None,
    revision=None,
    type=None,
)), {'properties': {'type': {'enum': DELETE_MARKER}}})


def create_report_access(db, data):
    tbl_report_accesses = db.meta.tables[REPORT_ACCESSES_TABLE]

    query = sa.insert(tbl_report_accesses).values(
        id=str(uuid.uuid4()),
        external_id=generate_qvarn_id(REPORT_ACCESS_RESOURCE_TYPE.replace('qvarn_', '')),
        customer_company_id=data.get('customer_company_id'),
        company_id=data.get('company_id'),
        person_id=data.get('person_id'),
        status=data.get('status'),
        language=data.get('language'),
        template_version=data.get('template_version'),
        access_time=data.get('access_time'),
        arkisto_id=data.get('arkisto_id'),
        report_id=data.get('report_id'),
        company_gov_id=data.get('company_gov_id'),
        company_gov_id_country=data.get('company_gov_id_country'),
        company_gov_id_type=data.get('company_gov_id_type'),
    )
    result = db.session.execute(query)
    return get_report_access(db, report_access_id=result.inserted_primary_key[0])


def search_report_accesses(db, path):
    # Set up Qvarn DB connection
    dbconn = qvarn.DatabaseConnection()
    dbconn.set_sql(qvarn.PostgresAdapterSimplified(db))

    # Set up ListResource
    resource = qvarn.ListResource()
    resource.set_path(REPORT_ACCESS_RESOURCE_TYPE)
    resource.set_item_type(REPORT_ACCESS_RESOURCE_TYPE)
    resource.set_item_prototype(REPORT_ACCESS_RESOURCE_PROTOTYPE)
    resource.prepare_resource(dbconn)

    result = resource.get_matching_items(path)
    return result


def update_report_access(db, report_access_id, data):
    tbl_report_accesses = db.meta.tables[REPORT_ACCESSES_TABLE]

    query = (
        sa.update(tbl_report_accesses)
        .where(tbl_report_accesses.c.id == report_access_id)
        .values(data)
    )
    db.session.execute(query)
    return get_report_access(db, report_access_id=report_access_id)


def delete_report_access(db, report_access_id):
    tbl_report_accesses = db.meta.tables[REPORT_ACCESSES_TABLE]

    qry = (
        sa.delete(tbl_report_accesses)
        .where(tbl_report_accesses.c.id == report_access_id)
    )
    db.session.execute(qry)


def get_report_accesses_list(db, user_active_org_id, limit=None, offset=0):
    "Deprecated: Qvarn-identical implementation"

    tbl_report_accesses = db.meta.tables[REPORT_ACCESSES_TABLE]

    qry = (
        sa.select([
            tbl_report_accesses.c.external_id,
            tbl_report_accesses.c.company_gov_id_country,
            tbl_report_accesses.c.company_gov_id_type,
            tbl_report_accesses.c.company_gov_id,

        ]).
        select_from(tbl_report_accesses).
        where(
            sa.and_(
                tbl_report_accesses.c.customer_company_id == user_active_org_id,
                tbl_report_accesses.c.status != HIDDEN_STATUS,
            )
        ).
        order_by(tbl_report_accesses.c.company_gov_id).
        limit(limit).
        offset(offset)
    )

    qry_result = db.session.execute(qry)

    return [
        {
            'id': row[tbl_report_accesses.c.external_id],
            'gov_org_ids': [
                {
                    'country': row[tbl_report_accesses.c.company_gov_id_country],
                    'org_id_type': row[tbl_report_accesses.c.company_gov_id_type],
                    'gov_org_id': row[tbl_report_accesses.c.company_gov_id],
                },
            ]
        }
        for row in (qry_result if qry_result.returns_rows else [])
    ]
