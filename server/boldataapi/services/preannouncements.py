import json
import uuid

import sqlalchemy as sa
import yaml
from sqlalchemy.orm import aliased

from boldataapi import exceptions
from boldataapi.schema import (
    PA_STATUSES,
    SUPPLIER_CONTRACT_TYPES,
    SUPPLIER_CONTRACT_WORK_AREAS,
)
from boldataapi.services.shared import (
    PREANNOUNCEMENTS_DECORATED_TABLE,
    query_table,
)
from boldataapi.storage.db import (
    PREANNOUNCEMENT_FORMS_TABLE,
    PREANNOUNCEMENTS_TABLE,
    PROJECTS_TABLE,
    SUPPLIERS_TABLE,
)
from boldataapi.swagger import DELETE_MARKER, schema_variant


def get_preannouncement_form_id(db, external_id):
    if external_id is None:
        return None

    pa_form = db.meta.tables[PREANNOUNCEMENT_FORMS_TABLE]

    qry = (
        sa.select([pa_form.c.id])
        .where(pa_form.c.external_id == external_id)
    )
    rez = db.session.execute(qry)
    ids = rez.fetchone()
    return ids[0] if ids else None


def get_preannouncement(db, *, external_id=None, preannouncement_id=None):
    preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]
    projects = db.meta.tables[PROJECTS_TABLE]
    params = [external_id, preannouncement_id]
    suppliers = db.meta.tables[SUPPLIERS_TABLE]
    pa_forms = db.meta.tables[PREANNOUNCEMENT_FORMS_TABLE]
    if not any(params) or all(params):
        raise exceptions.InternalServerError(
            msg="One of parameters must be provided: external_id or preannouncement_id"
        )

    search_predicate = (preannouncements.c.external_id == external_id)
    if preannouncement_id:
        search_predicate = (preannouncements.c.id == preannouncement_id)

    supp_by = aliased(suppliers)
    supp_for = aliased(suppliers)
    supp_assigned_to = aliased(suppliers)
    active_pa_form = aliased(pa_forms)
    qry = (
        sa.select([
            preannouncements,
            projects.c.external_id,
            supp_by.c.external_id,
            supp_for.c.external_id,
            supp_assigned_to.c.external_id,
            active_pa_form.c.external_id,
            supp_for.c.created_on
        ])
        .select_from(
            preannouncements
            .outerjoin(projects, preannouncements.c.project_id == projects.c.id)
            .outerjoin(supp_by, preannouncements.c.created_by_supplier_id == supp_by.c.id)
            .outerjoin(supp_for, preannouncements.c.for_supplier_id == supp_for.c.id)
            .outerjoin(active_pa_form, preannouncements.c.active_pa_form == active_pa_form.c.id)
            .outerjoin(supp_assigned_to,
                       preannouncements.c.assigned_to_supplier_id == supp_assigned_to.c.id)
        )
        .where(search_predicate)
    )
    rez = db.session.execute(qry)
    pa = rez.fetchone()

    if not pa:
        return None

    preannouncement = {
        'id': pa[preannouncements.c.id],
        'status': pa[preannouncements.c.status],
        'external_id': pa[preannouncements.c.external_id],
        'created_by_supplier_id': pa[supp_by.c.external_id],
        'for_supplier_id': pa[supp_for.c.external_id],
        'project_id': pa[projects.c.external_id],
        'assigned_to_company_id': pa[preannouncements.c.assigned_to_company_id],
        'assigned_to_supplier_id': pa[supp_assigned_to.c.external_id],
        'assigned_to_time': pa[preannouncements.c.assigned_to_time],
        'active_pa_form': pa[active_pa_form.c.external_id],
        'supplier_created_on': pa[supp_for.c.created_on]
    }
    return preannouncement


SWAGGER_PREANNOUNCEMENT_SCHEMA = yaml.safe_load(f"""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID) of the preannouncement
    example: 31058669-f722-469f-aa57-44b2c6c4848a
  status:
    type: string
    enum: {json.dumps(PA_STATUSES)}
    description: preannouncement status
  created_by_supplier_id:
    type: string
    description: >
      database ID (aka external ID) of the supplier that created this preannouncement
      (also known as buyer)
    example: e9a9-016b93218a8096924a414af9d945a962-99b2a553
  for_supplier_id:
    type: string
    description: >
      database ID (aka external ID) of the supplier that this preannouncement is about
      (also known as supplier)
    example: e2ab-3d3b99b56199b277baa41d00bb1efee2-a09fbd16
  project_id:
    type: string
    description: >
      database ID (aka external ID) of the project; should match the
      project_resource_id field in both suppliers referenced here
    example: e774-8164ce458050a2e47e7789cd5b4c6ede-0dded837
  assigned_to_company_id:
    type: string
    nullable: true
    description: >
      database ID of the supplier company that should take the next review
      step; matches the `supplier_org_id` field in the supplier referenced to
      by `assigned_to_supplier_id`
    example: 7e70-787cae9c409eeb877552ceb156bad4e4-7ec984df
  assigned_to_supplier_id:
    type: string
    nullable: true
    example: e9a9-016b93218a8096924a414af9d945a962-99b2a553
    description: database ID of the supplier that is supposed to take the next action
      in the preannouncement workflow
  assigned_to_time:
    type: string
    nullable: true
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: UTC timestamp (ISO-8601) when the `assigned_to_supplier_id` was last changed
  active_pa_form:
    type: string
    nullable: true
    description: database ID (aka external ID) of the active preannouncement form
    example: 8187de38-e064-4261-83c6-ed95e0ef2000
""")


SWAGGER_NEW_PREANNOUNCEMENT_SCHEMA = schema_variant(SWAGGER_PREANNOUNCEMENT_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_new_preannouncement
required:
  - status
  - project_id
  - for_supplier_id
  # Yes, created_by_supplier_id is not required.  I'm surprised too.
properties:
  id: {DELETE_MARKER}
  assigned_to_company_id:
    nullable: false
  assigned_to_supplier_id:
    nullable: false
  assigned_to_time:
    nullable: false
  active_pa_form:
    nullable: false
""")

SWAGGER_UPDATE_PREANNOUNCEMENT_SCHEMA = schema_variant(SWAGGER_PREANNOUNCEMENT_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_update_preannouncement
# No fields are required.
properties:
  id: {DELETE_MARKER}
""")


def create_preannouncement(db, payload):
    preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]

    query = sa.insert(preannouncements).values(
        id=str(uuid.uuid4()),
        external_id=str(uuid.uuid4()),
        status=payload.get('status'),
        created_by_supplier_id=payload.get('created_by_supplier_id'),
        for_supplier_id=payload.get('for_supplier_id'),
        project_id=payload.get('project_id'),
        assigned_to_company_id=payload.get('assigned_to_company_id'),
        assigned_to_supplier_id=payload.get('assigned_to_supplier_id'),
        assigned_to_time=payload.get('assigned_to_time'),
    )

    rez = db.session.execute(query)
    preannouncement_id = rez.inserted_primary_key[0]

    return get_preannouncement(db, preannouncement_id=preannouncement_id)


def update_preannouncement(db, external_id, data):
    preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]

    if data:
        query = (
            sa.update(preannouncements)
            .where(preannouncements.c.external_id == external_id)
            .values(data)
        )
        db.session.execute(query)

    return


def delete_preannouncement(db, preannouncement_id):
    preannouncement = db.meta.tables[PREANNOUNCEMENTS_TABLE]
    query = (
        sa.delete(preannouncement)
        .where(preannouncement.c.external_id == preannouncement_id)
    )
    db.session.execute(query)


def get_preannouncement_form(db, *, pa=None, pa_form_id=None):
    SKIP_SERVICE_FIELDS = ['created_on', 'last_changed']

    pa_form_table = db.meta.tables[PREANNOUNCEMENT_FORMS_TABLE]
    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    if pa_form_id is not None:
        search_predicate = (pa_form_table.c.id == pa_form_id)
    elif pa is not None:
        # Should we search for pa_form_table.c.pa_id == pa['id'] as a fallback
        # when pa['active_pa_form'] is None?  I don't think so.
        external_id = pa['active_pa_form']
        if external_id is None:
            return {}
        search_predicate = (pa_form_table.c.external_id == external_id)
    else:
        return {}

    supp_last_assigned_to = aliased(suppliers)
    qry = (
        sa.select([
            pa_form_table,
            supp_last_assigned_to.c.external_id.label('last_assigned_to_supplier_external_id'),
        ])
        .select_from(
            pa_form_table
            .outerjoin(supp_last_assigned_to,
                       pa_form_table.c.last_assigned_to_supplier == supp_last_assigned_to.c.id)
        )
        .where(search_predicate)
    )
    rez = db.session.execute(qry)
    pa_form = rez.fetchone()

    if not pa_form:
        return {}

    pa_form = {k: v for k, v in pa_form.items() if k not in SKIP_SERVICE_FIELDS}
    pa_form['last_assigned_to_supplier'] = pa_form.pop('last_assigned_to_supplier_external_id')
    return pa_form


def get_preannouncement_form_data(db, *, pa=None, pa_form_id=None):
    SKIP_PA_FORM_ID_FIELDS = ['id', 'external_id', 'pa_id']

    pa_form = get_preannouncement_form(db, pa=pa, pa_form_id=pa_form_id)

    if not pa_form:
        return pa_form

    for field in SKIP_PA_FORM_ID_FIELDS:
        pa_form.pop(field)

    return pa_form


SWAGGER_PREANNOUNCEMENT_SCHEMA_WITH_FORM_DATA = schema_variant(SWAGGER_PREANNOUNCEMENT_SCHEMA, f"""
properties:
  company_name:
    type: string
    nullable: true
    example: Test Company
    description: name of the supplier company
  company_gov_org_id:
    type: string
    example: "36-9963455"
    description: >
      company registration number or VAT number, depending on the value of `company_id_type`
  company_id_type:
    type: string
    nullable: true
    example: registration_number
    description: >
      company registration number; usually "registration_number" or "vat_number",
      but the set of values is not restricted by BolDataAPI
  company_country:
    type: string
    example: SWE
    description: >
      country of registration (3-letter country code)
  has_permanent_establishment:
    type: boolean
    nullable: true
    description: >
      is there a basis for the company to have a permanent place of business?
  is_one_man_company:
    type: boolean
    nullable: true
    description: >
      is the company a one-man business?
      (A one-man business is a company with one employee or a sole trader without employees.)
  has_collective_agreement:
    type: boolean
    nullable: true
    description: >
      is the company bound by a collective agreement or a suspension agreement?
  collective_agreement_name:
    type: string
    nullable: true
    description: name of the collective agreement
  buyer_name:
    type: string
    nullable: true
    description: name of the buyer company
  buyer_gov_org_id:
    type: string
    example: "36-9963455"
    description: >
      company registration number or VAT number, depending on the value of `company_id_type`
  buyer_id_type:
    type: string
    nullable: true
    example: registration_number
    description: >
      company registration number; usually "registration_number" or "vat_number",
      but the set of values is not restricted by BolDataAPI
  buyer_country:
    type: string
    example: SWE
    description: >
      country of registration (3-letter country code)
  foreman_is_on_site:
    type: boolean
    nullable: true
    description: will there be a foreman on site?
  foreman_first_name:
    type: string
    nullable: true
    description: foreman's first name
  foreman_last_name:
    type: string
    nullable: true
    description: foreman's last name
  foreman_phone_number:
    type: string
    nullable: true
    example: "+46 ***********"
    description: foreman's phone number
  foreman_email:
    type: string
    nullable: true
    example: <EMAIL>
    description: foreman's email address
  contract_type:
    type: string
    nullable: true
    enum: {json.dumps(SUPPLIER_CONTRACT_TYPES)}
    description: type of work for the contract
  contract_start_date:
    type: string
    format: date
    nullable: true
    description: contract start date (YYYY-MM-DD)
  contract_end_date:
    type: string
    format: date
    nullable: true
    description: contract end date (YYYY-MM-DD)
  contract_work_areas:
    type: array
    nullable: true
    description: list of work areas according to the contract
    items:
      type: string
      enum: {json.dumps(SUPPLIER_CONTRACT_WORK_AREAS)}
  confirmed_name:
    type: string
    nullable: true
    description: name of the company that confirmed the preannouncement
  confirmed_gov_org_id:
    type: string
    nullable: true
    example: "36-9963455"
    description: >
      company registration number or VAT number, depending on the value of `company_id_type`
  confirmed_id_type:
    type: string
    nullable: true
    example: registration_number
    description: >
      company registration number; usually "registration_number" or "vat_number",
      but the set of values is not restricted by BolDataAPI
  confirmed_time:
    type: string
    nullable: true
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: >
      UTC timestamp (ISO-8601) when the preannouncement was confirmed
  confirmed_country:
    type: string
    nullable: true
    example: SWE
    description: >
      country of registration (3-letter country code)
  rejected_name:
    type: string
    nullable: true
    description: name of the company that rejected the preannouncement
  rejected_gov_org_id:
    type: string
    nullable: true
    example: "36-9963455"
    description: >
      company registration number or VAT number, depending on the value of `company_id_type`
  rejected_id_type:
    type: string
    nullable: true
    example: registration_number
    description: >
      company registration number; usually "registration_number" or "vat_number",
      but the set of values is not restricted by BolDataAPI
  rejected_time:
    type: string
    nullable: true
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: >
      UTC timestamp (ISO-8601) when the preannouncement was rejected
  rejected_country:
    type: string
    nullable: true
    example: SWE
    description: >
      country of registration (3-letter country code)
  informant_supplier_first_name:
    type: string
    nullable: true
    description: first name of the informant supplier
  informant_supplier_last_name:
    type: string
    nullable: true
    description: last name of the informant supplier
  informant_supplier_phone:
    type: string
    nullable: true
    description: phone number of the informant supplier
  informant_supplier_email:
    type: string
    nullable: true
    example: <EMAIL>
    description: email address of the informant supplier
  submitted_by_first_name:
    type: string
    nullable: true
    description: first name of the informant customer
  submitted_by_last_name:
    type: string
    nullable: true
    description: last name of the informant customer
  submitted_by_phone:
    type: string
    nullable: true
    description: phone number of the informant customer
  submitted_by_email:
    type: string
    nullable: true
    example: <EMAIL>
    description: email address of the informant customer
  submitted_time:
    type: string
    nullable: true
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: >
      UTC timestamp (ISO-8601) when the preannouncement was submitted
  last_assigned_to_supplier:
    type: string
    nullable: true
    example: c4ab-542d50bf8ff0d5a9cfce46a402cbc376-a6e049c4
    description: the value of `assigned_to_supplier_id` before the preannouncement was restarted
  last_assigned_to_company:
    type: string
    nullable: true
    example: 42a6-139dd96be2652fc685ab2a8259b69102-9c6bba58
    description: the value of `assigned_to_company_id` before the preannouncement was restarted
  last_assigned_to_business_id:
    type: string
    nullable: true
    description: the value of `buyer_gov_org_id` before the preannouncement was restarted
  last_assigned_to_business_id_type:
    type: string
    nullable: true
    description: the value of `buyer_id_type` before the preannouncement was restarted
  last_assigned_to_time:
    type: string
    nullable: true
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: >
      UTC timestamp (ISO-8601) when the last_assigned_to_ fields were modified
""")

SWAGGER_UPDATE_PREANNOUNCEMENT_SCHEMA_WITH_NEW_FORM_DATA = schema_variant(
    SWAGGER_PREANNOUNCEMENT_SCHEMA_WITH_FORM_DATA, f"""
x-bolfak-schema: boldataapi.schema.schema_update_preannouncement_with_new_pa_form
required:
  - company_name
  - company_gov_org_id
  - company_country
  - buyer_name
  - buyer_gov_org_id
  - buyer_country
properties:
  id: {DELETE_MARKER}
  company_name:
    nullable: false
  company_id_type:
    nullable: false
  has_permanent_establishment:
    nullable: false
  is_one_man_company:
    nullable: false
  has_collective_agreement:
    nullable: false
  collective_agreement_name:
    nullable: false
  buyer_name:
    nullable: false
  buyer_id_type:
    nullable: false
  foreman_is_on_site:
    nullable: false
  foreman_first_name:
    nullable: false
  foreman_last_name:
    nullable: false
  foreman_phone_number:
    nullable: false
  foreman_email:
    nullable: false
  confirmed_name: {DELETE_MARKER}
  confirmed_gov_org_id: {DELETE_MARKER}
  confirmed_id_type: {DELETE_MARKER}
  confirmed_time: {DELETE_MARKER}
  confirmed_country: {DELETE_MARKER}
  rejected_name: {DELETE_MARKER}
  rejected_gov_org_id: {DELETE_MARKER}
  rejected_id_type: {DELETE_MARKER}
  rejected_time: {DELETE_MARKER}
  rejected_country: {DELETE_MARKER}
  submitted_time: {DELETE_MARKER}
""")

SWAGGER_UPDATE_PREANNOUNCEMENT_SCHEMA_WITH_UPDATED_FORM_DATA = schema_variant(
    SWAGGER_PREANNOUNCEMENT_SCHEMA_WITH_FORM_DATA, f"""
x-bolfak-schema: boldataapi.schema.schema_update_preannouncement_with_pa_form
properties:
  # wow we're deleting about half of the fields here
  id: {DELETE_MARKER}
  company_name: {DELETE_MARKER}
  company_gov_org_id: {DELETE_MARKER}
  company_id_type: {DELETE_MARKER}
  company_country: {DELETE_MARKER}
  has_permanent_establishment: {DELETE_MARKER}
  is_one_man_company: {DELETE_MARKER}
  has_collective_agreement: {DELETE_MARKER}
  collective_agreement_name: {DELETE_MARKER}
  buyer_name: {DELETE_MARKER}
  buyer_gov_org_id: {DELETE_MARKER}
  buyer_id_type: {DELETE_MARKER}
  buyer_country: {DELETE_MARKER}
  foreman_is_on_site: {DELETE_MARKER}
  foreman_first_name: {DELETE_MARKER}
  foreman_last_name: {DELETE_MARKER}
  foreman_phone_number: {DELETE_MARKER}
  foreman_email: {DELETE_MARKER}
  contract_type: {DELETE_MARKER}
  contract_start_date: {DELETE_MARKER}
  contract_end_date: {DELETE_MARKER}
  contract_work_areas: {DELETE_MARKER}
  # confirmed_name is editable
  # confirmed_gov_org_id is editable
  # confirmed_id_type is editable
  # confirmed_time is editable
  # confirmed_country is editable
  # rejected_name is editable
  # rejected_gov_org_id is editable
  # rejected_id_type is editable
  # rejected_time is editable
  # rejected_country is editable
  # informant_supplier_first_name is editable
  # informant_supplier_last_name is editable
  # informant_supplier_phone is editable
  # informant_supplier_email is editable
  # submitted_by_first_name is editable
  # submitted_by_last_name is editable
  # submitted_by_phone is editable
  # submitted_by_email is editable
  # submitted_time is editable
  # last_assigned_to_supplier is editable
  # last_assigned_to_company is editable
  # last_assigned_to_business_id is editable
  # last_assigned_to_business_id_type is editable
  # last_assigned_to_time is editable
""")


def upsert_pa_form(db, payload, preannouncement):
    pa_form = get_preannouncement_form(db, pa=preannouncement)

    if pa_form:
        update_preannouncement_form(db, pa_form['id'], payload)
        pa_form_id = pa_form['id']
    else:
        payload.update({'pa_id': preannouncement['id']})
        pa_form_id = create_preannouncement_form(db, payload)
        update_preannouncement(db, preannouncement['external_id'], {'active_pa_form': pa_form_id})

    return get_preannouncement_form_data(db, pa_form_id=pa_form_id)


def create_preannouncement_form(db, payload):
    preannouncement_form_table = db.meta.tables[PREANNOUNCEMENT_FORMS_TABLE]

    query = sa.insert(preannouncement_form_table).values(
        id=str(uuid.uuid4()),
        external_id=str(uuid.uuid4()),
        **payload
    )

    rez = db.session.execute(query)
    preannouncement_form_id = rez.inserted_primary_key[0]

    return preannouncement_form_id


def update_preannouncement_form(db, id, data):
    pa_form_table = db.meta.tables[PREANNOUNCEMENT_FORMS_TABLE]

    if data:
        query = (
            sa.update(pa_form_table)
            .where(pa_form_table.c.id == id)
            .values(data)
        )
        db.session.execute(query)

    return


def query_preannouncements(db, query):
    # Decorate preannouncements table with external_ids for linked objects
    preannouncements = db.meta.tables[PREANNOUNCEMENTS_TABLE]
    projects = db.meta.tables[PROJECTS_TABLE]
    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    supp_by = aliased(suppliers)
    supp_for = aliased(suppliers)
    supp_assigned_to = aliased(suppliers)
    sa_table = (
        sa.select([
            preannouncements,
            projects.c.external_id.label('project_external_id'),
            supp_by.c.external_id.label('created_by_supplier_external_id'),
            supp_for.c.external_id.label('for_supplier_external_id'),
            supp_assigned_to.c.external_id.label('assigned_to_supplier_external_id'),
        ])
        .select_from(
            preannouncements
            .outerjoin(projects, preannouncements.c.project_id == projects.c.id)
            .outerjoin(supp_by, preannouncements.c.created_by_supplier_id == supp_by.c.id)
            .outerjoin(supp_for, preannouncements.c.for_supplier_id == supp_for.c.id)
            .outerjoin(supp_assigned_to,
                       preannouncements.c.assigned_to_supplier_id == supp_assigned_to.c.id)
        )
    ).alias(PREANNOUNCEMENTS_DECORATED_TABLE)

    rows = query_table(db, query, table=sa_table)

    preannouncements = [
        {
            'id': pa[preannouncements.c.id],
            'status': pa[preannouncements.c.status],
            'external_id': pa[preannouncements.c.external_id],
            'created_by_supplier_id': pa[sa_table.c.created_by_supplier_external_id],
            'for_supplier_id': pa[sa_table.c.for_supplier_external_id],
            'project_id': pa[sa_table.c.project_external_id],
            'assigned_to_company_id': pa[preannouncements.c.assigned_to_company_id],
            'assigned_to_supplier_id': pa[sa_table.c.assigned_to_supplier_external_id],
            'assigned_to_time': pa[preannouncements.c.assigned_to_time],
        }
        for pa in rows
    ]
    return preannouncements


SWAGGER_PREANNOUNCEMENT_SCHEMA_WITHOUT_ACTIVE_PA_FORM = schema_variant(
    SWAGGER_PREANNOUNCEMENT_SCHEMA, f"""
properties:
  active_pa_form: {DELETE_MARKER}
""")
