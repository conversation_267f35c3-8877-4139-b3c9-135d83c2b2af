import sqlalchemy as sa
import yaml

from boldataapi import exceptions
from boldataapi.schema import (
    schema_existing_project_comment_viewer,
    schema_new_project_comment_viewer,
)
from boldataapi.storage.db import PROJECT_COMMENT_VIEWERS_TABLE
from boldataapi.swagger import schema_variant


SWAGGER_PROJECT_COMMENT_VIEWER_SCHEMA = yaml.safe_load("""
type: object
properties:
  id:
    type: string
    description: ID of the viewer
    example: 8164ce45-8050a2e47e77-89cd-5b4c-6ede
  comment_id:
    type: string
    description: database ID of the project comment
    example: 8164ce45-8050a2e47e77-89cd-5b4c-6ede
  read_by_person_id:
    type: string
    description: ID of the person who has read the comment
    example: d1f5-9d39ead1f496c84f89c7431e5f167095-280796a2
""")

SWAGGER_NEW_PROJECT_COMMENT_VIEWER_SCHEMA = schema_variant(
    SWAGGER_PROJECT_COMMENT_VIEWER_SCHEMA,
    """
x-bolfak-schema: boldataapi.schema.schema_new_project_comment_viewer
required:
  - comment_id
  - read_by_person_id
properties:
  id: <DELETED>
""",
)

schema_new = schema_new_project_comment_viewer
schema_existing = schema_existing_project_comment_viewer


def get_project_comment_viewer_by_comment_id_and_person_id(
    db, comment_id=None, person_id=None
):
    """Get project comment viewer records by comment_id and/or person_id

    Args:
        db: Database connection
        comment_id: Optional ID of the project comment to filter by
        person_id: Optional ID of the person to filter by

    Returns:
        List of viewer records matching the criteria
    """
    project_comment_viewers = db.meta.tables[PROJECT_COMMENT_VIEWERS_TABLE]

    conditions = []
    if comment_id is not None:
        conditions.append(project_comment_viewers.c.project_comment_id == comment_id)
    if person_id is not None:
        conditions.append(project_comment_viewers.c.read_by_person_id == person_id)

    query = sa.select([project_comment_viewers]).where(sa.and_(*conditions))
    result = db.session.execute(query)
    viewers = result.fetchall()

    return [
        {
            "id": viewer["id"],
            "comment_id": viewer["project_comment_id"],
            "read_by_person_id": viewer["read_by_person_id"],
        }
        for viewer in viewers
    ]


def create_project_comment_viewer(db, comment_id, person_id):
    """Create a new project comment viewer record.

    Args:
        db: Database connection
        comment_id: ID of the project comment
        person_id: ID of the person who read the comment

    Returns:
        The created viewer record
    """
    project_supplier_comments = db.meta.tables["project_supplier_comments"]
    comment_query = sa.select([project_supplier_comments.c.id]).where(
        project_supplier_comments.c.id == comment_id
    )
    comment_result = db.session.execute(comment_query)
    if not comment_result.fetchone():
        raise exceptions.NotFound(comment_id=comment_id)

    project_comment_viewers = db.meta.tables[PROJECT_COMMENT_VIEWERS_TABLE]

    query = sa.insert(project_comment_viewers).values(
        project_comment_id=comment_id,
        read_by_person_id=person_id,
    )
    db.session.execute(query)

    result = get_project_comment_viewer_by_comment_id_and_person_id(
        db,
        person_id=person_id,
        comment_id=comment_id,
    )

    return result[0]


def delete_project_comment_viewer(db, viewer_id):
    """Delete a project comment viewer record.

    Args:
        db: Database connection
        viewer_id: ID of the viewer record to delete

    Returns:
        True if the record was deleted, False if it wasn't found
    """
    project_comment_viewers = db.meta.tables[PROJECT_COMMENT_VIEWERS_TABLE]

    # Check if the viewer exists
    query = sa.select([project_comment_viewers]).where(
        project_comment_viewers.c.id == viewer_id
    )
    result = db.session.execute(query)
    viewer = result.fetchone()
    if not viewer:
        return False

    # Delete the viewer
    query = sa.delete(project_comment_viewers).where(
        project_comment_viewers.c.id == viewer_id
    )
    db.session.execute(query)

    return True
