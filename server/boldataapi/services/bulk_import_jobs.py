import json
from typing import Any, Dict, List, Optional

import sqlalchemy as sa
import yaml

from boldataapi.featureflags import feature_active
from boldataapi.schema import (
    BULK_IMPORT_JOB_STATUS_DONE,
    BULK_IMPORT_JOB_STATUS_FAILED,
    BULK_IMPORT_JOB_STATUS_IN_PROGRESS,
    BULK_IMPORT_JOB_STATUS_PENDING,
    is_valid_uuid,
)
from boldataapi.serialize import to_json_with_date
from boldataapi.storage.db.engine import SQLAlchemyEngine
from boldataapi.storage.db.schema import BULK_IMPORT_JOBS_TABLE
from boldataapi.swagger import DELETE_MARKER, schema_variant


Payload = Dict[str, Any]


def create_bulk_import_job(db: SQLAlchemyEngine, payload: Payload) -> Payload:
    if 'companies' in payload:
        payload['companies'] = _transform_companies_on_write(payload['companies'])
    bulk_import_jobs = db.meta.tables[BULK_IMPORT_JOBS_TABLE]
    query = sa.insert(bulk_import_jobs).values(
        **payload
    )
    result = db.session.execute(query)
    id = result.inserted_primary_key[0]
    job = get_bulk_import_job(db, id)
    assert job is not None  # transactions mean nobody could've deleted the row we've just inserted
    return job


def get_bulk_import_job(db: SQLAlchemyEngine, id: str) -> Optional[Payload]:
    if not is_valid_uuid(id):
        return None
    bulk_import_jobs = db.meta.tables[BULK_IMPORT_JOBS_TABLE]
    query = sa.select([bulk_import_jobs]).where(bulk_import_jobs.c.id == id)
    return _get_bulk_import_job_from_query(db, query)


def get_last_bulk_import_job(db: SQLAlchemyEngine, project_id: str) -> Optional[Payload]:
    bulk_import_jobs = db.meta.tables[BULK_IMPORT_JOBS_TABLE]
    query = (
        sa.select([bulk_import_jobs])
        .where(bulk_import_jobs.c.project_id == project_id)
        .order_by(
            sa.case({
                BULK_IMPORT_JOB_STATUS_PENDING: -1,
                BULK_IMPORT_JOB_STATUS_IN_PROGRESS: -2,
                BULK_IMPORT_JOB_STATUS_FAILED: 0,
                BULK_IMPORT_JOB_STATUS_DONE: 0,
            }, value=bulk_import_jobs.c.status, else_=0),
            bulk_import_jobs.c.created_on.desc())
        .limit(1)
    )
    return _get_bulk_import_job_from_query(db, query)


def _get_bulk_import_job_from_query(db: SQLAlchemyEngine, query: sa.select) -> Optional[Payload]:
    result = db.session.execute(query)
    job = result.fetchone()
    if job is None:
        return None
    job_dict = dict(job)
    job_dict['companies'] = _transform_companies_on_read(job_dict['companies'])
    return job_dict


def update_bulk_import_job(db: SQLAlchemyEngine, id: str, payload: Payload) -> Optional[Payload]:
    if not is_valid_uuid(id):
        return None
    if 'companies' in payload:
        payload['companies'] = _transform_companies_on_write(payload['companies'])
    bulk_import_jobs = db.meta.tables[BULK_IMPORT_JOBS_TABLE]
    qry = (
        sa.update(bulk_import_jobs)
        .where(bulk_import_jobs.c.id == id)
        .values(**payload)
    )
    db.session.execute(qry)
    return get_bulk_import_job(db, id)


def _transform_companies_on_write(companies: List[List[Any]]) -> List[List[Any]]:
    # XXX: probably not the most efficient transformation here
    companies = json.loads(to_json_with_date(companies))
    for company in companies:
        if len(company) == 8:
            company.append(None)  # external_id
            company.append(None)  # name
    return companies


def _transform_companies_on_read(companies: List[List[Any]]) -> List[List[Any]]:
    if feature_active('core'):
        # Return the companies as is with the 9th and 10th elements (external_id and name)
        return companies
    # Remove elements 9-10 (external_id and name) if they exist
    return [company[:8] for company in companies]


SWAGGER_BULK_IMPORT_JOB_SCHEMA = yaml.safe_load("""
type: object
properties:
  id:
    type: string
    description: database ID of the job
    example: 0b2708f2-98a2-4618-aff0-4497b7f51a46
  status:
    type: string
    enum:
      - pending
      - in_progress
      - failed
      - done
    description: job status
  imported:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    nullable: true
    description: >
      timestamp (UTC) of when the job was finished successfully
  canceled:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    nullable: true
    description: >
      timestamp (UTC) of when the job was requested to be canceled
  project_id:
    type: string
    minLength: 1
    example: 3d1e-7ffe28382d6f162fb2caf1462de40b87-2cca31d0
    description: >
      database ID (aka external ID) of the project for which
      these companies might be added as suppliers
  interested_org_id:
    type: string
    minLength: 1
    description: >
      database ID of the company on whose behalf the system
      will ask for company status reports
  companies:
    type: array
    items:
      type: array
      minItems: 8
      maxItems: 8
      description: |
        a list of [ company_gov_id, company_id, status, source,
        error, action, requested, completed ]

        **company_gov_id**: government registration number of the company

        **company_id**: database ID of the company

        **status**: import status (pending/canceled/error/done/already_in_project)

        **source**: data source

        **error**: error message, if any

        **action**: action taken (add/skip)

        **requested**: ISO-8601 timestamp of when the job was requested

        **completed**: ISO-8601 timestamp of when the job was completed
      items:
        type: string
        nullable: true
      # XXX: prefixItems is an OpenAPI 3.1.0 feature that is
      # not supported by swagger-ui yet.
      x-prefixItems:
        - type: string
          minLength: 1
          description: government registration number
        - type: string
          nullable: true
          description: >
            database ID of the company
        - type: string
          enum:
            - pending
            - canceled
            - error
            - done
            - already_in_project
          description: >
            import status of this company
        - type: string
          nullable: true
          description: >
            data source for this company
        - type: string
          nullable: true
          description: >
            import error, if any, for this company
        - type: string
          nullable: true
            - add
            - skip
          description: >
            decided import action
        - type: string
          format: date-time
          example: 2022-09-14T11:33:54.392579
          nullable: true
          description: >
            timestamp (UTC) of the import request
        - type: string
          format: date-time
          example: 2022-09-14T11:33:54.392579
          nullable: true
          description: >
            timestamp (UTC) of the import request completion
""")


SWAGGER_NEW_BULK_IMPORT_JOB_SCHEMA = schema_variant(SWAGGER_BULK_IMPORT_JOB_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_new_bulk_import_job
required:
  - project_id
  - interested_org_id
  - companies
properties:
  id: {DELETE_MARKER}
  status:
    default: pending
    description: >
      job status
      (usually you would not provide this field when creating a new job)
  imported:
    description: >
      timestamp (UTC) of when the job was finished successfully
      (usually you would not provide this field when creating a new job)
  canceled:
    description: >
      timestamp (UTC) of when the job was canceled
      (usually you would not provide this field when creating a new job)
  companies:
    items:
      description: |
        a list of [ company_gov_id, company_id, status, source,
        error, action, requested, completed ]

        **company_gov_id**: government registration number of the company

        **company_id**: database ID of the company
        (specify `null` when creating a new job)

        **status**: import status (pending/canceled/error/done/already_in_project)
        (specify `pending` when creating a new job)

        **source**: data source
        (specify `null` when creating a new job)

        **error**: error message, if any
        (specify `null` when creating a new job)

        **action**: action taken (add/skip)
        (specify `null` when creating a new job)

        **requested**: ISO-8601 timestamp of when the job was requested
        (specify the current date and time when creating a new job)

        **completed**: ISO-8601 timestamp of when the job was completed
        (specify `null` when creating a new job)
      # XXX: prefixItems is an OpenAPI 3.1.0 feature that is
      # not supported by swagger-ui yet.
      # XXX: schema_variant() doesn't support updating YAML lists in place,
      # so we must replace it entirely, repeating all the types etc. that
      # didn't change.
      x-prefixItems:
        - type: string
          minLength: 1
          description: government registration number
        - type: string
          nullable: true
          description: >
            database ID of the company
            (usually `null` when you're creating a new job)
        - type: string
          enum:
            - pending
            - canceled
            - error
            - done
            - already_in_project
          description: >
            import status of this company
            (usually `pending` when you're creating a new job)
        - type: string
          nullable: true
          description: >
            data source for this company
            (specify `null` when you're creating a new job)
        - type: string
          nullable: true
          description: >
            import error, if any, for this company
            (specify `null` when you're creating a new job)
        - type: string
          nullable: true
            - add
            - skip
          description: >
            decided import action
            (specify `null` when you're creating a new job)
        - type: string
          format: date-time
          example: 2022-09-14T11:33:54.392579
          nullable: true
          description: >
            timestamp (UTC) of the import request
            (specify current date and time when you're creating a new job)
        - type: string
          format: date-time
          example: 2022-09-14T11:33:54.392579
          nullable: true
          description: >
            timestamp (UTC) of the import request completion
            (specify `null` when you're creating a new job)
""")


SWAGGER_UPDATE_BULK_IMPORT_JOB_SCHEMA = schema_variant(SWAGGER_BULK_IMPORT_JOB_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_update_bulk_import_job
properties:
  # `id`, 'project_id', and `interested_org_id` properties are removed as they are not updatable
  id: {DELETE_MARKER}
  project_id: {DELETE_MARKER}
  interested_org_id: {DELETE_MARKER}
""")


SWAGGER_BULK_IMPORT_JOB_EXAMPLE_PENDING = """
summary: freshly created job
value:
  {
    "id": "4569e4fb-209c-4c1d-bbc3-6c6e7486d82c",
    "status": "pending",
    "project_id": "7111-e877470a0594ff214b893ca71111d8d5-bf11d345",
    "imported": null,
    "canceled": null,
    "companies": [
      ["38-4521384", null, "pending", null, null, null, "2022-08-30T11:33:10",
       null],
      ["41-1985581", null, "pending", null, null, null, "2022-08-30T11:33:10",
       null]
    ],
    "interested_org_id": "1cf7-a7ed03f70e65733eb3772b91a309a2c4-f3254db7",
    "last_changed": "2022-08-30T11:33:10.568013+00:00",
    "created_on": "2022-08-30T11:33:10.568013+00:00"
  }
"""


SWAGGER_BULK_IMPORT_JOB_EXAMPLE_IN_PROGRESS = """
summary: job in progress
value:
  {
    "id": "4569e4fb-209c-4c1d-bbc3-6c6e7486d82c",
    "status": "in_progress",
    "project_id": "7111-e877470a0594ff214b893ca71111d8d5-bf11d345",
    "imported": null,
    "canceled": null,
    "companies": [
      ["38-4521384", "3e78-f7060fb06fdeaaef5e2d41fcb625dbb0-02055120", "done",
       "CreditSafe", null, "add", "2022-08-30T11:37:03", "2022-08-30T11:33:37"],
      ["41-1985581", null, "pending", null, null, null, "2022-08-30T11:33:10", null]
    ],
    "interested_org_id": "1cf7-a7ed03f70e65733eb3772b91a309a2c4-f3254db7",
    "last_changed": "2022-08-30T11:33:37.964962+00:00",
    "created_on": "2022-08-30T11:33:10.568013+00:00"
  }
"""


SWAGGER_BULK_IMPORT_JOB_EXAMPLE_DONE = """
summary: finished job
value:
  {
    "id": "4569e4fb-209c-4c1d-bbc3-6c6e7486d82c",
    "status": "done",
    "project_id": "7111-e877470a0594ff214b893ca71111d8d5-bf11d345",
    "imported": "2022-08-30T11:33:39.013578+00:00",
    "canceled": null,
    "companies": [
      ["38-4521384", "3e78-f7060fb06fdeaaef5e2d41fcb625dbb0-02055120", "done",
       "CreditSafe", null, "add", "2022-08-30T11:37:03", "2022-08-30T11:33:37"],
      ["41-1985581", "4a8a-49141cdb88ae76d87b5d13c4d92d0381-0f773298",
       "already_in_project", null, null, "skip", "2022-08-30T11:33:10",
       "2022-08-30T11:33:39"]
    ],
    "interested_org_id": "1cf7-a7ed03f70e65733eb3772b91a309a2c4-f3254db7",
    "last_changed": "2022-08-30T11:33:39.013578+00:00",
    "created_on": "2022-08-30T11:33:10.568013+00:00"
  }
"""
