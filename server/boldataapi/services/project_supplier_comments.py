from datetime import datetime, timezone
from typing import Dict, List, Optional, TypedDict

import sqlalchemy as sa
import yaml
from sqlalchemy.sql import and_

from boldataapi.storage.db import (
    PROJECT_COMMENT_VIEWERS_TABLE,
    PROJECT_SUPPLIER_COMMENTS_TABLE,
    PROJECTS_TABLE,
    SUPPLIERS_TABLE,
)


# Swagger schema definitions
SWAGGER_PROJECT_SUPPLIER_COMMENT_SCHEMA = yaml.safe_load("""
type: object
properties:
  id:
    type: string
    description: |
      Unique identifier for the comment.
  project_id:
    type: string
    description: |
      The project ID this comment is associated with.
  supplier_id:
    type: string
    description: |
      The supplier ID this comment is associated with.
  org_id:
    type: string
    description: |
      The organization ID this comment is associated with.
  comment:
    type: string
    description: |
      The comment text.
  created_by_org_id:
    type: string
    description: |
      The organization ID of the user who created the comment.
  created_by_person_id:
    type: string
    description: |
      The person ID of the user who created the comment.
  created_timestamp:
    type: string
    format: date-time
    description: |
      When the comment was created.
  is_deleted:
    type: boolean
    description: |
      Whether the comment has been marked as deleted.
  deleted_by_org_id:
    type: string
    nullable: true
    description: |
      The organization ID of the user who deleted the comment.
  deleted_by_person_id:
    type: string
    nullable: true
    description: |
      The person ID of the user who deleted the comment.
  deleted_timestamp:
    type: string
    format: date-time
    nullable: true
    description: |
      When the comment was deleted.
  is_updated:
    type: boolean
    description: |
      Whether the comment has been updated.
  updated_by_org_id:
    type: string
    nullable: true
    description: |
      The organization ID of the user who last updated the comment.
  updated_by_person_id:
    type: string
    nullable: true
    description: |
      The person ID of the user who last updated the comment.
  updated_timestamp:
    type: string
    format: date-time
    nullable: true
    description: |
      When the comment was last updated.
  modified_timestamp:
    type: string
    format: date-time
    nullable: true
    description: |
      When the comment was last modified.
""")

SWAGGER_NEW_PROJECT_SUPPLIER_COMMENT_SCHEMA = yaml.safe_load("""
type: object
x-bolfak-schema: boldataapi.schema.schema_new_project_supplier_comment
required:
  - project_id
  - supplier_id
  - org_id
  - comment
  - created_by_org_id
  - created_by_person_id
properties:
  project_id:
    type: string
    description: |
      The project ID this comment is associated with.
  supplier_id:
    type: string
    description: |
      The supplier ID this comment is associated with.
  org_id:
    type: string
    description: |
      The organization ID this comment is associated with.
  comment:
    type: string
    description: |
      The comment text.
  created_by_org_id:
    type: string
    description: |
      The organization ID of the user who created the comment.
  created_by_person_id:
    type: string
    description: |
      The person ID of the user who created the comment.
""")

SWAGGER_UPDATE_PROJECT_SUPPLIER_COMMENT_SCHEMA = yaml.safe_load("""
type: object
x-bolfak-schema: boldataapi.schema.schema_update_project_supplier_comment
required:
  - updating_org_id
  - updating_person_id
properties:
  comment:
    type: string
    description: |
      The comment as text. One of comment and is_deleted must be provided.
  is_deleted:
    type: boolean
    enum: [true]
    description: |
      Set to true to mark comment as deleted. One of comment and is_deleted must be provided.
  updating_org_id:
    type: string
    description: |
      The org_id of the user updating the comment
  updating_person_id:
    type: string
    description: |
      The person_id of the user updating the comment
""")


class ProjectSupplierComment(TypedDict):
    id: str
    project_id: str
    supplier_id: str
    org_id: str
    comment: str
    created_by_org_id: str
    created_by_person_id: str
    created_timestamp: datetime
    is_deleted: bool
    deleted_by_org_id: Optional[str]
    deleted_by_person_id: Optional[str]
    deleted_timestamp: Optional[datetime]
    is_updated: bool
    updated_by_org_id: Optional[str]
    updated_by_person_id: Optional[str]
    updated_timestamp: Optional[datetime]
    modified_timestamp: Optional[datetime]
    is_read: Optional[bool]  # only set if fetching read status for a user


class NewProjectSupplierComment(TypedDict):
    project_id: str
    supplier_id: str
    org_id: str
    comment: str
    created_by_org_id: str
    created_by_person_id: str


def get_project_supplier_comment(
    db, comment_id: str
) -> Optional[ProjectSupplierComment]:
    """Get a project supplier comment by ID.

    Args:
        db: Database connection
        comment_id: ID of the comment

    Returns:
        The comment or None if not found
    """
    comments = query_project_supplier_comments(db, comment_id=comment_id)

    if not comments:
        return None

    return comments[0]


def create_project_supplier_comment(
    db, data: NewProjectSupplierComment
) -> ProjectSupplierComment:
    """Create a new project supplier comment.

    Args:
        db: Database connection
        data: Comment data

    Returns:
        The created comment
    """
    comments = db.meta.tables[PROJECT_SUPPLIER_COMMENTS_TABLE]
    project_comment_viewers = db.meta.tables[PROJECT_COMMENT_VIEWERS_TABLE]

    comment_query = sa.insert(comments).values(
        project_id=data.get("project_id"),
        supplier_id=data.get("supplier_id"),
        org_id=data.get("org_id"),
        comment=data.get("comment"),
        created_by_org_id=data.get("created_by_org_id"),
        created_by_person_id=data.get("created_by_person_id"),
    )
    result = db.session.execute(comment_query)
    comment_id = result.inserted_primary_key[0]
    viewer_query = sa.insert(project_comment_viewers).values(
        project_comment_id=comment_id,
        read_by_person_id=data.get("created_by_person_id"),
    )
    db.session.execute(viewer_query)

    comment = get_project_supplier_comment(db, comment_id=comment_id)
    assert (
        comment is not None
    ), f"Failed to retrieve created comment with ID {comment_id}"
    return comment


def update_project_supplier_comment(
    db, comment_id: str, comment: str, updated_by_org_id: str, updated_by_person_id: str
) -> ProjectSupplierComment:
    """Update a project supplier comment.

    Args:
        db: Database connection
        comment_id: ID of the comment to update
        comment: New comment as string
        updated_by_org_id: Organization ID of the user updating the comment
        updated_by_person_id: Person ID of the user updating the comment

    Returns:
        The updated comment
    """
    comments = db.meta.tables[PROJECT_SUPPLIER_COMMENTS_TABLE]

    now = datetime.now(timezone.utc)

    update_data = {
        "comment": comment,
        "updated_by_org_id": updated_by_org_id,
        "updated_by_person_id": updated_by_person_id,
        "updated_timestamp": now,
        "modified_timestamp": now,
        "is_updated": True,
    }

    query = sa.update(comments).where(comments.c.id == comment_id).values(update_data)
    db.session.execute(query)
    updated_comment = get_project_supplier_comment(db, comment_id=comment_id)
    assert (
        updated_comment is not None
    ), f"Failed to retrieve updated comment with ID {comment_id}"
    return updated_comment


def delete_project_supplier_comment(db, comment_id: str) -> None:
    """Delete a project supplier comment.

    Args:
        db: Database connection
        comment_id: ID of the comment to delete
    """
    comments = db.meta.tables[PROJECT_SUPPLIER_COMMENTS_TABLE]
    query = sa.delete(comments).where(comments.c.id == comment_id)
    db.session.execute(query)
    return


def mark_comment_as_deleted(
    db, comment_id: str, deleted_by_org_id: str, deleted_by_person_id: str
) -> ProjectSupplierComment:
    """Mark a project supplier comment as deleted.

    Args:
        db: Database connection
        comment_id: ID of the comment to mark as deleted
        deleted_by_org_id: Organization ID of the user marking the comment as deleted
        deleted_by_person_id: Person ID of the user marking the comment as deleted

    Returns:
        The updated comment
    """
    comments = db.meta.tables[PROJECT_SUPPLIER_COMMENTS_TABLE]

    now = datetime.now(timezone.utc)

    update_data = {
        "is_deleted": True,
        "deleted_by_org_id": deleted_by_org_id,
        "deleted_by_person_id": deleted_by_person_id,
        "deleted_timestamp": now,
        "modified_timestamp": now,
    }

    query = sa.update(comments).where(comments.c.id == comment_id).values(update_data)
    db.session.execute(query)
    deleted_comment = get_project_supplier_comment(db, comment_id=comment_id)
    assert (
        deleted_comment is not None
    ), f"Failed to retrieve deleted comment with ID {comment_id}"
    return deleted_comment


def query_project_supplier_comments(
    db,
    project_id=None,
    supplier_id=None,
    org_id=None,
    comment_id=None,
    hide_deleted=False,
    read_by_person_id=None,
) -> List[ProjectSupplierComment]:
    """Query project supplier comments.

    Args:
        db: Database connection
        project_id: External id of the project to filter by
        supplier_id: External id of the supplier to filter by
        org_id: Organization id to filter by
        comment_id: Comment id to filter by
        read_by_person_id: Person ID to fetch read status for

    Returns:
        List of comments matching the query
    """
    comments = db.meta.tables[PROJECT_SUPPLIER_COMMENTS_TABLE]
    viewers = db.meta.tables[PROJECT_COMMENT_VIEWERS_TABLE]
    projects = db.meta.tables[PROJECTS_TABLE]
    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    join_tables = comments.join(projects, projects.c.id == comments.c.project_id).join(
        suppliers, suppliers.c.id == comments.c.supplier_id
    )
    select_columns = [
        comments,
        projects.c.external_id.label("project_external_id"),
        suppliers.c.external_id.label("supplier_external_id"),
    ]

    conditions = []

    if project_id:
        conditions.append(projects.c.external_id == project_id)
    if supplier_id:
        conditions.append(suppliers.c.external_id == supplier_id)
    if org_id:
        conditions.append(comments.c.org_id == org_id)
    if comment_id:
        conditions.append(comments.c.id == comment_id)
    if hide_deleted:
        conditions.append(comments.c.is_deleted == False)  # noqa: E712
    # add "is_read" column to result, which is True if the comment has been read,
    # false if not and null if read_by_person_id is not provided
    if read_by_person_id:
        join_tables = join_tables.outerjoin(
            viewers,
            and_(
                viewers.c.project_comment_id == comments.c.id,
                viewers.c.read_by_person_id == read_by_person_id,
            ),
        )
        select_columns.append(
            sa.case([(viewers.c.id.is_(None), False)], else_=True).label("is_read")
        )
    else:
        select_columns.append(sa.null().label("is_read"))

    join_query = sa.select(select_columns).select_from(join_tables)

    if conditions:
        join_query = join_query.where(and_(*conditions))

    # Add ordering
    join_query = join_query.order_by(comments.c.created_timestamp.desc())

    result = db.session.execute(join_query)
    return [
        ProjectSupplierComment(
            id=comment[comments.c.id],
            project_id=comment["project_external_id"],
            supplier_id=comment["supplier_external_id"],
            org_id=comment[comments.c.org_id],
            comment=comment[comments.c.comment],
            created_by_org_id=comment[comments.c.created_by_org_id],
            created_by_person_id=comment[comments.c.created_by_person_id],
            created_timestamp=comment[comments.c.created_timestamp],
            is_deleted=comment[comments.c.is_deleted],
            deleted_by_org_id=comment[comments.c.deleted_by_org_id],
            deleted_by_person_id=comment[comments.c.deleted_by_person_id],
            deleted_timestamp=comment[comments.c.deleted_timestamp],
            is_updated=comment[comments.c.is_updated],
            updated_by_org_id=comment[comments.c.updated_by_org_id],
            updated_by_person_id=comment[comments.c.updated_by_person_id],
            updated_timestamp=comment[comments.c.updated_timestamp],
            modified_timestamp=comment[comments.c.modified_timestamp],
            is_read=comment["is_read"],
        )
        for comment in result
    ]


def query_project_supplier_comments_batch(
    db,
    project_ids: List[str],
    org_id=None,
    hide_deleted=False,
    read_by_person_id=None,
) -> Dict[str, List[ProjectSupplierComment]]:
    """Query project supplier comments for multiple projects efficiently."""
    if not project_ids:
        return {}

    comments = db.meta.tables[PROJECT_SUPPLIER_COMMENTS_TABLE]
    viewers = db.meta.tables[PROJECT_COMMENT_VIEWERS_TABLE]
    projects = db.meta.tables[PROJECTS_TABLE]
    suppliers = db.meta.tables[SUPPLIERS_TABLE]

    join_tables = comments.join(projects, projects.c.id == comments.c.project_id).join(
        suppliers, suppliers.c.id == comments.c.supplier_id
    )
    select_columns = [
        comments,
        projects.c.external_id.label("project_external_id"),
        suppliers.c.external_id.label("supplier_external_id"),
    ]

    conditions = [projects.c.external_id.in_(project_ids)]

    if org_id:
        conditions.append(comments.c.org_id == org_id)
    if hide_deleted:
        conditions.append(comments.c.is_deleted == False)  # noqa: E712

    # add "is_read" column to result, which is True if the comment has been read,
    # false if not and null if read_by_person_id is not provided
    if read_by_person_id:
        join_tables = join_tables.outerjoin(
            viewers,
            and_(
                viewers.c.project_comment_id == comments.c.id,
                viewers.c.read_by_person_id == read_by_person_id,
            ),
        )
        select_columns.append(
            sa.case([(viewers.c.id.is_(None), False)], else_=True).label("is_read")
        )
    else:
        select_columns.append(sa.null().label("is_read"))

    join_query = sa.select(select_columns).select_from(join_tables)
    join_query = join_query.where(and_(*conditions))

    # Add ordering by project first, then by timestamp
    join_query = join_query.order_by(
        projects.c.external_id.asc(),
        comments.c.created_timestamp.desc()
    )

    result = db.session.execute(join_query)

    # Group comments by project_id
    comments_by_project = {}
    for comment in result:
        project_id = comment["project_external_id"]

        comment_obj = ProjectSupplierComment(
            id=comment[comments.c.id],
            project_id=project_id,
            supplier_id=comment["supplier_external_id"],
            org_id=comment[comments.c.org_id],
            comment=comment[comments.c.comment],
            created_by_org_id=comment[comments.c.created_by_org_id],
            created_by_person_id=comment[comments.c.created_by_person_id],
            created_timestamp=comment[comments.c.created_timestamp],
            is_deleted=comment[comments.c.is_deleted],
            deleted_by_org_id=comment[comments.c.deleted_by_org_id],
            deleted_by_person_id=comment[comments.c.deleted_by_person_id],
            deleted_timestamp=comment[comments.c.deleted_timestamp],
            is_updated=comment[comments.c.is_updated],
            updated_by_org_id=comment[comments.c.updated_by_org_id],
            updated_by_person_id=comment[comments.c.updated_by_person_id],
            updated_timestamp=comment[comments.c.updated_timestamp],
            modified_timestamp=comment[comments.c.modified_timestamp],
            is_read=comment["is_read"],
        )

        if project_id not in comments_by_project:
            comments_by_project[project_id] = []
        comments_by_project[project_id].append(comment_obj)

    # Ensure all requested project_ids are in the result, even if they have no comments
    for project_id in project_ids:
        if project_id not in comments_by_project:
            comments_by_project[project_id] = []

    return comments_by_project
