import json
import logging
import uuid

import sqlalchemy as sa
import yaml
from sqlalchemy.sql.expression import bindparam

from boldataapi import exceptions
from boldataapi.schema import (
    ALLOWED_PROVIDERS,
    QVARN_COMPAT_FIELDS_FOR_REPORTS,
)
from boldataapi.services.reports import REPORT_RESOURCE_TYPE
from boldataapi.storage.db import STATUS_REPORTS_TABLE
from boldataapi.storage.db.schema import STATUS_REPORTS_HISTORY_TABLE
from boldataapi.storage.qvarn_helpers import generate_qvarn_id
from boldataapi.swagger import (
    DELETE_MARKER,
    qvarn_compat_schema_for_reading,
    qvarn_compat_schema_for_writing,
    schema_variant,
)


logger = logging.getLogger(__name__)


def get_status_report(db, *, external_id=None, report_id=None):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    params = [external_id, report_id]

    if not any(params) or all(params):
        raise exceptions.InternalServerError(
            msg="One of parameters must be provided: external_id or report_id"
        )

    search_predicate = (
        tbl_status_reports.c.external_id == external_id
    )
    if report_id:
        search_predicate = (
            tbl_status_reports.c.id == report_id
        )
    qry = (
        sa.select([tbl_status_reports])
        .where(search_predicate)
    )

    rez = db.session.execute(qry)
    db_status_report = rez.fetchone()

    if db_status_report is None:
        return None

    retrieved_status_report = {
        'id': db_status_report[tbl_status_reports.c.id],
        'external_id': db_status_report[tbl_status_reports.c.external_id],
        'tilaajavastuu_status': db_status_report[tbl_status_reports.c.status],
        'generated_timestamp': db_status_report[tbl_status_reports.c.generated_timestamp],
        'interested_org_id': db_status_report[tbl_status_reports.c.interested_company_id],
        'charge_reference': db_status_report[tbl_status_reports.c.charge_reference],
        'used_providers': db_status_report[tbl_status_reports.c.used_providers],
        'org': db_status_report[tbl_status_reports.c.company_id],
        'pdf': db_status_report[tbl_status_reports.c.json_],
        'report_type': 'bolagsfakta.company_report',  # Qvarn back compat
        'type': 'report',  # Qvarn back compat
        'revision': None,  # Qvarn back compat
    }
    retrieved_status_report.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    return retrieved_status_report


def get_historic_status_report(db, *, external_id):
    tbl_status_reports_history = db.meta.tables[STATUS_REPORTS_HISTORY_TABLE]

    qry = (
        sa.select([tbl_status_reports_history])
        .where(tbl_status_reports_history.c.external_id == external_id)
    )

    rez = db.session.execute(qry)
    db_status_report = rez.fetchone()

    if db_status_report is None:
        return None

    retrieved_historic_status_report = {
        'id': db_status_report[tbl_status_reports_history.c.id],
        'tilaajavastuu_status': db_status_report[tbl_status_reports_history.c.status],
        'external_id': db_status_report[tbl_status_reports_history.c.external_id],
        'generated_timestamp': db_status_report[tbl_status_reports_history.c.generated_timestamp],
        'interested_org_id': db_status_report[tbl_status_reports_history.c.interested_company_id],
        'org': db_status_report[tbl_status_reports_history.c.company_id],
        'charge_reference': db_status_report[tbl_status_reports_history.c.charge_reference],
        'used_providers': db_status_report[tbl_status_reports_history.c.used_providers],
    }

    return retrieved_historic_status_report


def get_all_status_report_intrstd_cmpny_id_to_cmpny_id(db, start_timestamp, end_timestamp):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    qry = (
      sa.select([
        tbl_status_reports.c.interested_company_id,
        tbl_status_reports.c.company_id
      ], distinct=True)
      .where(
        sa.and_(
          tbl_status_reports.c.generated_timestamp >= start_timestamp,
          tbl_status_reports.c.generated_timestamp < end_timestamp
        )
      )
    )

    rez = db.session.execute(qry)
    return rez.fetchall()


SWAGGER_STATUS_REPORT_SCHEMA = yaml.safe_load(f"""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID)
    example: 4c29-e2ced4332b3f01801173ec4505f8bb11-aa9062c2
  report_type:
    type: string
    enum:
      - bolagsfakta.company_report
    description: always `bolagsfakta.company_report`, for Qvarn compatibility
  org:
    type: string
    description: database ID (aka external ID) of the company, which is subject to the report
    example: ed2d-533e15fb6fe2a9455378f1c17d71c2e3-f9a89a44
  interested_org_id:
    type: string
    description: database ID (aka external ID) of the interested company
    example: ed2d-533e15fb6fe2a9455378f1c17d71c2e3-f9a89a44
  tilaajavastuu_status:
    type: string
    enum:
      - "100 STOP"
      - "200 INVESTIGATE"
      - "300 INCOMPLETE"
      - "400 ATTENTION"
      - "500 OK"
    description: |
      company status, according to the report

      The status values are designed to be sortable by importance.
  generated_timestamp:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: ISO 8601 timestamp (in UTC) of when the report was generated
  charge_reference:
    type: string
    description: charge reference id
  used_providers:
    type: array
    items:
      type: string
      enum: {json.dumps(ALLOWED_PROVIDERS)}
      description: used provider
      example: creditsafe_connect
  # the 'pdf' field is not always present
  # and sometimes it's renamed to 'json'
  # so I'm not going to document it here
  type:
    type: string
    enum:
      - report
    description: always "report", for Qvarn compatibility
  revision:
    type: string
    nullable: true
    example: null
    description: always `null`, for Qvarn compatibility
""")

SWAGGER_STATUS_REPORT_HISTORIC_SCHEMA = yaml.safe_load(f"""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID)
    example: 4c29-e2ced4332b3f01801173ec4505f8bb11-aa9062c2
  org:
    type: string
    description: database ID (aka external ID) of the company, which is subject to the report
    example: ed2d-533e15fb6fe2a9455378f1c17d71c2e3-f9a89a44
  interested_org_id:
    type: string
    description: database ID (aka external ID) of the interested company
    example: ed2d-533e15fb6fe2a9455378f1c17d71c2e3-f9a89a44
  tilaajavastuu_status:
    type: string
    enum:
      - "100 STOP"
      - "200 INVESTIGATE"
      - "300 INCOMPLETE"
      - "400 ATTENTION"
      - "500 OK"
  generated_timestamp:
    type: string
    format: date-time
    example: 2022-09-14T11:33:54.392579
    description: ISO 8601 timestamp (in UTC) of when the report was generated
  charge_reference:
    type: string
    description: charge reference id
  used_providers:
    type: array
    items:
      type: string
      enum: {json.dumps(ALLOWED_PROVIDERS)}
      description: used provider
      example: creditsafe_connect
""")


SWAGGER_STATUS_REPORT_JSON_SCHEMA = yaml.safe_load("""
type: object
description: contents of the report
properties: {}
example: {"clreports_interpretation": "OK"}
""")


SWAGGER_STATUS_REPORT_SCHEMA_WITH_JSON = schema_variant(SWAGGER_STATUS_REPORT_SCHEMA, """
properties:
  json:
    $ref: '#/components/schemas/status_report_json'
""")


SWAGGER_STATUS_REPORT_SCHEMA_QVARN_COMPAT = schema_variant(
    SWAGGER_STATUS_REPORT_SCHEMA,
    qvarn_compat_schema_for_reading(QVARN_COMPAT_FIELDS_FOR_REPORTS))


SWAGGER_NEW_STATUS_REPORT_SCHEMA = schema_variant(SWAGGER_STATUS_REPORT_SCHEMA, """
x-bolfak-schema: boldataapi.schema.schema_new_status_report
required:
  - type
  - report_type
  - org
  - generated_timestamp
  - tilaajavastuu_status
properties:
  id:
    nullable: true
    description: ignored (for Qvarn compatibility)
  type:
    description: ignored (for Qvarn compatibility)
  pdf:
    type: object
    required:
      - content_type
      - body
    properties:
      content_type:
        type: string
        enum:
          - "application/json"
        description: always "application/json"
      body:
        type: string
        description: content of the report, as a string containing JSON
        example: "{}"
""", qvarn_compat_schema_for_writing(QVARN_COMPAT_FIELDS_FOR_REPORTS))


SWAGGER_UPDATE_STATUS_REPORT_SCHEMA = schema_variant(SWAGGER_NEW_STATUS_REPORT_SCHEMA, f"""
x-bolfak-schema: boldataapi.schema.schema_existing_status_report
required: {DELETE_MARKER}
properties:
  pdf:
    required: {DELETE_MARKER}
""")


def get_latest_status_report_for_company(
    db, company_id, interested_company_id, provider=None, include_json=True,
):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    columns = [
        tbl_status_reports.c.id,
        tbl_status_reports.c.external_id,
        tbl_status_reports.c.status,
        tbl_status_reports.c.generated_timestamp,
        tbl_status_reports.c.interested_company_id,
        tbl_status_reports.c.charge_reference,
        tbl_status_reports.c.used_providers,
        tbl_status_reports.c.company_id,
    ]
    if include_json:
        columns.append(tbl_status_reports.c.json_)

    qry = (
        sa.select(columns)
        .where(
            sa.and_(
                tbl_status_reports.c.company_id == company_id,
                tbl_status_reports.c.interested_company_id == interested_company_id,
            )
         )
    )
    if provider:
        qry = qry.where(
            # WHERE json_->'used_providers' ? :provider
            tbl_status_reports.c.json_['used_providers'].op('?')(provider)
        )
    qry = (
        qry
        .order_by(tbl_status_reports.c.generated_timestamp.desc())
        .limit(1)
    )

    rez = db.session.execute(qry)
    db_status_report = rez.fetchone()

    if db_status_report is None:
        return None

    report = {
        'id': db_status_report[tbl_status_reports.c.id],
        'external_id': db_status_report[tbl_status_reports.c.external_id],
        'tilaajavastuu_status': db_status_report[tbl_status_reports.c.status],
        'generated_timestamp': db_status_report[tbl_status_reports.c.generated_timestamp],
        'interested_org_id': db_status_report[tbl_status_reports.c.interested_company_id],
        'charge_reference': db_status_report[tbl_status_reports.c.charge_reference],
        'used_providers': db_status_report[tbl_status_reports.c.used_providers],
        'org': db_status_report[tbl_status_reports.c.company_id],
    }
    if include_json:
        report['json'] = db_status_report[tbl_status_reports.c.json_]
    return report


def get_status_reports(db, start_timestamp=None, end_timestamp=None, provider=None,
                       company_ids=None, interested_company_id=None,
                       limit=None, cursor=None):
    """
    Get all status reports including historic filtered by date range, provider, company_ids and
    interested_company_id with global pagination support.
    """
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]
    tbl_status_reports_history = db.meta.tables[STATUS_REPORTS_HISTORY_TABLE]

    # Helper function to fetch reports from a single table
    def fetch_reports(table, is_current):
        query = sa.select([
            table.c.id,
            table.c.external_id,
            table.c.status,
            table.c.generated_timestamp,
            table.c.interested_company_id,
            table.c.charge_reference,
            table.c.used_providers,
            table.c.company_id,
            (sa.cast(sa.literal('{}'), sa.dialects.postgresql.JSONB).label('json_')
             if not is_current else table.c.json_),
        ]).where(
            sa.and_(
                (table.c.generated_timestamp >= start_timestamp if start_timestamp else True),
                (table.c.generated_timestamp < end_timestamp if end_timestamp else True),
                (provider == sa.any_(table.c.used_providers) if provider else True),
                (table.c.interested_company_id == interested_company_id
                 if interested_company_id else True),
                (table.c.company_id.in_(company_ids) if company_ids else True),
                (table.c.external_id > cursor if cursor else True)  # Use external_id as cursor
            )
        ).order_by(table.c.external_id)

        # Apply limit + 1 to check for "has_more"
        if limit:
            query = query.limit(limit + 1)

        return db.session.execute(query).fetchall()

    # Fetch data from both tables
    current_reports = fetch_reports(tbl_status_reports, is_current=True)
    history_reports = fetch_reports(tbl_status_reports_history, is_current=False)

    # Merge and sort results by external_id
    combined_reports = sorted(
        current_reports + history_reports,
        key=lambda r: r.external_id
    )

    # Apply global pagination
    has_more = len(combined_reports) > limit if limit else False
    combined_reports = combined_reports[:limit] if limit else combined_reports

    # Convert database records to API response format
    reports = []
    for report in combined_reports:
        reports.append({
            'id': report.id,
            'external_id': report.external_id,
            'tilaajavastuu_status': report.status,
            'generated_timestamp': report.generated_timestamp,
            'interested_org_id': report.interested_company_id,
            'charge_reference': report.charge_reference,
            'used_providers': report.used_providers,
            'org': report.company_id,
            'json': report.json_
        })

    # Determine next cursor based on the last record's external_id
    next_cursor = reports[-1]['external_id'] if has_more else None

    return {
        'reports': reports,
        'pagination': {
            'limit': limit,
            'has_more': has_more,
            'next_cursor': next_cursor
        }
    }


def create_status_report(db, data):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    qry = (
        sa.insert(tbl_status_reports).values(
            id=str(uuid.uuid4()),
            external_id=generate_qvarn_id(REPORT_RESOURCE_TYPE.replace('qvarn_', '')),
            status=data['status'],
            generated_timestamp=data['generated_timestamp'],
            interested_company_id=data['interested_company_id'],
            company_id=data['company_id'],
            charge_reference=data['charge_reference'],
            used_providers=data['used_providers'],
            json_=data['json_']
        )
    )
    rez = db.session.execute(qry)
    status_report_id = rez.inserted_primary_key[0]
    return get_status_report(db, report_id=status_report_id)


def update_status_report(db, report_id, data):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    qry = (
        sa.update(tbl_status_reports)
        .where(tbl_status_reports.c.id == report_id)
        .values(**data)
    )
    db.session.execute(qry)
    return get_status_report(db, report_id=report_id)


def delete_status_report(db, report_id):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    qry = (
        sa.delete(tbl_status_reports)
        .where(tbl_status_reports.c.id == report_id)
    )
    db.session.execute(qry)


def move_status_reports_to_history(db, intrstd_cmpny_ids_to_cmpny_ids):
    """
      Move row filtered by intrstd_cmpny_ids_to_cmpny_ids mappings
      from status_report to status_reports_history without json_ column.
    """
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]
    tbl_status_reports_history = db.meta.tables[STATUS_REPORTS_HISTORY_TABLE]

    # Rename fields to avoid sqlalchemy compile error
    # caused by variable name collisions.
    ids = [
              {
                '_interested_company_id': i['interested_company_id'],
                '_company_id': i['company_id']
              }
              for i in intrstd_cmpny_ids_to_cmpny_ids
          ]

    qry = (
      sa.insert(tbl_status_reports_history)
      .from_select(
        [
          'external_id',
          'status',
          'generated_timestamp',
          'interested_company_id',
          'company_id',
          'charge_reference',
          'used_providers'
        ],
        sa.select(
          [
            tbl_status_reports.c.external_id,
            tbl_status_reports.c.status,
            tbl_status_reports.c.generated_timestamp,
            tbl_status_reports.c.interested_company_id,
            tbl_status_reports.c.company_id,
            tbl_status_reports.c.charge_reference,
            tbl_status_reports.c.used_providers
          ]
        )
        .where(
          sa.and_(
            tbl_status_reports.c.interested_company_id == bindparam('_interested_company_id'),
            tbl_status_reports.c.company_id == bindparam('_company_id'))
        )
      )
    )

    db.session.execute(qry, ids)

    qry = (
      sa.delete(tbl_status_reports)
      .where(
        sa.and_(
          tbl_status_reports.c.interested_company_id == bindparam('_interested_company_id'),
          tbl_status_reports.c.company_id == bindparam('_company_id')
        )
      )
    )

    rez = db.session.execute(qry, ids)

    return rez.rowcount


def move_old_status_reports_to_history(db, start_timestamp, end_timestamp):
    """Move status reports from the status_reports table to the status_reports_history table.

    Drops the ``json_`` column along the way.

    ``start_timestamp`` and ``end_timestamp`` are used to filter out reports
    that are not in the given timestamp range.
    """
    data = {'start_timestamp': start_timestamp, 'end_timestamp': end_timestamp}

    qry_insert = sa.text("""
        INSERT INTO status_reports_history (
            external_id,
            status,
            generated_timestamp,
            interested_company_id,
            company_id,
            charge_reference,
            used_providers
        )
        SELECT
            external_id,
            status,
            generated_timestamp,
            interested_company_id,
            company_id,
            charge_reference,
            used_providers
        FROM status_reports sr
        WHERE generated_timestamp >= :start_timestamp AND generated_timestamp < :end_timestamp
    EXCEPT
        SELECT
            external_id,
            status,
            generated_timestamp,
            interested_company_id,
            company_id,
            charge_reference,
            used_providers
        FROM status_reports sr
        WHERE generated_timestamp = (
            SELECT MAX(generated_timestamp)
            FROM status_reports sr2
            WHERE sr.interested_company_id = sr2.interested_company_id AND
                  sr.company_id = sr2.company_id
        )
    """)

    rez_insert = db.session.execute(qry_insert, data)

    qry_delete = sa.text("""
        DELETE FROM status_reports sr
        WHERE id IN (
            SELECT id
            FROM status_reports sr
            WHERE generated_timestamp >= :start_timestamp AND generated_timestamp < :end_timestamp
        EXCEPT
            SELECT id
            FROM status_reports sr
            WHERE generated_timestamp = (
                SELECT MAX(generated_timestamp)
                FROM status_reports sr2
                WHERE sr.interested_company_id = sr2.interested_company_id
                      AND sr.company_id = sr2.company_id
            )
        )
    """)

    rez_delete = db.session.execute(qry_delete, data)

    if rez_insert.rowcount != rez_delete.rowcount:  # pragma: nocover
        logger.warning('moving old status reports to history: moved %d rows but deleted %d rows',
                       rez_insert.rowcount, rez_delete.rowcount)

    return rez_delete.rowcount


def update_status_report_pdf(db, external_id, report_data):
    tbl_status_reports = db.meta.tables[STATUS_REPORTS_TABLE]

    qry = (
        sa.update(tbl_status_reports)
        .where(tbl_status_reports.c.external_id == external_id)
        .values(json_=report_data)
    )
    db.session.execute(qry)
    return get_status_report(db, external_id=external_id)
