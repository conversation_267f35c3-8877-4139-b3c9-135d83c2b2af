import uuid

import sqlalchemy as sa
import yaml

from boldataapi.schema import is_valid_uuid
from boldataapi.services.shared import (
    PROJECT_USERS_DECORATED_TABLE,
    query_table,
)
from boldataapi.storage.db import PROJECT_USERS_TABLE, PROJECTS_TABLE
from boldataapi.storage.qvarn_helpers import generate_qvarn_id
from boldataapi.swagger import schema_variant


def _project_user_view(db):
    project_users = db.meta.tables[PROJECT_USERS_TABLE]
    projects = db.meta.tables[PROJECTS_TABLE]
    return (
        sa.select([
            project_users.c.id,
            project_users.c.external_id,
            projects.c.external_id.label('project_id'),
            project_users.c.role,
            project_users.c.notify,
            project_users.c.user_account_id,
            project_users.c.person_id,
            project_users.c.represented_company_id,
        ])
        .select_from(
            project_users
            .join(projects, projects.c.id == project_users.c.project_id)
        )
    )


def get_project_user(db, *, project_user_id=None, external_id=None):
    project_users = db.meta.tables[PROJECT_USERS_TABLE]

    if project_user_id is None and external_id is not None:
        condition = project_users.c.external_id == external_id
    elif project_user_id is not None and external_id is None:
        if not is_valid_uuid(project_user_id):
            return None
        condition = project_users.c.id == project_user_id
    else:
        raise TypeError("Exactly one of project_user_id or external_id must be provided")
    query = (
        _project_user_view(db)
        .where(condition)
    )
    result = db.session.execute(query)
    project_user = result.fetchone()
    if project_user is None:
        return None
    return dict(project_user)


SWAGGER_PROJECT_USER_SCHEMA = yaml.safe_load("""
type: object
properties:
  id:
    type: string
    description: database ID (aka external ID)
    example: 8dd2-dd85261da636a092a08a8a3be8e1f3a5-6006d01c
  project_id:
    type: string
    description: database ID (aka external ID) of the project
    example: e774-8164ce458050a2e47e7789cd5b4c6ede-0dded837
  role:
    type: string
    enum:
      - leader
      - manager
      - member
    description: the user's role in the project
  notify:
    type: boolean
    description: should the user be notified by email about changes to the project's status?
  user_account_id:
    type: string
    example: d1f5-9d39ead1f496c84f89c7431e5f167095-280796a2
    description: database ID of the user account (which is stored as a contract resource in Qvarn).\
 Deprecated in favor of person_id.
  person_id:
    type: string
    example: d1f5-9d39ead1f496c84f89c7431e5f167095-280796a2
    description: ID of the person (which is provided by the User Account API)
  represented_company_id:
    type: string
    example: f7a3-340b84553e9817f863dbcec98abfe9cd-1d009dff
    description: database ID of the company that the user represents
""")

# TODO: add person_id to required when data is migrated, see BOL-5833
SWAGGER_NEW_PROJECT_USER_SCHEMA = schema_variant(
    SWAGGER_PROJECT_USER_SCHEMA,
    """
x-bolfak-schema: boldataapi.schema.schema_new_project_user
required:
  - project_id
  - role
  - notify
  - represented_company_id
properties:
  id:
    nullable: true
    description: >
      can be omitted/null; if given, it must be different from any existing (external) ID
""",
)


SWAGGER_UPDATE_PROJECT_USER_SCHEMA = schema_variant(
    SWAGGER_PROJECT_USER_SCHEMA,
    """
x-bolfak-schema: boldataapi.schema.schema_update_project_user
properties:
  id:
    nullable: true
    description: >
      can be omitted/null, but if given, it must be equal to the id of the existing resource
  project_id:
    nullable: true
    description: >
      can be omitted/null, but if given, it must be equal to the project_id of
      the existing resource
  user_account_id:
    nullable: true
    description: >
      can be omitted/null, but if given, it must be equal to the user_account_id of the
      existing resource
  person_id:
    nullable: true
    description: >
      can be omitted/null, once migration is done (see BOL-5833) this should have the same \
constraints as other IDs (i.e. it should be equal to the person_id of the existing resource)
  represented_company_id:
    nullable: true
    description: >
      can be omitted/null, but if given, it must be equal to the represented_company_id
      of the existing resource
  role:
    nullable: true
    description: >
      the user's role in the project.
      Omitting or leaving it as `null` means do not change existing value.
  notify:
    nullable: true
    description: >
      should the user be notified by email about changes to the project's status?
      Omitting or leaving it as `null` means do not change existing value.
""",
)


def create_project_user(db, payload):
    project_users = db.meta.tables[PROJECT_USERS_TABLE]
    query = sa.insert(project_users).values(
        id=str(uuid.uuid4()),
        external_id=payload.get('external_id') or generate_qvarn_id('contracts'),
        project_id=payload.get('project_id'),
        role=payload.get('role'),
        notify=payload.get('notify'),
        user_account_id=payload.get('user_account_id'),
        person_id=payload.get('person_id'),
        represented_company_id=payload.get('represented_company_id'),
    )
    rez = db.session.execute(query)
    project_user_id = rez.inserted_primary_key[0]
    return get_project_user(db, project_user_id=project_user_id)


def update_project_user(db, project_user_id, payload):
    project_users = db.meta.tables[PROJECT_USERS_TABLE]
    if payload:
        query = (
            sa.update(project_users).where(project_users.c.id == project_user_id).values(payload)
        )
        db.session.execute(query)


def delete_project_user(db, project_user_id):
    project_users = db.meta.tables[PROJECT_USERS_TABLE]
    query = sa.delete(project_users).where(project_users.c.id == project_user_id)
    db.session.execute(query)


def query_project_users(db, query):
    table = _project_user_view(db).alias(PROJECT_USERS_DECORATED_TABLE)
    return [dict(row) for row in query_table(db, query, table)]
