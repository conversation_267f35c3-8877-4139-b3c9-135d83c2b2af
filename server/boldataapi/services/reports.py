from boldataapi.storage import qvarn
from boldataapi.storage.qvarn.resources import get_resource


REPORT_RESOURCE_NAME = 'reports'
REPORT_RESOURCE = get_resource(REPORT_RESOURCE_NAME)
REPORT_RESOURCE_TYPE = REPORT_RESOURCE.type
REPORT_RESOURCE_PROTOTYPE = REPORT_RESOURCE.schema


def search_reports(db, path):
    """This will search both status_reports and notification_reports."""
    # Set up Qvarn DB connection
    dbconn = qvarn.DatabaseConnection()
    dbconn.set_sql(qvarn.PostgresAdapterSimplified(db))

    # Set up ListResource
    resource = qvarn.ListResource()
    resource.set_path(REPORT_RESOURCE_TYPE)
    resource.set_item_type(REPORT_RESOURCE_TYPE)
    resource.set_item_prototype(REPORT_RESOURCE_PROTOTYPE)
    resource.prepare_resource(dbconn)

    result = resource.get_matching_items(path)
    return result
