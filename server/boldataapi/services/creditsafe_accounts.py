import json
import uuid

import sqlalchemy as sa
import yaml
from sqlalchemy.orm import aliased

from boldataapi import exceptions
from boldataapi.schema import CREDITSAFE_ACCOUNT_STATES
from boldataapi.services.shared import CREDITSAFE_ACCOUNT_DECORATED_TABLE, query_table
from boldataapi.storage.db import (
    CREDITSAFE_ACCOUNT_HISTORY_TABLE,
    CREDITSAFE_ACCOUNT_TABLE,
)
from boldataapi.swagger import DELETE_MARKER, schema_variant


def create_creditsafe_account(db, payload):
    creditsafe_accounts = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]
    creditsafe_account_id = payload.get('id', str(uuid.uuid4()))
    values = {
        'id': creditsafe_account_id,
        'person_id': payload.get('person_id'),
        'org_id': payload.get('org_id'),
        'username': payload.get('username'),
        'password': payload.get('password'),
        'state': payload.get('state'),
    }
    if 'created_on' in payload:
        values['created_on'] = payload['created_on']
    if 'last_changed' in payload:
        values['last_changed'] = payload['last_changed']

    query = sa.insert(creditsafe_accounts).values(**values)
    try:
        db.session.execute(query)
    except sa.exc.IntegrityError as e:
        if 'unique_active_org_id' in str(e.orig):
            raise exceptions.ConflictError(
                details='An active Creditsafe account already exists for the org_id',
                org_id=payload.get('org_id'),
            )
        raise
    if payload.get('history'):
        for h in payload.get('history'):
            create_creditsafeaccount_history_entry(db, creditsafe_account_id, h)
    else:
        create_creditsafeaccount_history_entry(db, creditsafe_account_id, payload)
    return get_creditsafe_account(db, creditsafe_account_id)


def get_creditsafe_account(db, creditsafe_account_id, *, include_history=True):
    creditsafe_accounts = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]
    creditsafe_account_history = db.meta.tables[CREDITSAFE_ACCOUNT_HISTORY_TABLE]
    history = aliased(creditsafe_account_history)
    if include_history:
        query = (
            sa.select([
                creditsafe_accounts,
                history
            ])
            .select_from(
                history
                .join(
                    creditsafe_accounts,
                    creditsafe_accounts.c.id == history.c.creditsafe_account_id
                )
            )
            .where(creditsafe_accounts.c.id == creditsafe_account_id)
            .order_by(history.c.created_on)
        )
    else:
        query = (
            sa.select([creditsafe_accounts])
            .where(creditsafe_accounts.c.id == creditsafe_account_id)
        )
    res = db.session.execute(query)
    csa = res.fetchall()
    if not csa:
        return None
    acct = {
        'id': csa[0][creditsafe_accounts.c.id],
        'person_id': csa[0][creditsafe_accounts.c.person_id],
        'org_id': csa[0][creditsafe_accounts.c.org_id],
        'username': csa[0][creditsafe_accounts.c.username],
        'password': csa[0][creditsafe_accounts.c.password],
        'state': csa[0][creditsafe_accounts.c.state],
    }
    if include_history:
        acct['history'] = [{
            'person_id': r[history.c.person_id],
            'org_id': r[history.c.org_id],
            'username': r[history.c.username],
            'state': r[history.c.state],
            'changed_by_person_id': r[history.c.changed_by_person_id],
            'comment': r[history.c.comment],
            'created_on': r[history.c.created_on],
        } for r in csa]
    return acct


def update_creditsafe_account(db, payload):
    creditsafe_account_id = payload.pop('creditsafe_account_id')
    changed_by_person_id = payload.pop('changed_by_person_id')
    comment = payload.pop('comment', None)
    creditsafe_accounts = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]
    query = (
        sa.update(creditsafe_accounts)
        .where(creditsafe_accounts.c.id == creditsafe_account_id)
        .values(payload)
    )
    db.session.execute(query)
    cs_account = get_creditsafe_account(db, creditsafe_account_id)
    create_creditsafeaccount_history_entry(
        db,
        creditsafe_account_id,
        {
            **cs_account,
            'changed_by_person_id': changed_by_person_id,
            'comment': comment,
        }
    )
    return get_creditsafe_account(db, creditsafe_account_id)


def delete_creditsafe_account(db, creditsafe_account_id):
    creditsafe_accounts = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]
    query = (
        sa.delete(creditsafe_accounts)
        .where(creditsafe_accounts.c.id == creditsafe_account_id)
    )
    db.session.execute(query)


def create_creditsafeaccount_history_entry(db, creditsafe_account_id, payload):
    creditsafe_account_history = db.meta.tables[CREDITSAFE_ACCOUNT_HISTORY_TABLE]
    values = {
        'creditsafe_account_id': creditsafe_account_id,
        'org_id': payload.get('org_id'),
        'person_id': payload.get('person_id'),
        'changed_by_person_id': payload.get('changed_by_person_id'),
        'username': payload.get('username'),
        'state': payload.get('state'),
        'comment': payload.get('comment'),
    }
    if 'created_on' in payload:
        values['created_on'] = payload['created_on']
    query = sa.insert(creditsafe_account_history).values(**values)
    db.session.execute(query)


def query_creditsafe_accounts(db, query):
    creditsafe_accounts = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]
    creditsafe_account_history = db.meta.tables[CREDITSAFE_ACCOUNT_HISTORY_TABLE]
    history = aliased(creditsafe_account_history)
    csa_decorated_table = (
        sa.select([
            creditsafe_accounts.c.id.label('cs_id'),
            creditsafe_accounts.c.person_id.label('cs_person_id'),
            creditsafe_accounts.c.org_id.label('cs_org_id'),
            creditsafe_accounts.c.username.label('cs_username'),
            creditsafe_accounts.c.password.label('cs_password'),
            creditsafe_accounts.c.state.label('cs_state'),
            creditsafe_accounts.c.created_on.label('cs_created_on'),
            history
        ])
        .select_from(
            history
            .join(
                creditsafe_accounts,
                creditsafe_accounts.c.id == history.c.creditsafe_account_id
            )
        )
        .order_by(creditsafe_accounts.c.id)
    ).alias(CREDITSAFE_ACCOUNT_DECORATED_TABLE)

    rows = query_table(db, query, table=csa_decorated_table)

    cs_accounts = []
    previous_cs_id = None
    for row in rows:
        cs_id = row[csa_decorated_table.c.cs_id]
        cs = {
            'id': cs_id,
            'person_id': row[csa_decorated_table.c.cs_person_id],
            'org_id': row[csa_decorated_table.c.cs_org_id],
            'username': row[csa_decorated_table.c.cs_username],
            'password': row[csa_decorated_table.c.cs_password],
            'state': row[csa_decorated_table.c.cs_state],
            'created_on': row[csa_decorated_table.c.cs_created_on],
            'history': [{
              'person_id': row[history.c.person_id],
              'org_id': row[history.c.org_id],
              'username': row[history.c.username],
              'state': row[history.c.state],
              'changed_by_person_id': row[history.c.changed_by_person_id],
              'comment': row[history.c.comment],
              'created_on': row[history.c.created_on],
            }]
        }
        if cs_id == previous_cs_id:
            cs_accounts[-1]['history'].append(cs['history'][0])
        else:
            cs_accounts.append(cs)
        previous_cs_id = cs_id

    return cs_accounts


SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITHOUT_HISTORY = yaml.safe_load(f"""
type: object
properties:
  id:
    type: string
    description: ID of the creditsafe account
    example: ********-f722-469f-aa57-44b2c6c4848a
  person_id:
    type: string
    description: person resource id of the user
    example: 0035-94c4f55599453307002f0731e0b67999-9ffa4cf4
  org_id:
    type: string
    description: organization resource id
    example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
  username:
    type: string
    description: creditsafe service username
    example: testuser
  password:
    type: string
    description: creditsafe service user's password as encrypted
    example: gAAAAABlulVRfJyl0Ncu_5bUWq8foDmmGGrtIHJP3djucpAOdh3_e6LtwbZdxXsFH5sbz4BElZBPi_e9dc==
  state:
    type: string
    enum: {json.dumps(CREDITSAFE_ACCOUNT_STATES)}
    description: creditsafe account state
    example: pending
""")


SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITH_HISTORY = schema_variant(
    SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITHOUT_HISTORY, f"""
properties:
  history:
    type: array
    description: Account revision history
    items:
      properties:
        person_id:
          type: string
          description: person resource id of the user
          example: 0035-94c4f55599453307002f0731e0b67999-9ffa4cf4
        org_id:
          type: string
          description: organization resource id
          example: f392-ac1d072f02afbf548d685dfecefe41a3-686620ba
        username:
          type: string
          description: creditsafe service username
          example: testuser
        comment:
          type: string
          description: comment from the retool updater
          example: Terminated due to password change
        changed_by_person_id:
          type: string
          description: resource id of the updater person
          example: 0035-94c4f55599453307002f0731e0b67999-9ffa4cf4
        created_on:
          type: string
          format: date-time
          description: history object creation timestamp
          example: 2024-03-13T09:55:52.972664+00:00
        state:
          type: string
          enum: {json.dumps(CREDITSAFE_ACCOUNT_STATES)}
          description: creditsafe account state
          example: pending
""")


SWAGGER_NEW_CREDITSAFE_ACCOUNT_SCHEMA = schema_variant(
    SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITH_HISTORY, """
x-bolfak-schema: boldataapi.schema.schema_new_creditsafe_account
required:
  - state
  - org_id
  - person_id
  - username
properties:
  comment:
    type: string
    description: comment from the retool updater
    example: Terminated due to password change
  changed_by_person_id:
    type: string
    description: resource id of the updater person
    example: 0035-94c4f55599453307002f0731e0b67999-9ffa4cf4
  created_on:
    type: string
    format: date-time
    description: object creation timestamp, used for migration
    example: 2024-03-13T09:55:52.972664+00:00
  last_changed:
    type: string
    format: date-time
    description: object last update timestamp, used for migration
    example: 2024-03-13T09:55:52.972664+00:00
""")


SWAGGER_UPDATE_CREDITSAFE_ACCOUNT_SCHEMA = schema_variant(
    SWAGGER_CREDITSAFE_ACCOUNT_SCHEMA_WITHOUT_HISTORY, f"""
x-bolfak-schema: boldataapi.schema.schema_update_creditsafe_account
properties:
  id: {DELETE_MARKER}
  comment:
    type: string
    description: comment from the retool updater
    example: Terminated due to password change
  changed_by_person_id:
    type: string
    description: resource id of the updater person
    example: 0035-94c4f55599453307002f0731e0b67999-9ffa4cf4
""")
