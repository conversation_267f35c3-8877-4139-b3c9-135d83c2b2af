import datetime
import json
from typing import Optional


class DateTimeEncoder(json.JSONEncoder):

    def default(self, obj):
        if isinstance(obj, datetime.date):
            return obj.isoformat()
        else:
            return json.JSONEncoder.default(self, obj)


def to_json_with_date(obj):
    return json.dumps(obj, cls=DateTimeEncoder)


def asbool(s: str, *, default: Optional[bool] = None) -> bool:
    """Convert a boolean value passed in the query string to an actual bool.

    Accepts 'True', 'true', '1' as true values.
    Accepts 'False', 'false', '0' as false values.

    Returns `default` if `s` is empty and `default` is provided.

    Raises for all other values.
    """
    if s in ('True', 'true', '1'):
        return True
    if s in ('False', 'false', '0'):
        return False
    if not s and default is not None:
        return default
    raise ValueError(f"bad boolean value ({s!r}), expected true/false")
