import configparser
import logging.config
import os
from typing import Dict, Optional, Union


# Every option can be overridden by an environment variable
# named BOL<PERSON>TAAPI_section_option, e.g
# BOLDATAAPI_MAIN_APP_NAME,
# BOLDATAAPI_DB_SQLALCHEMY_URL
# BOLDATAAPI_DB_SQLALCHEMY_USE_CLIENT_CERT
# BOLDATAAPI_DB_VAULT_DB_USERNAME
DEFAULTS = '''
[main]
app_name = bol-data-api
base_url = /api/v1/boldata
host = 0.0.0.0
port = 5555
sentry_dsn =

[auth]
verify_requests = true
gluu_addresses =

[db]
sqlalchemy_url =
sqlalchemy_use_client_cert = False
vault_db_username =
vault_db_password =

[feature-flags]
validate_input = true
first_visited = true
swagger_api = true
on_azure = false
azure_ai = false
person_id_for_project_users = false
core = false
'''


Filename = str
ConfigValue = Union[str, int, bool]
ConfigSection = Dict[str, ConfigValue]
ConfigDict = Dict[str, ConfigSection]
Config = configparser.RawConfigParser


_config: Optional[Config] = None


def get_config() -> Config:
    global _config
    # Read configuration
    if _config is None:
        raise RuntimeError('You need to call set_config() first!')

    return _config


def set_config(config: Union[str, ConfigDict, None], ignore_env: bool = False) -> Config:
    global _config

    _config = configparser.RawConfigParser()
    _config.read_string(DEFAULTS)

    if isinstance(config, str):
        _config.read(config)
    elif config is not None:
        _config.read_dict(config)

    if not ignore_env:
        for section in _config.sections():
            for option, value in _config.items(section):
                varname = f"BOLDATAAPI_{section}_{option}".upper().replace('-', '_')
                if varname in os.environ:
                    _config.set(section, option, os.environ[varname])

    # Configure logging only if configuration is provided.
    if _config.has_section('loggers'):
        # We can't disable existing loggers, because loggers are created at module level, before
        # this line.
        logging.config.fileConfig(_config, disable_existing_loggers=False)

    return _config


def reset_config() -> None:
    global _config
    _config = None
