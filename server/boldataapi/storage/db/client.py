import logging
import os

import sqlalchemy
import citext  # noqa: import-time side effects make sqlalchemy recognize 'citext' type

import boldataapi
from boldataapi.featureflags import feature_active
from .engine import SQLAlchemyEngine, wait_for_engine


logger = logging.getLogger(__name__)


def get_vault_db_credentials():
    config = boldataapi.get_config()
    return (config.get('db', 'vault_db_username'), config.get('db', 'vault_db_password'))


def get_db_client_connect_args(sqlalchemy_use_client_cert):
    if sqlalchemy_use_client_cert:
        user, _ = get_vault_db_credentials()
        connect_args = {
            'user': user,
            'sslmode': 'verify-ca',
            'sslrootcert': '/etc/bolfak/secrets/root.crt',
            'sslcert': '/etc/bolfak/secrets/super.crt',
            # libpq-ssl will check super.key and refuses to use the secret key
            # if it allows any access to world or group, as documented in
            # https://www.postgresql.org/docs/9.1/libpq-ssl.html.
            #
            # super.key is a symlic to a real file mounted in auto-generated
            # subfolder `..data/super.key` and permissions 0600 are applied to
            # the real file, not to the symlink:
            'sslkey': os.path.realpath('/etc/bolfak/secrets-600/super.key'),
        }
        logger.info('Connecting to VaultDB with SSL client certificate...')
    else:
        user, password = get_vault_db_credentials()
        connect_args = {
            'user': user,
            'password': password,
        }
        if feature_active('on_azure'):
            connect_args.update({
                'sslmode': 'verify-full',
                'sslrootcert': '/etc/bolfak/certs/combined.pem',
            })

        logger.info('Connecting to VaultDB with basic auth...')
    return connect_args


def setup_db_client(sqlalchemy_url, sqlalchemy_use_client_cert=False) -> SQLAlchemyEngine:
    try:
        connect_args = get_db_client_connect_args(sqlalchemy_use_client_cert)
        engine = sqlalchemy.create_engine(sqlalchemy_url, connect_args=connect_args)
        logger.info('VaultDB engine created: %s', engine)
    except Exception:
        logger.warning('Connecting to VaultDB at %s failed', sqlalchemy_url)
        raise
    wait_for_engine(engine, sqlalchemy_url)
    db = SQLAlchemyEngine(engine)
    return db
