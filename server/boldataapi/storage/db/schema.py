# Database constants used in code

# NB: please update together with constants mirrored in SQL:
#   /db/*.sql
DBSCHEMA = 'public.'

# We use the following magic because SQLAlchemy `db.engine.reflect(engine,
# schema=DBSCHEMA)` fails to load tables for `schema='public.'` but loads tables
# just fine for `schema='bol.'`.
if DBSCHEMA == 'public.':
    DBSCHEMA = ''

# Tables
ALEMBIC_VERSION_TABLE = DBSCHEMA + 'alembic_version'
INTERNAL_PROJECT_IDS_TABLE = DBSCHEMA + 'internal_project_ids'
NOTIFICATION_REPORTS_TABLE = DBSCHEMA + 'notification_reports'
PROJECTS_TABLE = DBSCHEMA + 'projects'
PROJECT_USERS_TABLE = DBSCHEMA + 'project_users'
REPORT_ACCESSES_TABLE = DBSCHEMA + 'report_accesses'
STATUS_REPORTS_HISTORY_TABLE = DBSCHEMA + 'status_reports_history'
STATUS_REPORTS_TABLE = DBSCHEMA + 'status_reports'
SUPPLIERS_TABLE = DBSCHEMA + 'suppliers'
SUPPLIER_CONTACTS_TABLE = DBSCHEMA + 'supplier_contacts'
BULK_IMPORT_JOBS_TABLE = DBSCHEMA + 'bulk_import_jobs'
PREANNOUNCEMENTS_TABLE = DBSCHEMA + 'preannouncements'
PREANNOUNCEMENT_FORMS_TABLE = DBSCHEMA + 'preannouncement_forms'
REPORT_CACHE_TABLE = DBSCHEMA + 'report_cache'
CREDITSAFE_ACCOUNT_TABLE = DBSCHEMA + 'creditsafe_account'
CREDITSAFE_ACCOUNT_HISTORY_TABLE = DBSCHEMA + 'creditsafe_account_history'
PROJECT_SUPPLIER_COMMENTS_TABLE = DBSCHEMA + "project_supplier_comments"
PROJECT_COMMENT_VIEWERS_TABLE = DBSCHEMA + "project_comment_viewers"

# Views that pretend to be Qvarn-identical tables
QVARN_BOL_SUPPLIER_VIEW = DBSCHEMA + 'qvarn_bol_supplier'
QVARN_PROJECT_VIEW = DBSCHEMA + 'qvarn_project'
QVARN_REPORT_ACCESS_VIEW = DBSCHEMA + 'qvarn_report_access'
QVARN_REPORT_VIEW = DBSCHEMA + 'qvarn_report'
