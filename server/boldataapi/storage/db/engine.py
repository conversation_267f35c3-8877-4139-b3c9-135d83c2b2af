import logging
import time

import sqlalchemy
from sqlalchemy.orm import scoped_session, sessionmaker

from .schema import DBSCHEMA


DEFAULT_RETRIES = 4
DEFAULT_RETRY_INTERVAL = 1
DEFAULT_RETRY_BACKOFF = 2

logger = logging.getLogger(__name__)


class SQLAlchemyEngine(object):

    def __init__(self, engine, init_meta=True):
        self.engine = engine
        self.session_factory = sessionmaker(bind=engine)
        self.meta = sqlalchemy.MetaData()
        self.session = scoped_session(self.session_factory)

        if init_meta:
            self._init_meta()

    def _init_meta(self):
        # For DB reflection we use
        if DBSCHEMA:
            self.meta.reflect(self.engine, schema=DBSCHEMA)
        else:
            self.meta.reflect(self.engine)
        # If we wanted to define the tables in Python, we'd use
        # self.meta.create_all(self.engine)


def wait_for_engine(engine, sqlalchemy_url=None):
    """Wait a bit for engine to come up."""
    logger.info('Waiting for VaultDB engine to come up...')
    connected = False
    interval = DEFAULT_RETRY_INTERVAL
    for i in range(DEFAULT_RETRIES):
        try:
            engine.execute('SELECT 1;')
        except Exception as e:
            if i == DEFAULT_RETRIES - 1:
                logger.warning("Failed to connect to %s (%s), giving up",
                               f'VaultDB at {sqlalchemy_url}' if sqlalchemy_url else 'VaultDB',
                               e.__class__.__name__)
                raise
            logger.warning("Failed to connect to %s (%s), retrying after %s seconds",
                           f'VaultDB at {sqlalchemy_url}' if sqlalchemy_url else 'VaultDB',
                           e.__class__.__name__, interval)
            time.sleep(interval)
            interval *= DEFAULT_RETRY_BACKOFF
        else:
            connected = True
            break
    if connected:
        logger.info('Connecting to VaultDB succeeded')
    else:
        logger.info('Connecting to VaultDB failed')
