# backend_app.py - implement main parts of a backend application
#
# Copyright 2015, 2016 Suomen Tilaajavastuu Oy
# Copyright 2017 Vaultit AB
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Affero General Public License for more details.
#
# You should have received a copy of the GNU Affero General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.


from boldataapi.storage import qvarn as qvarn


log = qvarn.StructuredLog()
log.add_log_writer(qvarn.StdoutSlogWriter(oneline=True), qvarn.FilterAllow())
##qvarn.hijack_logging(log)
