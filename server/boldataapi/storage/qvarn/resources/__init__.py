from .bol_supplier import bol_suppliers as bol_suppliers
from .project import projects as projects
from .report_accesses import report_accesses as report_accesses
from .reports import reports as reports


SUPPORTED_RESOURCES = [bol_suppliers, projects, report_accesses, reports]

RESOURCES = {}
for r in SUPPORTED_RESOURCES:
   for name, resource in r.items():
       RESOURCES[name] = resource
RESOURCE_NAMES = [name for name in RESOURCES.keys()]


class QvarnResourceMeta(object):
    def __init__(self, resource_name):
        if resource_name not in RESOURCE_NAMES:
            raise ValueError('Resource not found: {}'.format(resource_name))

        self.resource = RESOURCES.get(resource_name)
        self.type = self.resource['type']
        self.schema = self.prototype = self.resource['schema']


def get_resource(resource_name):
    return QvarnResourceMeta(resource_name)
