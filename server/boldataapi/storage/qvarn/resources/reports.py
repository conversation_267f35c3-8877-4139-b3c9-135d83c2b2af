# A dictified copy of
# https://git.vaultit.org/qvarn/resources/blob/master/resource-types/reports.yaml

from boldataapi.storage.db import QVARN_REPORT_VIEW


RESOURCE_NAME = 'reports'
RESOURCE = {
    'type': QVARN_REPORT_VIEW,
    'schema': {
        'id': '',
        'type': '',
        'revision': '',
        'org': '',
        'interested_org_id': '',
        'generated_timestamp': '',
        'tilaajavastuu_status': '',
        'report_type': '',
        'status': '',
        'service_provider': '',
        'report_version': '',
        'archive_code': '',
        'valid_from_date': '',
        'valid_until_date': '',
        'oldest_source_date': '',
        'interpretation': '',
        'notes': '',
        # 'notes': [
        #     {
        #         'note': '',
        #         'note_type': '',
        #     },
        # ],
        'company_information': '',
        # 'company_information':
        # [
        #     {
        #         'org_id': '',
        #         'names':
        #         [
        #             {
        #                 'name': '',
        #                 'source': '',
        #                 'update_time': '',
        #             },
        #         ],
        #         'gov_org_ids':
        #         [
        #             {
        #                 'country': '',
        #                 'org_id_type': '',
        #                 'gov_org_id': '',
        #             },
        #         ],
        #         'domicile': '',
        #         'main_line_of_business': '',
        #         'legal_entity_type': '',
        #         'company_status': '',
        #         'interpretation': '',
        #         'status': '',
        #         'notes':
        #         [
        #             {
        #                 'note': '',
        #                 'note_type': '',
        #             },
        #         ],
        #     },
        # ],
        'company_relations': '',
        # 'company_relations':
        # [
        #     {
        #         'interpretation': '',
        #         'status': '',
        #         'notes':
        #         [
        #             {
        #                 'note': '',
        #                 'note_type': '',
        #             },
        #         ],
        #         'persons':
        #         [
        #             {
        #                 'person_id': '',
        #                 'full_name': '',
        #                 'source': '',
        #                 'date_of_birth': '',
        #                 'role': '',
        #                 'business_ban_start_date': '',
        #                 'business_ban_end_date': '',
        #             },
        #         ],
        #         'orgs':
        #         [
        #             {
        #                 'org_id': '',
        #                 'name': '',
        #                 'source': '',
        #                 'role': '',
        #             }
        #         ],
        #     },
        # ],
        'registry_memberships': '',
        # 'registry_memberships':
        # [
        #     {
        #         'interpretation': '',
        #         'status': '',
        #         'source': '',
        #         'update_date': '',
        #         'registry_memberships':
        #         [
        #             {
        #                 'registry': '',
        #                 'registration_start_date': '',
        #                 'registration_end_date': '',
        #                 'interpretation': '',
        #                 'status': '',
        #                 'vat_type': '',
        #                 'next_check_date': '',
        #             },
        #         ],
        #     },
        # ],
        'registry_information': '',
        # 'registry_information':
        # [
        #     {
        #         'interpretation': '',
        #         'status': '',
        #         'notes':
        #         [
        #             {
        #                 'note': '',
        #                 'note_type': '',
        #             },
        #         ],
        #         'source': '',
        #         'registry_extract_id': '',
        #         'registry_extract_type': '',
        #         'registry_extract_date': '',
        #     },
        # ],
        'reported_information': '',
        # 'reported_information':
        # [
        #     {
        #         'interpretation': '',
        #         'status': '',
        #         'notes':
        #         [
        #             {
        #                 'note': '',
        #                 'note_type': '',
        #             },
        #         ],
        #         'information_type': '',
        #         'reported_data':
        #         [
        #             {
        #                 'source': '',
        #                 'update_date': '',
        #                 'status': '',
        #                 'name': '',
        #                 'issued_date': '',
        #                 'insurer': '',
        #                 'insurance_type': '',
        #                 'valid_from_date': '',
        #                 'valid_until_date': '',
        #                 'validity_area': '',
        #             },
        #         ],
        #     },
        # ],
        'certificates': '',
        # 'certificates':
        # [
        #     {
        #         'certificate_type': '',
        #         'external_certificate_id': '',
        #         'interpretation': '',
        #         'status': '',
        #         'notes':
        #         [
        #             {
        #                 'note': '',
        #                 'note_type': '',
        #             },
        #         ],
        #         'created_date': '',
        #         'update_date': '',
        #     },
        # ],
        'ratings': '',
        # 'ratings':
        # [
        #     {
        #         'rating_type': '',
        #         'source': '',
        #         'update_date': '',
        #         'notes':
        #         [
        #             {
        #                 'note': '',
        #                 'note_type': '',
        #             },
        #         ],
        #     },
        # ],
        'operating_licenses': '',
        # 'operating_licenses':
        # [
        #     {
        #         'source': '',
        #         'update_date': '',
        #         'license_types': '',
        #         'licenses':
        #         [
        #             {
        #                 'license_type': '',
        #                 'license_count': 0,
        #                 'vehicle_category': '',
        #                 'location': '',
        #             },
        #         ],
        #     },
        # ],
    },
}

reports = {RESOURCE_NAME: RESOURCE}
