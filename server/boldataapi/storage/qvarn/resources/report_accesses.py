# A dictified copy of
# https://git.vaultit.org/qvarn/resources/blob/master/resource-types/report_accesses.yaml

from boldataapi.storage.db import QVARN_REPORT_ACCESS_VIEW


RESOURCE_NAME = 'report_accesses'
RESOURCE = {
    'type': QVARN_REPORT_ACCESS_VIEW,
    'schema': {
        'id': '',
        'type': '',
        'revision': '',
        'access_time': '',
        'arkisto_id': '',
        'client_id': '',
        'report_id': '',
        'customer_org_id': '',
        'org_id': '',
        'person_id': '',
        'status': '',
        'language': '',
        'template_version': '',
        'gov_org_ids': [
            {
                'country': '',
                'org_id_type': '',
                'gov_org_id': '',
            },
        ],
    }
}

report_accesses = {RESOURCE_NAME: RESOURCE}
