# A dictified copy of
# https://git.vaultit.org/qvarn/resources/blob/master/resource-types/bol_suppliers.yaml

from boldataapi.storage.db import QVARN_BOL_SUPPLIER_VIEW

RESOURCE_NAME = 'bol_suppliers'
RESOURCE = {
    'type': QVARN_BOL_SUPPLIER_VIEW,
    'schema': {
        'id': '',
        'type': '',
        'revision': '',
        'project_resource_id': '',
        'internal_project_id': '',
        'supplier_type': u'',
        'parent_supplier_id': '',
        'parent_org_id': '',
        'supplier_org_id': '',
        'contract_start_date': '',
        'contract_end_date': '',
        'materialized_path': [''],
        'bolagsfakta_status': '',
        'supplier_role': '',  # supervisor, main_contractor, supplier
        'supplier_contacts': [
            {
                'supplier_contact_person_id': '',
                'supplier_contact_email': '',
            },
        ],
    }
}

bol_suppliers = {RESOURCE_NAME: RESOURCE}
