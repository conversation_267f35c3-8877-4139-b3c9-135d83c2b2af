# A dictified copy of
# https://git.vaultit.org/qvarn/resources/blob/master/resource-types/projects.yaml

from boldataapi.storage.db import QVARN_PROJECT_VIEW


RESOURCE_NAME = 'projects'
RESOURCE = {
    'type': QVARN_PROJECT_VIEW,
    'schema': {
        'id': '',
        'type': '',
        'revision': '',
        'names': [''],
        'project_responsible_org': '',
        'project_responsible_person': '',
        'project_ids': [
            {
                'project_id_type': '',
                'project_id': '',
            },
        ],
        'state': '',
        'start_date': '',
        'end_date': '',
        'country': '',
    }
}

projects = {RESOURCE_NAME: RESOURCE}
