# __init__.py - manage package namespace for Qvarn
#
# Copyright 2015, 2016 Suomen Tilaajavastuu Oy
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Affero General Public License for more details.
#
# You should have received a copy of the GNU Affero General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

from .slog import (
    StructuredLog,
#     SlogWriter,
#     NullSlogWriter,
#     FileSlogWriter,
#     SyslogSlogWriter,
    StdoutSlogWriter,
    hijack_logging,
)

from .slog_filter import (
    FilterAllow,
#     FilterDeny,
#     FilterFieldHasValue,
#     FilterFieldValueRegexp,
#     FilterHasField,
#     FilterInclude,
#     FilterAny,
#     construct_log_filter,
)

from .exc import (
    QvarnException,
)

from .http_statuses import (
    HTTPError,
    BadRequest,
#     Unauthorized,
#     Forbidden,
    NotFound,
#     Conflict,
    LengthRequired,
    UnsupportedMediaType,
)

from .counter import (
    Counter,
)

from .walker import (
    ItemWalker,
    TooDeeplyNestedPrototype,
)

# from .validate import (
#     ItemValidator,
#     ValidationError,
# )

# from .basic_validation_plugin import (
#     BasicValidationPlugin,
#     NoItemRevision,
#     ContentIsNotJSON,
#     ContentTypeIsNotJSON,
# )

from .sql import (
    SqliteAdapter,
    PostgresAdapter,
    PostgresAdapterSimplified,
    column_types,
)

from .measurement import (
    Measurement,
)

from .transaction import (
    Transaction,
)

from .dbconn import (
    DatabaseConnection,
)

from .subitem_protos import (
    SubItemPrototypes,
)

from .read_only import (
    ReadOnlyStorage,
    ItemDoesNotExist,
    FieldNotInResource,
    create_search_param,
)

from .backend_app import (
#    BackendApplication,
#    get_configuration,
    log,
#    DEFAULT_CONFIG,
)

# from .filler import (
#     add_missing_item_fields,
# )

from .util import (
    get_resource_type_from_path,
    route_to_scope,
    table_name,
    ComplicatedTableNameError,
    create_tables_for_resource_type,
)

# from .simple_resource import (
#     SimpleResource,
# )

from .list_resource import (
    ListResource,
)

from .idgen import (
    ResourceIdGenerator,
)

# from .string_to_unicode_plugin import (
#     StringToUnicodePlugin,
# )

# from .file_resource import (
#     FileResource,
#     ContentLengthMissing,
#     InvalidContentType,
# )

from .error_transform_plugin import (
    ErrorTransformPlugin,
)

from .schema import (
    schema_from_prototype,
)

# from .versioned_storage import (
#    VersionedStorage,
#    get_current_tables,
# )
