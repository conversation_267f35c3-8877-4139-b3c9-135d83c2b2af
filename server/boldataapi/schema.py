import datetime
import uuid
from itertools import groupby
from typing import (
    Any, Dict,
    Iterable, Optional, overload
)

from cerberus import Validator

from boldataapi.featureflags import feature_active


REPORT_TYPE_STATUS_REPORT = 'bolagsfakta.company_report'
REPORT_TYPE_NOTIFICATION_REPORT = 'bolagsfakta.status_change_report'

ALLOWED_PROVIDERS = ['creditsafe', 'creditsafe_v2',
                     'creditsafe_ggs', 'creditsafe_connect',
                     'creditsafe_connect_core',
                     'bisnode', 'skatteverket']

ALLOWED_SUPPLIERS_SHOW = [
        'id',
        'project_resource_id',
        'supplier_role',
        'supplier_type',
        'contract_start_date',
        'contract_end_date',
        'contract_type',
        'contract_work_areas',
        'materialized_path',
        'pa_id',
        'pa_status',
        'internal_project_id',
        'parent_supplier_id',
        'parent_org_id',
        'supplier_org_id',
        'supplier_contacts',
        'bolagsfakta_status',
        'type',
        'first_visited',
        'last_visited',
        'visitor_type',
]


REPORT_CACHE_TYPES = ['statusreports_raw', 'statusreports_parsed']

STATUS_STOP = '100 STOP'
STATUS_INVESTIGATE = '200 INVESTIGATE'
STATUS_INCOMPLETE = '300 INCOMPLETE'
STATUS_ATTENTION = '400 ATTENTION'
STATUS_OK = '500 OK'

REPORT_STATUSES = [
    STATUS_STOP, STATUS_INVESTIGATE, STATUS_INCOMPLETE, STATUS_ATTENTION, STATUS_OK
]

# BOL STATUSES
BOL_STATUS_STOP = 'stop'
BOL_STATUS_INVESTIGATE = 'investigate'
BOL_STATUS_INCOMPLETE = 'incomplete'
BOL_STATUS_ATTENTION = 'attention'
BOL_STATUS_OK = 'ok'

BOL_STATUSES = [BOL_STATUS_STOP, BOL_STATUS_INVESTIGATE, BOL_STATUS_INCOMPLETE,
                BOL_STATUS_ATTENTION, BOL_STATUS_OK]


PROJECT_ID_TYPE = 'trafikverket_project_id'
TAX_ID = 'tax_id'

# supplier_type - partitioning by sub-trees
LINKED = 'linked'
UNLINKED = 'unlinked'
VISITOR = 'visitor'

SUPPLIER_TYPES = [LINKED, UNLINKED, VISITOR]

# supplier contract_type
CONTRACTING = 'contracting'
CONSULTING = 'consulting'
PERSONNEL_LEASING = 'personnel_leasing'
MACHINE_EQUIPMENT_LEASING = 'machine_equipment_leasing'
MATERIALS_AND_PRODUCTS = 'materials_and_products'
TRANSPORTATION = 'transportation'

SUPPLIER_CONTRACT_TYPES = [CONTRACTING, CONSULTING, PERSONNEL_LEASING,
                           MACHINE_EQUIPMENT_LEASING, MATERIALS_AND_PRODUCTS, TRANSPORTATION]

# supplier contract work areas
CLEANING = 'cleaning'
DEMOLITION = 'demolition'
SANITATION = 'sanitation'
DRYWALL = 'drywall'
ASSEMBLY = 'assembly'
SCAFFOLDING = 'scaffolding'
LAND_WORKS = 'land_works'
CASTING_ALL_MATERIALS = 'casting_all_materials'
FRAMEWORK = 'framework'
MASONRY_AND_PLASTERING = 'masonry_and_plastering'
OTHER = 'other'

SUPPLIER_CONTRACT_WORK_AREAS = [
    CLEANING, DEMOLITION, SANITATION, DRYWALL, ASSEMBLY, SCAFFOLDING,
    LAND_WORKS, CASTING_ALL_MATERIALS, FRAMEWORK, MASONRY_AND_PLASTERING, OTHER
]

# Supplier role in a project
CLIENT_ROLE = 'client'
SUPERVISOR_ROLE = 'supervisor'
MAIN_CONTRACTOR_ROLE = 'main_contractor'
SUPPLIER_ROLE = 'supplier'
SUPPLIER_ROLES = [SUPERVISOR_ROLE, MAIN_CONTRACTOR_ROLE, SUPPLIER_ROLE]
TOP_LEVEL_SUPPLIER_ROLES = [SUPERVISOR_ROLE, MAIN_CONTRACTOR_ROLE]
TOP_LEVEL_SUPPLIER_ROLE = 'top_level'  # not stored in DB - meta-role
ADMIN_ROLE = 'admin'  # not a domain role - backoffice role

# Preannouncements
PA_STATUS_CREATED = 'created'
PA_STATUS_REGISTERED = 'registered'
PA_STATUS_CONFIRMED = 'confirmed'
PA_STATUS_REJECTED = 'rejected'
PA_STATUSES = [PA_STATUS_CREATED, PA_STATUS_REGISTERED, PA_STATUS_CONFIRMED,
               PA_STATUS_REJECTED]

# `reports` fields for Qvarn compatibility
QVARN_COMPAT_FIELDS_FOR_REPORTS: Dict[str, Any] = {
    'archive_code': None,
    'certificates': [],
    'company_information': [],
    'company_relations': [],
    'interpretation': None,
    'notes': [],
    'oldest_source_date': None,
    'operating_licenses': [],
    'ratings': [],
    'registry_information': [],
    'registry_memberships': [],
    'report_version': None,
    'reported_information': [],
    'service_provider': None,
    'status': None,
    'valid_from_date': None,
    'valid_until_date': None,
    'revision': None,
}

QVARN_COMPAT_FIELDS_FOR_PROJECTS: Dict[str, Any] = {
    'type': 'project',
    'country': None,
    'revision': None,
    'sync': {},
}

RAW = 'raw'
NONPAED = 'nonpaed'
VISITOR_TYPES = [RAW, NONPAED]

CREDITSAFE_ACCOUNT_STATE_PENDING = 'pending'
CREDITSAFE_ACCOUNT_STATE_ACTIVE = 'active'
CREDITSAFE_ACCOUNT_STATE_INACTIVE = 'inactive'
CREDITSAFE_ACCOUNT_STATES = [
    CREDITSAFE_ACCOUNT_STATE_PENDING,
    CREDITSAFE_ACCOUNT_STATE_ACTIVE,
    CREDITSAFE_ACCOUNT_STATE_INACTIVE,
]


# `projects` fields for Qvarn compatibility
class BaseValidator(Validator):

    validation_can_be_disabled_by_feature_flag = False

    def _validate_empty_string(self, allow_empty, field, value):
        """Validate project names against empty string.

        Check if string is provided and is not empty, padded string.

        The rule's arguments are validated against this schema:
        {'type': 'boolean'}
        """
        if not allow_empty and value is not None and (not value or not value.strip()):
            self._error(field, 'empty values not allowed')
            self._drop_remaining_rules('allowed')

    def validate(self, *args, **kwargs):
        if self.validation_can_be_disabled_by_feature_flag and not feature_active('validate_input'):
            return True
        return Validator.validate(self, *args, **kwargs)


class BolSupplierValidator(BaseValidator):
    validation_can_be_disabled_by_feature_flag = True


class BulkImportJobValidator(BaseValidator):
    pass


class ReportValidator(BaseValidator):
    validation_can_be_disabled_by_feature_flag = True


class ReportAccessValidator(BaseValidator):
    validation_can_be_disabled_by_feature_flag = True


class ProjectValidator(BaseValidator):
    validation_can_be_disabled_by_feature_flag = True

    def _validate_max_occurrence_per_type(self, max_occurrence, field, value):
        """Validate project_ids list.

        Check that we don't have too many items with the same project_id_type value.

        The rule's arguments are validated against this schema:
        {'type': 'integer'}
        """
        if value and len(value) > max_occurrence:
            value.sort(key=lambda x: x['project_id_type'])
            for k, v in groupby(value, key=lambda x: x['project_id_type']):
                if len([d['project_id'] for d in v]) > max_occurrence:
                    self._error(
                        field,
                        "Every project_id_type can occur in list only {} times".format(
                            max_occurrence
                        )
                    )

    def _validate_required_on_field_value(self, dep_field, field, value):
        # https://jira.tilaajavastuu.fi/browse/BOL-3092
        """Validate project_ids list.

        Check if project_ids are set when state is active.

        The rule's arguments are validated against this schema:
        {'type': 'dict', 'maxlength': 1}
        """
        if not value and dep_field:
            # we support one pair of key => value for now
            dep_field_key = list(dep_field.keys())[0]
            if (
                dep_field_key in self.document.keys()
                and self.document[dep_field_key] == dep_field[dep_field_key]
            ):
                self._error(
                    field, f'Field is required when {dep_field_key} is {dep_field[dep_field_key]}'
                )


class ProjectUserValidator(BaseValidator):
    pass


class PreannouncementValidator(BaseValidator):
    pass


class ReportCacheValidator(BaseValidator):
    validation_can_be_disabled_by_feature_flag = True


class CreditsafeAccountValidator(BaseValidator):
    validation_can_be_disabled_by_feature_flag = True

    def validate(self, *args, **kwargs):
        if not args[0]:
            return False
        return super().validate(*args, **kwargs)


class ProjectSupplierCommentValidator(BaseValidator):
    pass


class ProjectCommentViewerValidator(BaseValidator):
    pass


@overload
def to_date(s: str) -> datetime.datetime:
    pass


@overload
def to_date(s: None) -> None:
    pass


def to_date(s: Optional[str]) -> Optional[datetime.datetime]:
    if s is None:
        return None
    # XXX: somebody please explain why this is returning a datetime and not a date
    return datetime.datetime.strptime(s, '%Y-%m-%d')


@overload
def to_datetime(s: str) -> datetime.datetime:
    pass


@overload
def to_datetime(s: None) -> None:
    pass


def to_datetime(s: Optional[str]) -> Optional[datetime.datetime]:
    return _parse_datetime(s, ['%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'])


def _parse_datetime(s: Optional[str], formats: Iterable[str]) -> Optional[datetime.datetime]:
    if s is None:
        return None
    first_error = None
    for fmt in formats:
        try:
            return datetime.datetime.strptime(s, fmt)
        except ValueError as error:
            if first_error is None:
                first_error = error
    assert first_error is not None
    raise first_error


@overload
def to_datetimetz(s: str) -> datetime.datetime:
    pass


@overload
def to_datetimetz(s: None) -> None:
    pass


def to_datetimetz(s: Optional[str]) -> Optional[datetime.datetime]:
    return _parse_datetime(s, [
        '%Y-%m-%dT%H:%M:%S' + optional_microseconds + optional_tz
        for optional_microseconds in ['.%f', '']
        for optional_tz in ['%z', '']
    ])


Schema = Dict[str, Dict[str, Any]]


schemas_new_bol_supplier_supplier_contacts = [
    # both fields are required
    {
        'supplier_contact_person_id': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
        'supplier_contact_email': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
    },
    # only supplier_contact_person_id is required,
    # supplier_contact_email is optional,
    {
        'supplier_contact_person_id': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
        'supplier_contact_email': {
            'type': 'string',
            'nullable': True,
        },
    },
    # only supplier_contact_email is required,
    # supplier_contact_person_id is optional
    {
        'supplier_contact_person_id': {
            'type': 'string',
            'nullable': True,
        },
        'supplier_contact_email': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
    },
]


schemas_existing_bol_supplier_supplier_contacts = [
    # both fields are required
    {
        'supplier_contact_person_id': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
        'supplier_contact_email': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
    },
    # only supplier_contact_person_id is required,
    # supplier_contact_email is optional,
    {
        'supplier_contact_person_id': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
        'supplier_contact_email': {
            'type': 'string',
            'nullable': True,
        },
    },
    # only supplier_contact_email is required,
    # supplier_contact_person_id is optional
    {
        'supplier_contact_person_id': {
            'type': 'string',
            'nullable': True,
        },
        'supplier_contact_email': {
            'type': 'string',
            'required': True,
            'empty_string': False,
        },
    },
    # both are nullable
    {
        'supplier_contact_person_id': {
            'type': 'string',
            'nullable': True,
        },
        'supplier_contact_email': {
            'type': 'string',
            'nullable': True,
        },
    },
]


schema_new_bol_supplier: Schema = {
    'supplier_role': {
        'type': 'string',
        'required': True,
        'allowed': SUPPLIER_ROLES,
    },
    'supplier_type': {
        'type': 'string',
        'required': True,
        'allowed': SUPPLIER_TYPES,
    },
    'contract_start_date': {
        'type': 'date',
        'nullable': True,
        'coerce': to_date,
    },
    'contract_end_date': {
        'type': 'date',
        'nullable': True,
        'coerce': to_date,
    },
    'contract_work_areas': {
        'type': 'list',
        'nullable': True,
        'schema': {
            'type': 'string',
            'allowed': SUPPLIER_CONTRACT_WORK_AREAS,
        },
    },
    'materialized_path': {
        'type': 'list',
        'required': True,
        'schema': {
            'type': 'string',
            'empty_string': False,
        },
    },
    'project_resource_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'parent_supplier_id': {
        'type': 'string',
        'nullable': True,
    },
    'parent_org_id': {
        'type': 'string',
        'nullable': True,
    },
    'supplier_org_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'revision': {
        'type': 'string',
        'nullable': True,
    },
    'internal_project_id': {
        'type': 'string',
        'nullable': True,
    },
    'type': {
        'type': 'string',
        'allowed': ['bol_supplier'],
    },
    'last_visited': {
        'type': 'datetime',
        'required': False,
        'coerce': to_datetime,
        'nullable': True,
    },
    'first_visited': {
        'type': 'datetime',
        'required': False,
        'coerce': to_datetime,
        'nullable': True,
    },
    'supplier_contacts': {
        'type': 'list',
        'schema': {
            'type': 'dict',
            'anyof_schema': schemas_new_bol_supplier_supplier_contacts,
        },
    },
    'contract_type': {
        'type': 'string',
        'required': False,
        'allowed': SUPPLIER_CONTRACT_TYPES,
        'nullable': True,
    },
    'pa_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'pa_status': {
        'type': 'string',
        'required': False,
        'nullable': True,
        'empty_string': False,
        'allowed': PA_STATUSES,
    },
    'visitor_type': {
        'type': 'string',
        'required': False,
        'allowed': VISITOR_TYPES,
        'nullable': True,
    },
    'is_one_man_company': {
        'type': 'boolean',
        'required': False,
        'nullable': True,
    },
    'has_collective_agreement': {
        'type': 'boolean',
        'required': False,
        'nullable': True,
    },
    'collective_agreement_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    }
}

schema_existing_bol_supplier: Schema = {
    **schema_new_bol_supplier,
    'supplier_role': {
        'type': 'string',
        'allowed': SUPPLIER_ROLES,
    },
    'supplier_type': {
        'type': 'string',
        'allowed': SUPPLIER_TYPES,
    },
    'revision': {
        'type': 'string',
        'required': True,
    },
    'materialized_path': {
        'type': 'list',
        'required': False,
        'schema': {
            'type': 'string',
            'empty_string': False,
        },
    },
    'project_resource_id': {
        'type': 'string',
        'empty_string': False,
    },
    'supplier_org_id': {
        'type': 'string',
        'nullable': True,
        'empty_string': False,
    },
    'bolagsfakta_status': {
        'type': 'string',
        'nullable': True,
    },
    'id': {
        'type': 'string',
    },
    'supplier_contacts': {
        'type': 'list',
        'schema': {
            'type': 'dict',
            'anyof_schema': schemas_existing_bol_supplier_supplier_contacts,
        },
    },
    'is_one_man_company': {
        'type': 'boolean',
        'required': False,
        'nullable': True,
    },
    'has_collective_agreement': {
        'type': 'boolean',
        'required': False,
        'nullable': True,
    },
    'collective_agreement_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    }

}


schema_new_report_access: Schema = {
    'customer_org_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'org_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'person_id': {
        'type': 'string',
        'required': False,
        'nullable': True
    },
    'status': {
        'type': 'string',
        'required': True,
        'allowed': [
            'active',
            'hidden',
        ]
    },
    'access_time': {
        'type': 'datetime',
        'required': True,
        'coerce': to_datetimetz,
    },
    'arkisto_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'report_id': {  # Qvarn compatibility
        'type': 'string',
        'required': False,
        'nullable': True
    },
    'language': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'client_id': {  # Qvarn compatibility
        'type': 'string',
        'required': False,
        'nullable': True
    },
    'template_version': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'gov_org_ids': {
        'type': 'list',
        'required': True,
        'maxlength': 1,
        'empty': False,
        'schema': {
            'type': 'dict',
            'schema': {
                'gov_org_id': {
                    'type': 'string',
                    'required': True,
                    'empty_string': False,
                },
                'country': {
                    'type': 'string',
                    'required': True,
                    'allowed': ['FI', 'EE'],
                },
                'org_id_type': {
                    'type': 'string',
                    'required': True,
                    'allowed': ['registration_number'],
                },
            },
        },
    },
}

schema_existing_report_access: Schema = {
    'customer_org_id': {
        'type': 'string',
    },
    'org_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'person_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'status': {
        'type': 'string',
        'allowed': [
            'active',
            'hidden',
        ]
    },
    'access_time': {
        'type': 'datetime',
        'coerce': to_datetimetz,
    },
    'arkisto_id': {
        'type': 'string',
    },
    'report_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'language': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'template_version': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'revision': {
        'type': 'string',
        'nullable': True,
    },
    'client_id': {  # Qvarn compatibility
        'type': 'string',
        'required': False,
        'nullable': True
    },
    'id': {  # Qvarn compatibility
        'type': 'string',
        'required': False,
        'nullable': True
    },
    'type': {  # Qvarn compantibility
        'type': 'string',
    },
    'gov_org_ids': {
        'type': 'list',
        'default': [],
        'maxlength': 1,
        'schema': {
            'type': 'dict',
            'schema': {
                'gov_org_id': {
                    'type': 'string',
                    'required': True,
                },
                'country': {
                    'type': 'string',
                    'required': True,
                    'allowed': ['FI', 'EE'],
                },
                'org_id_type': {
                    'type': 'string',
                    'required': True,
                    'allowed': ['registration_number'],
                },
            },
        },
    },

}


schema_new_report_cache: Schema = {
    'correlation_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'expires_at': {
        'type': 'datetime',
        'coerce': to_datetimetz,
        'required': False,
        'nullable': True,
    },
    'interested_org_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'key': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'provider': {
        'type': 'string',
        'required': True,
        'allowed': ALLOWED_PROVIDERS,
    },
    'type': {
        'type': 'string',
        'required': True,
        'allowed': REPORT_CACHE_TYPES,
    },
    'value': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
}

schema_existing_report_cache: Schema = {
    'correlation_id': {
        'type': 'string',
        'nullable': True,
    },
    'expires_at': {
        'type': 'datetime',
        'coerce': to_datetimetz,
        'nullable': True,
    },
    'interested_org_id': {
        'type': 'string',
        'nullable': True,
    },
    'key': {
        'type': 'string',
        'empty_string': False,
    },
    'provider': {
        'type': 'string',
        'allowed': ALLOWED_PROVIDERS,
    },
    'type': {
        'type': 'string',
        'allowed': REPORT_CACHE_TYPES,
    },
    'value': {
        'type': 'string',
        'empty_string': False,
    },
}


schema_new_project: Schema = {
    'client_contact_person_id': {
        'type': 'string',
        'nullable': True,
    },
    'client_contact_person_email': {
        'type': 'string',
        'nullable': True,
    },
    'created_by_org_id': {
        'type': 'string',
        'nullable': True,
    },
    'names': {
        'type': 'list',
        'required': True,
        'empty': False,
        'schema': {
            'type': 'string',
            'empty_string': False,
        },
    },
    'sync': {
        'nullable': True,
        'type': 'dict',
    },
    'project_responsible_org': {
        'required': True,
        'type': 'string',
        'empty_string': False,
    },
    'project_responsible_person': {
        'nullable': True,
        'type': 'string',
    },
    'project_ids': {
        'type': 'list',
        'default': [],
        'max_occurrence_per_type': 1,
        'required_on_field_value': {
            'state': 'active',
        },
        'schema': {
            'type': 'dict',
            'schema': {
                'project_id_type': {
                    'type': 'string',
                    'allowed': [PROJECT_ID_TYPE, TAX_ID],
                },
                'project_id': {'type': 'string'},
            },
        },
    },
    'start_date': {
        'type': 'date',
        'nullable': True,
        'coerce': to_date,
    },
    'end_date': {
        'type': 'date',
        'nullable': True,
        'coerce': to_date,
    },
    'state': {
        'type': 'string',
        'required': True,
        'allowed': [
            'draft',
            'active',
            'closed',
        ],
    },
    'pa_form_enabled': {
        'type': 'boolean',
    },
    'added_client_confirmed': {
        'type': 'boolean',
    },
    'project_creator_role': {
        'type': 'string',
        'nullable': True,
        'allowed': [CLIENT_ROLE, MAIN_CONTRACTOR_ROLE],
    },
    'added_client_can_view': {
        'type': 'boolean',
    }
}

schemas_existing_project_project_ids = [
    {
        'project_id_type': {
            'type': 'string',
            'allowed': [TAX_ID],
        },
        'project_id': {
            'type': 'string',
            'empty_string': False,
            'nullable': True,
        },
    },
    {
        'project_id_type': {
            'type': 'string',
            'allowed': [PROJECT_ID_TYPE],
        },
        'project_id': {
            'type': 'string',
            'nullable': True,
        },
    },
]

schema_existing_project: Schema = {
    **schema_new_project,
    'project_responsible_org': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'names': {
        'type': 'list',
        'empty': False,
        'schema': {
            'type': 'string',
            'empty_string': False,
        },
    },
    'id': {
        'type': 'string',  # Qvarn compatibility
    },
    'type': {
        'type': 'string',
    },
    'revision': {
        'type': 'string',  # Qvarn compatibility
        'nullable': True,
    },
    'country': {
        'type': 'string',  # Qvarn compatibility
        'nullable': True,
    },
    'project_ids': {
        'type': 'list',
        'empty': True,
        'max_occurrence_per_type': 1,
        'schema': {
            'type': 'dict',
            'anyof_schema': schemas_existing_project_project_ids,
        },
    },
    'state': {
        'type': 'string',
        'allowed': [
            'draft',
            'active',
            'closed',
        ],
    },
    'pa_form_enabled': {
        'type': 'boolean',
        # workaround for internal project id edit in pre-PA projects
        # https://vaultit.atlassian.net/browse/BOL-4616
        'nullable': True,
    },
    'added_client_confirmed': {
        'type': 'boolean',
        'nullable': True,
    },
    'added_client_can_view': {
        'type': 'boolean',
        'nullable': True,
    }
}


schema_new_project_user: Schema = {
    'id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'project_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'role': {
        'type': 'string',
        'allowed': ['leader', 'manager', 'member'],
        'required': True,
        'empty_string': False,
    },
    'notify': {
        'type': 'boolean',
        'required': True,
    },
    'user_account_id': {  # TODO: Remove when data is migrated, see BOL-5833
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'person_id': {
        'type': 'string',
        'required': False,  # TODO: Set to true when data is migrated, see BOL-5833
        'empty_string': False,
    },
    'represented_company_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
}

schema_update_project_user: Schema = {
    **schema_new_project_user,
    'project_id': {
        **schema_new_project_user['project_id'],
        'required': False,
        'nullable': True,
    },
    'role': {
        **schema_new_project_user['role'],
        'required': False,
        'nullable': True,
    },
    'notify': {
        **schema_new_project_user['notify'],
        'required': False,
        'nullable': True,
    },
    'user_account_id': {
        **schema_new_project_user['user_account_id'],
        'required': False,
        'nullable': True,
    },
    'person_id': {
        **schema_new_project_user['person_id'],
        'required': False,
        'nullable': True,
    },
    'represented_company_id': {
        **schema_new_project_user['represented_company_id'],
        'required': False,
        'nullable': True,
    },
}


schema_new_base_report: Schema = {
    'type': {
        'type': 'string',
        'required': True,
        'allowed': ['report'],
    },
    'report_type': {
        'type': 'string',
        'required': True,
        'allowed': [
            REPORT_TYPE_NOTIFICATION_REPORT,
            REPORT_TYPE_STATUS_REPORT,
        ]
    },
}

_schema_qvarn_compat_report: Schema = {
    field: {
        'nullable': True,
    }
    for field in (list(QVARN_COMPAT_FIELDS_FOR_REPORTS.keys()) + ['id'])
}

_schema_new_status_report: Schema = {
    'type': {
        'type': 'string',
        'required': True,
        'allowed': ['report'],
    },
    'report_type': {
        'type': 'string',
        'required': True,
        'allowed': [
            REPORT_TYPE_STATUS_REPORT,
        ]
    },
    'org': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'generated_timestamp': {
        'type': 'datetime',
        'required': True,
        'coerce': to_datetime,
    },
    'tilaajavastuu_status': {
        'type': 'string',
        'required': True,
        'allowed': REPORT_STATUSES,
    },
    'interested_org_id': {
        'type': 'string',
        'required': False,
    },
    'pdf': {
        'type': 'dict',
        'required': False,
        'schema': {
            'content_type': {
                'type': 'string',
                'allowed': ['application/json'],
                'required': True,
            },
            'body': {
                'type': 'string',
                'required': True,
                'empty_string': False,
            }
        }
    },
    'charge_reference': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'used_providers': {
        'type': 'list',
        'required': False,
        'nullable': True,
        'allowed': ALLOWED_PROVIDERS
    },
}
schema_new_status_report: Schema = _schema_new_status_report
schema_new_status_report.update(_schema_qvarn_compat_report)

_schema_new_notification_report: Schema = {
    'type': {
        'type': 'string',
        'required': True,
        'allowed': ['report'],
    },
    'report_type': {
        'type': 'string',
        'required': True,
        'allowed': [
            REPORT_TYPE_NOTIFICATION_REPORT,
        ]
    },
    'org': {
        'type': 'string',
        'empty_string': False,
    },
    'generated_timestamp': {
        'required': True,
        'type': 'datetime',
        'coerce': to_datetime,
    },
    'tilaajavastuu_status': {
        'type': 'string',
        'allowed': REPORT_STATUSES,
    },
    'interested_org_id': {
        'type': 'string',
        'empty_string': False,
    },
    'pdf': {
        'type': 'dict',
        'required': False,
        'schema': {
            'content_type': {
                'type': 'string',
                'required': True,
                'empty_string': False,
                'allowed': ['application/json'],
            },
            'body': {
                'type': 'string',
                'required': True,
                'empty_string': False,
            }
        }
    }
}
schema_new_notification_report: Schema = _schema_new_notification_report
schema_new_notification_report.update(_schema_qvarn_compat_report)

_schema_existing_status_report: Schema = {
    'type': {
        'type': 'string',
        'allowed': ['report'],
    },
    'report_type': {
        'type': 'string',
        'allowed': [
            REPORT_TYPE_STATUS_REPORT,
        ]
    },
    'org': {
        'type': 'string',
        'empty_string': False,
    },
    'generated_timestamp': {
        'type': 'datetime',
        'coerce': to_datetime,
    },
    'tilaajavastuu_status': {
        'type': 'string',
        'allowed': REPORT_STATUSES,
    },
    'interested_org_id': {
        'type': 'string',
        'empty_string': False,
    },
    'pdf': {
        'type': 'dict',
        'schema': {
            'content_type': {
                'type': 'string',
                'allowed': ['application/json'],
            },
            'body': {
                'type': 'string',
            },
        },
    },
    'charge_reference': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'used_providers': {
        'type': 'list',
        'required': False,
        'nullable': True,
        'allowed': ALLOWED_PROVIDERS,
    },
}

schema_existing_status_report: Schema = _schema_existing_status_report
schema_existing_status_report.update(_schema_qvarn_compat_report)

schema_reports_pdf: Schema = {
    # hacky: pdf is not a field inside pdf json.
    # cerberus requires to name objects we validate
    'pdf': {
        'type': 'dict',
        'required': True,
        'empty': False,
    }
}

_schema_existing_notification_report: Schema = {
    'type': {
        'type': 'string',
        'allowed': ['report'],
    },
    'report_type': {
        'type': 'string',
        'allowed': [
            REPORT_TYPE_NOTIFICATION_REPORT,
        ]
    },
    'org': {
        'type': 'string',
        'empty_string': False,
        'nullable': True,  # Old data compat
    },
    'generated_timestamp': {
        'type': 'datetime',
        'coerce': to_datetime,
    },
    'tilaajavastuu_status': {
        'type': 'string',
        'allowed': REPORT_STATUSES,
        'nullable': True,  # Old data compat
    },
    'interested_org_id': {
        'type': 'string',
        'empty_string': False,
        'nullable': True,  # Old data compat
    },
    'pdf': {
        'type': 'dict',
        'schema': {
            'content_type': {
                'type': 'string',
                'allowed': ['application/json'],
            },
            'body': {
                'type': 'string',
            }
        }
    }
}
schema_existing_notification_report: Schema = _schema_existing_notification_report
schema_existing_notification_report.update(_schema_qvarn_compat_report)


schema_move_report_to_history: Schema = {
    'interested_company_ids_to_company_ids': {
        'type': 'list',
        'required': True,
        'schema': {
            'type': 'dict',
            'required': True,
            'schema': {
                'interested_company_id': {
                    'type': 'string',
                    'required': True,
                    'empty_string': True,
                },
                'company_id': {
                    'type': 'string',
                    'required': True,
                    'empty_string': True,
                },
            }
        }
    }
}

BULK_IMPORT_JOB_STATUS_PENDING = 'pending'
BULK_IMPORT_JOB_STATUS_IN_PROGRESS = 'in_progress'
BULK_IMPORT_JOB_STATUS_FAILED = 'failed'
BULK_IMPORT_JOB_STATUS_DONE = 'done'

BULK_IMPORT_JOB_ORG_STATUS_PENDING = 'pending'
BULK_IMPORT_JOB_ORG_STATUS_CANCELED = 'canceled'
BULK_IMPORT_JOB_ORG_STATUS_ERROR = 'error'
BULK_IMPORT_JOB_ORG_STATUS_DONE = 'done'
BULK_IMPORT_JOB_ORG_STATUS_ALREADY_IN_PROJECT = 'already_in_project'
BULK_IMPORT_JOB_ORG_STATUSES = [
    BULK_IMPORT_JOB_ORG_STATUS_PENDING,
    BULK_IMPORT_JOB_ORG_STATUS_CANCELED,
    BULK_IMPORT_JOB_ORG_STATUS_ERROR,
    BULK_IMPORT_JOB_ORG_STATUS_DONE,
    BULK_IMPORT_JOB_ORG_STATUS_ALREADY_IN_PROJECT,
]

BULK_IMPORT_JOB_ORG_ACTION_ADD = 'add'
BULK_IMPORT_JOB_ORG_ACTION_SKIP = 'skip'
BULK_IMPORT_JOB_ORG_ACTIONS = [
    BULK_IMPORT_JOB_ORG_ACTION_ADD,
    BULK_IMPORT_JOB_ORG_ACTION_SKIP,
]


schema_update_bulk_import_job: Schema = {
    'status': {
        'type': 'string',
        'required': False,
        'allowed': [
            BULK_IMPORT_JOB_STATUS_PENDING,
            BULK_IMPORT_JOB_STATUS_IN_PROGRESS,
            BULK_IMPORT_JOB_STATUS_FAILED,
            BULK_IMPORT_JOB_STATUS_DONE,
        ],
    },
    'imported': {
        'type': 'datetime',
        'nullable': True,
        'coerce': to_datetimetz,
    },
    'canceled': {
        'type': 'datetime',
        'nullable': True,
        'coerce': to_datetimetz,
    },
    'companies': {
        'type': 'list',
        'required': False,
        'schema': {
            'type': 'list',
            'minlength': 8,
            'maxlength': 8,
            'items': [
                # company_gov_id
                {
                    'type': 'string',
                    'empty_string': False,
                },
                # company_id
                {
                    'type': 'string',
                    'nullable': True,
                },
                # status
                {
                    'type': 'string',
                    'allowed': BULK_IMPORT_JOB_ORG_STATUSES,
                },
                # source
                {
                    'type': 'string',
                    'nullable': True,
                },
                # error
                {
                    'type': 'string',
                    'nullable': True,
                },
                # action
                {
                    'type': 'string',
                    'allowed': BULK_IMPORT_JOB_ORG_ACTIONS,
                    'nullable': True,
                },
                # requested
                {
                    'type': 'datetime',
                    'nullable': True,
                    'coerce': to_datetimetz,
                },
                # completed
                {
                    'type': 'datetime',
                    'nullable': True,
                    'coerce': to_datetimetz,
                },
            ],
        },
    },
}

# When Core is enabled, the companies list has 10 elements. The last two elements are external_id
# (an id used to subscribe later) and name of the company.
schema_update_bulk_import_job_core: Schema = {
    **schema_update_bulk_import_job,
    'companies': {
        **schema_update_bulk_import_job['companies'],
        'schema': {
            **schema_update_bulk_import_job['companies']['schema'],
            'minlength': 10,
            'maxlength': 10,
            'items': [
                *schema_update_bulk_import_job['companies']['schema']['items'],
                # external_id, fetched from core that fetches from monitor
                {'type': 'string', 'nullable': True},
                # Name, fetched from core that fetches from monitor
                {'type': 'string', 'nullable': True},
            ],
        },
    },
}


schema_new_bulk_import_job: Schema = {
    **schema_update_bulk_import_job,
    'status': {
        **schema_update_bulk_import_job['status'],
        'default': BULK_IMPORT_JOB_STATUS_PENDING,
    },
    'companies': {
        **schema_update_bulk_import_job['companies'],
        'required': True,
    },
    'project_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'interested_org_id': {
        'type': 'string',
        'required': True,
        'empty_string': True,
    },
}

# When Core is enabled, the companies list has 10 elements. The last two elements are external_id
# (an id used to subscribe later) and name of the company.
schema_new_bulk_import_job_core: Schema = {
    **schema_new_bulk_import_job,
    'companies': {
        **schema_new_bulk_import_job['companies'],
        'schema': {
            **schema_new_bulk_import_job['companies']['schema'],
            'minlength': 10,
            'maxlength': 10,
            'items': [
                *schema_new_bulk_import_job['companies']['schema']['items'],
                # external_id, fetched from core that fetches from monitor
                {'type': 'string', 'nullable': True},
                # Name, fetched from core that fetches from monitor
                {'type': 'string', 'nullable': True},
            ],
        },
    },
}


schema_new_preannouncement: Schema = {
    'status': {
        'type': 'string',
        'required': True,
        'empty_string': False,
        'allowed': PA_STATUSES,
    },
    'project_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'created_by_supplier_id': {
        'type': 'string',
        'required': False,
    },
    'for_supplier_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'assigned_to_supplier_id': {
        'type': 'string',
        'required': False,
    },
    'assigned_to_company_id': {
        'type': 'string',
        'required': False,
    },
    'assigned_to_time': {
        'type': 'datetime',
        'coerce': to_datetimetz,
    },
    'active_pa_form': {
        'type': 'string',
        'required': False,
    },
}


schema_update_preannouncement: Schema = {
    'status': {
        'type': 'string',
        'required': False,
        'empty_string': False,
        'allowed': PA_STATUSES,
    },
    'project_id': {
        'type': 'string',
        'required': False,
        'empty_string': True,
    },
    'created_by_supplier_id': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'for_supplier_id': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'assigned_to_supplier_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'assigned_to_company_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'assigned_to_time': {
        'type': 'datetime',
        'nullable': True,
        'coerce': to_datetimetz,
    },
    'active_pa_form': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
}


preannouncement_new_fields = set(schema_new_preannouncement.keys())
preannouncement_update_fields = set(schema_update_preannouncement.keys())


schema_new_pa_form: Schema = {
    'company_name': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'company_gov_org_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'company_id_type': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'company_country': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'has_permanent_establishment': {
        'type': 'boolean',
    },
    'is_one_man_company': {
        'type': 'boolean',
    },
    'has_collective_agreement': {
        'type': 'boolean',
    },
    'collective_agreement_name': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'buyer_name': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'buyer_gov_org_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'buyer_id_type': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'buyer_country': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'foreman_is_on_site': {
        'type': 'boolean',
    },
    'foreman_first_name': {
        'type': 'string',
        'required': False,
        'empty_string': True,
    },
    'foreman_last_name': {
        'type': 'string',
        'required': False,
        'empty_string': True,
    },
    'foreman_phone_number': {
        'type': 'string',
        'required': False,
        'empty_string': True,
    },
    'foreman_email': {
        'type': 'string',
        'required': False,
        'empty_string': True,
    },
    'contract_type': {
        'type': 'string',
        'required': False,
        'allowed': SUPPLIER_CONTRACT_TYPES,
        'nullable': True,
    },
    'contract_start_date': {
        'type': 'date',
        'nullable': True,
        'coerce': to_date,
    },
    'contract_end_date': {
        'type': 'date',
        'nullable': True,
        'coerce': to_date,
    },
    'contract_work_areas': {
        'type': 'list',
        'nullable': True,
        'schema': {
            'type': 'string',
            'allowed': SUPPLIER_CONTRACT_WORK_AREAS,
        },
    },
    'informant_supplier_first_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'informant_supplier_last_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'informant_supplier_email': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'informant_supplier_phone': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_by_first_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_by_last_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_by_email': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_by_phone': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_supplier': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_company': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_business_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_business_id_type': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_time': {
        'type': 'datetime',
        'coerce': to_datetimetz,
        'required': False,
        'nullable': True,
    },
}


schema_update_pa_form: Schema = {
    'confirmed_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'confirmed_gov_org_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'confirmed_id_type': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'confirmed_time': {
        'type': 'datetime',
        'coerce': to_datetimetz,
        'required': False,
        'nullable': True,
    },
    'confirmed_country': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'rejected_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'rejected_gov_org_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'rejected_id_type': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'rejected_time': {
        'type': 'datetime',
        'coerce': to_datetimetz,
        'required': False,
        'nullable': True,
    },
    'rejected_country': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'informant_supplier_first_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'informant_supplier_last_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'informant_supplier_email': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'informant_supplier_phone': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_time': {
        'type': 'datetime',
        'coerce': to_datetimetz,
        'required': False,
        'nullable': True,
    },
    'submitted_by_first_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_by_last_name': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_by_email': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'submitted_by_phone': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_supplier': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_company': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_business_id': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_business_id_type': {
        'type': 'string',
        'required': False,
        'nullable': True,
    },
    'last_assigned_to_time': {
        'type': 'datetime',
        'coerce': to_datetimetz,
        'required': False,
        'nullable': True,
    },

}


pa_form_new_fields = set(schema_new_pa_form.keys())
pa_form_update_fields = set(schema_update_pa_form.keys())
pa_form_fields = pa_form_new_fields.union(pa_form_update_fields)


schema_update_preannouncement_with_new_pa_form = {
    **schema_update_preannouncement,
    **schema_new_pa_form,
}

schema_update_preannouncement_with_pa_form = {
    **schema_update_preannouncement,
    **schema_update_pa_form,
}


def is_valid_uuid(maybe_uuid: str) -> bool:
    try:
        uuid.UUID(maybe_uuid)
    except ValueError:
        return False
    else:
        return True


schema_new_creditsafe_account: Schema = {
    'id': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'person_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'org_id': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'username': {
        'type': 'string',
        'required': True,
        'empty_string': False,
    },
    'password': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'state': {
        'type': 'string',
        'required': True,
        'allowed': CREDITSAFE_ACCOUNT_STATES,
        'nullable': False,
    },
    'changed_by_person_id': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'comment': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'created_on': {
        'type': 'datetime',
        'required': False,
        'coerce': to_datetimetz,
    },
    'last_changed': {
        'type': 'datetime',
        'required': False,
        'coerce': to_datetimetz,
    },
    'history': {
        'type': 'list',
        'minlength': 1,
        'required': False,
        'schema': {
            'type': 'dict',
            'schema': {
                'state': {
                    'type': 'string',
                    'required': True,
                    'allowed': CREDITSAFE_ACCOUNT_STATES,
                    'nullable': False,
                },
                'changed_by_person_id': {
                    'type': 'string',
                    'required': False,
                    'empty_string': False,
                },
                'comment': {
                    'type': 'string',
                    'required': False,
                    'empty_string': False,
                },
                'created_on': {
                    'type': 'datetime',
                    'required': False,
                    'coerce': to_datetimetz,
                },
            }
        }
    }
}


schema_update_creditsafe_account: Schema = {
    'person_id': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'org_id': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'username': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'password': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'state': {
        'type': 'string',
        'required': False,
        'allowed': CREDITSAFE_ACCOUNT_STATES,
        'nullable': False,
    },
    'changed_by_person_id': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
    'comment': {
        'type': 'string',
        'required': False,
        'empty_string': False,
    },
}


schema_new_project_supplier_comment: Schema = {
    "project_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
    "supplier_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
    "org_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
    "comment": {
        "type": "string",
        "required": True,
    },
    "created_by_org_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
    "created_by_person_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
}

schema_existing_project_supplier_comment: Schema = {
    **schema_new_project_supplier_comment,
    "project_id": {
        "type": "string",
        "empty_string": False,
    },
    "supplier_id": {
        "type": "string",
        "empty_string": False,
    },
    "org_id": {
        "type": "string",
        "empty_string": False,
    },
    "comment": {
        "type": "string",
        "empty_string": False,
    },
    "created_by_org_id": {
        "type": "string",
        "empty_string": False,
    },
    "created_by_person_id": {
        "type": "string",
        "empty_string": False,
    },
    "id": {
        "type": "string",
    },
    "created_timestamp": {
        "type": "datetime",
        "coerce": to_datetimetz,
    },
    "is_deleted": {
        "type": "boolean",
    },
    "deleted_by_org_id": {
        "type": "string",
        "nullable": True,
    },
    "deleted_by_person_id": {
        "type": "string",
        "nullable": True,
    },
    "deleted_timestamp": {
        "type": "datetime",
        "coerce": to_datetimetz,
        "nullable": True,
    },
    "is_updated": {
        "type": "boolean",
    },
    "updated_by_org_id": {
        "type": "string",
        "nullable": True,
    },
    "updated_by_person_id": {
        "type": "string",
        "nullable": True,
    },
    "updated_timestamp": {
        "type": "datetime",
        "coerce": to_datetimetz,
        "nullable": True,
    },
    "modified_timestamp": {
        "type": "datetime",
        "coerce": to_datetimetz,
        "nullable": True,
    },
    "is_read": {
        "type": "boolean",
        "nullable": True,
    },
}

schema_update_project_supplier_comment: Schema = {
    "comment": {
        "type": "string",
        "required": False,
    },
    "is_deleted": {
        "type": "boolean",
        "required": False,
        "allowed": [True],  # do not allow "un-deleting" comments
    },
    "updating_org_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
    "updating_person_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
}


schema_new_project_comment_viewer: Schema = {
    "comment_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
    "read_by_person_id": {
        "type": "string",
        "required": True,
        "empty_string": False,
    },
}

schema_existing_project_comment_viewer: Schema = {
    "id": {
        "type": "string",
        "empty_string": False,
    },
    "comment_id": {
        "type": "string",
        "empty_string": False,
    },
    "read_by_person_id": {
        "type": "string",
        "empty_string": False,
    },
}
