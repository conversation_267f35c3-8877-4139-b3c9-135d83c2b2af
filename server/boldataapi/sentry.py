import logging

import raven.conf
import raven.contrib.bottle
from raven.handlers.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>

from boldataapi.version import get_bda_version


logger = logging.getLogger(__name__)


class BolDataAPISentryHandler(SentryHandler):
    """Handler for logging configuration from file.

    Example:

        [handler_sentry]
        class = BolDataAPISentryHandler.sentry.SentryHandler
        level = ERROR
        dsn = ('sentry_dsn', {'service': 'myservice'})

    If you configure logging via configuration file, you don't need to call setup_sentry, because it
    would be a duplication.

    """

    def __init__(self, dsn, tags=None, **kwargs):
        super().__init__(
            dsn=dsn,
            tags=tags or {},
            release=get_bda_version(),
            list_max_length=200,
            include_paths=['boldataapi'],
            **kwargs
        )


def setup_sentry(config, tags={}):
    dsn = config.get('main', 'sentry_dsn')
    if dsn:
        logger.info('Reporting errors to Sentry at %s', dsn)
        handler = BolDataAPISentryHandler(dsn, tags, level=logging.ERROR)
        raven.conf.setup_logging(handler)
        return handler.client
