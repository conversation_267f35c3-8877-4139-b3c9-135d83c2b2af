#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --find-links=vendor/ --no-emit-find-links --no-emit-index-url --output-file=requirements.txt requirements/prod.in
#
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
attrs==22.2.0
    # via zeep
azure-core==1.29.1
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   msrest
azure-core-tracing-opentelemetry==1.0.0b11
    # via azure-monitor-opentelemetry
azure-monitor-opentelemetry==1.6.1
    # via stv-utils
azure-monitor-opentelemetry-exporter==1.0.0b28
    # via azure-monitor-opentelemetry
bottle==0.12.25
    # via
    #   -r requirements/prod.in
    #   stv-utils
bottleswagger==1.5
    # via -r requirements/prod.in
cerberus==1.3.2
    # via -r requirements/prod.in
certifi==2020.4.5.1
    # via
    #   msrest
    #   requests
cffi==1.15.1
    # via cryptography
charset-normalizer==2.0.12
    # via requests
cryptography==38.0.4
    # via qvarn-utils
deprecated==1.2.14
    # via
    #   opentelemetry-api
    #   opentelemetry-semantic-conventions
expiringdict==1.1.4
    # via -r requirements/prod.in
fixedint==0.1.6
    # via azure-monitor-opentelemetry-exporter
idna==2.9
    # via requests
importlib-metadata==8.4.0
    # via
    #   opentelemetry-api
    #   opentelemetry-instrumentation-flask
isodate==0.6.1
    # via
    #   msrest
    #   zeep
lxml==4.9.2
    # via zeep
msrest==0.7.1
    # via azure-monitor-opentelemetry-exporter
newrelic==8.4.0
    # via -r requirements/prod.in
oauthlib==3.2.2
    # via requests-oauthlib
opentelemetry-api==1.27.0
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-instrumentation==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
opentelemetry-instrumentation-asgi==0.48b0
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-dbapi==0.48b0
    # via opentelemetry-instrumentation-psycopg2
opentelemetry-instrumentation-django==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-fastapi==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-flask==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-psycopg2==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-requests==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-threading==0.48b0
    # via stv-utils
opentelemetry-instrumentation-urllib==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-urllib3==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-wsgi==0.48b0
    # via
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-flask
opentelemetry-resource-detector-azure==0.1.5
    # via azure-monitor-opentelemetry
opentelemetry-sdk==1.27.0
    # via
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-resource-detector-azure
opentelemetry-semantic-conventions==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
opentelemetry-util-http==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
packaging==24.1
    # via opentelemetry-instrumentation-flask
platformdirs==2.6.0
    # via zeep
psutil==5.9.4
    # via azure-monitor-opentelemetry-exporter
psycopg2-binary==2.9.5
    # via -r requirements/prod.in
pycparser==2.21
    # via cffi
pyjwt==2.6.0
    # via
    #   -r requirements/prod.in
    #   qvarn-utils
    #   stv-utils
python-dateutil==2.8.2
    # via
    #   qvarn-utils
    #   stv-utils
pytz==2022.7
    # via
    #   stv-utils
    #   zeep
pyyaml==5.3.1
    # via
    #   -r requirements/prod.in
    #   bottleswagger
qvarn-utils==2.26
    # via stv-utils
raven==6.10.0
    # via -r requirements/prod.in
requests==2.27.1
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   msrest
    #   qvarn-utils
    #   requests-file
    #   requests-futures
    #   requests-oauthlib
    #   requests-toolbelt
    #   stv-utils
    #   zeep
requests-file==1.5.1
    # via zeep
requests-futures==1.0.0
    # via
    #   qvarn-utils
    #   stv-utils
requests-oauthlib==2.0.0
    # via msrest
requests-toolbelt==0.10.1
    # via zeep
six==1.16.0
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   isodate
    #   python-dateutil
    #   requests-file
sqlalchemy==1.3.17
    # via
    #   -r requirements/prod.in
    #   sqlalchemy-citext
sqlalchemy-citext==1.7.0
    # via -r requirements/prod.in
stv-utils==3.0.6
    # via -r requirements/prod.in
typing-extensions==4.4.0
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   opentelemetry-sdk
urllib3==1.26.15
    # via requests
wrapt==1.14.1
    # via
    #   deprecated
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib3
zeep==4.2.1
    # via stv-utils
zipp==3.20.1
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
