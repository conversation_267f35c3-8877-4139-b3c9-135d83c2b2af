#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --find-links=vendor/ --no-emit-find-links --no-emit-index-url --output-file=requirements-dev.txt requirements/dev.in requirements/prod.in
#
alembic==1.13.2
    # via -r requirements/dev.in
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
attrs==19.3.0
    # via
    #   pytest
    #   zeep
azure-core==1.29.1
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   msrest
azure-core-tracing-opentelemetry==1.0.0b11
    # via azure-monitor-opentelemetry
azure-monitor-opentelemetry==1.6.2
    # via stv-utils
azure-monitor-opentelemetry-exporter==1.0.0b28
    # via azure-monitor-opentelemetry
bandit==1.6.2
    # via -r requirements/dev.in
beautifulsoup4==4.9.0
    # via webtest
bottle==0.12.25
    # via
    #   -r requirements/prod.in
    #   stv-utils
bottleswagger==1.5
    # via -r requirements/prod.in
cerberus==1.3.2
    # via -r requirements/prod.in
certifi==2020.4.5.1
    # via
    #   msrest
    #   requests
cffi==1.15.1
    # via cryptography
charset-normalizer==2.0.12
    # via requests
coverage==5.1
    # via
    #   -r requirements/dev.in
    #   pytest-cov
cryptography==38.0.4
    # via qvarn-utils
deprecated==1.2.14
    # via
    #   opentelemetry-api
    #   opentelemetry-semantic-conventions
diff-cover==2.6.1
    # via -r requirements/dev.in
expiringdict==1.1.4
    # via -r requirements/prod.in
fixedint==0.1.6
    # via azure-monitor-opentelemetry-exporter
flake8==6.0.0
    # via -r requirements/dev.in
flake8-import-order==0.18.2
    # via -r requirements/dev.in
freezegun==0.3.15
    # via -r requirements/dev.in
gitdb==4.0.4
    # via gitpython
gitpython==3.1.1
    # via bandit
idna==2.9
    # via requests
importlib-metadata==8.4.0
    # via
    #   opentelemetry-api
    #   opentelemetry-instrumentation-flask
inflect==4.1.0
    # via jinja2-pluralize
iniconfig==1.1.1
    # via pytest
isodate==0.6.1
    # via
    #   msrest
    #   zeep
jinja2==2.11.2
    # via
    #   diff-cover
    #   jinja2-pluralize
jinja2-pluralize==0.3.0
    # via diff-cover
lxml==4.9.2
    # via zeep
mako==1.1.3
    # via alembic
markupsafe==1.1.1
    # via
    #   jinja2
    #   mako
mccabe==0.7.0
    # via flake8
mock==3.0.5
    # via -r requirements/dev.in
msrest==0.7.1
    # via azure-monitor-opentelemetry-exporter
mypy==1.0.0
    # via -r requirements/dev.in
mypy-extensions==0.4.3
    # via mypy
newrelic==8.4.0
    # via -r requirements/prod.in
oauthlib==3.2.2
    # via requests-oauthlib
opentelemetry-api==1.27.0
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-instrumentation==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
opentelemetry-instrumentation-asgi==0.48b0
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-dbapi==0.48b0
    # via opentelemetry-instrumentation-psycopg2
opentelemetry-instrumentation-django==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-fastapi==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-flask==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-psycopg2==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-requests==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-threading==0.48b0
    # via stv-utils
opentelemetry-instrumentation-urllib==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-urllib3==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-wsgi==0.48b0
    # via
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-flask
opentelemetry-resource-detector-azure==0.1.5
    # via azure-monitor-opentelemetry
opentelemetry-sdk==1.27.0
    # via
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-resource-detector-azure
opentelemetry-semantic-conventions==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
opentelemetry-util-http==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
packaging==24.1
    # via
    #   opentelemetry-instrumentation-flask
    #   pytest
    #   setuptools-scm
pbr==5.4.5
    # via stevedore
platformdirs==2.6.0
    # via zeep
pluggy==0.13.1
    # via
    #   diff-cover
    #   pytest
psutil==5.9.4
    # via azure-monitor-opentelemetry-exporter
psycopg2-binary==2.9.5
    # via -r requirements/prod.in
py==1.11.0
    # via pytest
pycodestyle==2.10.0
    # via
    #   flake8
    #   flake8-import-order
pycparser==2.21
    # via cffi
pyflakes==3.0.1
    # via flake8
pygments==2.6.1
    # via diff-cover
pyjwt==2.6.0
    # via
    #   -r requirements/prod.in
    #   qvarn-utils
    #   stv-utils
pytest==7.1.2
    # via
    #   -r requirements/dev.in
    #   pytest-cov
    #   pytest-mock
pytest-cov==2.8.1
    # via -r requirements/dev.in
pytest-mock==1.11.1
    # via -r requirements/dev.in
python-dateutil==2.8.1
    # via
    #   freezegun
    #   qvarn-utils
    #   stv-utils
python-dotenv==0.15.0
    # via -r requirements/dev.in
pytz==2022.7
    # via
    #   stv-utils
    #   zeep
pyyaml==5.3.1
    # via
    #   -r requirements/prod.in
    #   bandit
    #   bottleswagger
qvarn-utils==2.26
    # via stv-utils
raven==6.10.0
    # via -r requirements/prod.in
requests==2.27.1
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   msrest
    #   qvarn-utils
    #   requests-file
    #   requests-futures
    #   requests-mock
    #   requests-oauthlib
    #   requests-toolbelt
    #   stv-utils
    #   zeep
requests-file==1.5.1
    # via zeep
requests-futures==1.0.0
    # via
    #   qvarn-utils
    #   stv-utils
requests-mock==1.4.0
    # via -r requirements/dev.in
requests-oauthlib==2.0.0
    # via msrest
requests-toolbelt==0.10.1
    # via zeep
setuptools-scm==8.0.4
    # via -r requirements/dev.in
six==1.16.0
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   bandit
    #   diff-cover
    #   freezegun
    #   isodate
    #   mock
    #   python-dateutil
    #   requests-file
    #   requests-mock
    #   stevedore
    #   webtest
smmap==3.0.2
    # via gitdb
soupsieve==2.0
    # via beautifulsoup4
sqlalchemy==1.3.17
    # via
    #   -r requirements/prod.in
    #   alembic
    #   sqlalchemy-citext
sqlalchemy-citext==1.7.0
    # via -r requirements/prod.in
stevedore==1.32.0
    # via bandit
stv-utils==3.0.6
    # via -r requirements/prod.in
tomli==2.0.1
    # via pytest
typing-extensions==4.5.0
    # via
    #   -r requirements/prod.in
    #   alembic
    #   azure-core
    #   mypy
    #   opentelemetry-sdk
    #   setuptools-scm
urllib3==1.26.15
    # via requests
waitress==1.4.3
    # via webtest
webob==1.8.6
    # via webtest
webtest==2.0.33
    # via -r requirements/dev.in
wrapt==1.14.1
    # via
    #   deprecated
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib3
zeep==4.2.1
    # via stv-utils
zipp==3.20.1
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
