Vendored packages
=================

Our builds require some packages that are not publicly available. To make life
easier we're vendoring them here.

Note that CI builds do use these vendored packages as the packages are no longer
available from an internal PyPI.


bottleswagger
-------------

This is an internal library, written by Su<PERSON><PERSON>, not to
be confused with bottle-swagger that is available on PyPI.

Source: https://git.vaultit.org/common/bottle-swagger


stv-utils
---------

This is an internal library.

Source: https://dev.azure.com/vaultit/Common/_git/stv-utils
