PYTHON = python3.11
ENV = env
PYBIN = $(ENV)/bin
PIP = $(PYBIN)/pip
PIP_COMPILE = $(PYBIN)/pip-compile -f vendor/ --no-emit-find-links --no-emit-index-url
PYTEST = $(PYBIN)/pytest
FLAKE8 = $(PYBIN)/flake8 boldataapi tests setup.py
TESTDIR = tests

# We can use either Docker Compose v1 (docker-compose), or v2 (docker compose)
DOCKER_COMPOSE = docker compose -f ../docker-compose.yaml
# Compose v1 uses _ as a separator, v2 uses -
DOCKER_COMPOSE_NAME_SEPARATOR := -

# /home/<USER>/src/bol-data-api/server/ -> bol-data-api
# problem: if /home/<USER>/src/bda is a symlink to bol-data-api, this will compute 'bol-data-api'
# but 'docker compose' itself will use 'bda' as a prefix!
DOCKER_COMPOSE_NAME_PREFIX := $(notdir $(patsubst %/,%,$(dir $(abspath $(CURDIR)))))
DOCKER_COMPOSE_NAME_PREFIX_ := $(DOCKER_COMPOSE_NAME_PREFIX)$(DOCKER_COMPOSE_NAME_SEPARATOR)

# in case `docker compose run` needs to build an image
export VERSION = $(shell python3 -Wignore setup.py --version)

.PHONY: environ
environ: $(PYBIN)/$(PYTHON) $(ENV)/.done

.PHONY: help
help:
	@echo "make                  # build everything"
	@echo "make test             # run tests"
	@echo "make lint             # run all linters"
	@echo "make flake8           # run flake8 linter"
	@echo "make requirements     # update requirements*.txt from requirements/*.in"
	@echo "make run              # run API in a connected state"
	@echo "make swagger-ui       # run Swagger API doc viewer on http://localhost:8123/"
	@echo "make stop             # stop the running docker containers"
	@echo "make down             # stop and remove the running docker containers"
	@echo "make clean            # clean API and databases"
	@echo "make clean-db         # clean only databases"

$(PIP) $(PYBIN)/$(PYTHON):
	rm -rf $(ENV)
	$(PYTHON) -m venv $(ENV)
	$(PIP) install -U pip setuptools wheel pip-tools
	$(PIP) install -I setuptools   # workaround for https://github.com/pypa/setuptools/issues/887

.PHONY: requirements
requirements: $(PIP)
	$(PIP_COMPILE) requirements/prod.in -o requirements.txt
	$(PIP_COMPILE) requirements/prod.in requirements/dev.in -o requirements-dev.txt
	$(MAKE) constraints.txt

constraints.txt: requirements-dev.txt
	# extras are not allowed in constraints files
	sed -e 's/[[][^]]*]//' $< > $@

$(ENV)/.done: $(PIP) setup.py requirements-dev.txt
	$(PIP) install -f vendor/ -r requirements-dev.txt -e .
	touch $(ENV)/.done

.PHONY: run
run: environ
	${DOCKER_COMPOSE} up bol-data-api

.PHONY: swagger-ui
swagger-ui: environ
	${DOCKER_COMPOSE} up bol-data-api swagger-ui

.PHONY: stop
stop: environ
	${DOCKER_COMPOSE} stop

.PHONY: down
down: environ
	${DOCKER_COMPOSE} down

.PHONY: test
test: environ
	status=0 ; \
	$(PYTEST) $(TESTDIR) || status=$$? ; \
	$(FLAKE8) || status=$$? ; \
	exit $$status

.PHONY: app
app:
	$(DOCKER_COMPOSE) up -d bol-data-api

.PHONY: app-down
app-down:
	$(DOCKER_COMPOSE) rm -sf bol-data-api
	${DOCKER_COMPOSE} rm -sf db
	-docker image rm $(DOCKER_COMPOSE_NAME_PREFIX_)db
	-docker volume rm $(DOCKER_COMPOSE_NAME_PREFIX_)bda-pg-data

.PHONY: db-test-up
db-test-up:
	${DOCKER_COMPOSE} up -d db-test

.PHONY: db-test-down
db-test-down:
	${DOCKER_COMPOSE} rm -sf db-test

.PHONY: db-test-clean
db-test-clean:
	${MAKE} db-test-down
	-docker image rm $(DOCKER_COMPOSE_NAME_PREFIX_)db-test
	-docker volume rm $(DOCKER_COMPOSE_NAME_PREFIX_)bda-pg-test-data

.PHONY: coverage
coverage: environ
	$(PYBIN)/coverage run -m pytest $(TESTDIR)
	$(PYBIN)/coverage report -m

.PHONY: diff-cover
diff-cover: coverage
	$(PYBIN)/coverage xml
	$(PYBIN)/diff-cover coverage.xml

.PHONY: lint
lint: flake8 mypy

.PHONY: flake8
flake8: environ
	$(FLAKE8)

.PHONY: mypy
mypy: environ
	$(PYBIN)/mypy boldataapi

.PHONY: clean
clean:
	rm -rf $(ENV)
	${MAKE} clean-pycache
	${MAKE} clean-app
	${MAKE} clean-db
	${MAKE} clean-test-db

.PHONY: clean-app
clean-app:
	${DOCKER_COMPOSE} rm -sf bol-data-api
	-docker image rm $(DOCKER_COMPOSE_NAME_PREFIX_)bol-data-api

.PHONY: clean-db
clean-db:
	${DOCKER_COMPOSE} rm -sf db
	-docker image rm $(DOCKER_COMPOSE_NAME_PREFIX_)db
	-docker volume rm $(DOCKER_COMPOSE_NAME_PREFIX_)bda-pg-data

.PHONY: clean-test-db
clean-test-db:
	${DOCKER_COMPOSE} rm -sf db-test
	-docker image rm $(DOCKER_COMPOSE_NAME_PREFIX_)db-test
	-docker volume rm $(DOCKER_COMPOSE_NAME_PREFIX_)bda-pg-test-data

.PHONY: clean-pycache clean_pycache
clean-pycache clean_pycache:
	-find . -name '__pycache__' -type d -exec rm -rf "{}" +
	find . -name '.pytest_cache' -type d -exec rm -rf "{}" +
	find . -name '*.pyc' -delete
