[tox]
envlist = py311,flake8

[testenv]
usedevelop = true
deps =
    -rrequirements-dev.txt
install_command = python -m pip install -f vendor/ {opts} {packages}
commands =
    pytest {posargs}
passenv =
    BOL_TEST_*

[testenv:flake8]
skip_install = true
deps =
    flake8
    flake8-import-order
    -cconstraints.txt
commands =
    flake8 boldataapi tests setup.py ../db/alembic/migrations {posargs:--statistics --count}

[testenv:mypy]
basepython = python3.11
skip_install = true
deps =
    -rrequirements-dev.txt
commands =
    mypy boldataapi {posargs}
