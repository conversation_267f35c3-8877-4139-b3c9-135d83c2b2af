[build-system]
requires = ["setuptools", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[project]
name = "bol-data-api"
authors = [
    {name = "VaultIT AB", email = "<EMAIL>"},
]
description = "DB wrapper for BOL-related resources"
requires-python = ">=3.11"
dynamic = ["dependencies", "version"]

[project.scripts]
bol-data-api = "boldataapi.__main__:main"

[tool.setuptools.packages.find]
include = ["boldataapi*"]
exclude = ["tests*", "vendor*"]

[tool.setuptools.dynamic]
dependencies = {file = "requirements/prod.in"}

[tool.setuptools_scm]
root = ".."
