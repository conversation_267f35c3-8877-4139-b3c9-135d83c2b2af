import datetime

import pytest

from boldataapi.schema import (
    BaseValidator,
    to_date,
    to_datetime,
    to_datetimetz,
)


def _test_function_value(fn, *args, expected):
    if isinstance(expected, Exception):
        with pytest.raises(type(expected)) as ctx:
            fn(*args)
        assert str(ctx.value) == str(expected)
    else:
        assert fn(*args) == expected


@pytest.mark.parametrize("value, expected", [
    (None, None),
    ('2020-10-22', datetime.datetime(2020, 10, 22)),
    ('last tuesday', ValueError("time data 'last tuesday' does not match format '%Y-%m-%d'")),
])
def test_to_date(value, expected):
    _test_function_value(to_date, value, expected=expected)


@pytest.mark.parametrize("value, expected", [
    (None, None),
    ('2020-10-22T12:08:05', datetime.datetime(2020, 10, 22, 12, 8, 5)),
    ('2020-10-22T12:08:05.012345', datetime.datetime(2020, 10, 22, 12, 8, 5, 12345)),
    ('last tuesday', ValueError(
        "time data 'last tuesday' does not match format '%Y-%m-%dT%H:%M:%S.%f'")),
])
def test_to_datetime(value, expected):
    _test_function_value(to_datetime, value, expected=expected)


@pytest.mark.parametrize("value, expected", [
    (None, None),
    ('2020-10-22T12:08:05', datetime.datetime(2020, 10, 22, 12, 8, 5)),
    ('2020-10-22T12:08:05.012345', datetime.datetime(2020, 10, 22, 12, 8, 5, 12345)),
    ('2020-10-22T12:08:05Z',
     datetime.datetime(2020, 10, 22, 12, 8, 5, tzinfo=datetime.timezone.utc)),
    ('2020-10-22T12:08:05.012345Z',
     datetime.datetime(2020, 10, 22, 12, 8, 5, 12345, tzinfo=datetime.timezone.utc)),
    ('last tuesday', ValueError(
        "time data 'last tuesday' does not match format '%Y-%m-%dT%H:%M:%S.%f%z'")),
])
def test_to_datetimetz(value, expected):
    _test_function_value(to_datetimetz, value, expected=expected)


def test_disable_validation(featureflags):
    sample_schema = {
        'sample_field': {
            'type': 'string',
        }
    }

    validator = BaseValidator(sample_schema)
    broken_input = {'sample_field': 123}

    # Validation breaks on bad input
    assert False == validator.validate(broken_input)
    assert validator.validated(broken_input) is None
    assert broken_input == validator.normalized(broken_input)

    # Disable validation
    featureflags({'validate_input': False})

    validator = BaseValidator(sample_schema)
    validator.validation_can_be_disabled_by_feature_flag = True
    broken_input = {'sample_field': 123}

    # Validation succeeds even on bad input and we can get the data
    assert True == validator.validate(broken_input)
    assert validator.validated(broken_input) is None
    assert broken_input == validator.normalized(broken_input)

    # But only if the validator is marked as one where validation can be disabled
    validator.validation_can_be_disabled_by_feature_flag = False
    assert False == validator.validate(broken_input)
