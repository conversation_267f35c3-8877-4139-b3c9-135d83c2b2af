import cerberus
import pytest

from boldataapi.schema import (
    REPORT_TYPE_NOTIFICATION_REPORT,
    REPORT_TYPE_STATUS_REPORT,
    ReportValidator,
    schema_existing_notification_report,
    schema_existing_status_report,
    schema_new_base_report,
    schema_new_notification_report,
    schema_new_status_report,
    schema_reports_pdf,
    STATUS_ATTENTION,
    STATUS_INCOMPLETE,
    STATUS_INVESTIGATE,
    STATUS_OK,
    STATUS_STOP,
)


pytestmark = pytest.mark.usefixtures("config")


def test_new_base_report_required():
    validator = ReportValidator(schema_new_base_report)
    required_fields = [
        'type',
        'report_type',
    ]
    assert not validator.validate({})
    assert len(validator.errors.keys()) == len(required_fields)
    for field in required_fields:
        assert cerberus.errors.REQUIRED_FIELD in \
            validator.document_error_tree[field]


def test_new_base_report_not_nullable():
    validator = ReportValidator(schema_new_base_report)
    base_report = {
        'type': None,
        'report_type': None,
    }
    assert not validator.validate(base_report)
    for field in base_report.keys():
        assert cerberus.errors.NOT_NULLABLE in \
            validator.document_error_tree[field]


# NEW STATUS REPORTS TESTS

def test_new_status_report_required():
    validator = ReportValidator(schema_new_status_report)
    required_fields = [
        'type',
        'report_type',
        'generated_timestamp',
        'tilaajavastuu_status',
        'org',
    ]
    assert not validator.validate({})
    assert len(validator.errors.keys()) == len(required_fields)
    for field in required_fields:
        assert cerberus.errors.REQUIRED_FIELD in \
            validator.document_error_tree[field]


def test_new_status_report_not_nullable():
    validator = ReportValidator(schema_new_status_report)
    status_report = {
        'type': None,
        'report_type': None,
        'generated_timestamp': None,
        'tilaajavastuu_status': None,
        'org': None,
        'interested_org_id': None,
        'pdf': None,
    }
    assert not validator.validate(status_report)
    for field in status_report.keys():
        assert cerberus.errors.NOT_NULLABLE in \
            validator.document_error_tree[field]


@pytest.mark.parametrize("org, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid org ', True),
])
def test_new_status_report_org_values(org, valid):
    validator = ReportValidator(schema_new_status_report)
    status_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'tilaajavastuu_status': '200 INVESTIGATE',
        'org': org,
        'interested_org_id': 'test-interrested-org-id',
        'pdf': {
            'content_type': 'application/json',
            'body': 'json',
        },
    }
    assert validator.validate(status_report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['org']


@pytest.mark.parametrize("interested_org_id, valid", [
    ('', True),
    ('  ', True),
    ('test-test', True),
    (' valid test ', True),
])
def test_new_status_report_interested_org_id_values(interested_org_id, valid):
    validator = ReportValidator(schema_new_status_report)
    status_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'tilaajavastuu_status': '200 INVESTIGATE',
        'org': 'test-org',
        'interested_org_id': interested_org_id,
        'pdf': {
            'content_type': 'application/json',
            'body': 'json',
        },
    }
    assert validator.validate(status_report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['interested_org_id']


@pytest.mark.parametrize("pdf, valid", [
    ({}, False),
    ({'content_type': 'application/json'}, False),
    ({'body': 'json'}, False),
    (
        {
            'content_type': 'application/json',
            'body': 'json',
        },
        True,
    )
])
def test_new_status_report_pdf_structure(pdf, valid):
    validator = ReportValidator(schema_new_status_report)
    status_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'tilaajavastuu_status': '200 INVESTIGATE',
        'org': 'test-org',
        'interested_org_id': 'interested-org-id',
        'pdf': pdf,
    }
    assert validator.validate(status_report) == valid


@pytest.mark.parametrize("body, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid test ', True),
])
def test_new_status_report_pdf_body_values(body, valid):
    validator = ReportValidator(schema_new_status_report)
    status_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'tilaajavastuu_status': '200 INVESTIGATE',
        'org': 'test-org',
        'interested_org_id': 'interested-org-id',
        'pdf': {
            'content_type': 'application/json',
            'body': body,
        },
    }
    assert validator.validate(status_report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['pdf'].errors[0].info[0]


# NEW NOTIFICATION REPORTS TESTS


def test_new_notification_report_required():
    validator = ReportValidator(schema_new_notification_report)
    required_fields = [
        'type',
        'report_type',
        'generated_timestamp',
    ]
    assert not validator.validate({})
    assert len(validator.errors.keys()) == len(required_fields)
    for field in required_fields:
        assert cerberus.errors.REQUIRED_FIELD in \
            validator.document_error_tree[field]


def test_new_notification_report_not_nullable():
    validator = ReportValidator(schema_new_notification_report)
    notification_report = {
        'type': None,
        'report_type': None,
        'generated_timestamp': None,
        'pdf': None,
    }
    assert not validator.validate(notification_report)
    for field in notification_report.keys():
        assert cerberus.errors.NOT_NULLABLE in \
            validator.document_error_tree[field]


@pytest.mark.parametrize("pdf, valid", [
    ({}, False),
    ({'content_type': 'application/json'}, False),
    ({'body': 'json'}, False),
    (
        {
            'content_type': 'application/json',
            'body': 'json',
        },
        True,
    )
])
def test_new_notification_report_pdf_structure(pdf, valid):
    validator = ReportValidator(schema_new_notification_report)
    notification_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.status_change_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'pdf': pdf,
    }
    assert validator.validate(notification_report) == valid


@pytest.mark.parametrize("body, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid test ', True),
])
def test_new_notification_report_pdf_body_values(body, valid):
    validator = ReportValidator(schema_new_notification_report)
    notification_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.status_change_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'pdf': {
            'content_type': 'application/json',
            'body': body,
        },
    }
    assert validator.validate(notification_report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['pdf'].errors[0].info[0]


@pytest.mark.parametrize("org, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid org ', True),
])
def test_new_notification_report_org_values(org, valid):
    validator = ReportValidator(schema_new_notification_report)
    report = {
        'type': 'report',
        'report_type': 'bolagsfakta.status_change_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'org': org,
        'pdf': {
            'content_type': 'application/json',
            'body': 'json',
        },
    }
    assert validator.validate(report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['org']


@pytest.mark.parametrize("interested_org_id, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid test ', True),
])
def test_new_notification_report_interested_org_id_values(interested_org_id, valid):
    validator = ReportValidator(schema_new_notification_report)
    report = {
        'type': 'report',
        'report_type': 'bolagsfakta.status_change_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': interested_org_id,
        'pdf': {
            'content_type': 'application/json',
            'body': 'json',
        },
    }
    assert validator.validate(report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['interested_org_id']


# EXISTING STATUS REPORTS TESTS

def test_existing_status_report_not_required():
    validator = ReportValidator(schema_existing_status_report)
    assert validator.validate({})


@pytest.mark.parametrize("status,valid", [
    (STATUS_STOP, True),
    (STATUS_INCOMPLETE, True),
    (STATUS_INVESTIGATE, True),
    (STATUS_ATTENTION, True),
    (STATUS_OK, True),
    ('NOT VALID', False),
    ('', False),
])
def test_existing_status_report_status(status, valid):
    validator = ReportValidator(schema_existing_status_report)
    assert valid == validator.validate({'tilaajavastuu_status': status})
    if not valid:
        assert validator.errors == {
            'tilaajavastuu_status': ['unallowed value %s' % status]
        }


@pytest.mark.parametrize("report_type,valid", [
    (REPORT_TYPE_NOTIFICATION_REPORT, False),
    (REPORT_TYPE_STATUS_REPORT, True),
    ('NOT VALID', False),
    ('', False),
])
def test_existing_status_report_report_type(report_type, valid):
    validator = ReportValidator(schema_existing_status_report)
    assert valid == validator.validate({'report_type': report_type})
    if not valid:
        assert validator.errors == {
            'report_type': ['unallowed value %s' % report_type]
        }


@pytest.mark.parametrize("interested_org_id, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid test ', True),
])
def test_existing_status_report_interested_org_id_values(interested_org_id, valid):
    validator = ReportValidator(schema_existing_status_report)
    report = {
        'interested_org_id': interested_org_id,
    }
    assert validator.validate(report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['interested_org_id']


@pytest.mark.parametrize("org, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid org ', True),
])
def test_existing_status_report_org_values(org, valid):
    validator = ReportValidator(schema_existing_status_report)
    report = {
        'org': org,
    }
    assert validator.validate(report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['org']


# EXISTING NOTIFICATION REPORTS TESTS

def test_existing_notification_report_not_required():
    validator = ReportValidator(schema_existing_notification_report)
    assert validator.validate({})


@pytest.mark.parametrize("status,valid", [
    (STATUS_STOP, True),
    (STATUS_INCOMPLETE, True),
    (STATUS_INVESTIGATE, True),
    (STATUS_ATTENTION, True),
    (STATUS_OK, True),
    ('NOT VALID', False),
    ('', False),
])
def test_existing_notification_report_status(status, valid):
    validator = ReportValidator(schema_existing_notification_report)
    assert valid == validator.validate({'tilaajavastuu_status': status})
    if not valid:
        assert validator.errors == {
            'tilaajavastuu_status': ['unallowed value %s' % status]
        }


@pytest.mark.parametrize("report_type,valid", [
    (REPORT_TYPE_NOTIFICATION_REPORT, True),
    (REPORT_TYPE_STATUS_REPORT, False),
    ('NOT VALID', False),
    ('', False),
])
def test_existing_notification_report_report_type(report_type, valid):
    validator = ReportValidator(schema_existing_notification_report)
    assert valid == validator.validate({'report_type': report_type})
    if not valid:
        assert validator.errors == {
            'report_type': ['unallowed value %s' % report_type]
        }


@pytest.mark.parametrize("org, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid org ', True),
])
def test_existing_notification_report_org_values(org, valid):
    validator = ReportValidator(schema_existing_notification_report)
    report = {
        'org': org,
    }
    assert validator.validate(report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['org']


@pytest.mark.parametrize("interested_org_id, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid test ', True),
])
def test_existing_notification_report_interested_org_id_values(interested_org_id, valid):
    validator = ReportValidator(schema_existing_notification_report)
    report = {
        'interested_org_id': interested_org_id,
    }
    assert validator.validate(report) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['interested_org_id']


# REPORT PDF UPDATE

@pytest.mark.parametrize("report_pdf, valid", [
    ({}, False),
    ({'test': 'test-test'}, True),
])
def test_report_pdf_values(report_pdf, valid):
    validator = ReportValidator(schema_reports_pdf)
    assert validator.validate({'pdf': report_pdf}) == valid
