import cerberus
import pytest


from boldataapi.schema import (
    BolSupplierValidator,
    schema_existing_bol_supplier,
    schema_new_bol_supplier,
)

pytestmark = pytest.mark.usefixtures("config")


# NEW PROJECT TESTS

def test_new_bol_supplier_required_fields():
    required_fields = [
        'materialized_path',
        'project_resource_id',
        'supplier_role',
        'supplier_type',
        'supplier_org_id',
    ]
    validator = BolSupplierValidator(schema_new_bol_supplier)
    assert not validator.validate({})
    assert len(validator.errors.keys()) == len(required_fields)
    for field in required_fields:
        assert cerberus.errors.REQUIRED_FIELD in \
            validator.document_error_tree[field]


def test_new_bol_supplier_required_not_nullable():
    validator = BolSupplierValidator(schema_new_bol_supplier)
    new_supplier = {
        'materialized_path': None,
        'project_resource_id': None,
        'supplier_role': None,
        'supplier_type': None,
        'supplier_org_id': None,
    }
    assert not validator.validate(new_supplier)
    for field in new_supplier.keys():
        assert cerberus.errors.NOT_NULLABLE in \
            validator.document_error_tree[field]


@pytest.mark.parametrize("supplier_role, valid", [
    ('', False),
    ('  ', False),
    ('not valid', False),
    ('main_contractor', True),
    ('supervisor', True),
    ('supplier', True),
])
def test_new_supplier_supplier_role_values(supplier_role, valid):
    validator = BolSupplierValidator(schema_new_bol_supplier)
    new_supplier = {
        'materialized_path': ['test'],
        'project_resource_id': 'test-project-resource-id',
        'supplier_type': 'linked',
        'supplier_org_id': 'test-supplier-org-id',
        'supplier_role': supplier_role,
    }
    assert validator.validate(new_supplier) == valid
    if not valid:
        assert cerberus.errors.UNALLOWED_VALUE in \
            validator.document_error_tree['supplier_role']


@pytest.mark.parametrize("supplier_type, valid", [
    ('', False),
    ('  ', False),
    ('not valid', False),
    ('linked', True),
    ('unlinked', True),
])
def test_new_supplier_supplier_type_values(supplier_type, valid):
    validator = BolSupplierValidator(schema_new_bol_supplier)
    new_supplier = {
        'materialized_path': ['test'],
        'project_resource_id': 'test-project-resource-id',
        'supplier_role': 'supplier',
        'supplier_org_id': 'test-supplier-org-id',
        'supplier_type': supplier_type,
    }
    assert validator.validate(new_supplier) == valid
    if not valid:
        assert cerberus.errors.UNALLOWED_VALUE in \
            validator.document_error_tree['supplier_type']


@pytest.mark.parametrize("project_resource_id, valid", [
    ('', False),
    ('  ', False),
    ('any-value', True),
])
def test_new_supplier_project_resource_id(project_resource_id, valid):
    validator = BolSupplierValidator(schema_new_bol_supplier)
    new_supplier = {
        'materialized_path': ['test'],
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'supplier_org_id': 'test-supplier-org-id',
        'project_resource_id': project_resource_id,
    }
    assert validator.validate(new_supplier) == valid
    if not valid:
        field_error = validator.document_error_tree['project_resource_id']
        assert cerberus.errors.CUSTOM in field_error
        assert field_error.errors[0].info == ('empty values not allowed',)


@pytest.mark.parametrize("supplier_org_id, valid", [
    ('', False),
    ('  ', False),
    ('any-value', True),
])
def test_new_supplier_supplier_org_id(supplier_org_id, valid):
    validator = BolSupplierValidator(schema_new_bol_supplier)
    new_supplier = {
        'materialized_path': ['test'],
        'project_resource_id': 'test-project-resource-id',
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'supplier_org_id': supplier_org_id,
    }
    assert validator.validate(new_supplier) == valid
    if not valid:
        field_error = validator.document_error_tree['supplier_org_id']
        assert cerberus.errors.CUSTOM in field_error
        assert field_error.errors[0].info == ('empty values not allowed',)


@pytest.mark.parametrize("materialized_path, valid", [
    (['', ''], False),
    (['  ', ' '], False),
    (['', '  '], False),
    (['any-value', '', '  '], False),
    (['any-value', 'test value'], True),
])
def test_new_supplier_materialized_path(materialized_path, valid):
    validator = BolSupplierValidator(schema_new_bol_supplier)
    new_supplier = {
        'project_resource_id': 'test-project-resource-id',
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'supplier_org_id': 'test-supplier_org_id',
        'materialized_path': materialized_path,
    }
    assert validator.validate(new_supplier) == valid
    if not valid:
        # nested field
        field_error = validator.document_error_tree['materialized_path'].errors[0].info[0]
        assert cerberus.errors.CUSTOM in field_error
        assert field_error[0].info == ('empty values not allowed',)


@pytest.mark.parametrize("supplier_contacts, valid", [
    ([], True),
    (
        [
            {
                'supplier_contact_person_id': 'fake-person-id',
                'supplier_contact_email': 'email@example'
            }
        ], True
    ),
    (
        [
            {
                'supplier_contact_person_id': 'fake-person-id',
                'supplier_contact_email': None,
            }
        ], True
    ),
    (
        [
            {
                'supplier_contact_person_id': None,
                'supplier_contact_email': 'email@example',
            }
        ], True
    ),
    (
        [
            {
                'supplier_contact_person_id': None,
                'supplier_contact_email': None,
            }
        ], False
    )
])
def test_new_bol_supplier_supplier_contacts_values(supplier_contacts, valid):
    validator = BolSupplierValidator(schema_new_bol_supplier)
    new_supplier = {
        'materialized_path': ['test'],
        'project_resource_id': 'test-project-resource-id',
        'supplier_type': 'linked',
        'supplier_org_id': 'test-supplier-org-id',
        'supplier_role': 'supplier',
        'supplier_contacts': supplier_contacts,
    }
    assert validator.validate(new_supplier) == valid
    if not valid:
        assert cerberus.errors.ANYOF in \
            validator.document_error_tree['supplier_contacts'].errors[0].child_errors


# EXISTING BOL SUPPLIER TESTS

@pytest.mark.parametrize("supplier_contacts, valid", [
    ([], True),
    (
        [
            {
                'supplier_contact_person_id': 'fake-person-id',
                'supplier_contact_email': 'email@example'
            }
        ], True
    ),
    (
        [
            {
                'supplier_contact_person_id': 'fake-person-id',
                'supplier_contact_email': None,
            }
        ], True
    ),
    (
        [
            {
                'supplier_contact_person_id': None,
                'supplier_contact_email': 'email@example',
            }
        ], True
    ),
    (
        [
            {
                'supplier_contact_person_id': None,
                'supplier_contact_email': None,
            }
        ], True
    )
])
def test_existing_bol_supplier_supplier_contacts_values(supplier_contacts, valid):
    validator = BolSupplierValidator(schema_existing_bol_supplier)
    supplier = {
        'supplier_contacts': supplier_contacts,
        'revision': 'anything',
    }
    assert validator.validate(supplier) == valid
    if not valid:
        assert cerberus.errors.ANYOF in \
            validator.document_error_tree['supplier_contacts'].errors[0].child_errors


def test_existing_supplier_only_revision_required():
    validator = BolSupplierValidator(schema_existing_bol_supplier)
    assert validator.validate({'revision': 'anything'})


@pytest.mark.parametrize("supplier_role, valid", [
    ('', False),
    ('  ', False),
    ('not valid', False),
    ('main_contractor', True),
    ('supervisor', True),
    ('supplier', True),
])
def test_existing_supplier_supplier_role_values(supplier_role, valid):
    validator = BolSupplierValidator(schema_existing_bol_supplier)
    supplier = {
        'revision': 'test revision',
        'supplier_role': supplier_role,
    }
    assert validator.validate(supplier) == valid
    if not valid:
        assert cerberus.errors.UNALLOWED_VALUE in \
            validator.document_error_tree['supplier_role']


@pytest.mark.parametrize("supplier_type, valid", [
    ('', False),
    ('  ', False),
    ('not valid', False),
    ('linked', True),
    ('unlinked', True),
])
def test_existin_supplier_supplier_type_values(supplier_type, valid):
    validator = BolSupplierValidator(schema_existing_bol_supplier)
    supplier = {
        'revision': 'test revision',
        'supplier_type': supplier_type,
    }
    assert validator.validate(supplier) == valid
    if not valid:
        assert cerberus.errors.UNALLOWED_VALUE in \
            validator.document_error_tree['supplier_type']


@pytest.mark.parametrize("project_resource_id, valid", [
    ('', False),
    ('  ', False),
    ('any-value', True),
])
def test_existing_supplier_project_resource_id(project_resource_id, valid):
    validator = BolSupplierValidator(schema_existing_bol_supplier)
    supplier = {
        'revision': 'test revision',
        'project_resource_id': project_resource_id,
    }
    assert validator.validate(supplier) == valid
    if not valid:
        field_error = validator.document_error_tree['project_resource_id']
        assert cerberus.errors.CUSTOM in field_error
        assert field_error.errors[0].info == ('empty values not allowed',)


@pytest.mark.parametrize("supplier_org_id, valid", [
    ('', False),
    ('  ', False),
    ('any-value', True),
])
def test_existing_supplier_supplier_org_id(supplier_org_id, valid):
    validator = BolSupplierValidator(schema_existing_bol_supplier)
    supplier = {
        'revision': 'test revision',
        'supplier_org_id': supplier_org_id,
    }
    assert validator.validate(supplier) == valid
    if not valid:
        field_error = validator.document_error_tree['supplier_org_id']
        assert cerberus.errors.CUSTOM in field_error
        assert field_error.errors[0].info == ('empty values not allowed',)


@pytest.mark.parametrize("materialized_path, valid", [
    (['', ''], False),
    (['  ', ' '], False),
    (['', '  '], False),
    (['any-value', '', '  '], False),
    (['any-value', 'test value'], True),
])
def test_existing_supplier_materialized_path(materialized_path, valid):
    validator = BolSupplierValidator(schema_existing_bol_supplier)
    new_supplier = {
        'revision': 'test revision',
        'materialized_path': materialized_path,
    }
    assert validator.validate(new_supplier) == valid
    if not valid:
        # nested field
        field_error = validator.document_error_tree['materialized_path'].errors[0].info[0]
        assert cerberus.errors.CUSTOM in field_error
        assert field_error[0].info == ('empty values not allowed',)
