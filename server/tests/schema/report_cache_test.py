import cerberus
import pytest

from boldataapi.schema import (
    ReportCacheValidator,
    schema_existing_report_cache,
    schema_new_report_cache,
)


pytestmark = pytest.mark.usefixtures("config")


def test_new_report_cache_required():
    validator = ReportCacheValidator(schema_new_report_cache)
    required_fields = [
        'key',
        'provider',
        'type',
        'value',
    ]
    assert not validator.validate({})
    assert len(validator.errors.keys()) == len(required_fields)
    for field in required_fields:
        assert cerberus.errors.REQUIRED_FIELD in validator.document_error_tree[field]


def test_new_report_cache_nullable():
    validator = ReportCacheValidator(schema_new_report_cache)
    report_cache = {
        'correlation_id': None,
        'expires_at': None,
        'interested_org_id': None,
        'key': 'any-key',
        'provider': 'creditsafe_ggs',
        'type': 'statusreports_raw',
        'value': 'any-value',
    }
    assert validator.validate(report_cache)


@pytest.mark.parametrize("provider, valid", [
    ('creditsafe_connect', True),
    ('creditsafe_ggs', True),
    ('creditsafe_v2', True),
    ('creditsafe', True),
    ('creditsafe_connect_core', True),
    ('some-provider', False),
    (' ', False),
    ('', False),
    (' creditsafe ', False),
])
def test_new_report_cache_provider_required(provider, valid):
    validator = ReportCacheValidator(schema_new_report_cache)
    report_cache = {
        'key': 'any-key',
        'provider': provider,
        'type': 'statusreports_raw',
        'value': 'any-value',
    }
    assert validator.validate(report_cache) == valid
    if not valid:
        assert 'unallowed value' in validator.errors['provider'][0]


@pytest.mark.parametrize("type, valid", [
    ('statusreports_raw', True),
    ('statusreports_parsed', True),
    ('', False),
    (' ', False),
    ('some-type', False),
    (' statusreports_parsed ', False),
])
def test_new_report_cache_type_required(type, valid):
    validator = ReportCacheValidator(schema_new_report_cache)
    report_cache = {
        'key': 'any-key',
        'provider': 'creditsafe',
        'type': type,
        'value': 'any-value',
    }
    assert validator.validate(report_cache) == valid
    if not valid:
        assert 'unallowed value' in validator.errors['type'][0]


@pytest.mark.parametrize("test_value, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid value ', True),
])
def test_new_report_cache_key_value_validation(test_value, valid):
    report_cache = {
        'key': test_value,
        'provider': 'creditsafe_connect',
        'type': 'statusreports_raw',
        'value': test_value,
    }
    validator = ReportCacheValidator(schema_new_report_cache)
    assert validator.validate(report_cache) == valid
    if not valid:
        assert 'empty values not allowed' in validator.errors['key'][0]
        assert 'empty values not allowed' in validator.errors['value'][0]


def test_existing_report_cache_not_required():
    validator = ReportCacheValidator(schema_existing_report_cache)
    assert validator.validate({})
