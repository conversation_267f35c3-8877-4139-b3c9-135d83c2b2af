import pytest

from boldataapi.schema import (
    ProjectValidator,
    schema_existing_project,
    schema_new_project,
)
from boldataapi.services.projects import (
    PROJECT_ID_TYPE,
    TAX_ID,
)


pytestmark = pytest.mark.usefixtures("config")


# NEW PROJECT TESTS

def test_new_project_names_required():
    validator = ProjectValidator(schema_new_project)
    assert not validator.validate({})
    assert validator.errors['names'] == ['required field']


# TODO: make error messages more readable
# implement helper function to remove [{}]
@pytest.mark.parametrize("names, error_msg", [
    (None, ['null value not allowed']),
    ([''], [{0: ['empty values not allowed']}]),
    (['     '], [{0: ['empty values not allowed']}]),
    ([], ['empty values not allowed']),
    ([1],  [{0: ['must be of string type']}]),
    ([True],  [{0: ['must be of string type']}]),
])
def test_new_project_names_not_valid(names, error_msg):
    validator = ProjectValidator(schema_new_project)
    assert not validator.validate({'names': names})
    assert validator.errors['names'] == error_msg


def test_new_project_project_responsible_org_required():
    validator = ProjectValidator(schema_new_project)
    new_project = {
        'names': ['Test project'],
        'state': 'draft',
    }
    assert not validator.validate(new_project)
    assert validator.errors['project_responsible_org'] == ['required field']


@pytest.mark.parametrize("project_responsible_org, error_msg", [
    (None, ['null value not allowed']),
    ('', ['empty values not allowed']),
    (' ', ['empty values not allowed']),
])
def test_new_project_project_responsible_org_not_allowed(project_responsible_org, error_msg):
    new_project = {
        'names': ['Test project name'],
        'state': 'active',
        'project_responsible_org': project_responsible_org,
    }
    validator = ProjectValidator(schema_new_project)
    assert not validator.validate(new_project)
    assert validator.errors['project_responsible_org'] == error_msg


def test_new_project_state_required():
    validator = ProjectValidator(schema_new_project)
    new_project = {
        'names': ['Test project'],
    }
    assert not validator.validate(new_project)
    assert validator.errors['state'] == ['required field']


@pytest.mark.parametrize("state, error_msg", [
    (None, ['null value not allowed']),
    ('not valid', ['unallowed value not valid']),
    ('', ['unallowed value ']),
])
def test_new_project_state_not_allowed(state, error_msg):
    validator = ProjectValidator(schema_new_project)
    assert not validator.validate({'state': state})
    assert validator.errors['state'] == error_msg


@pytest.mark.parametrize("not_valid_project_ids", [
    [
        {
            'not-valid-key': 'iamnotvalid',
            'project_id_type': TAX_ID,
            'project_id': 'tax-id',
        }
    ],
    [
        {
            'project_id_type': 'not_valid-key',
            'project_id': 'tax-id',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'project-id',
        }
    ],
    [
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'project-id',
        },
        {
            'project_id_type': TAX_ID,
            'project_id': 'tax-id',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'other-project-id',
        },
    ]
])
def test_new_project_project_ids_not_valid(not_valid_project_ids):
    validator = ProjectValidator(schema_new_project)
    assert not validator.validate({'project_ids': not_valid_project_ids})
    assert validator.errors['project_ids']


def test_new_project_project_id_type_max_occurrences():
    project_ids = [
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        },
    ]

    validator = ProjectValidator(schema_new_project)
    assert not validator.validate({'project_ids': project_ids})
    assert validator.errors['project_ids'] == \
        ['Every project_id_type can occur in list only 1 times']


@pytest.mark.parametrize("project_ids, state, valid", [
    ({'project_ids': None}, 'active', False),
    ({'project_ids': None}, 'draft', True),
    ({'project_ids': []}, 'active', False),
    ({'project_ids': []}, 'draft', True),
    (None, 'active', False),
    (None, 'closed', True),
    ({
        'project_ids': [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'test project id'
            }
        ]
    }, 'active', True),
])
def test_new_project_project_ids_required_state_active(project_ids, state, valid):
    validator = ProjectValidator(schema_new_project)
    new_project = {
        'names': ['Test project name'],
        'project_responsible_org': 'any-project-responsible-org',
        'state': state,
    }
    if project_ids:
        new_project.update(project_ids)
    assert validator.validate(new_project) == valid
    if valid:
        assert not validator.errors.get('project_ids')
    else:
        assert validator.errors['project_ids'] == ['Field is required when state is active']


# EXISTING PROJECT TESTS


# catch all
def test_existing_project_none_required():
    validator = ProjectValidator(schema_existing_project)
    assert validator.validate({})


# TODO: make error messages more readable
# implement helper function to remove [{}]
@pytest.mark.parametrize("names, error_msg", [
    (None, ['null value not allowed']),
    ([''], [{0: ['empty values not allowed']}]),
    (['     '], [{0: ['empty values not allowed']}]),
    ([], ['empty values not allowed']),
    ([1],  [{0: ['must be of string type']}]),
    ([True],  [{0: ['must be of string type']}]),
])
def test_existing_project_names_not_valid(names, error_msg):
    validator = ProjectValidator(schema_existing_project)
    assert not validator.validate({'names': names})
    assert validator.errors['names'] == error_msg


@pytest.mark.parametrize("not_valid_project_ids", [
    None,
    [
        {
            'not-valid-key': 'iamnotvalid',
            'project_id_type': TAX_ID,
            'project_id': 'tax-id',
        }
    ],
    [
        {
            'project_id_type': 'not_valid-key',
            'project_id': 'tax-id',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'project-id',
        }
    ],
    [
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'project-id',
        },
        {
            'project_id_type': TAX_ID,
            'project_id': 'tax-id',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'other-project-id',
        },
    ]
])
def test_existing_project_project_ids_not_valid(not_valid_project_ids):
    validator = ProjectValidator(schema_existing_project)
    assert not validator.validate({'project_ids': not_valid_project_ids})
    assert validator.errors['project_ids']


def test_existing_project_project_id_type_max_occurrences():
    project_ids = [
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        },
    ]

    validator = ProjectValidator(schema_existing_project)
    assert not validator.validate({'project_ids': project_ids})
    assert validator.errors['project_ids'] == \
        ['Every project_id_type can occur in list only 1 times']


@pytest.mark.parametrize("state, error_msg", [
    (None, ['null value not allowed']),
    ('not valid', ['unallowed value not valid']),
    ('', ['unallowed value ']),
])
def test_existing_project_state_not_allowed(state, error_msg):
    validator = ProjectValidator(schema_existing_project)
    assert not validator.validate({'state': state})
    assert validator.errors['state'] == error_msg


@pytest.mark.parametrize("project_responsible_org, error_msg", [
    (None, ['null value not allowed']),
    ('', ['empty values not allowed']),
    (' ', ['empty values not allowed']),
])
def test_existing_project_project_responsible_org_not_allowed(project_responsible_org, error_msg):
    new_project = {
        'names': ['Test project name'],
        'state': 'active',
        'project_responsible_org': project_responsible_org,
    }
    validator = ProjectValidator(schema_existing_project)
    assert not validator.validate(new_project)
    assert validator.errors['project_responsible_org'] == error_msg
