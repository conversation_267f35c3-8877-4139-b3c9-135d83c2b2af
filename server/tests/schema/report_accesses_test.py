import cerberus
import pytest

from boldataapi.schema import (
    ReportAccessValidator,
    schema_existing_report_access,
    schema_new_report_access,
)


pytestmark = pytest.mark.usefixtures("config")


def test_new_report_accesses_required():
    validator = ReportAccessValidator(schema_new_report_access)
    required_fields = [
        'access_time',
        'arkisto_id',
        'status',
        'customer_org_id',
        'gov_org_ids',
    ]
    assert not validator.validate({})
    assert len(validator.errors.keys()) == len(required_fields)
    for field in required_fields:
        assert cerberus.errors.REQUIRED_FIELD in \
            validator.document_error_tree[field]


def test_new_report_access_nullable():
    validator = ReportAccessValidator(schema_new_report_access)
    report_access = {
        'report_id': None,
        'client_id': None,
        'org_id': 'any-org-id',
        'customer_org_id': 'any-id',
        'status': 'active',
        'access_time': '2012-01-14T00:00:00',
        'arkisto_id': 'test-arkisto-id',
        'gov_org_ids': [
            {
                'gov_org_id': 'test',
                'country': 'FI',
                'org_id_type': 'registration_number',
            }
        ],
    }
    assert validator.validate(report_access)


def test_new_report_accesses_gov_org_ids_required():
    validator = ReportAccessValidator(schema_new_report_access)
    required_gov_org_ids_fields = [
        'gov_org_id',
        'country',
        'org_id_type',
    ]
    report_access = {
        'org_id': 'any-org-id',
        'customer_org_id': 'any-id',
        'status': 'active',
        'access_time': '2012-01-14T00:00:00',
        'arkisto_id': 'test-arkisto-id',
        'gov_org_ids': [{}],
    }
    # schema does not validate
    assert not validator.validate(report_access)
    # only single type of error raised
    assert len(validator.errors) == 1
    nested_errors = \
        validator.document_error_tree['gov_org_ids'].errors[0].child_errors[0].child_errors

    # amount of errors is same as required fields
    assert len(nested_errors) == len(required_gov_org_ids_fields)
    # error is based on required constraint
    assert cerberus.errors.REQUIRED_FIELD in \
        nested_errors


def test_new_report_accesses_gov_org_ids_maxlength():
    validator = ReportAccessValidator(schema_new_report_access)
    gov_org_ids = [
        {
            'country': 'FI',
            'gov_org_id': 'test-company-gov-id',
            'org_id_type': 'registration_number',
        },
        {
            'country': 'FI',
            'gov_org_id': 'other-company-gov-id',
            'org_id_type': 'registration_number',
        },
    ]

    report_access = {
        'org_id': 'any-org-id',
        'customer_org_id': 'any-id',
        'status': 'active',
        'access_time': '2012-01-14T00:00:00',
        'arkisto_id': 'test-arkisto-id',
        'gov_org_ids': gov_org_ids,
    }
    assert not validator.validate(report_access)
    assert len(validator.errors) == 1
    assert cerberus.errors.MAX_LENGTH in validator.document_error_tree['gov_org_ids']


@pytest.mark.parametrize("customer_org_id, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid value ', True),
])
def test_new_report_access_customer_org_id_values(customer_org_id, valid):
    report_access = {
        'org_id': 'any-org-id',
        'customer_org_id': customer_org_id,
        'status': 'active',
        'access_time': '2012-01-14T00:00:00',
        'arkisto_id': 'test-arkisto-id',
        'gov_org_ids': [
            {
                'country': 'FI',
                'gov_org_id': 'test-company-gov-id',
                'org_id_type': 'registration_number',
            }
        ],
    }
    validator = ReportAccessValidator(schema_new_report_access)
    assert validator.validate(report_access) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['customer_org_id']


@pytest.mark.parametrize("arkisto_id, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid value ', True),
])
def test_new_report_access_customer_arkisto_id_values(arkisto_id, valid):
    report_access = {
        'org_id': 'any-org-id',
        'customer_org_id': 'test',
        'status': 'active',
        'access_time': '2012-01-14T00:00:00',
        'arkisto_id': arkisto_id,
        'gov_org_ids': [
            {
                'country': 'FI',
                'gov_org_id': 'test-company-gov-id',
                'org_id_type': 'registration_number',
            }
        ],
    }
    validator = ReportAccessValidator(schema_new_report_access)
    assert validator.validate(report_access) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['arkisto_id']


@pytest.mark.parametrize("gov_org_id, valid", [
    ('', False),
    ('  ', False),
    ('test-test', True),
    (' valid value ', True),
])
def test_new_report_access_customer_gov_org_id_values(gov_org_id, valid):
    report_access = {
        'org_id': 'any-org-id',
        'customer_org_id': 'test',
        'status': 'active',
        'access_time': '2012-01-14T00:00:00',
        'arkisto_id': 'arkisto_id',
        'gov_org_ids': [
            {
                'country': 'FI',
                'gov_org_id': gov_org_id,
                'org_id_type': 'registration_number',
            }
        ],
    }
    validator = ReportAccessValidator(schema_new_report_access)
    assert validator.validate(report_access) == valid
    if not valid:
        assert cerberus.errors.CUSTOM in \
            validator.document_error_tree['gov_org_ids'].errors[0].child_errors[0].child_errors


def test_existing_report_accesses_not_required():
    validator = ReportAccessValidator(schema_existing_report_access)
    assert validator.validate({})
