import pytest

from boldataapi.schema import (
    PreannouncementValidator,
    schema_new_pa_form,
    schema_new_preannouncement,
    schema_update_pa_form,
    schema_update_preannouncement,
)


pytestmark = pytest.mark.usefixtures("config")


# POST PREANNOUNCEMENT TESTS

def test_new_preannouncement_fields_required(config):
    validator = PreannouncementValidator(schema_new_preannouncement)
    assert not validator.validate({})
    assert validator.errors['for_supplier_id'] == ['required field']
    assert validator.errors['project_id'] == ['required field']
    assert validator.errors['status'] == ['required field']


@pytest.mark.parametrize('field', [
    'for_supplier_id',
])
@pytest.mark.parametrize('field_value, error_msg', [
    (None, ['null value not allowed']),
    ('', ['empty values not allowed']),
    ('     ', ['empty values not allowed']),
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
])
def test_new_preannouncement_supplier_fks_not_valid(field, field_value,
                                                    error_msg):
    validator = PreannouncementValidator(schema_new_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'project_id'
])
@pytest.mark.parametrize('field_value, error_msg', [
    (None, ['null value not allowed']),
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
])
def test_new_preannouncement_project_id_not_valid(field, field_value,
                                                  error_msg):
    validator = PreannouncementValidator(schema_new_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'status'
])
@pytest.mark.parametrize('field_value, error_msg', [
    (None, ['null value not allowed']),
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
    ('NOT IN ENUM',  ['unallowed value NOT IN ENUM']),
])
def test_new_preannouncement_status_not_valid(field, field_value,
                                              error_msg):
    validator = PreannouncementValidator(schema_new_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'assigned_to_company_id',
    'assigned_to_supplier_id'
])
@pytest.mark.parametrize('field_value, error_msg', [
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
])
def test_new_preannouncement_string_fields_not_valid(field, field_value,
                                                     error_msg):
    validator = PreannouncementValidator(schema_new_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'assigned_to_time'
])
@pytest.mark.parametrize('field_value, error_msg', [
    ([], ["field 'assigned_to_time' cannot be coerced: strptime() argument 1 must be "
          'str, not list',
          'must be of datetime type']
     ),
    (1, ["field 'assigned_to_time' cannot be coerced: strptime() argument 1 must be "
         'str, not int',
         'must be of datetime type']
     ),
    ('TrUe', ["field 'assigned_to_time' cannot be coerced: time data 'TrUe' does not match "
              "format '%Y-%m-%dT%H:%M:%S.%f%z'",
              'must be of datetime type']
     ),
])
def test_new_preannouncement_datetime_field_not_valid(field, field_value,
                                                      error_msg):
    validator = PreannouncementValidator(schema_new_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('status', [
    'created',
    'registered',
    'confirmed',
    'rejected',
])
def test_new_preannouncement_status(status):
    validator = PreannouncementValidator(schema_new_preannouncement)
    new_pa = {
        'status': status,
        'created_by_supplier_id': 'some ID',
        'for_supplier_id': 'some ID',
        'project_id': 'some ID',
    }
    assert validator.validate(new_pa)


# PUT PREANNOUNCEMENT TESTS

@pytest.mark.parametrize('field', [
    'collective_agreement_name',
    'foreman_first_name',
    'foreman_last_name',
    'foreman_phone_number',
    'foreman_email',
])
@pytest.mark.parametrize('field_value, error_msg', [
    (None, ['null value not allowed']),
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
])
def test_new_pa_form_text_fields_not_valid(field, field_value,
                                           error_msg):
    validator = PreannouncementValidator(schema_new_pa_form)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'has_collective_agreement',
    'is_one_man_company',
    'has_permanent_establishment',
])
@pytest.mark.parametrize('field_value, error_msg', [
    (None, ['null value not allowed']),
    ([], ['must be of boolean type']),
    (1,  ['must be of boolean type']),
    ('True',  ['must be of boolean type']),
])
def test_new_pa_form_bool_fields_not_valid(field, field_value,
                                           error_msg):
    validator = PreannouncementValidator(schema_new_pa_form)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'rejected_name',
    'confirmed_name',
])
@pytest.mark.parametrize('field_value, error_msg', [
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
])
def test_update_pa_form_text_fields_not_valid(field, field_value,
                                              error_msg):
    validator = PreannouncementValidator(schema_update_pa_form)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'confirmed_time',
    'rejected_time',
])
@pytest.mark.parametrize('field_value, error_msg', [
    ([], 'must be of datetime type'),
    (1, 'must be of datetime type'),
    ('TrUe', 'must be of datetime type'),
])
def test_update_pa_form_time_fields_not_valid(field, field_value,
                                              error_msg):
    validator = PreannouncementValidator(schema_update_pa_form)
    assert not validator.validate({field: field_value})
    assert validator.errors[field][-1] == error_msg


@pytest.mark.parametrize('field', [
    'status',
])
@pytest.mark.parametrize('field_value, error_msg', [
    (None, ['null value not allowed']),
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
    ('NOT IN ENUM',  ['unallowed value NOT IN ENUM']),
])
def test_update_preannouncement_status_not_valid(field, field_value, error_msg):
    validator = PreannouncementValidator(schema_update_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'assigned_to_company_id',
    'assigned_to_supplier_id'
])
@pytest.mark.parametrize('field_value, error_msg', [
    ([], ['must be of string type']),
    (1,  ['must be of string type']),
    (True,  ['must be of string type']),
])
def test_update_preannouncement_assigned_to_fields_not_valid(field,
                                                             field_value,
                                                             error_msg):
    validator = PreannouncementValidator(schema_update_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'assigned_to_time'
])
@pytest.mark.parametrize('field_value, error_msg', [
    ([], ["field 'assigned_to_time' cannot be coerced: strptime() argument 1 must be "
          'str, not list',
          'must be of datetime type']
     ),
    (1, ["field 'assigned_to_time' cannot be coerced: strptime() argument 1 must be "
         'str, not int',
         'must be of datetime type']
     ),
    ('TrUe', ["field 'assigned_to_time' cannot be coerced: time data 'TrUe' does not match "
              "format '%Y-%m-%dT%H:%M:%S.%f%z'",
              'must be of datetime type']
     ),
])
def test_update_preannouncement_datetime_field_not_valid(field, field_value,
                                                         error_msg):
    validator = PreannouncementValidator(schema_update_preannouncement)
    assert not validator.validate({field: field_value})
    assert validator.errors[field] == error_msg


@pytest.mark.parametrize('field', [
    'assigned_to_company_id',
    'assigned_to_supplier_id',
    'assigned_to_time',
])
def test_update_preannouncement_nullable_fields(field):
    '''Some fields can be wiped - updated to NULL'''
    validator = PreannouncementValidator(schema_update_preannouncement)

    field_value = None
    assert validator.validate({field: field_value})
