import logging
import os
import pathlib
import subprocess
import time

import bottle
import mock
import pytest
import sqlalchemy as sa
import sqlalchemy.exc

import boldataapi.application
from boldataapi.application import application, setup_application
from boldataapi.fixtures.factories import create_cs_account
from boldataapi.storage.db import SQLAlchemyEngine
from boldataapi.testing import TestApp

DOCKER_COMPOSE = ['docker', 'compose']  # ['docker-compose'] for v1, ['docker', 'compose'] for v2

CONFIG = {
    'main': {
        'app_name': 'bol-data-api',
        'base_url': '/api/v1/boldata',
        'host': '0.0.0.0',
        'port': '5555',
        'sentry_dsn': '',
        'test_db_service': '',
    },
    'auth': {
        'verify_requests': True,
        'gluu_addresses': 'http://localhost:99999',
    },
    'db': {
        'sqlalchemy_url': os.getenv('BOL_TEST_SQLA_URL',
                                    'postgresql://localhost:54325/bol'),
        'vault_db_username': os.getenv('BOL_TEST_SQLA_USERNAME', 'dbuser'),
        'vault_db_password': os.getenv('BOL_TEST_SQLA_PASSWORD', 'dbpwd'),
    },
    'feature-flags': {
        'core': False,
    },
}


@pytest.fixture
def config():
    try:
        yield boldataapi.config.set_config(CONFIG)
    finally:
        boldataapi.config.reset_config()


@pytest.fixture
def set_feature_flags(config):
    def factory(flags, reset=False):
        if reset:
            config['feature-flags'].clear()
        config.read_dict({'feature-flags': flags})
    return factory


@pytest.fixture
def set_feature_flag(set_feature_flags):
    def factory(flag, value):
        set_feature_flags({flag: value})
    return factory


@pytest.fixture
def app(config, db):
    setup_application(
        db=db,
        catch_exceptions=False,
        add_auth_plugin=False,
    )
    app = TestApp(application)
    yield app
    # Reset bottle request.
    bottle.request = bottle.LocalRequest()


# Used by accesscontrol that does not use fixtures. Refusing to make a fixture.
# Favoring consistency with the original accesscontrol code for easier diffing.
def get_null_log():
    null_log = logging.getLogger(__name__)
    null_log.setLevel(logging.NOTSET)
    return null_log


@pytest.fixture(scope='session')
def local_postgresql():
    if os.getenv('BOL_TEST_SQLA_URL'):
        # looks like we're using a real database, no need for docker-compose
        yield
        return
    engine = create_engine()
    if is_postgres_rechable(engine):
        # already running, great!
        ensure_schema(engine)
        yield
        return
    # start it up, then shut it down
    root = pathlib.Path(__file__).parents[2]
    test_db_service = CONFIG['main'].get('test_db_service') or 'db-test'
    # Docker Compose v2 uses $PWD to determine the name prefix of containers/networks/everything.
    # You get mysterious "network XXXXXXX not found" errors when $PWD doesn't match the actual
    # working directory.
    env = {**os.environ, 'PWD': str(root)}
    subprocess.run(
        DOCKER_COMPOSE + ['--ansi=never', 'up', '-d', test_db_service], cwd=root, env=env
    )
    wait_for_postgres(engine)
    ensure_schema(engine)
    yield
    try:
        subprocess.Popen(
            DOCKER_COMPOSE + ['--ansi=never', 'rm', '-sf', test_db_service],
            cwd=root,
            env=env,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        ).wait(timeout=2.0)
    except subprocess.TimeoutExpired:
        # guess pytest will show the user a warning about subprocess still
        # running, but we don't care
        pass


def create_engine():
    sqlalchemy_url = CONFIG['db']['sqlalchemy_url']
    vault_db_username = CONFIG['db']['vault_db_username']
    vault_db_password = CONFIG['db']['vault_db_password']
    return sa.create_engine(
        sqlalchemy_url,
        echo=False,
        connect_args={
            'user': vault_db_username,
            'password': vault_db_password,
        },
    )


def is_postgres_rechable(engine):
    try:
        engine.execute("SELECT 1;")
    except Exception:
        return False
    else:
        return True


def wait_for_postgres(engine, timeout=30):
    deadline = time.clock_gettime(time.CLOCK_MONOTONIC) + timeout
    while not is_postgres_rechable(engine):
        if time.clock_gettime(time.CLOCK_MONOTONIC) >= deadline:
            engine.execute("SELECT 1;")
            return
        time.sleep(0.1)


def ensure_schema(engine):
    run_alembic('upgrade', 'head')


def run_alembic(*args):
    root = pathlib.Path(__file__).parents[2].absolute()
    alembic_bin = root / 'server' / 'env' / 'bin' / 'alembic'
    alembic_root = pathlib.Path(__file__).parents[2] / 'db' / 'alembic'
    subprocess.run(
        [alembic_bin, *args],
        cwd=alembic_root,
        env={
            **os.environ,
            'CWD': str(alembic_root),  # alembic doesn't care, unlike docker compose, but still
            'PYTHONPATH': str(alembic_root / 'migrations'),
            'BOLDATAAPI_DB_SQLALCHEMY_URL': CONFIG['db']['sqlalchemy_url'],
            'BOLDATAAPI_DB_VAULT_DB_USERNAME': CONFIG['db']['vault_db_username'],
            'BOLDATAAPI_DB_VAULT_DB_PASSWORD': CONFIG['db']['vault_db_password'],
        },
        check=True,
    )


@pytest.fixture(scope='session')
def db_engine(local_postgresql):
    engine = create_engine()
    db = SQLAlchemyEngine(engine)
    return db


@pytest.fixture
def db(db_engine):
    db = db_engine
    # Neutralize SQLAlchemyPlugin:
    # - if it commits to the DB, subsequent tests will not get clean tables and will fail;
    # - if it rolls back the transaction on requests that return 4xx errors,
    #   the tests will not be able to verify that the controller didn't make any
    #   changes to the contents of the database;
    # - if it removes the session, the test will not see any pending changes and will fail.
    # Note that we're using mock.patch directly here instead of the 'mocker' pytest fixture,
    # because we want to call the real db.session.remove() ourselves.
    with mock.patch.object(db.session, 'commit'), \
            mock.patch.object(db.session, 'rollback'), \
            mock.patch.object(db.session, 'remove'):
        yield db
    # Remove `db.session` after each test run to roll back the pending transaction.
    db.session.remove()


@pytest.fixture
def featureflags(config):
    def factory(flags):
        for flag, setting in flags.items():
            config.set('feature-flags', flag, str(setting))
    return factory


@pytest.fixture(scope="function")
def cs_account(db):
    data = {
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new account'
    }
    yield create_cs_account(db, **data)
