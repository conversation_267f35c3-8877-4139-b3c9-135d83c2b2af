# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

import pytest
import requests

import boldataapi.exceptions as exceptions
from boldataapi.accesscontrol.access_validator import AccessValidator
from tests.conftest import get_null_log


IDENTITY_MANAGER = "some address"
IDENTITY_MANAGERS = [IDENTITY_MANAGER]
VERIFY_REQUESTS = False
DEFAULT_SCOPES = ["scope1", "scope3", "scope2"]
EXTERNAL_TOKEN_VALIDATOR = (
    "boldataapi.accesscontrol.external_token_validator.ExternalTokenValidator.validate"
)
TOKEN_READER = "boldataapi.accesscontrol.token_reader.TokenReader.read_and_parse_token"


def test_raises_error_when_token_cannot_be_read(mocker):
    headers = {"foo": "boo"}
    token_reader = mocker.patch(TOKEN_READER)
    token_reader.side_effect = exceptions.InvalidTokenError()
    validator = AccessValidator(get_null_log())

    with pytest.raises(exceptions.InvalidTokenError):
        validator.validate(
            headers, DEFAULT_SCOPES, IDENTITY_MANAGERS, VERIFY_REQUESTS
        )


def test_raises_error_when_token_has_no_aud_field(mocker):
    decoded_token = {
        "scope": "scope1 scope2 scope3",
    }
    encoded_token = "some token"
    mocker.patch(TOKEN_READER, return_value=(encoded_token, decoded_token))
    validator = AccessValidator(get_null_log())

    with pytest.raises(exceptions.InvalidTokenError):
        validator.validate(
            "headers", DEFAULT_SCOPES, IDENTITY_MANAGERS, VERIFY_REQUESTS
        )


def test_raises_error_when_token_has_no_scope_field(mocker):
    decoded_token = {
        "aud": "some aud",
    }
    encoded_token = "some token"
    mocker.patch(TOKEN_READER, return_value=(encoded_token, decoded_token))
    validator = AccessValidator(get_null_log())

    with pytest.raises(exceptions.InvalidTokenError):
        validator.validate(
            "headers", DEFAULT_SCOPES, IDENTITY_MANAGERS, VERIFY_REQUESTS
        )


def test_raises_error_when_token_is_invalid_by_external_check(mocker):
    decoded_token = {"aud": "some aud", "scope": "scope1 scope2 scope3",
                     "iss": IDENTITY_MANAGER}
    encoded_token = "some token"
    mocker.patch(TOKEN_READER, return_value=(encoded_token, decoded_token))
    ext_validator = mocker.patch(EXTERNAL_TOKEN_VALIDATOR)
    ext_validator.return_value = False
    validator = AccessValidator(get_null_log())

    with pytest.raises(exceptions.InvalidTokenError):
        validator.validate(
            "headers", DEFAULT_SCOPES, IDENTITY_MANAGERS, VERIFY_REQUESTS
        )


def test_does_not_swallow_connection_error(mocker):
    decoded_token = {"aud": "some aud", "scope": "scope1 scope2 scope3",
                     "iss": IDENTITY_MANAGER}
    encoded_token = "some token"
    mocker.patch(TOKEN_READER, return_value=(encoded_token, decoded_token))
    ext_validator = mocker.patch(EXTERNAL_TOKEN_VALIDATOR)
    exception = requests.exceptions.ConnectionError()
    ext_validator.side_effect = exception
    validator = AccessValidator(get_null_log())

    with pytest.raises(requests.exceptions.ConnectionError):
        validator.validate(
            "headers", DEFAULT_SCOPES, IDENTITY_MANAGERS, VERIFY_REQUESTS
        )


def test_raises_error_when_token_does_not_have_one_scope(mocker):
    decoded_token = {"aud": "some aud", "scope": "scope1 scope2",
                     "iss": IDENTITY_MANAGER}
    encoded_token = "some token"
    mocker.patch(TOKEN_READER, return_value=(encoded_token, decoded_token))
    ext_validator = mocker.patch(EXTERNAL_TOKEN_VALIDATOR)
    ext_validator.return_value = True
    validator = AccessValidator(get_null_log())

    with pytest.raises(exceptions.InsufficientScopeError):
        validator.validate(
            "headers", DEFAULT_SCOPES, IDENTITY_MANAGERS, VERIFY_REQUESTS
        )


def test_returns_client_id_for_valid_token(mocker):
    headers = "headers"
    client_id = "some client"
    decoded_token = {"aud": client_id, "scope": "scope1 scope2 scope3",
                     "iss": IDENTITY_MANAGER}
    encoded_token = "some token"

    token_parser_mock = mocker.patch(
        TOKEN_READER, return_value=(encoded_token, decoded_token)
    )
    ext_validator = mocker.patch(EXTERNAL_TOKEN_VALIDATOR)
    ext_validator.return_value = True
    validator = AccessValidator(get_null_log())

    result = validator.validate(
        headers, DEFAULT_SCOPES, IDENTITY_MANAGERS, VERIFY_REQUESTS
    )

    assert client_id == result
    token_parser_mock.assert_called_once_with(headers)
    ext_validator.assert_called_once_with(
        encoded_token, IDENTITY_MANAGER, VERIFY_REQUESTS
    )


def test_returns_client_id_for_valid_token_if_no_scopes_required(mocker):
    headers = "headers"
    client_id = "some client"
    decoded_token = {"aud": client_id, "scope": "scope1 scope2 scope3",
                     "iss": IDENTITY_MANAGER}
    encoded_token = "some token"

    token_parser_mock = mocker.patch(
        TOKEN_READER, return_value=(encoded_token, decoded_token)
    )
    ext_validator = mocker.patch(EXTERNAL_TOKEN_VALIDATOR)
    ext_validator.return_value = True
    validator = AccessValidator(get_null_log())

    result = validator.validate(headers, "", IDENTITY_MANAGERS, VERIFY_REQUESTS)

    assert client_id == result
    token_parser_mock.assert_called_once_with(headers)
    ext_validator.assert_called_once_with(
        encoded_token, IDENTITY_MANAGER, VERIFY_REQUESTS
    )
