# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

import datetime

import jwt
import mock
import pytest
from freezegun import freeze_time

import boldataapi.accesscontrol.constants as constants
import boldataapi.exceptions as exceptions
from boldataapi.accesscontrol.token_reader import TokenReader


DEFAULT_SCOPES = ["scope1", "scope3", "scope2"]


def test_raises_error_when_no_auth_header():
    headers = {"foo": "boo"}
    reader = TokenReader(mock.MagicMock())

    with pytest.raises(exceptions.InvalidTokenError):
        reader.read_and_parse_token(headers)


def test_raises_error_when_empty_auth_header():
    headers = {constants.AUTH_HEADER_NAME: ""}
    reader = TokenReader(mock.MagicMock())

    with pytest.raises(exceptions.InvalidTokenError):
        reader.read_and_parse_token(headers)


def test_raises_error_when_bearer_without_token():
    headers = {constants.AUTH_HEADER_NAME: "bearer"}
    reader = TokenReader(mock.MagicMock())

    with pytest.raises(exceptions.InvalidTokenError):
        reader.read_and_parse_token(headers)


def test_raises_error_when_token_without_bearer():
    headers = {constants.AUTH_HEADER_NAME: "sometoken"}
    reader = TokenReader(mock.MagicMock())

    with pytest.raises(exceptions.InvalidTokenError):
        reader.read_and_parse_token(headers)


def test_raises_error_when_token_cannot_be_decoded():
    headers = {constants.AUTH_HEADER_NAME: "bearer sometoken"}

    reader = TokenReader(mock.MagicMock())

    with pytest.raises(exceptions.InvalidTokenError):
        reader.read_and_parse_token(headers)


def test_raises_error_when_token_is_expired():
    token = {
        "scope": "scope1 scope2",
        "aud": "some aud",
        "sub": "some sub",
        "exp": datetime.datetime(2016, 1, 1).timestamp()
    }
    encoded_token = jwt.encode(token, "secret")
    headers = {constants.AUTH_HEADER_NAME: "bearer " + encoded_token}
    reader = TokenReader(mock.MagicMock())

    with pytest.raises(exceptions.ExpiredTokenError):
        reader.read_and_parse_token(headers)


@freeze_time("2016-01-01")
def test_returns_encoded_and_decoded_tokens():
    token = {
        "scope": "scope1 scope2",
        "exp": datetime.datetime(2016, 1, 2).timestamp(),
        "sub": "some sub",
        "aud": "some aud"
    }
    encoded_token = jwt.encode(token, "secret")
    headers = {constants.AUTH_HEADER_NAME: "bearer " + encoded_token}
    reader = TokenReader(mock.MagicMock())

    result = reader.read_and_parse_token(headers)

    assert (encoded_token, token) == result
