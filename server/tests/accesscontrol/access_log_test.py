import logging
from datetime import datetime

import jwt
from freezegun import freeze_time

from boldataapi.fixtures import factories


API_URL = '/api/v1/boldata/'


def get_logs(caplog):
    log = ''
    for r in caplog.records:
        time = datetime.fromtimestamp(r.created).isoformat()
        log += f"[{r.levelno}.{r.levelname}] {r.message} {time}"
        if r.stack_info is not None:
            log += f'\n{r.stack_info}'
        log += "\n"
    return log


def make_token():
    return jwt.encode({
        'sub': 'le subject',
        'aud': 'ze client',
    }, key="secret", algorithm="HS256")


@freeze_time('2020-11-11')
def test_get_list_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)
    p1 = factories.create_project(db)
    p2 = factories.create_project(db)

    resp = app.get(API_URL + 'projects', headers={
        'Qvarn-why': "for important reasons",
        'Authorization': f"Bearer {make_token()}",
    })
    assert 200 == resp.status_code

    expected = (
        '[20.INFO] Getting all project ids 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "LIST", '
        '"resource_type": "project", '
        '"resource_ids": ["%s", "%s"], '
        '"resource_revision": null, '
        '"accessors": ['
        '{"accessor_id": "le subject",'
        ' "accessor_type": "person"}, '
        '{"accessor_id": "ze client",'
        ' "accessor_type": "client"}], '
        '"reason": "for important reasons", '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % tuple(sorted([p1['external_id'], p2['external_id']]))
    )

    assert expected == get_logs(caplog)


@freeze_time('2020-11-11')
def test_get_one_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)
    p = factories.create_project(db)

    resp = app.get(API_URL + 'projects/' + p['external_id'])
    assert 200 == resp.status_code

    expected = (
        '[20.INFO] Getting project %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "GET", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["external_id"], p["external_id"])
    )

    assert expected == get_logs(caplog)


@freeze_time('2020-11-11')
def test_post_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)

    project_ids = []

    project = {
        'names': ['Test project'],
        'project_responsible_org': 'client-comp-id',
        'start_date': '2020-01-01',
        'end_date': '2020-12-01',
        'state': 'draft',
        'project_ids': project_ids,
        'project_responsible_person': None,
    }
    resp = app.post_json(API_URL + 'projects', project)
    p = resp.json
    assert 201 == resp.status_code

    expected = (
        '[20.INFO] Creating a new project 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] New project created %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "POST", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["id"], p["id"])
    )

    assert expected == get_logs(caplog)


@freeze_time('2020-11-11')
def test_put_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)

    project = factories.create_project(db)
    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'],
        {
            'names': ['Updated project name'],
        }
    )
    assert 200 == resp.status_code
    p = resp.json

    expected = (
        '[20.INFO] Updating project %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "PUT", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["id"], p["id"])
    )

    assert expected == get_logs(caplog)


@freeze_time('2020-11-11')
def test_delete_log(config, app, db, caplog):
    caplog.set_level(logging.INFO)

    p = factories.create_project(db)

    resp = app.delete(API_URL + 'projects/' + p['external_id'])
    assert 200 == resp.status_code

    expected = (
        '[20.INFO] Deleting project %s 2020-11-11T00:00:00'
        '\n'
        '[20.INFO] {'
        '"msg_type": "access_log", '
        '"operation": "DELETE", '
        '"resource_type": "project", '
        '"resource_ids": ["%s"], '
        '"resource_revision": null, '
        '"accessors": [], '
        '"reason": null, '
        '"ip_address": null, '
        '"timestamp": "2020-11-11T00:00:00Z"} '
        '2020-11-11T00:00:00'
        '\n'
        % (p["external_id"], p["external_id"])
    )

    assert expected == get_logs(caplog)
