# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

import json

import bottle
import mock
import pytest

import boldataapi.exceptions as exceptions
from boldataapi.accesscontrol.access_control_plugin import (
    AccessControlConfig,
    AccessControlPlugin,
)
from tests.conftest import get_null_log


ACCESS_CONTROL_MOCK = (
    "boldataapi.accesscontrol.access_validator.AccessValidator.validate"
)
REQUEST_MOCK = "boldataapi.accesscontrol.access_control_plugin.bottle.request"
CLIENT_ID = "some client id"
REQUEST_HEADERS = "some headers"
GLUU_ADDRESSES = "some address"


def test_checks_access_when_auth_required(mocker):
    access_validator_mock = mock.MagicMock()
    access_validator_mock.validate.return_value = CLIENT_ID

    request_mock = mocker.patch(REQUEST_MOCK)
    request_mock.headers = REQUEST_HEADERS
    request_mock.environ = {}

    config = AccessControlConfig(
        verify_requests=True,
        gluu_addresses=GLUU_ADDRESSES,
    )

    test_logger = get_null_log()
    plugin = AccessControlPlugin(test_logger, config, access_validator_mock)
    required_scopes = ["foo", "boo"]
    route = mock.Mock(config={"require_auth": True, "scopes": required_scopes})

    @plugin.apply(stub_call_back, route)
    def test():
        pass

    access_validator_mock.validate.assert_called_once_with(
        REQUEST_HEADERS, required_scopes, GLUU_ADDRESSES, True
    )


def test_checks_access_when_auth_required_with_no_scopes(mocker):
    access_validator_mock = mock.MagicMock()
    access_validator_mock.validate.return_value = CLIENT_ID

    request_mock = mocker.patch(REQUEST_MOCK)
    request_mock.headers = REQUEST_HEADERS
    request_mock.environ = {}

    config = AccessControlConfig(
        verify_requests=True,
        gluu_addresses=GLUU_ADDRESSES,
    )

    test_logger = get_null_log()
    plugin = AccessControlPlugin(test_logger, config, access_validator_mock)
    route = mock.Mock(config={"require_auth": True})

    @plugin.apply(stub_call_back, route)
    def test():
        pass

    access_validator_mock.validate.assert_called_once_with(
        REQUEST_HEADERS, [], GLUU_ADDRESSES, True
    )


def test_checks_access_when_auth_required_raises_error(mocker):
    access_validator_mock = mock.MagicMock()
    exception = exceptions.InvalidTokenError()
    access_validator_mock.validate.side_effect = exception

    request_mock = mocker.patch(REQUEST_MOCK)
    request_mock.headers = REQUEST_HEADERS
    request_mock.environ = {}

    config = AccessControlConfig(
        verify_requests=True,
        gluu_addresses=GLUU_ADDRESSES,
    )

    test_logger = get_null_log()
    plugin = AccessControlPlugin(test_logger, config, access_validator_mock)
    required_scopes = ["foo", "boo"]
    route = mock.Mock(config={"require_auth": True, "scopes": required_scopes})

    with pytest.raises(bottle.HTTPResponse) as ex:

        @plugin.apply(stub_call_back, route)
        def test():
            pass

    assert ex.value.status_code == exception.status_code
    assert json.loads(ex.value.body) == exception.serialize()

    access_validator_mock.validate.assert_called_once_with(
        REQUEST_HEADERS, required_scopes, GLUU_ADDRESSES, True
    )


def test_does_not_check_access_when_auth_not_required(mocker):
    access_validator_mock = mock.MagicMock()

    request_mock = mocker.patch(REQUEST_MOCK)
    request_mock.headers = REQUEST_HEADERS
    request_mock.environ = {}

    test_logger = get_null_log()
    plugin = AccessControlPlugin(test_logger, {}, access_validator_mock)
    route = mock.Mock(config={"require_auth": False})

    @plugin.apply(stub_call_back, route)
    def test():
        pass

    assert 0 == access_validator_mock.validate.call_count


def test_checks_access_require_auth_by_default(mocker):
    access_validator_mock = mock.MagicMock()

    request_mock = mocker.patch(REQUEST_MOCK)
    request_mock.headers = REQUEST_HEADERS
    request_mock.environ = {}

    config = AccessControlConfig(
        verify_requests=True,
        gluu_addresses=GLUU_ADDRESSES,
    )

    test_logger = get_null_log()
    plugin = AccessControlPlugin(test_logger, config, access_validator_mock)

    route = mock.Mock(config={})

    @plugin.apply(stub_call_back, route)
    def test():
        pass

    # access_validator gets called because ``require_auth == True``
    access_validator_mock.validate.assert_called_once_with(
        REQUEST_HEADERS, [], GLUU_ADDRESSES, True
    )


def stub_call_back(a, **kwargs):
    pass
