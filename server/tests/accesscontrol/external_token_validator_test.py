# Copied from https://git.vaultit.org/company/company-qvarn-poc/commit/7f14dc27

import requests_mock

import boldataapi.accesscontrol.constants as constants
from boldataapi.accesscontrol.external_token_validator import (
    ExternalTokenValidator,
)
from tests.conftest import get_null_log


TOKEN = "some token"
IDENTITY_MANAGER = "http://foo.com"
VERIFY_REQUESTS = False


def test_calls_identity_manager_with_correct_data_for_valid_token():
    with requests_mock.mock() as m:
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }
        m.post(
            IDENTITY_MANAGER + constants.URL_AUTH_TOKEN_VALIDATION,
            request_headers=headers,
            status_code=200,
        )
        validator = ExternalTokenValidator(get_null_log())
        result = validator.validate(TOKEN, IDENTITY_MANAGER, VERIFY_REQUESTS)
        assert result is True
