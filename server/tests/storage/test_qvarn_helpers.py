import pytest

from boldataapi.services.bol_suppliers import SUPPLIER_RESOURCE_TYPE
from boldataapi.services.projects import PROJECT_RESOURCE_TYPE
from boldataapi.services.report_accesses import REPORT_ACCESS_RESOURCE_TYPE
from boldataapi.services.status_reports import REPORT_RESOURCE_TYPE
from boldataapi.storage.qvarn_helpers import generate_qvarn_id


@pytest.mark.parametrize("resource_type,id_static_part", [
    (SUPPLIER_RESOURCE_TYPE, '594d'),
    (PROJECT_RESOURCE_TYPE, '45b1'),
    (REPORT_RESOURCE_TYPE, '4e67'),
    (REPORT_ACCESS_RESOURCE_TYPE, '28de'),
])
def test_generate_qvarn_id_resource_static_part(resource_type, id_static_part):
    assert generate_qvarn_id(resource_type.replace('qvarn_', ''))[:4] == id_static_part
