from boldataapi.exceptions import (
    ApiError,
    BadSearchCondition,
    ExpiredTokenError,
    InsufficientScopeError,
    InvalidTokenError,
    ParameterValidationFailed,
)


class SampleApiError(ApiError):
    status_code = 42
    message = "The frobnicator is on the fritz again"


def test_ApiError_serialize():
    # ApiError is an abstract base class, so we have to test a subclass
    err = SampleApiError(this='something', that='something else')
    assert err.serialize() == {
        'message': 'The frobnicator is on the fritz again',
        'error_code': 'SampleApiError',
        'error': {
            'this': 'something',
            'that': 'something else',
        },
    }


def test_ApiError_serialize_no_details():
    # ApiError is an abstract base class, so we have to test a subclass
    err = SampleApiError()
    assert err.serialize() == {
        'message': 'The frobnicator is on the fritz again',
        'error_code': 'SampleApiError',
    }


def test_ApiError_str():
    err = SampleApiError()
    assert str(err) == "The frobnicator is on the fritz again"


def test_InvalidTokenError():
    err = InvalidTokenError()
    assert err.status_code == 401
    assert str(err) == "Could not find token in header"


def test_ExpiredTokenError():
    err = ExpiredTokenError()
    assert err.status_code == 403
    assert str(err) == "Expired token"


def test_InsufficientScopeError():
    err = InsufficientScopeError()
    assert err.status_code == 403
    assert str(err) == "Bearer error='insufficient_scope'"


def test_BadSearchCondition():
    err = BadSearchCondition()
    assert err.status_code == 400
    assert str(err) == "Could not parse search condition"


def test_BadSearchCondition_custom_message():
    err = BadSearchCondition("don't know how to search on X")
    assert err.status_code == 400
    assert str(err) == "don't know how to search on X"


def test_ParameterValidationFailed():
    err = ParameterValidationFailed(bank_account=['required field'])
    assert err.status_code == 400
    assert err.message == 'Failed to validate parameters'
    assert str(err) == "Failed to validate parameters\n  - bank_account: ['required field']"
    assert err.error == dict(bank_account=['required field'])
