import sys

import mock

from boldataapi.version import get_alembic_version, get_bda_version


def test_returns_ok_status(config, app):
    resp = app.get('/api/v1/boldata/version')
    assert 200 == resp.status_code


def test_returns_version(config, app, db):
    resp = app.get('/api/v1/boldata/version')
    assert resp.json == {
        'version': get_bda_version(),
        'alembic_version': get_alembic_version(db),
        'alembic_version_on_startup': get_alembic_version(db),
        'docker_tag': None,
        'python_version': sys.version
    }


def test_does_not_fail_if_db_is_down(config, app, db):
    with mock.patch.object(db.session, 'execute', side_effect=Exception('(database fail)')):
        resp = app.get('/api/v1/boldata/version')
    assert resp.json == {
        'version': get_bda_version(),
        'alembic_version': '(database fail)',
        'alembic_version_on_startup': get_alembic_version(db),
        'docker_tag': None,
        'python_version': sys.version
    }
