import uuid

import pytest

from boldataapi.fixtures import factories
from boldataapi.services.project_supplier_comments import (
    get_project_supplier_comment,
    mark_comment_as_deleted,
)

API_URL = "/api/v1/boldata/"


def test_get_project_supplier_comment(app, db):
    """Test getting a project supplier comment by ID."""
    comment = factories.create_comment(db)

    resp = app.get(API_URL + f'project_supplier_comments/{comment["id"]}')

    assert resp.status_code == 200
    assert resp.json["id"] == comment["id"]


def test_get_not_found(app):
    """Test that getting a non-existing comment returns a 404 error."""
    non_existing_id = str(uuid.uuid4())
    resp = app.get(
        API_URL + f"project_supplier_comments/{non_existing_id}", expect_errors=True
    )

    assert resp.status_code == 404
    assert resp.json == {
        "error_code": "NotFound",
        "message": "Not found",
        "error": {"comment_id": non_existing_id},
    }


def test_get_invalid_uuid(app):
    """Test that getting a non-existing comment returns a 404 error."""
    not_uuid = "hello"
    resp = app.get(
        API_URL + f"project_supplier_comments/{not_uuid}", expect_errors=True
    )

    assert resp.status_code == 400
    assert resp.json == {
        "error_code": "ParameterValidationFailed",
        "message": "Failed to validate parameters",
        "error": {"comment_id": f"{not_uuid} is not a UUID"},
    }


def test_post_project_supplier_comment(app, db):
    """Test creating a new project supplier comment."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])

    comment_data = {
        "project_id": project["external_id"],
        "supplier_id": supplier["external_id"],
        "org_id": "test-org-id",
        "comment": "Test comment",
        "created_by_org_id": "test-org-id",
        "created_by_person_id": "test-person-id",
    }
    resp = app.post_json(API_URL + "project_supplier_comments", comment_data)

    assert resp.status_code == 201
    assert resp.json["project_id"] == project["external_id"]
    assert resp.json["supplier_id"] == supplier["external_id"]
    assert resp.json["comment"] == "Test comment"
    assert resp.json["org_id"] == "test-org-id"
    assert resp.json["comment"] == "Test comment"
    assert resp.json["created_by_org_id"] == "test-org-id"
    assert resp.json["created_by_person_id"] == "test-person-id"
    assert resp.json["is_deleted"] is False
    assert resp.json["is_updated"] is False


@pytest.mark.parametrize(
    "key",
    [
        "project_id",
        "supplier_id",
        "org_id",
        "comment",
        "created_by_org_id",
        "created_by_person_id",
    ],
)
@pytest.mark.parametrize("value", [None, ""])
def test_post_project_supplier_comment_invalid_data(app, key, value):
    """Test creating a comment with invalid data returns a 400 error."""
    # Allow empty comments
    if key == "comment" and value == "":
        return
    comment_data = {
        "project_id": "some-project-id",
        "supplier_id": "some-supplier-id",
        "org_id": "test-org-id",
        "comment": "Test comment",
        "created_by_org_id": "test-org-id",
        "created_by_person_id": "test-person-id",
    }
    comment_data[key] = value

    resp = app.post_json(
        API_URL + "project_supplier_comments", comment_data, expect_errors=True
    )

    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"


def test_post_project_supplier_comment_project_not_found(app, db):
    """Test creating a comment with a non-existing project returns a 404 error."""
    supplier = factories.create_supplier(db)

    comment_data = {
        "project_id": "non-existing-project",
        "supplier_id": supplier["external_id"],
        "org_id": "test-org-id",
        "comment": "Test comment",
        "created_by_org_id": "test-org-id",
        "created_by_person_id": "test-person-id",
    }

    resp = app.post_json(
        API_URL + "project_supplier_comments", comment_data, expect_errors=True
    )

    assert resp.status_code == 404
    assert resp.json["error_code"] == "NotFound"
    assert "project_id" in resp.json["error"]


def test_post_project_supplier_comment_supplier_not_found(app, db):
    """Test creating a comment with a non-existing supplier returns a 404 error."""
    project = factories.create_project(db)

    comment_data = {
        "project_id": project["external_id"],
        "supplier_id": "non-existing-supplier",
        "org_id": "test-org-id",
        "comment": "Test comment",
        "created_by_org_id": "test-org-id",
        "created_by_person_id": "test-person-id",
    }

    resp = app.post_json(
        API_URL + "project_supplier_comments", comment_data, expect_errors=True
    )

    assert resp.status_code == 404
    assert resp.json["error_code"] == "NotFound"
    assert "supplier_id" in resp.json["error"]


def test_post_project_supplier_comment_supplier_not_in_project(app, db):
    """Test creating a comment with a supplier not in the project returns a 400 error."""
    project1 = factories.create_project(db, name="Project 1")
    project2 = factories.create_project(db, name="Project 2")
    supplier = factories.create_supplier(db, project_id=project2["id"])

    comment_data = {
        "project_id": project1["external_id"],
        "supplier_id": supplier["external_id"],
        "org_id": "test-org-id",
        "comment": "Test comment",
        "created_by_org_id": "test-org-id",
        "created_by_person_id": "test-person-id",
    }

    resp = app.post_json(
        API_URL + "project_supplier_comments", comment_data, expect_errors=True
    )

    assert resp.status_code == 400
    assert resp.json["error_code"] == "BadRequest"
    assert "Supplier does not belong to the specified project" in str(resp.json)


def test_patch_project_supplier_comment(app, db):
    """Test updating a project supplier comment."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])

    comment = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier["id"]
    )
    update_data = {
        "comment": "Updated comment",
        "updating_org_id": "update-org-id",
        "updating_person_id": "update-person-id",
    }
    resp = app.patch_json(API_URL + f'project_supplier_comments/{comment["id"]}', update_data)

    assert resp.status_code == 200
    assert resp.json["id"] == comment["id"]
    assert resp.json["comment"] == "Updated comment"
    assert resp.json["is_updated"] is True
    assert resp.json["updated_by_org_id"] == "update-org-id"
    assert resp.json["updated_by_person_id"] == "update-person-id"
    assert resp.json["updated_timestamp"] is not None
    assert resp.json["modified_timestamp"] is not None


def test_patch_project_supplier_comment_not_found(app):
    """Test updating a non-existing comment returns a 404 error."""
    non_existing_id = str(uuid.uuid4())
    update_data = {
        "comment": "Updated comment",
        "updating_org_id": "update-org-id",
        "updating_person_id": "update-person-id",
    }
    resp = app.patch_json(
        API_URL + f'project_supplier_comments/{non_existing_id}',
        update_data,
        expect_errors=True,
    )
    assert resp.status_code == 404
    assert resp.json["error_code"] == "NotFound"
    assert "comment_id" in resp.json["error"]


def test_patch_project_supplier_comment_invalid_uuid(app):
    """Test that getting a non-existing comment returns a 404 error."""
    not_uuid = "hello"
    update_data = {
        "updating_org_id": "update-org-id",
        "updating_person_id": "update-person-id",
    }

    resp = app.patch_json(
        API_URL + f'project_supplier_comments/{not_uuid}',
        update_data,
        expect_errors=True,
    )

    assert resp.status_code == 400
    assert resp.json == {
        "error_code": "ParameterValidationFailed",
        "message": "Failed to validate parameters",
        "error": {"comment_id": f"{not_uuid} is not a UUID"},
    }


def test_patch_project_supplier_comment_no_update_data(app, db):
    """Test updating a comment with no update data returns a 400 error."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])

    comment = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier["id"]
    )

    update_data = {
        "updating_org_id": "update-org-id",
        "updating_person_id": "update-person-id",
    }

    resp = app.patch_json(
        API_URL + f'project_supplier_comments/{comment["id"]}',
        update_data,
        expect_errors=True,
    )

    assert resp.status_code == 400
    assert resp.json["error_code"] == "BadRequest"
    assert "comment" in resp.json["error"]
    assert "is_deleted" in resp.json["error"]


def test_patch_project_supplier_comment_too_much_update_data(app, db):
    """Test updating a comment with both comment and is_deleted returns a 400 error."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])

    comment = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier["id"]
    )

    update_data = {
        "updating_org_id": "update-org-id",
        "updating_person_id": "update-person-id",
        "comment": "Updated comment text",
        "is_deleted": True,
    }

    resp = app.patch_json(
        API_URL + f'project_supplier_comments/{comment["id"]}',
        update_data,
        expect_errors=True,
    )

    assert resp.status_code == 400
    assert resp.json["error_code"] == "BadRequest"
    assert "comment" in resp.json["error"]
    assert "is_deleted" in resp.json["error"]


def test_patch_project_supplier_comment_mark_as_deleted(app, db):
    """Test marking a comment as deleted via update."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])
    comment = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier["id"]
    )

    update_data = {
        "is_deleted": True,
        "updating_org_id": "delete-org-id",
        "updating_person_id": "delete-person-id",
    }

    resp = app.patch_json(API_URL + f'project_supplier_comments/{comment["id"]}', update_data)
    assert resp.status_code == 200
    assert resp.json["id"] == comment["id"]
    assert resp.json["is_deleted"] is True
    assert resp.json["deleted_by_org_id"] == "delete-org-id"
    assert resp.json["deleted_by_person_id"] == "delete-person-id"
    assert resp.json["deleted_timestamp"] is not None


def test_patch_project_supplier_comment_cannot_mark_as_undeleted(app, db):
    """Test that you cannot mark a comment as not deleted via update."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])
    comment = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier["id"]
    )
    mark_comment_as_deleted(db, comment["id"], "delete-org-id", "delete-person-id")

    update_data = {
        "is_deleted": False,
        "updating_org_id": "delete-org-id",
        "updating_person_id": "delete-person-id",
    }

    resp = app.patch_json(
        API_URL + f'project_supplier_comments/{comment["id"]}',
        update_data,
        expect_errors=True,
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert "is_deleted" in resp.json["error"]


def test_patch_project_supplier_comment_cannot_update_deleted(app, db):
    """Test that you cannot update a deleted comment."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])
    comment = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier["id"]
    )
    mark_comment_as_deleted(db, comment["id"], "delete-org-id", "delete-person-id")

    update_data = {
        "comment": "Hey",
        "updating_org_id": "delete-org-id",
        "updating_person_id": "delete-person-id",
    }

    resp = app.patch_json(
        API_URL + f'project_supplier_comments/{comment["id"]}',
        update_data,
        expect_errors=True,
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "BadRequest"
    assert "comment_id" in resp.json["error"]


def test_delete_project_supplier_comment(app, db):
    """Test deleting a project supplier comment."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])
    comment = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier["id"]
    )

    resp = app.delete(API_URL + f'project_supplier_comments/{comment["id"]}')
    existing_comment = get_project_supplier_comment(db, comment["id"])

    assert resp.status_code == 200
    assert resp.json == {}
    assert existing_comment is None


def test_delete_project_supplier_comment_invalid_uuid(app):
    """Test that getting a non-existing comment returns a 404 error."""
    not_uuid = "hello"
    resp = app.delete(
        API_URL + f"project_supplier_comments/{not_uuid}", expect_errors=True
    )

    assert resp.status_code == 400
    assert resp.json == {
        "error_code": "ParameterValidationFailed",
        "message": "Failed to validate parameters",
        "error": {"comment_id": f"{not_uuid} is not a UUID"},
    }


def test_delete_project_supplier_comment_not_found(app):
    """Test deleting a non-existing comment returns a 404 error."""
    non_existing_id = str(uuid.uuid4())
    resp = app.delete(
        API_URL + f"project_supplier_comments/{non_existing_id}", expect_errors=True
    )

    assert resp.status_code == 404
    assert resp.json["error_code"] == "NotFound"
    assert "comment_id" in resp.json["error"]
