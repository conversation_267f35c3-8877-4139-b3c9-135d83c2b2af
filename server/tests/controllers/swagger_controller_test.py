import copy
import importlib

from boldataapi.controllers import swagger_controller


ORIGINAL_SWAGGER_TEMPLATE = copy.deepcopy(swagger_controller.SWAGGER_TEMPLATE)


def test_swagger(app):
    resp = app.get('/api/v1/boldata/swagger.json')
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    assert resp.headers.get('Access-Control-Allow-Origin') == '*'
    # Make sure all that in-place modification doesn't accidentally modify
    # the swagger template.
    assert swagger_controller.SWAGGER_TEMPLATE == ORIGINAL_SWAGGER_TEMPLATE


def traverse(root, path):
    obj = root
    for step in path.split('/'):
        obj = obj[step]
    return obj


def iter_walk(node, path=''):
    if isinstance(node, dict):
        yield node, path or '/'
        for k, v in node.items():
            subpath = '%s/%s' % (path, f'"{k}"' if '/' in k else k)
            yield from iter_walk(v, subpath)
    elif isinstance(node, list):
        yield node, path or '/'
        for i, v in enumerate(node):
            yield from iter_walk(v, f'{path}[{i}]')


def resolve_refs(node, root):
    if isinstance(node, dict):
        if '$ref' in node:
            return traverse(root, node['$ref'].partition('#/')[-1])
        return {
            k: resolve_refs(v, root) for k, v in node.items()
        }
    elif isinstance(node, list):
        return [
            resolve_refs(item, root) for item in node
        ]
    else:
        return node


def iter_schemas(swagger_doc):  # noqa: C901
    components = swagger_doc.get('components', {})
    for name, schema in components.get('schemas', {}).items():
        yield schema, f'/components/schemas/{name}'
    for path_name, path in swagger_doc.get('paths', {}).items():
        for method, op in path.items():
            if method not in {'get', 'put', 'post', 'patch', 'delete', 'head', 'options', 'trace'}:
                continue
            prefix = f'/paths/"{path_name}"/{method}'
            for index, param in enumerate(op.get('parameters', [])):
                if 'schema' in param:
                    yield param['schema'], f'{prefix}/parameters[{index}]/schema'
            for mtype, media_type in op.get('requestBody', {}).get('content', {}).items():
                if 'schema' in media_type:
                    yield media_type['schema'], f'{prefix}/requestBody/content/"{mtype}"/schema'
            for code, response in op.get('responses', {}).items():
                for mtype, media_type in response.get('content', {}).items():
                    if 'schema' in media_type:
                        path = f'{prefix}/responses/{code}/content/"{mtype}"/schema'
                        yield media_type['schema'], path


def get_cerberus_schema(name):
    module_name, schema_name = name.rsplit('.', 1)
    module = importlib.import_module(module_name)
    return getattr(module, schema_name)


def test_swagger_references(app):
    swagger_doc = app.get('/api/v1/boldata/swagger.json').json
    error = None
    for node, path in iter_walk(swagger_doc):
        if '$ref' in node:
            ref = node['$ref']
            try:
                traverse(swagger_doc, ref.partition('#/')[-1])
            except KeyError:
                # I want to see all the bad references at once, and then I want the test to raise
                # any one, I don't care which one
                error = f'Bad reference {ref}\n  (found in {path})'
                print(error)
    if error:
        raise AssertionError(error)


def test_swagger_schemas(app):  # noqa: C901, yes, it's complex, sorry about that
    swagger_doc = app.get('/api/v1/boldata/swagger.json').json
    for schema, path in iter_schemas(swagger_doc):
        if not schema.get('x-bolfak-schema'):
            continue
        if schema.get('type', 'object') != 'object':
            continue
        # iter_schemas() returns all the schemas from /components, so we don't
        # want to validate them _again_ when we find them referenced from
        # elsewhere, hence check for x-bolfak-schema and for type: object
        # before doing resolve_refs().  We need the resolve_refs() because sometimes
        # a schema refers to another schema to avoid duplicating enum values and such.
        schema = resolve_refs(schema, swagger_doc)
        cerberus_schema = get_cerberus_schema(schema['x-bolfak-schema'])
        form_fields = set(cerberus_schema)
        required_form_fields = set(n for n, p in cerberus_schema.items() if p.get('required'))
        assert set(schema['properties']) == form_fields, path
        assert set(schema.get('required', [])) == required_form_fields, f"{path}/required"
        for field in sorted(form_fields):
            sfield = schema['properties'][field]
            cfield = cerberus_schema[field]
            fpath = f'{path}/properties/{field}'
            expected_type = cfield.get('type')
            if expected_type == 'date':
                # date fields are encoded in swagger as type: string, format: date
                # and in cerberus as type: date
                expected_type = 'string'
                assert sfield.get('format') == 'date', f"{fpath}/type"
            elif expected_type == 'datetime':
                # date fields are encoded in swagger as type: string, format: date-time
                # and in cerberus as type: datetime
                expected_type = 'string'
                assert sfield.get('format') == 'date-time', f"{fpath}/type"
            elif expected_type == 'list':
                # lists have type: array in swagger and type: list in cerberus
                expected_type = 'array'
            elif expected_type == 'dict':
                # dicts have type: object in swagger and type: dict in cerberus
                expected_type = 'object'
            if expected_type is not None:
                # type is optional for cerberus! some qvarn compat fields (that
                # are ignored) omit it!
                assert sfield.get('type') == expected_type, f"{fpath}/type"
            if expected_type == 'array':
                assert (
                    set(sfield.get('items', {}).get('enum', ()))
                    == set(cfield.get('allowed', cfield.get('schema', {}).get('allowed', ())))
                ), f"{fpath}/items/enum"
            else:
                assert (
                    set(sfield.get('enum', ())) == set(cfield.get('allowed', ()))
                ), f"{fpath}/enum"
