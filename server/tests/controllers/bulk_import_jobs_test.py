import datetime
import json

import pytest

from boldataapi.fixtures import factories
from boldataapi.serialize import to_json_with_date
from boldataapi.services.bulk_import_jobs import get_bulk_import_job


def test_get_bulk_import_job(app, db):
    job = factories.create_bulk_import_job(db)
    response = app.get(f"/api/v1/boldata/bulk_import_jobs/{job['id']}")
    assert response.json == json.loads(to_json_with_date(job))


def test_get_bulk_import_job_core_disabled(app, db, set_feature_flag):
    set_feature_flag('core', False)
    companies = [
        ['123456-7890', None, 'pending', None, None, None, None, None, 'some_external_id'],
    ]
    job = factories.create_bulk_import_job(db, companies=companies)
    response = app.get(f"/api/v1/boldata/bulk_import_jobs/{job['id']}")
    # When core is disabled, the external_id is not returned
    job['companies'][0].pop()
    assert response.json == json.loads(to_json_with_date(job))


def test_get_bulk_import_job_core_enabled(app, db, set_feature_flag):
    set_feature_flag('core', True)
    companies = [
        ['123456-7890', None, 'pending', None, None, None, None, None, 'some_external_id'],
    ]
    job = factories.create_bulk_import_job(db, companies=companies)
    response = app.get(f"/api/v1/boldata/bulk_import_jobs/{job['id']}")
    assert response.json == json.loads(to_json_with_date(job))


@pytest.mark.parametrize("job_id", [
    "b1826fa3-7fd2-4112-bbae-d0b3423d507d",  # random uuid
    "no-such-id",  # because PostgreSQL complains about invalid syntax when it's not an UUID
])
def test_get_bulk_import_job_missing(app, job_id):
    response = app.get(f"/api/v1/boldata/bulk_import_jobs/{job_id}", status=404)
    assert response.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        "error": {
            'id': job_id,
        }
    }


def test_get_last_bulk_import_job(app, db):
    job = factories.create_bulk_import_job(db)
    response = app.get(f"/api/v1/boldata/bulk_import_jobs/last/{job['project_id']}")
    assert response.json == json.loads(to_json_with_date(job))


def test_get_last_bulk_import_job_missing(app, db):
    project = factories.create_project(db)
    response = app.get(f"/api/v1/boldata/bulk_import_jobs/last/{project['id']}", status=404)
    assert response.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        "error": {
            'project_id': project['id'],
        }
    }


def test_create_bulk_import_job(app, db):
    project = factories.create_project(db)
    interested_org_id = '638f763c-8638-451d-9e24-f9d85042af96'  # random uuid
    now = datetime.datetime.utcnow().isoformat()
    response = app.post_json('/api/v1/boldata/bulk_import_jobs', {
        'project_id': project['id'],
        'interested_org_id': interested_org_id,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None, now, None],
        ],
    })
    job = get_bulk_import_job(db, response.json['id'])
    assert job.pop('id') == response.json['id']
    assert job.pop('created_on') == job.pop('last_changed')
    assert job == {
        'canceled': None,
        'imported': None,
        'interested_org_id': interested_org_id,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None, now, None],
        ],
        'project_id': project['id'],
        'status': 'pending',
    }


def test_create_bulk_import_job_adds_null_when_core_is_disabled(app, db, set_feature_flag):
    set_feature_flag('core', False)
    project = factories.create_project(db)
    interested_org_id = '638f763c-8638-451d-9e24-f9d85042af96'  # random uuid
    now = datetime.datetime.utcnow().isoformat()
    response = app.post_json('/api/v1/boldata/bulk_import_jobs', {
        'project_id': project['id'],
        'interested_org_id': interested_org_id,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None, now, None],
        ],
    })
    set_feature_flag('core', True)
    job = get_bulk_import_job(db, response.json['id'])
    assert job['companies'] == [
        ['123456-7890', None, 'pending', None, None, None, now, None, None, None],
    ]


def test_create_bulk_import_job_can_add_external_id(app, db, set_feature_flag):
    set_feature_flag('core', False)
    project = factories.create_project(db)
    interested_org_id = '638f763c-8638-451d-9e24-f9d85042af96'  # random uuid
    now = datetime.datetime.utcnow().isoformat()
    response = app.post_json('/api/v1/boldata/bulk_import_jobs', {
        'project_id': project['id'],
        'interested_org_id': interested_org_id,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None,
                now, None, 'some_external_id', 'some_name'],
        ],
    })
    set_feature_flag('core', True)
    job = get_bulk_import_job(db, response.json['id'])
    assert job['companies'] == [
        ['123456-7890', None, 'pending', None, None, None, now, None, 'some_external_id',
         'some_name'],
    ]


def test_create_bulk_import_validation_failed_core(app, db, set_feature_flag):
    set_feature_flag('core', True)
    project = factories.create_project(db)
    interested_org_id = '638f763c-8638-451d-9e24-f9d85042af96'  # random uuid
    now = datetime.datetime.utcnow().isoformat()
    response = app.post_json('/api/v1/boldata/bulk_import_jobs', {
        'project_id': project['id'],
        'interested_org_id': interested_org_id,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None, now, None],
        ],
    }, status=400)
    assert response.json['error_code'] == 'ParameterValidationFailed'
    assert response.json['error'] == {
        'companies': [
            {'0': ['length of list should be 10, it is 8', 'min length is 10']},
        ],
    }


def test_create_bulk_import_job_validation_failed(app, db):
    response = app.post_json('/api/v1/boldata/bulk_import_jobs', {
        'companies': [
            ['123456-7890', None, 'pexding', None, None, None, None, None],
        ],
    }, status=400)
    assert response.json['error_code'] == 'ParameterValidationFailed'
    assert response.json['error'] == {
        'project_id': ['required field'],
        'interested_org_id': ['required field'],
        'companies': [
            {
                '0': [
                    {'2': ['unallowed value pexding']},
                ],
            },
        ],
    }


def test_update_bulk_import_job(app, db):
    job = factories.create_bulk_import_job(db)
    response = app.post_json(f"/api/v1/boldata/bulk_import_jobs/{job['id']}", {
        'status': 'in_progress',
    }, status=200)
    assert response.json == json.loads(to_json_with_date(dict(job, status='in_progress')))


def test_update_bulk_import_job_with_companies(app, db):
    job = factories.create_bulk_import_job(db)
    then = (datetime.datetime.utcnow() - datetime.timedelta(seconds=20)).isoformat()
    now = datetime.datetime.utcnow().isoformat()
    uuid = '694d09bb-2675-4d34-a548-c95d6355b98d'
    new_companies = [
        ['123456-7890', uuid, 'done', 'sndbox', None, 'add', then, now],
    ]
    response = app.post_json(f"/api/v1/boldata/bulk_import_jobs/{job['id']}", {
        'companies': new_companies,
    }, status=200)
    expected = json.loads(to_json_with_date(dict(job, companies=new_companies)))
    assert response.json == expected


def test_update_bulk_import_job_validation_failed(app, db):
    job = factories.create_bulk_import_job(db)
    response = app.post_json(f"/api/v1/boldata/bulk_import_jobs/{job['id']}", {
        'status': 'who knows',
    }, status=400)
    assert response.json['error_code'] == 'ParameterValidationFailed'
    assert response.json['error'] == {
        'status': ['unallowed value who knows'],
    }


@pytest.mark.parametrize("job_id", [
    "b1826fa3-7fd2-4112-bbae-d0b3423d507d",  # random uuid
    "no-such-id",  # because PostgreSQL complains about invalid syntax when it's not an UUID
])
def test_update_bulk_import_job_not_found(app, job_id, caplog):
    response = app.post_json(f"/api/v1/boldata/bulk_import_jobs/{job_id}", {
        'status': 'in_progress',
    }, status=404)
    assert response.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        "error": {
            'id': job_id,
        }
    }
