import datetime
import json
import uuid

import pytest
import sqlalchemy as sa

from boldataapi.fixtures.factories import create_cs_account, create_cs_account_history
from boldataapi.services.creditsafe_accounts import get_creditsafe_account
from boldataapi.storage.db import CREDITSAFE_ACCOUNT_TABLE

API_URL = '/api/v1/boldata'


def datetime_to_json(obj):
    if isinstance(obj, (datetime.date, datetime.datetime)):
        return obj.strftime('%Y-%m-%dT%H:%M:%S.%f')
    raise TypeError("Type %s not serializable" % type(obj))


def test_get_creditsafe_account_history(app, db):
    data = {
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new account'
    }
    cs_account = create_cs_account(db, **data)
    history = {
        'creditsafe_account_id': cs_account['id'],
        'org_id': 'f392-test2',
        'person_id': '0035-test2',
        'username': 'cs_user2',
        'state': 'active',
        'comment': 'history',
        'changed_by_person_id': '0035-modified',
    }

    history = create_cs_account_history(db, **history)
    res = app.get(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}')
    assert res.status_code == 200
    data = res.json
    assert data['id'] == cs_account['id']
    assert data['person_id'] == '0035-test'
    assert data['org_id'] == 'f392-test'
    assert data['username'] == 'cs_user'
    assert data['password'] == 'cs_pass'
    assert data['state'] == 'pending'
    assert len(data['history']) == 2
    assert data['history'][0]['org_id'] == 'f392-test'
    assert data['history'][0]['created_on'] == cs_account['created_on'].isoformat()
    assert data['history'][0]['person_id'] == '0035-test'
    assert data['history'][0]['username'] == 'cs_user'
    assert data['history'][0]['state'] == 'pending'
    assert data['history'][0]['changed_by_person_id'] == '0035-test'
    assert data['history'][0]['comment'] == 'new account'
    assert data['history'][1]['org_id'] == 'f392-test2'
    assert data['history'][1]['created_on'] == history['created_on'].isoformat()
    assert data['history'][1]['person_id'] == '0035-test2'
    assert data['history'][1]['username'] == 'cs_user2'
    assert data['history'][1]['state'] == 'active'
    assert data['history'][1]['changed_by_person_id'] == '0035-modified'
    assert data['history'][1]['comment'] == 'history'
    assert len(data.keys()) == 7
    assert len(data['history'][0].keys()) == 7


def test_get_creditsafe_account_no_history(app, db):
    data = {
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new account'
    }
    cs_account = create_cs_account(db, **data)
    history = {
        'creditsafe_account_id': cs_account['id'],
        'org_id': 'f392-test2',
        'person_id': '0035-test2',
        'username': 'cs_user2',
        'state': 'active',
        'comment': 'history',
        'changed_by_person_id': '0035-modified',
    }

    history = create_cs_account_history(db, **history)
    res = app.get(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}?include_history=false')
    assert res.status_code == 200
    data = res.json
    assert data['id'] == cs_account['id']
    assert data['person_id'] == '0035-test'
    assert data['org_id'] == 'f392-test'
    assert data['username'] == 'cs_user'
    assert data['password'] == 'cs_pass'
    assert data['state'] == 'pending'
    assert 'history' not in data
    assert len(data.keys()) == 6


def test_get_creditsafe_account_validation(app, db):
    cs_account = create_cs_account(db)
    res = app.get(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}?include_history=maybe',
                  status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {'include_history': ["bad boolean value ('maybe'), expected true/false"]},
    }


@pytest.mark.parametrize('csid', [
    'nonexistent',
    str(uuid.uuid4())
])
def test_get_creditsafe_account_not_found(app, db, csid):
    res = app.get(f'{API_URL}/creditsafe_accounts/{csid}', status=404)
    assert res.json == {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': csid}
    }


def test_post_creditsafe_account(app):
    data = {
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new'
    }
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data)
    assert res.status_code == 201
    data = res.json
    assert data['person_id'] == '0035-test'
    assert data['org_id'] == 'f392-test'
    assert data['username'] == 'cs_user'
    assert data['password'] == 'cs_pass'
    assert data['state'] == 'pending'
    assert data['history'][0]['org_id'] == 'f392-test'
    assert data['history'][0]['created_on'] is not None
    assert data['history'][0]['person_id'] == '0035-test'
    assert data['history'][0]['username'] == 'cs_user'
    assert data['history'][0]['state'] == 'pending'
    assert data['history'][0]['changed_by_person_id'] == '0035-test'
    assert data['history'][0]['comment'] == 'new'


def test_post_creditsafe_account_for_migration(app, db):
    data = {
        'id': '2d3889cd-539c-4693-8f44-de04a63c52f1',
        'created_on': '2023-11-06T19:31:42.73501Z',
        'last_changed': '2024-01-01T19:32:42.73501Z',
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'active',
        'comment': 'new',
        'history': [
            {
                'state': 'pending',
                'comment': 'test',
                'changed_by_person_id': 'modified by',
                'created_on': '2023-11-06T19:31:42.73501Z'
            },
            {
                'state': 'active',
                'comment': 'activating',
                'changed_by_person_id': 'modified by 2',
                'created_on': '2024-01-01T19:32:42.73501Z'
            },
        ]
    }
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data)
    assert res.status_code == 201
    data = res.json
    assert data['person_id'] == '0035-test'
    assert data['org_id'] == 'f392-test'
    assert data['username'] == 'cs_user'
    assert data['password'] == 'cs_pass'
    assert data['state'] == 'active'
    assert data['id'] == '2d3889cd-539c-4693-8f44-de04a63c52f1'
    assert data['history'][0]['org_id'] is None
    assert data['history'][0]['created_on'] == '2023-11-06T19:31:42.735010+00:00'
    assert data['history'][0]['person_id'] is None
    assert data['history'][0]['username'] is None
    assert data['history'][0]['state'] == 'pending'
    assert data['history'][0]['changed_by_person_id'] == 'modified by'
    assert data['history'][0]['comment'] == 'test'
    assert data['history'][1]['org_id'] is None
    assert data['history'][1]['created_on'] == '2024-01-01T19:32:42.735010+00:00'
    assert data['history'][1]['person_id'] is None
    assert data['history'][1]['username'] is None
    assert data['history'][1]['state'] == 'active'
    assert data['history'][1]['changed_by_person_id'] == 'modified by 2'
    assert data['history'][1]['comment'] == 'activating'

    # Check last_changed
    creditsafe_accounts = db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]
    query = (
        sa.select([creditsafe_accounts])
        .where(creditsafe_accounts.c.id == '2d3889cd-539c-4693-8f44-de04a63c52f1')
    )
    res = db.session.execute(query)
    csa = res.fetchone()
    assert csa.last_changed.isoformat() == '2024-01-01T19:32:42.735010+00:00'


def test_post_creditsafe_account_with_changed_person_id(app):
    data = {
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new',
        'changed_by_person_id': 'person_who_created',
    }
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data)
    assert res.status_code == 201
    data = res.json
    assert data['person_id'] == '0035-test'
    assert data['org_id'] == 'f392-test'
    assert data['username'] == 'cs_user'
    assert data['password'] == 'cs_pass'
    assert data['state'] == 'pending'
    assert data['history'][0]['org_id'] == 'f392-test'
    assert data['history'][0]['created_on'] is not None
    assert data['history'][0]['person_id'] == '0035-test'
    assert data['history'][0]['username'] == 'cs_user'
    assert data['history'][0]['state'] == 'pending'
    assert data['history'][0]['changed_by_person_id'] == 'person_who_created'
    assert data['history'][0]['comment'] == 'new'


@pytest.mark.parametrize('field', [
    'person_id',
    'org_id',
    'username',
    'state'
])
def test_post_creditsafe_account_required_fields(app, field):
    data = {
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'org_id': 'f392-test',
    }
    del data[field]
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {field: ['required field']}
    }


@pytest.mark.parametrize('field', [
    'person_id',
    'org_id',
    'username',
    'password',
    'changed_by_person_id',
    'comment',
])
def test_post_creditsafe_account_empty_string_not_allowed(app, field):
    data = {
        'org_id': 'f392-test',
        'state': 'pending',
        'username': 'test',
        'person_id': 'test',
    }
    data[field] = ''
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {field: ['empty values not allowed']}
    }


@pytest.mark.parametrize('state, message', [
    ('', 'unallowed value '),
    ('terminated', 'unallowed value terminated'),
    (None, 'null value not allowed'),
    (1, 'must be of string type'),
])
def test_post_creditsafe_account_unknown_state(app, state, message):
    data = {
        'org_id': 'f392-test',
        'state': state,
        'username': 'test',
        'person_id': 'test',
    }
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {'state': [message]}
    }


@pytest.mark.parametrize('state', [
    'pending',
    'active',
    'inactive',
])
def test_post_creditsafe_account_correct_state(app, state):
    data = {
        'org_id': 'f392-test',
        'state': state,
        'username': 'test',
        'person_id': 'test',
    }
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data)
    assert res.status_code == 201
    assert res.json['state'] == state


@pytest.mark.parametrize('field', [
    'person_id',
    'org_id',
    'username',
    'password',
    'changed_by_person_id',
    'comment',
])
def test_post_creditsafe_account_incorrect_type(app, field):
    data = {
        'org_id': 'f392-test',
        'state': 'active',
        'username': 'test',
        'person_id': 'test',
    }
    data[field] = 0
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {field: ['must be of string type']}
    }


def test_post_creditsafe_account_unknown_field(app):
    data = {
        'org_id': 'f392-test',
        'state': 'active',
        'unknown': 'test',
        'username': 'test',
        'person_id': 'test',
    }
    res = app.post_json(f'{API_URL}/creditsafe_accounts', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {'unknown': ['unknown field']}
    }


def test_post_creditsafe_account_duplicate_org_id_conflict(app, db):
    # First, create an active account for an organization
    data1 = {
        'org_id': 'f392-duplicate-test',
        'person_id': '0035-test',
        'username': 'cs_user1',
        'password': 'cs_pass1',
        'state': 'active',
        'comment': 'first account',
    }
    res1 = app.post_json(f'{API_URL}/creditsafe_accounts', data1)
    assert res1.status_code == 201

    query = sa.select([db.meta.tables[CREDITSAFE_ACCOUNT_TABLE]])
    result = db.session.execute(query).fetchall()
    assert len(result) == 1

    # Try to create another active account for the same organization
    data2 = {
        'org_id': 'f392-duplicate-test',  # Same org_id as above
        'person_id': '0035-test2',
        'username': 'cs_user2',
        'password': 'cs_pass2',
        'state': 'active',  # Also active - should trigger conflict
        'comment': 'duplicate org_id',
    }
    res2 = app.post_json(f'{API_URL}/creditsafe_accounts', data2, status=409)

    # Verify the error response
    assert res2.status_code == 409
    assert 'error_code' in res2.json
    assert res2.json['error_code'] == 'ConflictError'
    assert (
        'An active Creditsafe account already exists for the org_id'
        in res2.json['error']['details']
    )
    assert res2.json['error']['org_id'] == 'f392-duplicate-test'


def test_update_creditsafe_account_unknown_field(app, cs_account):
    data = {'unknown': 'test'}
    res = app.put_json(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {'unknown': ['unknown field']}
    }


@pytest.mark.parametrize('field, value', [
    ('person_id', '0035-updated'),
    ('org_id', 'f392-updated'),
    ('username', 'username-updated'),
    ('password', 'password-updated'),
    ('state', 'active'),
])
def test_update_creditsafe_account_single_field(app, field, value, cs_account):
    data = {field: value}
    res = app.put_json(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}', data)
    assert res.json[field] == value
    # The rest of the fields should be retained
    del cs_account[field]
    del cs_account['id']
    del cs_account['last_changed']
    del cs_account['created_on']
    for k, v in cs_account.items():
        assert res.json[k] == v
    # The history should be updated as well (except password)
    assert len(res.json['history']) == 2
    assert 'password' not in res.json['history'][1]
    if field != 'password':
        assert res.json['history'][1][field] == value


def test_update_creditsafe_account_all_fields(app, cs_account):
    data = {
        'person_id': '0035-updated',
        'org_id': 'f392-updated',
        'username': 'username-updated',
        'password': 'password-updated',
        'state': 'active',
        'comment': 'updated',
        'changed_by_person_id': 'retool-user'
    }
    res = app.put_json(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}', data)
    data = res.json
    assert data['person_id'] == '0035-updated'
    assert data['org_id'] == 'f392-updated'
    assert data['username'] == 'username-updated'
    assert data['password'] == 'password-updated'
    assert data['state'] == 'active'
    assert data['history'][1]['org_id'] == 'f392-updated'
    assert data['history'][1]['created_on'] is not None
    assert data['history'][1]['person_id'] == '0035-updated'
    assert data['history'][1]['username'] == 'username-updated'
    assert data['history'][1]['state'] == 'active'
    assert data['history'][1]['changed_by_person_id'] == 'retool-user'
    assert data['history'][1]['comment'] == 'updated'

    # The history should be updated as well (except password)
    assert len(res.json['history']) == 2
    assert len(res.json['history'][1]) == 7
    assert 'password' not in res.json['history'][1]


@pytest.mark.parametrize('field', [
    'person_id',
    'org_id',
    'username',
    'password',
    'changed_by_person_id',
    'comment',
])
def test_update_creditsafe_account_incorrect_type(app, field, cs_account):
    data = {
        'org_id': 'f392-test',
        'state': 'active',
        'username': 'test',
        'person_id': 'test',
    }
    data[field] = 0
    res = app.put_json(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {field: ['must be of string type']}
    }


@pytest.mark.parametrize('state, message', [
    ('', 'unallowed value '),
    ('terminated', 'unallowed value terminated'),
    (None, 'null value not allowed'),
    (1, 'must be of string type'),
])
def test_update_creditsafe_account_unknown_state(app, state, message, cs_account):
    data = {
        'org_id': 'f392-test',
        'state': state,
        'username': 'test',
        'person_id': 'test',
    }
    res = app.put_json(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}', data, status=400)
    assert res.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {'state': [message]}
    }


@pytest.mark.parametrize('csid', [
    'nonexistent',
    str(uuid.uuid4())
])
def test_update_creditsafe_account_nonexistent(app, db, csid):
    data = {'person_id': '0035-updated'}
    res = app.put_json(f'{API_URL}/creditsafe_accounts/{csid}', data, status=404)
    assert res.json == {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': csid}
    }


def test_delete_creditsafe_account(app, db):
    cs1 = create_cs_account(db)
    cs2 = create_cs_account(db)
    create_cs_account_history(db, creditsafe_account_id=cs1['id'])
    res = app.delete(f'{API_URL}/creditsafe_accounts/{cs1["id"]}')
    assert res.json == {}
    assert get_creditsafe_account(db, cs1['id']) is None  # deleted
    # Let's also make sure I did not forget a WHERE clause in for the DELETE query
    assert get_creditsafe_account(db, cs2['id']) is not None


@pytest.mark.parametrize('csid', [
    'nonexistent',
    str(uuid.uuid4())
])
def test_delete_creditsafe_account_not_found(app, db, csid):
    res = app.delete(f'{API_URL}/creditsafe_accounts/{csid}', status=404)
    assert res.json == {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': csid}
    }


# query tests

@pytest.mark.parametrize(('q_field'), [
                         ('id'),
                         ('org_id'),
                         ('person_id'),
                         ('username'),
                         ('state'),
                         ('created_on'),
                         ])
def test_creditsafe_accounts_query(app, db, q_field):
    data1 = {
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new account',
    }
    cs_account = create_cs_account(db, **data1)
    history = {
        'creditsafe_account_id': cs_account['id'],
        'org_id': 'f392-test2',
        'person_id': '0035-test2',
        'username': 'cs_user2',
        'state': 'active',
        'comment': 'history',
        'changed_by_person_id': '0035-modified',
    }
    history = create_cs_account_history(db, **history)
    data2 = {
        'org_id': 'f392-test2',
        'person_id': '0035-test2',
        'username': 'cs_user2',
        'password': 'cs_pass2',
        'state': 'active',
        'comment': 'new account',
        'created_on': '2020-04-01',
    }
    create_cs_account(db, **data2)

    query = {q_field: cs_account[q_field]}
    response = app.get(
        f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query, default=datetime_to_json)}'
    )
    result = response.json['resources']
    expected = [
        {'id': cs_account['id'],
         'org_id': 'f392-test',
         'password': 'cs_pass',
         'person_id': '0035-test',
         'state': 'pending',
         'username': 'cs_user',
         'created_on': datetime_to_json(cs_account['created_on']) + '+00:00',
         'history': [
             {'changed_by_person_id': '0035-test',
              'comment': 'new account',
              'created_on': datetime_to_json(cs_account['created_on']) + '+00:00',
              'org_id': 'f392-test',
              'person_id': '0035-test',
              'state': 'pending',
              'username': 'cs_user'},
             {'changed_by_person_id': '0035-modified',
              'comment': 'history',
              'created_on': datetime_to_json(cs_account['created_on']) + '+00:00',
              'org_id': 'f392-test2',
              'person_id': '0035-test2',
              'state': 'active',
              'username': 'cs_user2'},
              ],
         }
    ]
    assert result == expected
    assert response.status_code == 200


def test_creditsafe_accounts_query_result_multiple(app, db):
    cs1 = create_cs_account(db, org_id='org-id-1')
    cs2 = create_cs_account(db, org_id='org-id-1')
    create_cs_account(db, org_id='org-id-2')
    query = {'org_id': 'org-id-1'}
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}').json['resources']
    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([cs1['id'], cs2['id']])


def test_creditsafe_accounts_query_result_empty(app, db):
    create_cs_account(db, org_id='org-id-2')
    query = {'org_id': 'org-id-1'}
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}').json['resources']
    assert result == []


@pytest.mark.parametrize(('q_field'), [
                         ('id'),
                         ('org_id'),
                         ('person_id'),
                         ('username'),
                         ('state'),
                         ('created_on'),
                         ])
def test_creditsafe_accounts_query__ne(app, db, q_field):
    data1 = {
        'org_id': 'f392-test',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new account',
        'created_on': '2020-04-01',
    }
    cs_account_1 = create_cs_account(db, **data1)
    data2 = {
        'org_id': 'f392-test2',
        'person_id': '0035-test2',
        'username': 'cs_user2',
        'password': 'cs_pass2',
        'state': 'active',
        'comment': 'new account',
    }
    cs_account_2 = create_cs_account(db, **data2)
    query = {q_field+'__ne': cs_account_1[q_field]}
    result = app.get(
        f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query, default=datetime_to_json)}'
    ).json['resources']
    expected = [
        {
            'created_on': datetime_to_json(cs_account_2['created_on']) + '+00:00',
            'history': [{'changed_by_person_id': '0035-test2',
                         'comment': 'new account',
                         'created_on': datetime_to_json(cs_account_2['created_on']) + '+00:00',
                         'org_id': 'f392-test2',
                         'person_id': '0035-test2',
                         'state': 'active',
                         'username': 'cs_user2'}],
            'id': cs_account_2['id'],
            'org_id': 'f392-test2',
            'password': 'cs_pass2',
            'person_id': '0035-test2',
            'state': 'active',
            'username': 'cs_user2'
        },
    ]
    assert result == expected


def test_creditsafe_accounts_query__ne_result_multiple(app, db):
    cs1 = create_cs_account(db, org_id='org-id-1')
    cs2 = create_cs_account(db, org_id='org-id-1')
    create_cs_account(db, org_id='org-id-other')
    query = {'org_id__ne': 'org-id-other'}
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}').json['resources']
    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([cs1['id'], cs2['id']])


def test_creditsafe_accounts_query__ne_result_empty(app, db):
    create_cs_account(db, org_id='org-id-other')
    query = {'org_id__ne': 'org-id-other'}
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}').json['resources']
    assert result == []


def test_creditsafe_account_query_any(app, db):
    data1 = {
        'org_id': 'f392-test-1',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new account'
    }
    cs_account1 = create_cs_account(db, **data1)
    data2 = {
        'org_id': 'f392-test-2',
        'person_id': '0035-test',
        'username': 'cs_user',
        'password': 'cs_pass',
        'state': 'pending',
        'comment': 'new account'
    }
    create_cs_account(db, **data2)

    query = {'org_id__any': [cs_account1['org_id'], 'other-id']}
    result = [
        cs_acc['id']
        for cs_acc in app.get(
            f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}'
        ).json['resources']
    ]
    expected = [cs_account1['id']]
    assert sorted(result) == sorted(expected)


@pytest.mark.parametrize("query, expected", [
    ({'created_on__lt': '2022-01-03'}, {'2022-01-01', '2022-01-02'}),
    ({'created_on__gt': '2022-01-07'}, {'2022-01-08', '2022-01-09', '2022-01-10'}),
    ({'created_on__le': '2022-01-03'}, {'2022-01-01', '2022-01-02', '2022-01-03'}),
    ({'created_on__ge': '2022-01-07'}, {'2022-01-07', '2022-01-08', '2022-01-09', '2022-01-10'}),
    ({'created_on__ge': '2022-01-03', 'created_on__lt': '2022-01-07'},
     {'2022-01-03', '2022-01-04', '2022-01-05', '2022-01-06'}),
])
def test_creditsafe_accounts_query_inequality_tests(app, db, query, expected):
    for n in range(10):
        data = {
            'org_id': f'f392-test-{n}',
            'person_id': '0035-test',
            'username': 'cs_user',
            'password': 'cs_pass',
            'state': 'pending',
            'comment': 'new account',
            'created_on': datetime_to_json(datetime.date(2022, 1, 1) + datetime.timedelta(n)),
        }
        create_cs_account(db, **data)
    result = {
        csa['created_on'].replace('T00:00:00+00:00', '')
        for csa in app.get(
            f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}'
        ).json['resources']
    }
    assert result == expected


def test_creditsafe_accounts_query__any_result_multiple(app, db):
    cs1 = create_cs_account(db, org_id='org-id-1')
    create_cs_account(db, org_id='org-id-2')
    cs3 = create_cs_account(db, org_id='org-id-3')
    query = {'org_id__any': ['org-id-1', 'org-id-3']}
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}').json['resources']
    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([cs1['id'], cs3['id']])


def test_creditsafe_accounts_query__any_result_empty(app, db):
    create_cs_account(db, org_id='org-id-other')
    query = {'org_id__any': ['No such']}
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}').json['resources']
    assert result == []


@pytest.mark.parametrize("query, error", [
    ("stuff", 'Could not parse search condition'),
    (json.dumps({'gold': 42}), 'Unsupported search field for creditsafe_account_decorated: gold'),
    (json.dumps({'org_id__smells_like': 'roses'}), 'Unsupported search operator: smells_like'),
    (json.dumps(['gold']), 'Bad search query structure'),
    (json.dumps({'org_id__eq': [42]}), 'Bad value type for eq'),
    (json.dumps({'org_id__eq': {42: 42}}), 'Bad value type for eq'),
    (json.dumps({'org_id__any': 42}), 'Bad value type for any'),
    (json.dumps({'state': 'suspended'}), 'invalid input value for enum t_creditsafe_account_state: "suspended"'),  # noqa
    (json.dumps({'org_id': False}), 'operator does not exist: text = boolean'),
    (json.dumps({'id': '123'}), 'invalid input syntax for type uuid: "123"'),
    (json.dumps({'password': 'CS password'}), 'Unsupported search field for creditsafe_account_decorated: password'),  # noqa
])
def test_creditsafe_accounts_query_bad_syntax(app, query, error):
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={query}', expect_errors=True)
    assert result.status_code == 400
    assert result.json['error_code'] == 'BadSearchCondition'
    assert error in result.json['message']


def test_creditsafe_accounts_history_query_result_multiple(app, db):
    cs_1 = create_cs_account(db, org_id='org-id-1')
    cs_2 = create_cs_account(db, org_id='org-id-2')
    cs_2_hist_2 = create_cs_account_history(db, creditsafe_account_id=cs_2['id'],
                                            org_id='org-id-22')
    cs_2_hist_3 = create_cs_account_history(db, creditsafe_account_id=cs_2['id'],
                                            org_id='org-id-23')
    cs_2_hist_4 = create_cs_account_history(db, creditsafe_account_id=cs_2['id'],
                                            org_id='org-id-24')
    cs_3 = create_cs_account(db, org_id='org-id-3')
    cs_3_hist_2 = create_cs_account_history(db, creditsafe_account_id=cs_3['id'],
                                            org_id='org-id-32')
    cs_3_hist_3 = create_cs_account_history(db, creditsafe_account_id=cs_3['id'],
                                            org_id='org-id-33')
    cs_4 = create_cs_account(db, org_id='org-id-4')
    query = {'id__any': [cs_1['id'], cs_2['id'], cs_3['id'], cs_4['id']]}
    result = app.get(f'{API_URL}/creditsafe_accounts/query?q={json.dumps(query)}').json['resources']

    assert len(result) == 4
    assert set([i['id'] for i in result]) == set([cs_1['id'], cs_2['id'], cs_3['id'], cs_4['id']])

    cs_1_history_res = [i['history'] for i in result if i['id'] == cs_1['id']][0]
    assert len(cs_1_history_res) == 1
    set(i['org_id'] for i in cs_1_history_res) == set([cs_1['org_id']])

    cs_2_history_res = [i['history'] for i in result if i['id'] == cs_2['id']][0]
    assert len(cs_2_history_res) == 4
    set(i['org_id'] for i in cs_2_history_res) == set([cs_2['org_id'],
                                                       cs_2_hist_2['org_id'],
                                                       cs_2_hist_3['org_id'],
                                                       cs_2_hist_4['org_id']])

    cs_3_history_res = [i['history'] for i in result if i['id'] == cs_3['id']][0]
    assert len(cs_3_history_res) == 3
    set(i['org_id'] for i in cs_3_history_res) == set([cs_3['org_id'],
                                                       cs_3_hist_2['org_id'],
                                                       cs_3_hist_3['org_id']])

    cs_4_history_res = [i['history'] for i in result if i['id'] == cs_4['id']][0]
    assert len(cs_4_history_res) == 1
    set(i['org_id'] for i in cs_4_history_res) == set([cs_4['org_id']])


@pytest.mark.parametrize('data', [
    '',
    None,
    {},
])
def test_update_creditsafe_account_with_empty_payload(app, data, cs_account):
    res = app.put_json(f'{API_URL}/creditsafe_accounts/{cs_account["id"]}', data, status=400)
    assert res.status_code == 400
    assert res.json == {
        "message": "Failed to validate parameters",
        "error_code": "ParameterValidationFailed"
    }
