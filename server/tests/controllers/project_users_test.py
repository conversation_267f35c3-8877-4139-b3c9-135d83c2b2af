import json
from operator import itemgetter

import pytest

from boldataapi.fixtures.factories import (
    create_project,
    create_project_user,
    get_project_user,
)


API_URL = '/api/v1/boldata/'


def expected_json(project_user, project):
    expected = project_user.copy()
    expected['id'] = expected.pop('external_id')
    expected['project_id'] = project['external_id']
    del expected['created_on']
    del expected['last_changed']
    return expected


def test_get(app, db):
    project = create_project(db)
    project_user = create_project_user(db, project_id=project['id'])

    resp = app.get(API_URL + f'project_users/{project_user["external_id"]}')
    assert resp.status_code == 200

    expected = expected_json(project_user, project)
    assert resp.json == expected


@pytest.mark.parametrize("bad_id", [
    # When we didn't have an external_id column in project_user, using an invalid UUID would cause
    # an SQL error unless handled early, so it was important to test both
    # cases.  Now we don't expose the internal UUID id column in the API so we
    # don't really need two test cases here.
    'this-id-does-not-exist',  # not a syntactically valid UUID
    'b6649ced-ddbf-4bb6-9f19-94a22ab84bf0',  # syntactically valid UUID
])
def test_get_not_found(app, db, bad_id):
    resp = app.get(API_URL + f'project_users/{bad_id}', expect_errors=True)
    assert resp.status_code == 404
    assert resp.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {'external_id': bad_id},
    }


def test_post_without_person_id(app, db):
    project = create_project(db)
    project_user = {
        'project_id': project['external_id'],
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'notify': False,  # important that this be false, I'm paranoid about
        # schema libraries deciding that required boolean fields mean "fail
        # validation if this checkbox is unchecked"
    }
    resp = app.post_json(API_URL + 'project_users', project_user)

    expected = project_user.copy()
    expected['id'] = resp.json['id']
    expected['person_id'] = None
    assert resp.status_code == 201
    assert resp.json == expected


def test_post_with_person_id(app, db):
    project = create_project(db)
    project_user = {
        'project_id': project['external_id'],
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'person_id': 'b0bd-aa28522286454db5f2d740998f07873c-adbdd809',
        'notify': False,
    }
    resp = app.post_json(API_URL + 'project_users', project_user)

    expected = project_user.copy()
    expected['id'] = resp.json['id']
    assert resp.status_code == 201
    assert resp.json == expected


def test_post_with_person_id_feature_flag(
    app, db, set_feature_flags
):
    set_feature_flags({
        'person_id_for_project_users': True,
    })
    project = create_project(db)
    project_user = {
        'project_id': project['external_id'],
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'person_id': 'b0bd-aa28522286454db5f2d740998f07873c-adbdd809',
        'notify': False,
    }
    resp = app.post_json(API_URL + 'project_users', project_user)

    expected = project_user.copy()
    expected['id'] = resp.json['id']
    expected['user_account_id'] = None
    assert resp.status_code == 201
    assert resp.json == expected


def test_post_validation_error_with_person_id_feature_flag(
    app, db, set_feature_flags
):
    set_feature_flags({
        'person_id_for_project_users': True,
    })
    project_user = {'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808'}
    resp = app.post_json(API_URL + 'project_users', project_user, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {
            'notify': ['required field'],
            'project_id': ['required field'],
            'represented_company_id': ['required field'],
            'role': ['required field'],
            'user_account_id': ['unknown field'],
            'person_id': ['required field'],
        },
    }


def test_post_with_id(app, db):
    project = create_project(db)
    payload = {
        'id': 'cfe0-85494218e03013b4842fae9151170460-2b19ce7c',
        'project_id': project['external_id'],
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'person_id': 'd808-ce28522286454db5f2d740998f07873a-b0bdd808',
        'notify': False,
    }
    resp = app.post_json(API_URL + 'project_users', payload)
    assert resp.status_code == 201
    assert resp.json == payload

    # I want to double-check that the user exists in the database, but I cannot
    # use get_project_user() here because I don't know the internal ID.
    resp = app.get(API_URL + f'project_users/{payload["id"]}')
    assert resp.json == payload


def test_post_with_existing_id(app, db):
    project_user = create_project_user(db)
    project = create_project(db)
    payload = {
        'id': project_user['external_id'],  # clash!
        'project_id': project['external_id'],
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'notify': False,
    }
    resp = app.post_json(API_URL + 'project_users', payload, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {
            'id': ['already exists'],
        },
    }


def test_post_validation_error(app):
    project_user = {}
    resp = app.post_json(API_URL + 'project_users', project_user, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {
            'notify': ['required field'],
            'project_id': ['required field'],
            'represented_company_id': ['required field'],
            'role': ['required field'],
        },
    }


def test_post_validation_error_without_person_id_and_user_account_id(
    app,
    db,
    set_feature_flags
):
    set_feature_flags({
        'person_id_for_project_users': False,
    })
    project = create_project(db)
    project_user = {
        'notify': True,
        'project_id': project['id'],
        'represented_company_id': 'some-id',
        'role': 'manager',
    }
    resp = app.post_json(API_URL + 'project_users', project_user, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {
            'user_account_id': ['One of user_account_id and person_id must be provided'],
            'person_id': ['One of user_account_id and person_id must be provided'],
        },
    }


def test_post_bad_project_id(app, db):
    project_user = {
        'project_id': 'this-id-does-not-exist-in-the-database',
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'notify': True,
    }
    resp = app.post_json(API_URL + 'project_users', project_user, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {
            'project_id': ['not found'],
        },
    }


def test_put_roundtrips(app, db):
    project_user = create_project_user(db)
    payload = app.get(API_URL + f'project_users/{project_user["external_id"]}').json
    resp = app.put_json(API_URL + f'project_users/{project_user["external_id"]}', payload)
    assert resp.status_code == 200
    assert resp.json == payload
    assert get_project_user(db, project_user['id']) == project_user


def test_put_omitted_fields_are_unchanged(app, db):
    initial = {
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'notify': True,
    }
    project_user = create_project_user(db, **initial)
    expected = app.get(API_URL + f'project_users/{project_user["external_id"]}').json
    resp = app.put_json(API_URL + f'project_users/{project_user["external_id"]}', {})
    assert resp.status_code == 200
    assert resp.json == expected


def test_put_can_change_some_fields(app, db):
    initial = {
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'notify': True,
    }
    project_user = create_project_user(db, **initial)
    expected = app.get(API_URL + f'project_users/{project_user["external_id"]}').json
    expected['role'] = 'member'
    expected['notify'] = False
    expected["person_id"] = "b0bd-aa28522286454db5f2d740998f07873c-adbdd809"
    resp = app.put_json(
        API_URL + f'project_users/{project_user["external_id"]}',
        {
            "role": "member",
            "notify": False,  # important to test True -> False transition!
            "person_id": "b0bd-aa28522286454db5f2d740998f07873c-adbdd809",
        },
    )
    assert resp.status_code == 200
    assert resp.json == expected


def test_put_cannot_change_other_fields(app, db):
    initial = {
        'represented_company_id': '1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392',
        'role': 'manager',
        'user_account_id': 'f680-ce28522286454db5f2d740998f07873a-b0bdd808',
        'notify': True,
    }
    project_user = create_project_user(db, **initial)
    payload = {
        'id': 'lets-change-it-why-not',
        'project_id': 'all-of-them',
        'represented_company_id': '9999-********************************-********',
        'user_account_id': '8888-********************************-********',
    }
    resp = app.put_json(API_URL + f'project_users/{project_user["external_id"]}', payload,
                        expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {
            'id': ['read-only field'],
            'project_id': ['read-only field'],
            'user_account_id': ['read-only field'],
            'represented_company_id': ['read-only field'],
        },
    }
    assert get_project_user(db, project_user['id']) == project_user


def test_put_cannot_change_user_account_id_with_person_id_feature_flag(
    app, db, set_feature_flags
):
    set_feature_flags({
        'person_id_for_project_users': True,
    })
    initial = {
        "represented_company_id": "1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392",
        "role": "manager",
        "user_account_id": "f680-ce28522286454db5f2d740998f07873a-b0bdd808",
        "notify": True,
        "person_id": "b2c5-201de1629ecb3a44365f8cf90e67e616-01a6c6e9",
    }
    project_user = create_project_user(db, **initial)
    payload = {
        "id": "lets-change-it-why-not",
        "project_id": "all-of-them",
        "represented_company_id": "9999-********************************-********",
        "user_account_id": "8888-********************************-********",
        "person_id": "uuids-are-dull",
    }
    resp = app.put_json(
        API_URL + f'project_users/{project_user["external_id"]}',
        payload,
        expect_errors=True,
    )
    assert resp.status_code == 400
    assert resp.json == {
        "message": "Failed to validate parameters",
        "error_code": "ParameterValidationFailed",
        "error": {
            "user_account_id": ["unknown field"],
        },
    }
    assert get_project_user(db, project_user["id"]) == project_user


def test_put_cannot_change_some_fields_with_person_id_feature_flag(
    app, db, set_feature_flags
):
    set_feature_flags({
        'person_id_for_project_users': True,
    })

    initial = {
        "represented_company_id": "1449-44a2d57630c305dd99efca8a4aeb8e2b-2d752392",
        "role": "manager",
        "person_id": "f680-ce28522286454db5f2d740998f07873a-b0bdd808",
        "notify": True,
    }
    project_user = create_project_user(db, **initial)
    payload = {
        "id": "lets-change-it-why-not",
        "project_id": "all-of-them",
        "represented_company_id": "9999-********************************-********",
        "person_id": "lets-try-a-different-one",
    }
    resp = app.put_json(
        API_URL + f'project_users/{project_user["external_id"]}',
        payload,
        expect_errors=True,
    )
    assert resp.status_code == 400
    assert resp.json == {
        "message": "Failed to validate parameters",
        "error_code": "ParameterValidationFailed",
        "error": {
            "id": ["read-only field"],
            "project_id": ["read-only field"],
            "represented_company_id": ["read-only field"],
            # uncomment this when we no longer need to run the person_id migration
            # "person_id": ["read-only field"],
        },
    }
    assert get_project_user(db, project_user["id"]) == project_user


def test_put_with_bad_project_user_id(app, db):
    resp = app.put_json(API_URL + 'project_users/no-such-project-user-id', {}, expect_errors=True)
    assert resp.status_code == 404
    assert resp.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {'external_id': 'no-such-project-user-id'},
    }


def test_put_with_bad_values(app, db):
    project_user = create_project_user(db)
    payload = {
        'role': 'clown',
        'notify': 'maybe',
    }
    resp = app.put_json(API_URL + f'project_users/{project_user["external_id"]}', payload,
                        expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'role': ['unallowed value clown'],
            'notify': ['must be of boolean type'],
        }
    }


def test_delete_with_bad_project_user_id(app):
    resp = app.delete(API_URL + 'project_users/no-such-project-user-id', {}, expect_errors=True)
    assert resp.status_code == 404
    assert resp.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {'external_id': 'no-such-project-user-id'},
    }


def test_delete(app, db):
    project = create_project(db)
    project_user1 = create_project_user(db, project_id=project['id'])
    project_user2 = create_project_user(db, project_id=project['id'])
    resp = app.delete(API_URL + f'project_users/{project_user1["external_id"]}')
    assert resp.status_code == 200
    assert resp.json == {}
    assert get_project_user(db, project_user1['id']) is None
    assert get_project_user(db, project_user2['id']) == project_user2


def test_query(app, db):
    project = create_project(db)
    project_user1 = create_project_user(db, project_id=project['id'])
    project_user2 = create_project_user(db, project_id=project['id'])
    create_project_user(db)  # create something that should be filtered out
    query = {
        'project_id': project['external_id'],
    }
    response = app.get(f'{API_URL}project_users/query?q={json.dumps(query)}')
    result = response.json
    result['resources'].sort(key=itemgetter('id'))
    assert result == {
        'resource_type': 'project_user',
        'resources': sorted([
            expected_json(project_user1, project),
            expected_json(project_user2, project),
        ], key=itemgetter('id')),
    }


def test_query_bad_query(app, db):
    response = app.get(f'{API_URL}project_users/query?q=gimme+sumfing', expect_errors=True)
    assert response.status_code == 400
    assert response.json == {
        "error_code": "BadSearchCondition",
        "message": "Could not parse search condition",
    }
