import uuid

from boldataapi.fixtures.factories import create_comment
from boldataapi.services.project_comment_viewers import (
    create_project_comment_viewer,
    get_project_comment_viewer_by_comment_id_and_person_id,
)

API_URL = "/api/v1/boldata/"


def test_post_project_comment_viewer(app, db):
    comment = create_comment(db)
    person_id = str(uuid.uuid4())

    payload = {"comment_id": str(comment["id"]), "read_by_person_id": person_id}

    resp = app.post_json(API_URL + "project_comment_viewers", payload)
    assert resp.status_code == 201

    assert resp.json["comment_id"] == str(comment["id"])
    assert resp.json["read_by_person_id"] == person_id
    assert "id" in resp.json


def test_post_project_comment_viewer_duplicate(app, db):
    """Test that creating a duplicate viewer fails"""
    comment = create_comment(db)
    person_id = str(uuid.uuid4())

    payload = {"comment_id": str(comment["id"]), "read_by_person_id": person_id}

    resp = app.post_json(API_URL + "project_comment_viewers", payload)
    assert resp.status_code == 201

    # Second creation with same data should fail
    resp = app.post_json(
        API_URL + "project_comment_viewers", payload, expect_errors=True
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert "comment_id" in resp.json["error"]


def test_post_project_comment_viewer_missing_person_id(app, db):
    comment = create_comment(db)

    payload = {"comment_id": str(comment["id"])}
    resp = app.post_json(
        API_URL + "project_comment_viewers", payload, expect_errors=True
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert "read_by_person_id" in resp.json["error"]

    payload = {"comment_id": str(comment["id"]), "read_by_person_id": ""}
    resp = app.post_json(
        API_URL + "project_comment_viewers", payload, expect_errors=True
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert "read_by_person_id" in resp.json["error"]


def test_post_project_comment_viewer_missing_comment_id(app, db):
    payload = {"read_by_person_id": str(uuid.uuid4())}
    resp = app.post_json(
        API_URL + "project_comment_viewers", payload, expect_errors=True
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert "comment_id" in resp.json["error"]


def test_post_project_comment_viewer_invalid_comment_id(app, db):
    # Invalid comment id (the comment does not exist)
    comment_id = str(uuid.uuid4())
    payload = {"read_by_person_id": str(uuid.uuid4()), "comment_id": comment_id}
    resp = app.post_json(
        API_URL + "project_comment_viewers", payload, expect_errors=True
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert "comment_id" in resp.json["error"]


def test_delete_project_comment_viewer(app, db):
    comment = create_comment(db)
    person_id = str(uuid.uuid4())

    viewer = create_project_comment_viewer(db, str(comment["id"]), person_id)

    resp = app.delete(API_URL + f"project_comment_viewers/{viewer['id']}")
    assert resp.status_code == 200
    assert resp.json == {}

    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, comment_id=str(comment["id"]), person_id=person_id
    )
    assert len(viewers) == 0


def test_delete_nonexistent_project_comment_viewer(app):
    non_existent_id = str(uuid.uuid4())
    resp = app.delete(
        API_URL + f"project_comment_viewers/{non_existent_id}", expect_errors=True
    )
    assert resp.status_code == 404
    assert resp.json["error_code"] == "NotFound"
    assert resp.json["error"]["viewer_id"] == non_existent_id


def test_delete_non_uuid(app):
    non_uuid = "Hi"
    resp = app.delete(
        API_URL + f"project_comment_viewers/{non_uuid}", expect_errors=True
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert resp.json["error"]["viewer_id"] == f"{non_uuid} is not a UUID"


def test_query_by_comment_id(app, db):
    comment = create_comment(db)

    # Create multiple viewers for the same comment
    person_id1 = str(uuid.uuid4())
    person_id2 = str(uuid.uuid4())

    create_project_comment_viewer(db, str(comment["id"]), person_id1)
    create_project_comment_viewer(db, str(comment["id"]), person_id2)

    # Query by comment_id
    resp = app.get(
        API_URL + "project_comment_viewers/query", {"comment_id": str(comment["id"])}
    )
    assert resp.status_code == 200

    # Should return both added viewers and the creator viewer
    viewers = resp.json["resources"]
    assert len(viewers) == 3
    viewer_person_ids = [viewer["read_by_person_id"] for viewer in viewers]
    assert person_id1 in viewer_person_ids
    assert person_id2 in viewer_person_ids


def test_query_by_comment_id_that_is_not_uuid(app):
    non_uuid = "Hi"
    resp = app.get(
        API_URL + "project_comment_viewers/query",
        {"comment_id": non_uuid},
        expect_errors=True,
    )
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert resp.json["error"]["comment_id"] == f"{non_uuid} is not a UUID"


def test_query_by_person_id(app, db):
    comment1 = create_comment(db)
    comment2 = create_comment(db)

    # Create viewers with the same person for different comments
    person_id = str(uuid.uuid4())

    create_project_comment_viewer(db, str(comment1["id"]), person_id)
    create_project_comment_viewer(db, str(comment2["id"]), person_id)

    # Query by person_id
    resp = app.get(API_URL + "project_comment_viewers/query", {"person_id": person_id})
    assert resp.status_code == 200

    # Should return both viewers
    viewers = resp.json["resources"]
    assert len(viewers) == 2
    viewer_comment_ids = [viewer["comment_id"] for viewer in viewers]
    assert str(comment1["id"]) in viewer_comment_ids
    assert str(comment2["id"]) in viewer_comment_ids


def test_query_by_comment_id_and_person_id(app, db):
    comment1 = create_comment(db)
    comment2 = create_comment(db)

    person_id1 = str(uuid.uuid4())
    person_id2 = str(uuid.uuid4())

    create_project_comment_viewer(db, str(comment1["id"]), person_id1)
    create_project_comment_viewer(db, str(comment1["id"]), person_id2)
    create_project_comment_viewer(db, str(comment2["id"]), person_id1)
    create_project_comment_viewer(db, str(comment2["id"]), person_id2)

    # Query by both comment_id and person_id
    resp = app.get(
        API_URL + "project_comment_viewers/query",
        {"comment_id": str(comment1["id"]), "person_id": person_id1},
    )
    assert resp.status_code == 200

    # Should return only the matching viewer
    viewers = resp.json["resources"]
    assert len(viewers) == 1
    assert viewers[0]["comment_id"] == str(comment1["id"])
    assert viewers[0]["read_by_person_id"] == person_id1


def test_query_without_parameters(app):
    """Test that querying without parameters fails"""
    resp = app.get(API_URL + "project_comment_viewers/query", expect_errors=True)
    assert resp.status_code == 400
    assert resp.json["error_code"] == "ParameterValidationFailed"
    assert "message" in resp.json
