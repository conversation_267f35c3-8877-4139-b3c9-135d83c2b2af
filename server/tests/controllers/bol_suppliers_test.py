import datetime
import json
import operator
import uuid
from operator import itemgetter

import pytest

from boldataapi.fixtures import factories
from boldataapi.fixtures.factories import (
    FIXTURE_SUPPLIER,
    FIXTURE_SUPPLIER_RESPONSE,
)
from boldataapi.schema import (
    CLEANING,
    CONTRACTING,
    DEMOLITION,
    schema_existing_bol_supplier,
    schema_new_bol_supplier,
)
from boldataapi.services.project_supplier_comments import mark_comment_as_deleted


API_URL = '/api/v1/boldata/'


# GET tests

def test_get_suppliers_not_found(app):
    resp = app.get(API_URL + "bol_suppliers")

    assert 200 == resp.status_code
    result = json.loads(resp.body)
    result['resources'] = []


def test_get_suppliers(config, app, db):
    project_1 = factories.create_project(db)

    factories.create_supplier(
        db,
        project_id=project_1['id'],
        external_id='EXTERNAL_ID_1_1',
    )
    factories.create_supplier(
        db,
        project_id=project_1['id'],
        external_id='EXTERNAL_ID_1_2',
    )

    project_2 = factories.create_project(db)

    factories.create_supplier(
        db,
        project_id=project_2['id'],
        external_id='EXTERNAL_ID_2',
    )

    resp = app.get(API_URL + 'bol_suppliers')

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    expected = {
        'resource_type': 'bol_supplier',
        'resources': [
            {'id': 'EXTERNAL_ID_1_1'},
            {'id': 'EXTERNAL_ID_1_2'},
            {'id': 'EXTERNAL_ID_2'}
        ],
    }
    result = json.loads(resp.body)
    result['resources'] = sorted(result['resources'], key=itemgetter('id'))
    assert result == expected


def test_get_returns_ok_status(app, db):
    project = factories.create_project(db)

    internal_id_payload = {
        'project_id': project['id'],
        'company_id': 'any-company-id',
        'internal_project_id': 'test_internal_project_id',
    }

    factories.create_internal_project_id(db, internal_id_payload)

    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    resp = app.get(API_URL + f"bol_suppliers/{supplier['external_id']}")
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    new_supplier = json.loads(resp.body)
    new_supplier.pop('id')
    new_supplier.pop('revision')
    assert new_supplier == {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': None,
        'contract_work_areas': None,
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test_internal_project_id',
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': None,
        'last_visited': None,
        'visitor_type': None,
        'is_one_man_company': None,
        'has_collective_agreement': None,
        'collective_agreement_name': None,
    }


def test_get_not_found(app):
    resp = app.get(API_URL + 'bol_suppliers/not-existing', expect_errors=True)
    expected = {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {'external_id': 'not-existing'},
    }
    assert resp.json == expected
    assert resp.status_code == 404


def test_get_first_visited_disabled(app, db, featureflags):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        first_visited=datetime.datetime(2020, 10, 1, 0, 0, 0)
    )
    featureflags({'first_visited': False})
    resp = app.get(API_URL + f"bol_suppliers/{supplier['external_id']}")
    new_supplier = json.loads(resp.body)

    assert new_supplier['first_visited'] is None


# SEARCH tests


def get_bol_suppliers_search(search_url, app):
    resp = app.get(API_URL + f'bol_suppliers/search/{search_url}')
    assert resp.content_type == 'application/json'
    assert 200 == resp.status_code
    result = json.loads(resp.body.decode('utf-8'))
    return result['resources']


def test_bol_suppliers_search_id(config, app, db):
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    search_url = f"exact/id/{supplier['external_id']}"
    resp = app.get(API_URL + f'bol_suppliers/search/{search_url}')
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [
            {'id': supplier['external_id']}
        ],
    }


def test_query_suppliers_invalid_query(app, db):
    query = "invalid_query"
    resp = app.get(f'{API_URL}bol_suppliers/query?q={json.dumps(query)}', expect_errors=True)

    expected_response = {
        'error_code': 'BadSearchCondition',
        'message': 'Bad search query structure'
    }

    assert 400 == resp.status_code
    assert expected_response == resp.json


def test_query_suppliers_ge_lt(app, db):
    # Tweakable test.
    # E.g. could be suitable to test
    # larger amounts of suppliers locally at times.
    num_of_supps = 5
    batch_size = 1

    project = factories.create_project(db, name='Test')
    for _ in range(num_of_supps):
        factories.create_supplier(
            db,
            project_id=project['id'],
            parent_company_id='any-parent-id',
            company_id='any-company-id',
            parent_supplier_id='any-parent-supplier-id',
            contract_start_date=datetime.date(2020, 1, 1),
            contract_end_date=datetime.date(2020, 12, 1),
            materialized_path=['item1', 'item2'],
        )

    resp = app.get(API_URL + "bol_suppliers")
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'

    resp_json = resp.json
    ids = [x['id'] for x in resp_json['resources']]
    ids.sort()

    all_suppliers = []
    for batch in range(0, len(ids), batch_size):
        query = {}
        if batch:
            # All but the first batch get a lower limit
            query['id__ge'] = ids[batch]
        if batch + batch_size < len(ids):
            # All but the last batch get an upper limit
            query['id__lt'] = ids[batch + batch_size]
        response = app.get(f'{API_URL}bol_suppliers/query?q={json.dumps(query)}')
        result = response.json['resources']
        all_suppliers += result

    assert len(all_suppliers) == num_of_supps


def test_query_suppliers_show(app, db):
    show = ['project_resource_id', 'supplier_type']

    project = factories.create_project(db, name='Test')

    num_supps = 2
    for _ in range(num_supps):
        factories.create_supplier(
            db,
            project_id=project['id'],
            parent_company_id='any-parent-id',
            company_id='any-company-id',
            parent_supplier_id='any-parent-supplier-id',
            contract_start_date=datetime.date(2020, 1, 1),
            contract_end_date=datetime.date(2020, 12, 1),
            materialized_path=['item1', 'item2'],
        )

    resp = app.get(API_URL + "bol_suppliers")
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'

    resp_json = resp.json
    ids = [x['id'] for x in resp_json['resources']]

    query = {'id__any': ids}
    resp = app.get(f'{API_URL}bol_suppliers/query?q={json.dumps(query)}&show={json.dumps(show)}')
    all_suppliers = resp.json['resources']
    assert len(all_suppliers) == num_supps

    for supp in all_suppliers:
        assert set(supp) == set(show).union({'id'})


@pytest.mark.parametrize('to_show', ['invalid', ['rogue_drop_table']])
def test_query_suppliers_invalid_show(app, to_show):
    query = {'id': 'test'}
    show = "invalid"
    resp = app.get(
        f'{API_URL}bol_suppliers/query?q={json.dumps(query)}&show={json.dumps(show)}',
        expect_errors=True
    )
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


# Test that internal project ids functionality work
def test_query_suppliers_internal_project_ids(app, db):
    project = factories.create_project(db, name='Test')

    for i in range(2):
        project = factories.create_project(db)
        internal_project_id_payload = {
            'internal_project_id': 'Test Internal Project ID ' + str(i),
            'project_id': project['id'],
            'company_id': 'any-company-id' + str(i),
        }
        factories.create_internal_project_id(db, internal_project_id_payload)
        factories.create_supplier(
            db,
            project_id=project['id'],
            parent_company_id='any-parent-id',
            company_id='any-company-id' + str(i),
            parent_supplier_id='any-parent-supplier-id',
            contract_start_date=datetime.date(2020, 1, 1),
            contract_end_date=datetime.date(2020, 12, 1),
            materialized_path=['item1', 'item2'],
        )

    factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id3',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    resp = app.get(API_URL + "bol_suppliers")
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'

    resp_json = resp.json
    ids = [x['id'] for x in resp_json['resources']]
    query = {'id__any': ids}

    response = app.get(f'{API_URL}bol_suppliers/query?q={json.dumps(query)}')
    result = response.json['resources']

    result_sorted = sorted(result, key=lambda s: s['supplier_org_id'])

    for i in range(2):
        assert result_sorted[i]['internal_project_id'] == 'Test Internal Project ID ' + str(i)
        assert result_sorted[i]['supplier_org_id'] == 'any-company-id' + str(i)

    assert result_sorted[2]['internal_project_id'] is None
    assert result_sorted[2]['supplier_org_id'] == 'any-company-id3'


@pytest.mark.parametrize(('q_field'), [
    ('id'),
    ('project_resource_id'),
    ('supplier_role'),
    ('supplier_type'),
    ('contract_start_date'),
    ('contract_end_date'),
    ('contract_type'),
    ('contract_work_areas'),
    ('visitor_type'),
    ('parent_supplier_id'),
    ('parent_org_id'),
    ('supplier_org_id'),
    ('first_visited'),
    ('last_visited'),
    ('revision'),
    ('pa_id'),
    ('pa_status'),
])
def test_suppliers_query(app, db, q_field):

    project = factories.create_project(db)
    internal_project_id_payload = {
        'internal_project_id': 'Test Internal Project ID',
        'project_id': project['id'],
        'company_id': 'any-company-id',
    }
    factories.create_internal_project_id(db, internal_project_id_payload)
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'supplier_contacts': [
            {
                'supplier_contact_person_id': 'fake_person_id',
                'supplier_contact_email': '<EMAIL>'
            },
            {
                'supplier_contact_person_id': 'fake_person_id2',
                'supplier_contact_email': '<EMAIL>'
            }
        ]
    }
    resp = app.post_json('/api/v1/boldata/bol_suppliers', supplier)
    assert 201 == resp.status_code
    new_supplier = json.loads(resp.body)

    query = {q_field: new_supplier[q_field]}

    response = app.get(f'{API_URL}bol_suppliers/query?q={json.dumps(query)}')
    result = response.json['resources']

    result[0].pop('id')
    result[0].pop('revision')
    expected = [{
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': None,
        'contract_work_areas': None,
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'internal_project_id': 'Test Internal Project ID',
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'supplier_contacts': [
            {
                'supplier_contact_email': '<EMAIL>',
                'supplier_contact_person_id': 'fake_person_id'
            },
            {
                'supplier_contact_person_id': 'fake_person_id2',
                'supplier_contact_email': '<EMAIL>'
            }
        ],
        'first_visited': None,
        'last_visited': None,
        'visitor_type': None,
        'is_one_man_company': None,
        'has_collective_agreement': None,
        'collective_agreement_name': None,
    }]
    assert result == expected
    assert response.status_code == 200


def test_bol_suppliers_search(config, app, db):
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    search_url = f"exact/project_resource_id/{project['external_id']}"
    result = get_bol_suppliers_search(search_url, app)
    assert [{'id': supplier['external_id']}] == result


@pytest.mark.parametrize('field, q_field',
                         [('parent_company_id', 'parent_org_id'),
                          ('parent_supplier_id', 'parent_supplier_id'),
                          ('company_id', 'supplier_org_id')
                          ])
def test_bol_supppliers_search_exact_basic_fields_mapp(config, app, db, field, q_field):
    """Search finds supplier based on field values."""
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    search_url = f'exact/{q_field}/{supplier[field]}'
    result = get_bol_suppliers_search(search_url, app)
    assert {'id': supplier['external_id']} in result


def test_bol_supppliers_search_exact_project_id(config, app, db):
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    search_url = f"exact/project_resource_id/{project['external_id']}"
    result = get_bol_suppliers_search(search_url, app)
    assert {'id': supplier['external_id']} in result


@pytest.mark.parametrize(('q_field', 'field'),
                         [('contract_start_date', 'contract_start_date'),
                          ('contract_end_date', 'contract_end_date')])
def test_bol_suppliers_search_exact_date_fields_map(config, app, db, q_field, field):
    """Search finds supplier based on field values."""
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date.today(),
        contract_end_date=datetime.date.today(),
        materialized_path=['item1', 'item2'],
    )

    search_url = f"exact/{q_field}/{supplier[field]}"
    result = get_bol_suppliers_search(search_url, app)
    assert {'id': supplier['external_id']} in result


@pytest.mark.parametrize(('q_field', 'field'),
                         [('supplier_role', 'role'),
                          ('supplier_type', 'type')])
def test_bol_supppliers_search_exact_enum_fields_map(config, app, db, q_field, field):
    """Search finds supplier based on field values."""
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
        role='supplier',
        type='linked',
    )

    search_url = f'exact/{q_field}/{supplier[field]}'
    result = get_bol_suppliers_search(search_url, app)
    assert {'id': supplier['external_id']} in result


@pytest.mark.parametrize(('search_for', 'found'),
                         [('supplier_company_id', True),
                          ('client_company_id', True),
                          ('non_existent_id', False)])
def test_bol_suppliers_search_exact_list(config, app, db, search_for, found):
    """Search finds supplier based on field values in list values."""
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date.today(),
        contract_end_date=datetime.date.today(),
        materialized_path=['client_company_id', 'supplier_company_id'],
    )
    search_url = f'exact/materialized_path/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


@pytest.mark.parametrize(('q_field', 'search_for', 'found'),
                         [('supplier_contact_email', '<EMAIL>', True),
                          ('supplier_contact_email', 'spam', False),
                          ('supplier_contact_person_id', 'some_person_id', True),
                          ('supplier_contact_person_id', 'spam', False)])
def test_bol_suppliers_search_exact_subresource(config, app, db, q_field, search_for, found):
    """Search finds supplier based on field values in sub_resources."""
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date.today(),
        contract_end_date=datetime.date.today(),
        materialized_path=['item1', 'item2'],
    )

    supplier_contact_payload = {
        'supplier_id': supplier['id'],
        'person_id': 'some_person_id',
        'person_email': '<EMAIL>',
    }
    factories.create_supplier_contact(db, supplier_contact_payload)

    search_url = f'exact/{q_field}/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


@pytest.mark.xfail(reason="Insert violates foreign key constraint fk_suppliers_id")
def test_bol_suppliers_search_exact_subresource_dangling(config, app, db):
    """Search does not find result in dangling sub_resources."""
    supplier = factories.create_supplier(
        db,
        project_id=str(uuid.uuid4()),
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date.today(),
        contract_end_date=datetime.date.today(),
        materialized_path=['item1', 'item2'],
    )

    supplier_contact_payload = {
        'supplier_id': '-dangling-pointer',
        'person_id': 'some_person_id',
    }
    factories.create_supplier_contact(db, supplier_contact_payload)

    search_url = 'exact/supplier_contact_person_id/some_person_id'
    result = get_bol_suppliers_search(search_url, app)
    assert {'id': supplier['external_id']} not in result


def test_bol_suppliers_search_exact_error_parse(config, app, db):
    """Search any fails on empty list."""
    resp = app.get(API_URL + 'bol_suppliers/search/foo', expect_errors=True)

    expected_response = {
        'error_code': 'BadSearchCondition',
        'message': 'Could not parse search condition'
    }
    assert 400 == resp.status_code
    assert expected_response == resp.json


@pytest.mark.parametrize(
    ('q_field', 'search_for', 'found'),
    [('supplier_contact_email', '["<EMAIL>"]', True),
     ('supplier_contact_email', '["<EMAIL>", "<EMAIL>"]', True),
     ('supplier_contact_email', '["<EMAIL>"]', False),
     ('supplier_contact_person_id', '["exact_person_id", "spam_id"]', True),
     ('supplier_contact_person_id', '["spam_id"]', False)])
def test_bol_suppliers_search_any(config, app, db, q_field, search_for, found):
    """Search any of the values in resource and in sub_resources."""
    # Top-level fields
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date.today(),
        contract_end_date=datetime.date.today(),
        materialized_path=['item1', 'item2'],
    )
    # Sub-resource fields
    supplier_contact_payload = {
        'supplier_id': supplier['id'],
        'person_id': 'exact_person_id',
        'person_email': '<EMAIL>',
    }
    factories.create_supplier_contact(db, supplier_contact_payload)

    search_url = f'any/exact/{q_field}/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


@pytest.mark.parametrize(
    ('q_field', 'search_for', 'found'),
    [('project_resource_id', '["exact-project-id"]', True),
     ('project_resource_id', '["exact-project-id", "spam-id"]', True),
     ('project_resource_id', '["spam-id"]', False)])
def test_bol_suppliers_search_any_project_id(config, app, db, q_field, search_for, found):
    # Top-level fields
    project = factories.create_project(db, name='Test', external_id='exact-project-id')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date.today(),
        contract_end_date=datetime.date.today(),
        materialized_path=['item1', 'item2'],
    )

    search_url = f'any/exact/{q_field}/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


def test_bol_suppliers_search_any_error_empty(config, app, db):
    """Search any fails on empty list."""
    search_url = 'any/exact/project_resource_id/[]'
    resp = app.get(API_URL + f'bol_suppliers/search/{search_url}', expect_errors=True)

    expected_response = {
        'error_code': 'FieldNotInResource',
        'field': 'project_resource_id',
        'message': 'Resource does not contain given field: {field}'
    }
    assert 400 == resp.status_code
    assert expected_response == resp.json


def test_bol_suppliers_search_any_error_parse(config, app, db):
    """Search any fails on empty list."""
    search_url = 'any/exact/project_resource_id/foo'
    resp = app.get(API_URL + f'bol_suppliers/search/{search_url}', expect_errors=True)

    expected_response = {
        'error_code': 'BadAnySearchValue',
        'error': 'Expecting value: line 1 column 1 (char 0)',
        'message': "Can't parse ANY search value: {error}."
    }
    assert 400 == resp.status_code
    assert expected_response == resp.json


@pytest.mark.parametrize(
    ('q_field', 'search_for', 'found'),
    [('supplier_contact_person_id', 'exact_person_id', True),
     ('supplier_contact_person_id', 'person_id', True),
     ('supplier_contact_person_id', 'exact_person_spam', False)])
def test_bol_suppliers_search_contains(config, app, db, q_field, search_for, found):
    """Search any of the values in resource and in sub_resources."""
    # Top-level fields
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    # Sub-resource fields
    supplier_contact_payload = {
        'supplier_id': supplier['id'],
        'person_id': 'exact_person_id',
    }
    factories.create_supplier_contact(db, supplier_contact_payload)

    search_url = f'contains/{q_field}/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


@pytest.mark.parametrize(
    ('q_field', 'search_for', 'found'),
    [('project_resource_id', 'exact-project-id', True),
     ('project_resource_id', 'project-id', True),
     ('project_resource_id', 'spam-project-id', False)])
def test_bol_suppliers_search_contains_project_id(config, app, db, q_field, search_for, found):
    """Search any of the values in resource and in sub_resources."""
    # Top-level fields
    project = factories.create_project(db, name='Test', external_id='exact-project-id')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    search_url = f'contains/{q_field}/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


@pytest.mark.parametrize(
    ('q_field', 'search_for', 'found'),
    [('supplier_contact_person_id', 'exact_person_id', True),
     ('supplier_contact_person_id', 'exact', True),
     ('supplier_contact_person_id', 'person_id', False)])
def test_bol_suppliers_search_startswith(config, app, db, q_field, search_for, found):
    """Search any of the values in resource and in sub_resources."""
    # Top-level fields
    project = factories.create_project(db, name='Test')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    # Sub-resource fields
    supplier_contact_payload = {
        'supplier_id': supplier['id'],
        'person_id': 'exact_person_id',
    }
    factories.create_supplier_contact(db, supplier_contact_payload)

    search_url = f'startswith/{q_field}/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


@pytest.mark.parametrize(
    ('q_field', 'search_for', 'found'),
    [('project_resource_id', 'exact-project-id', True),
     ('project_resource_id', 'exact', True),
     ('project_resource_id', 'project-id', False)])
def test_bol_suppliers_search_startswith_project_id(config, app, db, q_field, search_for, found):
    """Search any of the values in resource and in sub_resources."""
    # Top-level fields
    project = factories.create_project(db, name='Test', external_id='exact-project-id')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    search_url = f'startswith/{q_field}/{search_for}'
    result = get_bol_suppliers_search(search_url, app)
    assert ({'id': supplier['external_id']} in result) == found


def test_bol_suppliers_search_show_all(config, app, db):
    # Deprecated: no calls are made with /show_all/ notation
    # Need a project to satisfy foreign keys
    project = factories.create_project(db)
    # Create supplier
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-company-id',
        company_id='exact-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['client_company_id', 'supplier_company_id'],
        role='supplier',
        type='linked',
    )

    supplier_contact_payload = {
        'supplier_id': supplier['id'],
    }
    # Create supplier_contact
    factories.create_supplier_contact(db, supplier_contact_payload)
    internal_id_payload = {
        'project_id': supplier['project_id'],
        'company_id': supplier['company_id'],
        'internal_project_id': 'exact_internal_project_id',
    }
    # Create internal_project_id
    factories.create_internal_project_id(db, internal_id_payload)

    show_url = 'show_all'
    supplier_id = supplier['external_id']
    search_url = f'{show_url}/exact/id/{supplier_id}'
    result = get_bol_suppliers_search(search_url, app)

    assert [
        {
            'id': supplier_id,
            'bolagsfakta_status': None,
            'contract_end_date': '2020-12-01',
            'contract_start_date': '2020-01-01',
            'internal_project_id': 'exact_internal_project_id',
            'materialized_path': ['client_company_id', 'supplier_company_id'],
            'parent_org_id': 'any-parent-company-id',
            'parent_supplier_id': 'any-parent-supplier-id',
            'project_resource_id': project['external_id'],
            'revision': result[0]['revision'],
            'supplier_contacts': [{'supplier_contact_email': '<EMAIL>',
                                   'supplier_contact_person_id': 'fake_person_id'}],
            'supplier_org_id': 'exact-company-id',
            'supplier_role': 'supplier',
            'supplier_type': 'linked',
            'type': 'bol_supplier',
        }
    ] == result


@pytest.mark.parametrize(
    ('show', 'extra_display'),
    [
        # (['project_resource_id'], {'project_resource_id': 'any-project-id'}),
        (['supplier_contacts'], {
            'supplier_contacts': [
                {
                    'supplier_contact_email': '<EMAIL>',
                    'supplier_contact_person_id': 'fake_person_id'
                }
            ]
        }),
    ]
)
def test_bol_suppliers_search_show_subresource(config, app, db, show, extra_display):
    """Search any of the values in resource and in sub_resources."""
    # Top-level fields
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    supplier_contact_payload = {
        'supplier_id': supplier['id'],
    }
    factories.create_supplier_contact(db, supplier_contact_payload)

    show_prefix = 'show/' + '/show/'.join(show)
    search_url = show_prefix + f"/exact/id/{supplier['external_id']}"
    result = get_bol_suppliers_search(search_url, app)
    assert [
        {
            'id': supplier['external_id'],
            **extra_display
        }
    ] == result


def test_bol_suppliers_search_show_subresource_project_id(config, app, db):
    project = factories.create_project(db, external_id='any-project-id')
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    show_prefix = 'show/project_resource_id'
    search_url = show_prefix + f"/exact/id/{supplier['external_id']}"
    result = get_bol_suppliers_search(search_url, app)
    assert [
        {
            'id': supplier['external_id'],
            'project_resource_id': 'any-project-id',
        }
    ] == result


@pytest.mark.parametrize(
    ('show', 'extra_display'),
    [(['supplier_contact_email'], {})])
def test_bol_suppliers_search_show_subresource_not_possible(config, app, db, show, extra_display):
    """Search any of the values in resource and in sub_resources."""
    # Top-level fields
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    supplier_contact_payload = {
        'supplier_id': supplier['id'],
    }
    factories.create_supplier_contact(db, supplier_contact_payload)

    show_prefix = 'show/' + '/show/'.join(show)
    search_url = show_prefix + f"/exact/id/{supplier['external_id']}"
    result = get_bol_suppliers_search(search_url, app)
    assert [
        {
            'id': supplier['external_id'],
            **extra_display
        }
    ] == result


def test_bol_suppliers_search_show_param_error_is_ok(config, app, db):
    """Search any fails on empty list."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    show_prefix = 'show/foo/show/bar'
    resp = app.get(
        API_URL +
        f"bol_suppliers/search/{show_prefix}/exact/id/{supplier['external_id']}",
        expect_errors=True
    )

    expected_response = {
        'resources': [{'id': supplier['external_id']}]
    }
    assert 200 == resp.status_code
    assert expected_response == resp.json


# POST tests

def test_post_returns_ok_status(app, db):
    project = factories.create_project(db)
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'supplier_contacts': [],
        'visitor_type': 'raw',
    }
    resp = app.post_json(API_URL + 'bol_suppliers', supplier)
    assert 201 == resp.status_code
    new_supplier = json.loads(resp.body)

    # Some mangling
    new_supplier.pop('id')
    new_supplier.pop('revision')

    assert new_supplier == {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'visitor_type': 'raw',
        'is_one_man_company': None,
        'has_collective_agreement': None,
        'collective_agreement_name': None,
    }


def test_add_supplier_and_update_fields(app, db):
    project = factories.create_project(db, pa_form_enabled=True)
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'supplier_contacts': [],
        'visitor_type': 'raw',
        "is_one_man_company": True,
        "has_collective_agreement": True,
        "collective_agreement_name": 'ABC Collective',
    }
    resp = app.post_json(API_URL + 'bol_suppliers', supplier)
    assert 201 == resp.status_code
    new_supplier = json.loads(resp.body)

    # Some mangling
    supplier_id = new_supplier.pop('id')
    revision = new_supplier.pop('revision')
    assert new_supplier == {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'visitor_type': 'raw',
        'is_one_man_company': True,
        'has_collective_agreement': True,
        'collective_agreement_name': 'ABC Collective',
    }

    resp = app.put_json(
        API_URL + 'bol_suppliers/%s' % supplier_id,
        {
            'revision': revision,
            'is_one_man_company': False,
            'has_collective_agreement': False,
            'collective_agreement_name': None,
        }
    )
    assert 200 == resp.status_code
    updated_supplier = resp.json

    # Some mangling
    updated_supplier.pop('id')
    updated_supplier.pop('revision')

    assert updated_supplier == {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'visitor_type': 'raw',
        'is_one_man_company': False,
        'has_collective_agreement': False,
        'collective_agreement_name': None,
    }


def test_add_supplier_and_update_fields_none(app, db):
    project = factories.create_project(db, pa_form_enabled=True)
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'supplier_contacts': [],
        'visitor_type': 'raw',
        "is_one_man_company": True,
        "has_collective_agreement": True,
        "collective_agreement_name": 'ana are mere',
    }
    resp = app.post_json(API_URL + 'bol_suppliers', supplier)
    assert 201 == resp.status_code
    new_supplier = json.loads(resp.body)

    # Some mangling
    supplier_id = new_supplier.pop('id')
    revision = new_supplier.pop('revision')
    assert new_supplier == {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'visitor_type': 'raw',
        'is_one_man_company': True,
        'has_collective_agreement': True,
        'collective_agreement_name': 'ana are mere',
    }

    resp = app.put_json(
        API_URL + 'bol_suppliers/%s' % supplier_id,
        {
            'revision': revision,
            'is_one_man_company': False,
            'has_collective_agreement': False,
            'collective_agreement_name': None,
        }
    )
    assert 200 == resp.status_code
    updated_supplier = resp.json

    # Some mangling
    updated_supplier.pop('id')
    updated_supplier.pop('revision')

    assert updated_supplier == {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'visitor_type': 'raw',
        'is_one_man_company': False,
        'has_collective_agreement': False,
        'collective_agreement_name': None,
    }

    resp = app.put_json(
        API_URL + 'bol_suppliers/%s' % supplier_id,
        {
            'revision': revision,
            'is_one_man_company': None,
            'has_collective_agreement': None,
            'collective_agreement_name': "qwerty!@#$%^&*()",
            'parent_supplier_id': None,
            'parent_org_id': None,
        }
    )
    assert 200 == resp.status_code
    updated_supplier = resp.json

    # Some mangling
    updated_supplier.pop('id')
    updated_supplier.pop('revision')

    assert updated_supplier == {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': None,
        'parent_org_id': None,
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': '2020-01-01T00:00:00',
        'last_visited': '2020-01-22T00:00:00',
        'visitor_type': 'raw',
        'is_one_man_company': None,
        'has_collective_agreement': None,
        'collective_agreement_name': "qwerty!@#$%^&*()",
    }


def test_post_not_existing_project_raises_not_found(app):
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': 'not-existing',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'test-internal-project-id',
    }

    resp = app.post_json(API_URL + 'bol_suppliers', supplier, expect_errors=True)
    expected = {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {'project_resource_id': 'not-existing'},
    }

    assert 404 == resp.status_code
    assert resp.json == expected


def test_post_validation_error(app, mocker, caplog):
    resp = app.post_json(API_URL + 'bol_suppliers', {}, expect_errors=True)
    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_post_validation_schema_used(app, mocker):
    validator_mock = mocker.patch(
        'boldataapi.controllers.bol_suppliers_controller.BolSupplierValidator')

    app.post_json(API_URL + 'bol_suppliers', {}, expect_errors=True)
    validator_mock.assert_called_with(schema_new_bol_supplier)


def test_post_server_error(app, db, mocker, caplog):
    project = factories.create_project(db)
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'supplier_contacts': []
    }
    mocker.patch(
        'boldataapi.controllers.bol_suppliers_controller.create_supplier',
        side_effect=Exception('Serious system problem occurred!'))
    resp = app.post_json(API_URL + 'bol_suppliers', supplier, expect_errors=True)

    expected_error = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }

    assert 500 == resp.status_code
    assert expected_error == resp.json

    assert 'Serious system problem occurred' in caplog.text


def test_post_supplier_contacts(app, db):
    project = factories.create_project(db)
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'supplier_contacts': [
            {
                'supplier_contact_person_id': 'other-person-id',
                'supplier_contact_email': '<EMAIL>'
            }
        ]
    }
    resp = app.post_json('/api/v1/boldata/bol_suppliers', supplier)
    assert 201 == resp.status_code
    new_supplier = json.loads(resp.body)
    assert new_supplier['supplier_contacts'] == supplier['supplier_contacts']


def test_post_empty_supplier_contacts(app, db):
    project = factories.create_project(db)
    supplier = {
        **FIXTURE_SUPPLIER,
        'project_resource_id': project['external_id'],
        'supplier_contacts': [],
    }
    expected_response = {
        **FIXTURE_SUPPLIER_RESPONSE,
        'project_resource_id': project['external_id'],
        'supplier_contacts': [],
        'is_one_man_company': None,
        'has_collective_agreement': None,
        'collective_agreement_name': None,
    }

    supplier['project_resource_id'] = project['external_id']
    resp = app.post_json('/api/v1/boldata/bol_suppliers', supplier)
    assert 201 == resp.status_code
    new_supplier = json.loads(resp.body)
    new_supplier.pop('id')
    new_supplier.pop('revision')

    assert new_supplier == expected_response


def test_post_internal_project_id_existing(app, db):
    project = factories.create_project(db)

    internal_id_payload = {
        'project_id': project['id'],
        'company_id': 'any-company-id',
        'internal_project_id': 'existing_project_id',
    }

    factories.create_internal_project_id(db, internal_id_payload)

    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': '2020-01-01',
        'contract_end_date': '2020-12-01',
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'internal_project_id': 'new-project-id',
        'supplier_contacts': [],
    }
    resp = app.post_json(API_URL + 'bol_suppliers', supplier)
    new_supplier = json.loads(resp.body)
    assert 201 == resp.status_code
    assert new_supplier['internal_project_id'] == 'existing_project_id'


def test_post_first_visited_disabled(app, db, featureflags):
    project = factories.create_project(db)
    supplier = {
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'first_visited': '2020-01-01T00:00:00',
    }

    featureflags({'first_visited': False})

    resp = app.post_json(API_URL + 'bol_suppliers', supplier)
    assert 201 == resp.status_code
    new_supplier = json.loads(resp.body)

    assert new_supplier['first_visited'] is None


# PUT tests


def test_put_roundtrip(app, db, mocker, caplog):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    resp = app.get(API_URL + f"bol_suppliers/{supplier['external_id']}")
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    payload = resp.json

    resp = app.put_json(
        API_URL + f"bol_suppliers/{supplier['external_id']}",
        payload, expect_errors=True
    )
    assert 200 == resp.status_code, resp.text
    updated_supplier = factories.get_supplier(db, supplier['id'])
    assert supplier == updated_supplier


def test_put_validation_error(app, db, mocker, caplog):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    payload = {'supplier_role': 'notvalid'}
    resp = app.put_json(
        API_URL + f"bol_suppliers/{supplier['external_id']}",
        payload, expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_put_validation_schema_used(app, db, mocker, caplog):
    project = factories.create_project(db)
    validator_mock = mocker.patch(
        'boldataapi.controllers.bol_suppliers_controller.BolSupplierValidator')

    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    payload = {'supplier_role': 'notvalid'}
    app.put_json(API_URL + f"bol_suppliers/{supplier['external_id']}", payload, expect_errors=True)
    validator_mock.assert_called_with(schema_existing_bol_supplier)


def test_put_returns_ok_status(app, db):
    project = factories.create_project(db)
    project_to_update = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    resp = app.put_json(
        API_URL + 'bol_suppliers/%s' % supplier['external_id'],
        {
            'project_resource_id': project_to_update['external_id'],
            'parent_supplier_id': 'updated-parent-supplier-id',
            'supplier_org_id': 'updated-org-id',
            'parent_org_id': 'updated-parent-org-id',
            'revision': supplier['revision'],
            'supplier_role': 'main_contractor',
            'supplier_type': 'unlinked',
            'contract_start_date': '2020-02-01',
            'contract_end_date': '2020-06-01',
            'contract_work_areas': ['cleaning'],
            'materialized_path': ['item1'],
            'first_visited': '2020-10-01T00:00:00',
            'last_visited': '2020-10-20T00:00:00',
            'visitor_type': 'nonpaed',
            'is_one_man_company': True,
            'has_collective_agreement': True,
            'collective_agreement_name': 'Test Agreement',
        }
    )
    assert 200 == resp.status_code
    updated_supplier = resp.json
    assert updated_supplier == {
        'bolagsfakta_status': None,
        'contract_end_date': '2020-06-01',
        'contract_start_date': '2020-02-01',
        'contract_type': None,
        'contract_work_areas': ['cleaning'],
        'id': supplier['external_id'],
        'internal_project_id': None,
        'first_visited': '2020-10-01T00:00:00',
        'last_visited': '2020-10-20T00:00:00',
        'materialized_path': ['item1'],
        'pa_id': None,
        'pa_status': None,
        'parent_org_id': 'updated-parent-org-id',
        'parent_supplier_id': 'updated-parent-supplier-id',
        'project_resource_id': project_to_update['external_id'],
        'revision': supplier['revision'],
        'supplier_contacts': [],
        'supplier_org_id': 'updated-org-id',
        'supplier_role': 'main_contractor',
        'supplier_type': 'unlinked',
        'type': 'bol_supplier',
        'visitor_type': 'nonpaed',
        'is_one_man_company': True,
        'has_collective_agreement': True,
        'collective_agreement_name': 'Test Agreement',
    }


@pytest.mark.parametrize("initial_id, updated_id, expected_id", [
    (None, 'updated', 'updated'),
    ('project-id', None,  None),
    ('project-id', 'updated-id',  'updated-id'),
])
def test_put_internal_project_id(app, db, initial_id, updated_id, expected_id):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='test-comppany-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    if initial_id is not None:
        factories.create_internal_project_id(db, {
            'project_id': project['id'],
            'internal_project_id': initial_id,
            'company_id': 'test-comppany-id',
        })
    internal_project_ids = factories.get_internal_project_ids(db, project['id'])
    db_initial_id = internal_project_ids[0]['internal_project_id'] if internal_project_ids else None
    assert db_initial_id == initial_id

    resp = app.put_json(
        API_URL + 'bol_suppliers/%s' % supplier['external_id'],
        {
            'revision': supplier['revision'],
            'internal_project_id': updated_id
        }
    )
    assert 200 == resp.status_code

    internal_project_ids = factories.get_internal_project_ids(db, project['id'])
    db_expected_id = (internal_project_ids[0]['internal_project_id']
                      if internal_project_ids else None)
    assert db_expected_id == expected_id


def test_put_returns_not_found_status(app):
    resp = app.put_json(
        API_URL + 'bol_suppliers/not-existing',
        {'revision': 'xxx'},
        expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'not-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_put_returns_wrong_revision_error(app, db):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    resp = app.put_json(API_URL + f"bol_suppliers/{supplier['external_id']}",
                        {'revision': 'wrong-revision'},
                        expect_errors=True)
    expected_error = {
        'message': 'Failed to validate parameters',
        'error_code': 'ParameterValidationFailed',
        'error': {'revision': ['Wrong supplier revision provided']},
    }
    assert 400 == resp.status_code
    assert expected_error == resp.json


@pytest.mark.parametrize("initial_contacts, updated_contacts", [
    ([], []),
    ([], [
        # add new contact
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        }
    ]),
    ([
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        }
        # remove existing contact
    ], []),
    ([
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        }
    ], [
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        },
        {
            'person_id': 'xxx',
            'person_email': '<EMAIL>'
        }
    ]),
    ([
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        }
    ], [
        # change email
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        }
    ]),
    ([
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        }
    ], [
        # change person_id
        {
            'person_id': 'updated-person-id',
            'person_email': '<EMAIL>'
        }
    ]),
    ([
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        },
        {
            'person_id': 'other-person-id',
            'person_email': '<EMAIL>'
        }
        # remove one and and another
    ], [
        {
            'person_id': 'any-person-id',
            'person_email': '<EMAIL>'
        },
        {
            'person_id': 'fake-person-id',
            'person_email': '<EMAIL>'
        },
    ]),
])
def test_put_update_contacts(
    app, db, initial_contacts, updated_contacts
):
    def get_items(list_of_dicts, cols):
        return list(map(operator.itemgetter(*cols), list_of_dicts))

    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    for initial_contact in initial_contacts:
        supplier_contact = {
            **initial_contact,
            'supplier_id': supplier['id'],
        }
        factories.create_supplier_contact(db, supplier_contact)

    updated_supplier_contacts_payload = []
    for updated_supplier in updated_contacts:
        updated_supplier_contacts_payload.append(
            {
                'supplier_contact_person_id': updated_supplier['person_id'],
                'supplier_contact_email': updated_supplier['person_email']
            }
        )
    resp = app.put_json(
        API_URL + f"bol_suppliers/{supplier['external_id']}",
        {
            'revision': supplier['revision'],
            'supplier_contacts': updated_supplier_contacts_payload,
        }
    )
    assert resp.status_code == 200
    supplier_contacts = factories.get_supplier_contacts(db, supplier['id'])
    cols = ['person_id', 'person_email']
    assert get_items(supplier_contacts, cols) == get_items(updated_contacts, cols)


@pytest.mark.parametrize("initial_work_areas, updated_work_areas", [
    (None, ["other"]),
    ([], ["other"]),
    (["other"], []),
])
def test_put_contract_work_areas(app, db, initial_work_areas, updated_work_areas):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
        contract_work_areas=initial_work_areas,
    )

    resp = app.put_json(
        API_URL + f"bol_suppliers/{supplier['external_id']}",
        {
            'revision': supplier['revision'],
            'contract_work_areas': updated_work_areas,
        }
    )

    assert resp.status_code == 200
    supplier = factories.get_supplier(db, supplier['id'])
    assert supplier['contract_work_areas'] == updated_work_areas


def test_put_first_visted_disabled(app, db, featureflags):
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project['id'])

    featureflags({'first_visited': False})

    resp = app.put_json(
        API_URL + 'bol_suppliers/%s' % supplier['external_id'],
        {
            'revision': supplier['revision'],
            'first_visited': '2020-10-01T00:00:00',
        }
    )
    assert 200 == resp.status_code
    updated_supplier = resp.json

    assert updated_supplier['first_visited'] is None


# DELETE


def test_delete_supplier_status_ok(app, db):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    resp = app.delete(API_URL + f"bol_suppliers/{supplier['external_id']}")
    assert 200 == resp.status_code
    assert {} == json.loads(resp.body)
    assert not factories.get_supplier(db, supplier['id'])


def test_delete_supplier_not_found(app, db):
    resp = app.delete(API_URL + 'bol_suppliers/not-existing', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'supplier_id': 'not-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_delete_supplier_deletes_supplier_contacts(app, db):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    supplier_contact_payload = {
        'person_id': 'other-person-id',
        'person_email': '<EMAIL>',
        'supplier_id': supplier['id'],
    }
    factories.create_supplier_contact(db, supplier_contact_payload)
    resp = app.delete(API_URL + f"bol_suppliers/{supplier['external_id']}")
    assert 200 == resp.status_code
    assert not factories.get_supplier(db, supplier['id'])
    assert not factories.get_supplier_contacts(db, supplier['id'])


def test_delete_server_error(app, db, mocker, caplog):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    mocker.patch(
        'boldataapi.controllers.bol_suppliers_controller.delete_supplier',
        side_effect=Exception('Serious system problem occurred!'))
    resp = app.delete(API_URL + 'bol_suppliers/%s' % supplier['external_id'], expect_errors=True)

    expected_error = {
        'message': 'Internal Server Error',
        'error_code': 'InternalServerError'
    }
    assert 500 == resp.status_code
    assert expected_error == resp.json
    assert 'Serious system problem occurred' in caplog.text


def test_delete_internal_project_id(app, db):
    project = factories.create_project(db)
    supplier = factories.create_supplier(db,
                                         project_id=project['id'],
                                         company_id='any-company-id'
                                         )

    internal_id_payload = {
        'project_id': project['id'],
        'company_id': 'any-company-id',
        'internal_project_id': 'test_internal_project_id',
    }
    factories.create_internal_project_id(db, internal_id_payload)
    resp = app.delete(API_URL + f"bol_suppliers/{supplier['external_id']}")
    assert 200 == resp.status_code
    assert not factories.get_internal_project_ids(db, project['id'])


def test_delete_internal_project_id_multiple_suppliers(app, db):
    project = factories.create_project(db)
    supplier1 = factories.create_supplier(db,
                                          project_id=project['id'],
                                          company_id='any-company-id',
                                          )
    factories.create_supplier(db,
                              project_id=project['id'],
                              company_id='any-company-id',
                              )
    internal_id_payload = {
        'project_id': project['id'],
        'company_id': 'any-company-id',
        'internal_project_id': 'test_internal_project_id',
    }

    factories.create_internal_project_id(db, internal_id_payload)

    resp = app.delete(API_URL + f"bol_suppliers/{supplier1['external_id']}")
    assert 200 == resp.status_code
    assert not factories.get_supplier(db, supplier1['id'])

    internal_project_ids = factories.get_internal_project_ids(db, project['id'])
    assert internal_project_ids[0]['internal_project_id'] == 'test_internal_project_id'


def test_get_supplier_comments(app, db):
    """Test getting all comments for a supplier."""
    supplier = factories.create_supplier(db)
    expected_comment1 = factories.create_comment(
        db, supplier_id=supplier["id"], comment="Comment1"
    )
    expected_comment2 = factories.create_comment(
        db, supplier_id=supplier["id"], comment="Comment2"
    )
    factories.create_comment(db)
    mark_comment_as_deleted(db, expected_comment2["id"], "", "")

    resp = app.get(API_URL + f'bol_suppliers/{supplier["external_id"]}/comments')

    assert resp.status_code == 200
    assert resp.json["resource_type"] == "project_supplier_comments"
    comments = resp.json["resources"]
    assert len(comments) == 2
    assert any(comment["id"] == expected_comment1["id"] for comment in comments)
    assert any(comment["id"] == expected_comment2["id"] for comment in comments)


def test_get_supplier_comments_deleted(app, db):
    """Test hiding deleted comments for a supplier."""
    supplier = factories.create_supplier(db)
    expected_comment1 = factories.create_comment(
        db, supplier_id=supplier["id"], comment="Comment1"
    )
    expected_comment2 = factories.create_comment(
        db, supplier_id=supplier["id"], comment="Comment2"
    )
    factories.create_comment(db)
    mark_comment_as_deleted(db, expected_comment2["id"], "", "")

    resp = app.get(
        API_URL + f'bol_suppliers/{supplier["external_id"]}/comments?hide_deleted=true'
    )

    assert resp.status_code == 200
    assert resp.json["resource_type"] == "project_supplier_comments"
    comments = resp.json["resources"]
    assert len(comments) == 1
    assert comments[0]["id"] == expected_comment1["id"]


def test_get_supplier_comments_empty(app, db):
    """Test getting comments for a supplier with no comments."""
    supplier = factories.create_supplier(db)

    resp = app.get(API_URL + f'bol_suppliers/{supplier["external_id"]}/comments')

    assert resp.status_code == 200
    assert resp.json["resource_type"] == "project_supplier_comments"
    assert len(resp.json["resources"]) == 0


def test_get_supplier_comments_invalid_supplier_id(app, db):
    """Test getting comments for a non-existing supplier"""

    resp = app.get(API_URL + "bol_suppliers/missing-id/comments", expect_errors=True)

    assert resp.status_code == 404
