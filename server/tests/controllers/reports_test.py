import datetime
import json

import pytest
import sqlalchemy as sa
from freezegun import freeze_time

from boldataapi.controllers.reports_controller import CONFLICTING_IDS_ERROR_MSG
from boldataapi.fixtures import factories
from boldataapi.schema import (
    QVARN_COMPAT_FIELDS_FOR_REPORTS,
    schema_existing_notification_report,
    schema_existing_status_report,
    schema_new_base_report,
    schema_new_notification_report,
    schema_new_status_report,
    schema_reports_pdf,
)
from boldataapi.storage.db import NOTIFICATION_REPORTS_TABLE

API_URL = '/api/v1/boldata/'


# GET tests


def test_get_not_found_response(app):
    resp = app.get(API_URL + 'reports/non-existing', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'non-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


@freeze_time("2012-01-14")
def test_get_status_reports_ok(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect',
                        'creditsafe', 'creditsafe_connect_core'],
    )

    resp = app.get(API_URL + 'reports/%s' % status_report['external_id'])

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    new_status_report = resp.json
    expected = {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': 'test-interested-company-id',
        'charge_reference': '23436521',
        'used_providers': ['creditsafe_v2', 'creditsafe_connect',
                           'creditsafe', 'creditsafe_connect_core'],
        'org': 'test-company-id',
        'report_type': 'bolagsfakta.company_report',
        'type': 'report',   # Qvarn back compat
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert new_status_report == expected


@freeze_time("2012-01-14")
def test_get_status_reports_interested_company_ids_to_company_ids(app, db):
    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='i1',
        company_id='c1',
        json_={'test': 'test'},
    )

    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='i2',
        company_id='c2',
        json_={'test': 'test'},
    )

    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=10),
        interested_company_id='i3',
        company_id='c3',
        json_={'test': 'test'},
    )

    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() + datetime.timedelta(days=10),
        interested_company_id='i4',
        company_id='c4',
        json_={'test': 'test'},
    )

    start_date = '2012-01-13'
    end_date = '2012-01-14'
    resp = app.get(
        API_URL + 'reports/interested_company_ids_to_company_ids?start_date=%s&end_date=%s'
        % (start_date, end_date)
    )

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json

    expected = {
        'interested_company_ids_to_company_ids': [
            {
                'interested_company_id': 'i1',
                'company_id': 'c1',
            },
            {
                'interested_company_id': 'i2',
                'company_id': 'c2',
            },
        ]
    }

    # For loop to avoid ordering issues
    for item in expected['interested_company_ids_to_company_ids']:
        assert item in result['interested_company_ids_to_company_ids']


@pytest.mark.parametrize(("start_date", "end_date"), [
    ("INVALID", "2012-01-15"),
    ("2012-01-13", "SELECT * FROM users"),
])
def test_date_validation_get_interested_company_ids_to_company_ids(app, db, start_date, end_date):
    resp = app.get(
        API_URL + 'reports/interested_company_ids_to_company_ids?start_date=%s&end_date=%s'
        % (start_date, end_date),
        expect_errors=True
    )

    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json

    assert result['error'].get('start_date') == 'Must be in YYYY-MM-DD format' or \
        result['error'].get('end_date') == 'Must be in YYYY-MM-DD format'


def test_date_validation_get_interested_company_ids_to_company_ids_no_dates(app):
    resp = app.get(
        API_URL + 'reports/interested_company_ids_to_company_ids',
        expect_errors=True
    )

    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('start_date') == 'Must be specified'

    start_date = "2012-01-13"
    resp = app.get(
        API_URL + 'reports/interested_company_ids_to_company_ids?start_date=%s'
        % (start_date),
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('end_date') == 'Must be specified'

    end_date = "2012-01-15"
    resp = app.get(
        API_URL + 'reports/interested_company_ids_to_company_ids?end_date=%s'
        % (end_date),
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('start_date') == 'Must be specified'


def test_date_validation_get_interested_company_ids_to_company_ids_bad_date_range(app):
    start_date = "2012-01-13"
    end_date = "2012-01-12"
    resp = app.get(
        API_URL + 'reports/interested_company_ids_to_company_ids?start_date=%s&end_date=%s'
        % (start_date, end_date),
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('end_date') == 'Must be greater than or equal to start_date'


@freeze_time("2012-01-14")
@pytest.mark.parametrize("end_date", [
    "2012-01-13", "2012-01-14", "2012-01-15",
])
def test_move_old_to_history(app, db, end_date):
    # status_report1 not moved
    # - either because end_date=2012-01-13 cuts it off
    # - or because it's the latest report and is kept even when it falls inside the date range
    status_report1 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='i1',
        company_id='c1',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    # status_report2 is moved
    status_report2 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='i1',
        company_id='c1',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    # status_report3 is moved
    status_report3 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=2),
        interested_company_id='i1',
        company_id='c1',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    # status_report4 is not moved
    # - because start_date=2012-01-11 cuts it off
    status_report4 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=4),
        interested_company_id='i1',
        company_id='c1',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    report2_pre_move = app.get(API_URL + 'reports/%s' % status_report2['external_id']).json
    report3_pre_move = app.get(API_URL + 'reports/%s' % status_report3['external_id']).json

    start_date = '2012-01-11'
    resp = app.post(
        API_URL + 'reports/move_old_to_history?start_date=%s&end_date=%s'
        % (start_date, end_date)
    )
    assert 201 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['moved'] == 2

    resp = app.get(API_URL + 'reports/historic/%s' % status_report2['external_id'])
    assert 200 == resp.status_code
    for k, v in resp.json.items():
        assert report2_pre_move[k] == v
    resp = app.get(API_URL + 'reports/historic/%s' % status_report3['external_id'])
    assert 200 == resp.status_code
    for k, v in resp.json.items():
        assert report3_pre_move[k] == v

    # status_report1 and status_report4 still exist

    resp = app.get(API_URL + 'reports/%s' % status_report1['external_id'])
    assert 200 == resp.status_code

    resp = app.get(API_URL + 'reports/%s' % status_report4['external_id'])
    assert 200 == resp.status_code

    # status_report2 and status_report3 are gone

    resp = app.get(API_URL + 'reports/%s' % status_report2['external_id'], expect_errors=True)
    assert 404 == resp.status_code

    resp = app.get(API_URL + 'reports/%s' % status_report3['external_id'], expect_errors=True)
    assert 404 == resp.status_code


@freeze_time("2012-01-14")
def test_move_old_reports_when_no_old_reports(app, db):
    status_report1 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='i1',
        company_id='c1',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    status_report2 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='i4',
        company_id='c2',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    status_report3 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=2),
        interested_company_id='i3',
        company_id='c3',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    start_date = '2012-01-11'
    end_date = '2012-01-15'
    resp = app.post(
        API_URL + 'reports/move_old_to_history?start_date=%s&end_date=%s'
        % (start_date, end_date)
    )

    # State should be unchanged.
    assert 201 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['moved'] == 0

    resp = app.get(API_URL + 'reports/%s' % status_report1['external_id'])
    assert 200 == resp.status_code
    resp = app.get(API_URL + 'reports/%s' % status_report2['external_id'])
    assert 200 == resp.status_code
    resp = app.get(API_URL + 'reports/%s' % status_report3['external_id'])
    assert 200 == resp.status_code


@pytest.mark.parametrize(("start_date", "end_date"), [
    ("INVALID", "2012-01-15"),
    ("2012-01-13", "SELECT * FROM users"),
])
def test_date_validation_move_old_reports(app, start_date, end_date):
    resp = app.post(
        API_URL + 'reports/move_old_to_history?start_date=%s&end_date=%s'
        % (start_date, end_date),
        expect_errors=True
    )

    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('start_date') == 'Must be in YYYY-MM-DD format' or \
        result['error'].get('end_date') == 'Must be in YYYY-MM-DD format'


def test_date_validation_move_old_reports_when_no_dates(app):
    resp = app.post(
        API_URL + 'reports/move_old_to_history',
        expect_errors=True
    )

    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('start_date') == 'Must be specified'

    start_date = "2012-01-13"
    resp = app.post(
        API_URL + 'reports/move_old_to_history?start_date=%s'
        % (start_date),
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('end_date') == 'Must be specified'

    end_date = "2012-01-15"
    resp = app.post(
        API_URL + 'reports/move_old_to_history?end_date=%s'
        % (end_date),
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('start_date') == 'Must be specified'


def test_date_validation_move_old_reports_bad_date_range(app):
    start_date = "2012-01-13"
    end_date = "2012-01-12"
    resp = app.post(
        API_URL + 'reports/move_old_to_history?start_date=%s&end_date=%s'
        % (start_date, end_date),
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('end_date') == 'Must be greater than or equal to start_date'


@freeze_time("2012-01-14")
def test_move_to_history(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='i1',
        company_id='c1',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )
    status_report2 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='i2',
        company_id='c2',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )
    status_report3 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='i3',
        company_id='c3',
        json_={'test': 'test'},
        charge_reference='23436521',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )

    payload = {
        'interested_company_ids_to_company_ids': [
            {
                'interested_company_id': 'i1',
                'company_id': 'c1',
            },
            {
                'interested_company_id': 'i2',
                'company_id': 'c2',
            },
        ]
    }

    resp = app.post_json(
        API_URL + 'reports/move_to_history',
        payload,
    )
    assert 201 == resp.status_code

    resp = app.get(API_URL + 'reports/historic/%s' % status_report['external_id'])

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    historic_status_report = resp.json

    expected = {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': 'i1',
        'org': 'c1',
        'charge_reference': '23436521',
        'used_providers': ['creditsafe_v2', 'creditsafe_connect'],
    }
    assert historic_status_report == expected

    resp = app.get(API_URL + 'reports/historic/%s' % status_report2['external_id'])

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    historic_status_report = resp.json

    expected = {
        'id': status_report2['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': 'i2',
        'org': 'c2',
        'charge_reference': '23436521',
        'used_providers': ['creditsafe_v2', 'creditsafe_connect'],
    }
    assert historic_status_report == expected

    # Nothing should be changed in status_report3 as we did not
    # request a change for that.
    resp = app.get(API_URL + 'reports/%s' % status_report3['external_id'])
    assert 200 == resp.status_code

    # status_report1 and status_report2 should have been removed.
    resp = app.get(API_URL + 'reports/%s' % status_report['external_id'], expect_errors=True)
    assert 404 == resp.status_code

    resp = app.get(API_URL + 'reports/%s' % status_report2['external_id'], expect_errors=True)
    assert 404 == resp.status_code


@freeze_time("2012-01-14")
def test_move_to_history_validation(app, db):
    payload = {
        'interested_company_ids_to_company_ids': [
            {
                'interested_company_id': 'i1',
                'company_id': 'c1',
            },
            {
                'interested_company_id': 'i2',
                'company_id': 'c2',
            },
        ]
    }
    resp = app.post_json(
        API_URL + 'reports/move_to_history',
        payload,
    )
    assert 201 == resp.status_code

    payload = {
        'interested_company_ids_to_company_ids': []
    }
    resp = app.post_json(
        API_URL + 'reports/move_to_history',
        payload,
        expect_errors=True
    )
    assert 201 == resp.status_code

    payload = {
        'interested_company_ids_to_company_ids': [
            {
                'interested_company_id': 'i1',
            },
        ]
    }
    resp = app.post_json(
        API_URL + 'reports/move_to_history',
        payload,
        expect_errors=True
    )
    assert 400 == resp.status_code

    payload = {
        'interested_company_ids_to_company_ids': [
            {
                'rogue_key': 'c1',
            },
        ]
    }
    resp = app.post_json(
        API_URL + 'reports/move_to_history',
        payload,
        expect_errors=True
    )
    assert 400 == resp.status_code


def test_get_status_reports_sub_resource_ok(app, db):
    status_report = factories.create_status_report(db)
    resp = app.get(API_URL + 'reports/%s/sync' % status_report['external_id'])

    assert 200 == resp.status_code
    assert resp.json == {}


def test_get_status_report_server_error(app, mocker, caplog):
    mocker.patch(
        'boldataapi.controllers.reports_controller.get_status_report',
        side_effect=Exception('Serious system problem occurred!'))
    resp = app.get(API_URL + 'reports/any-id', expect_errors=True)

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500
    assert 'Serious system problem occurred' in caplog.text


@freeze_time("2012-01-14")
def test_get_imported_notification_report_ok(app, db):
    db_notification_report = factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        period='daily',
        to_timestamp=datetime.datetime.strptime('2012-12-31', '%Y-%m-%d'),
        from_timestamp=datetime.datetime.strptime('2012-06-30', '%Y-%m-%d'),
        statuses=['any-status'],
        company_ids=['any-company-id'],
        user_company_id='any-user-company-id',
        user_email='<EMAIL>',
        user_name='test_user',
        user_locale='sv',
        user_report={'test': 'test value'},
        qvarn_report={'test': 'qvarn report'},
    )
    resp = app.get(API_URL + f"reports/{db_notification_report['external_id']}")

    assert 200 == resp.status_code
    notification_report = resp.json
    expected = {
        'id': db_notification_report['external_id'],
        'tilaajavastuu_status': None,
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': None,
        'org': None,
        'report_type': 'bolagsfakta.status_change_report',
        'type': 'report',
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert notification_report == expected


@freeze_time("2012-01-14")
def test_get_notification_report_ok(app, db):
    db_notification_report = factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        period='daily',
        to_timestamp=datetime.datetime.strptime('2012-12-31', '%Y-%m-%d'),
        from_timestamp=datetime.datetime.strptime('2012-06-30', '%Y-%m-%d'),
        statuses=['any-status'],
        company_ids=['any-company-id'],
        user_company_id='any-user-company-id',
        user_email='<EMAIL>',
        user_name='test_user',
        user_locale='sv',
        user_report={'test': 'test value'},
        qvarn_report={'test': 'qvarn report'},
    )
    resp = app.get(API_URL + f"reports/{db_notification_report['external_id']}")

    assert 200 == resp.status_code
    notification_report = resp.json
    expected = {
        'id': db_notification_report['external_id'],
        'tilaajavastuu_status': None,
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': None,
        'org': None,
        'report_type': 'bolagsfakta.status_change_report',
        'type': 'report',
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert notification_report == expected


@freeze_time("2012-01-14")
def test_get_notification_report_multiple_ok(app, db):
    external_id = 'test-qvarn-id'
    factories.create_notification_report(
        db,
        external_id=external_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )
    factories.create_notification_report(
        db,
        external_id=external_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )
    factories.create_notification_report(
        db,
        external_id=external_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )

    resp = app.get(API_URL + 'reports/%s' % external_id)

    assert 200 == resp.status_code
    notification_report = resp.json
    expected = {
        'id': external_id,
        'tilaajavastuu_status': None,
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': None,
        'org': None,
        'report_type': 'bolagsfakta.status_change_report',
        'type': 'report',
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert notification_report == expected


def test_get_status_and_notification_report_error(app, db):
    conflicting_id = 'test-qvarn-id'
    factories.create_status_report(
        db,
        external_id=conflicting_id,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )
    factories.create_notification_report(
        db,
        external_id=conflicting_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )
    resp = app.get(API_URL + 'reports/%s' % conflicting_id, expect_errors=True)

    expected_error = {
        'message': 'Internal Server Error',
        'error_code': 'InternalServerError',
        'error': {
            'external_id': [CONFLICTING_IDS_ERROR_MSG]
        }
    }

    assert 500 == resp.status_code
    assert expected_error == resp.json


def test_get_status_report_pdf_ok(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )
    resp = app.get(API_URL + f"reports/{status_report['external_id']}/pdf")

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    assert {'test': 'test'} == resp.json


def test_get_notification_report_pdf_ok(app, db):
    notification_report = factories.create_notification_report(
        db,
        qvarn_report={'test': 'test'},
    )
    resp = app.get(API_URL + f"reports/{notification_report['external_id']}/pdf")

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    assert {'test': 'test'} == resp.json


def test_get_report_pdf_not_found_report(app):
    resp = app.get(API_URL + 'reports/non-existing/pdf', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'non-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_get_status_report_pdf_not_found_pdf(app, db):
    status_report = factories.create_status_report(db)
    assert not status_report['json_']

    resp = app.get(
        API_URL + f"reports/{status_report['external_id']}/pdf",
        expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': status_report['external_id']}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_get_notification_report_pdf_not_found_pdf(app, db):
    notification_report = factories.create_notification_report(db)
    assert not notification_report['qvarn_report']

    resp = app.get(
        API_URL + f"reports/{notification_report['external_id']}/pdf",
        expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': notification_report['external_id']}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_get_latest_report_by_company_ok(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['provider01', 'provider02'],
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-other-company-id',
        json_={'test': 'other'},
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-other-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'other'},
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(1),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'old'},
    )
    resp = app.get(API_URL + "reports/by_company?company_id=test-company-id"
                   "&interested_company_id=test-interested-company-id")
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    assert resp.json == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '21432153',
        'used_providers': ['provider01', 'provider02'],
    }


def test_get_latest_report_by_company_ok_no_json(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['provider01', 'provider02'],
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-other-company-id',
        json_={'test': 'other'},
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-other-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'other'},
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(1),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'old'},
    )
    resp = app.get(API_URL + "reports/by_company?company_id=test-company-id"
                   "&interested_company_id=test-interested-company-id&include_json=false")
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    assert resp.json == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'charge_reference': '21432153',
        'used_providers': ['provider01', 'provider02'],
    }


def test_get_latest_report_by_company_not_found(app, db):
    resp = app.get(API_URL + "reports/by_company?company_id=test-company-id"
                   "&interested_company_id=test-interested-company-id", status=404)
    assert resp.status_code == 404
    assert resp.content_type == 'application/json'
    assert resp.json == {
        "error_code": "NotFound",
        "message": "Not found",
        "error": {
            "company_id": "test-company-id",
            "interested_company_id": "test-interested-company-id",
        },
    }


def test_get_latest_report_by_company_bad_params(app, db):
    resp = app.get(API_URL + "reports/by_company", status=400)
    assert resp.status_code == 400
    assert resp.content_type == 'application/json'
    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'company_id': 'parameter is required',
            'interested_company_id': 'parameter is required',
        },
    }


def test_get_latest_report_by_company_bad_params_bad_bool(app, db):
    resp = app.get(
        API_URL + "reports/by_company?company_id=a&interested_company_id=b&include_json=c",
        status=400)
    assert resp.status_code == 400
    assert resp.content_type == 'application/json'
    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'include_json': "bad boolean value ('c'), expected true/false",
        },
    }


def test_get_latest_report_by_company_ok_blank_interested_org_id(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21345421',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )
    resp = app.get(API_URL + "reports/by_company?company_id=test-company-id"
                   "&interested_company_id=")
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    assert resp.json == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': '',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '21345421',
        'used_providers': ['creditsafe_v2', 'creditsafe_connect'],
    }


def test_get_latest_report_by_company_filter_by_provider(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test',
               'used_providers': ['creditsafe_v2', 'also_something_unrelated']},
        charge_reference='32461249',
        used_providers=['creditsafe_v2', 'creditsafe_connect'],
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test',
               'used_providers': ['creditsafe']},
    )
    resp = app.get(API_URL + "reports/by_company?company_id=test-company-id"
                   "&interested_company_id=test-interested-company-id"
                   "&provider=creditsafe_v2")
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    assert resp.json == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {
            'test': 'test',
            'used_providers': ['creditsafe_v2', 'also_something_unrelated'],
        },
        'charge_reference': '32461249',
        'used_providers': ['creditsafe_v2', 'creditsafe_connect'],
    }


def test_get_latest_report_by_company_filter_by_provider_not_found(app, db):
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test',
               'used_providers': ['creditsafe']},
    )
    resp = app.get(API_URL + "reports/by_company?company_id=test-company-id"
                   "&interested_company_id=test-interested-company-id"
                   "&provider=creditsafe_v2", status=404)
    assert resp.status_code == 404
    assert resp.content_type == 'application/json'
    assert resp.json == {
        "error_code": "NotFound",
        "message": "Not found",
        "error": {
            "company_id": "test-company-id",
            "interested_company_id": "test-interested-company-id",
            "provider": "creditsafe_v2",
        },
    }


@freeze_time("2012-01-14")
def test_get_reports(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='14321532',
        used_providers=['creditsafe', 'provider02'],
    )
    status_report2 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['provider01', 'creditsafe'],
    )
    status_report3 = factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-other-company-id',
        json_={'test': 'other'},
        charge_reference='31432153',
        used_providers=['creditsafe', 'provider02'],
    )

    start_date = (datetime.datetime.now() - datetime.timedelta(1)).strftime('%Y-%m-%d')
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    provider = 'creditsafe'
    resp = app.get(API_URL + 'reports', {'start_date': start_date,
                                         'end_date': end_date,
                                         'provider': provider})

    assert resp.status_code == 200
    assert resp.content_type == 'application/json'

    reports = resp.json['reports']
    assert len(reports) == 3

    reports = sorted(reports, key=lambda r: r['charge_reference'])
    assert reports[0] == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '14321532',
        'used_providers': ['creditsafe', 'provider02'],
    }
    assert reports[1] == {
        'id': status_report2['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report2['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '21432153',
        'used_providers': ['provider01', 'creditsafe'],
    }
    assert reports[2] == {
        'id': status_report3['external_id'],
        'tilaajavastuu_status': '300 INCOMPLETE',
        'generated_timestamp': status_report3['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-other-company-id',
        'json': {'test': 'other'},
        'charge_reference': '31432153',
        'used_providers': ['creditsafe', 'provider02'],
    }


@pytest.mark.parametrize("provider, expected_status, expected_result", [
    ('creditsafe', 200, {'pagination': {
                            'limit': None, 'has_more': False, 'next_cursor': None},
                         'reports': []}),
    ('creditsafe_ggs', 200, {'pagination': {
                            'limit': None, 'has_more': False, 'next_cursor': None},
                             'reports': []}),
    ('creditsafe_v2', 200, {'pagination': {
                            'limit': None, 'has_more': False, 'next_cursor': None},
                            'reports': []}),
    ('creditsafe_connect', 200, {'pagination': {
                                 'limit': None, 'has_more': False, 'next_cursor': None},
                                 'reports': []}),
    ('some_provider', 400, {
        'error': {'provider': 'Allowed values: creditsafe, creditsafe_v2, '
                  'creditsafe_ggs, creditsafe_connect, creditsafe_connect_core, '
                  'bisnode, skatteverket'},
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters'
    }),
    ('creditsafe_v', 400, {
        'error': {'provider': 'Allowed values: creditsafe, creditsafe_v2, '
                  'creditsafe_ggs, creditsafe_connect, creditsafe_connect_core, '
                  'bisnode, skatteverket'},
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters'
    }),
    ('ggs', 400, {
        'error': {'provider': 'Allowed values: creditsafe, creditsafe_v2, '
                  'creditsafe_ggs, creditsafe_connect, creditsafe_connect_core, '
                  'bisnode, skatteverket'},
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters'
    }),
    ('credit', 400, {
        'error': {'provider': 'Allowed values: creditsafe, creditsafe_v2, '
                  'creditsafe_ggs, creditsafe_connect, creditsafe_connect_core, '
                  'bisnode, skatteverket'},
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters'
    }),
])
def test_get_reports_provider_validation(app, provider, expected_status, expected_result):
    start_date = (datetime.datetime.now() - datetime.timedelta(1)).strftime('%Y-%m-%d')
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    res = app.get(API_URL + 'reports', {'start_date': start_date,
                                        'end_date': end_date,
                                        'provider': provider},
                  status=expected_status)
    assert res.json.get('reports') == expected_result.get('reports')
    assert res.json.get('error') == expected_result.get('error')


@pytest.mark.parametrize("provider, expected_count", [
    ('creditsafe', 3),
    ('creditsafe_v2', 1),
    ('creditsafe_connect', 1),
    ('creditsafe_ggs', 1),
])
def test_get_reports_by_provider(app, db, provider, expected_count):
    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='14321532',
        used_providers=['creditsafe', 'creditsafe_connect'],
    )
    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['creditsafe_ggs', 'creditsafe'],
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-other-company-id',
        json_={'test': 'other'},
        charge_reference='31432153',
        used_providers=['creditsafe', 'creditsafe_v2'],
    )
    start_date = (datetime.datetime.now() - datetime.timedelta(1)).strftime('%Y-%m-%d')
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    res = app.get(API_URL + 'reports', {'start_date': start_date,
                                        'end_date': end_date,
                                        'provider': provider})
    reports = res.json['reports']
    assert len(reports) == expected_count


@freeze_time("2012-01-14")
def test_get_reports_with_history(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='14321532',
        used_providers=['provider01', 'provider02'],
    )
    status_report2 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['provider01', 'provider02'],
    )
    status_report3 = factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-other-company-id',
        json_={'test': 'other'},
        charge_reference='31432153',
        used_providers=['provider01', 'provider02'],
    )

    start_date = (datetime.datetime.now() - datetime.timedelta(1)).strftime('%Y-%m-%d')
    end_date = (datetime.datetime.now() + datetime.timedelta(1)).strftime('%Y-%m-%d')

    resp = app.post(
        API_URL + 'reports/move_old_to_history?start_date=%s&end_date=%s'
        % (start_date, end_date)
    )
    assert 201 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['moved'] == 1

    resp = app.get(API_URL + "reports?start_date={}&end_date={}".format(start_date, end_date))
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'

    reports = resp.json['reports']
    assert len(reports) == 3

    reports = sorted(reports, key=lambda r: r['charge_reference'])
    assert reports[0] == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '14321532',
        'used_providers': ['provider01', 'provider02'],
    }
    # Note that this report has an empty json field
    # since it is a historic report.
    assert reports[1] == {
        'id': status_report2['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report2['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {},
        'charge_reference': '21432153',
        'used_providers': ['provider01', 'provider02'],
    }
    assert reports[2] == {
        'id': status_report3['external_id'],
        'tilaajavastuu_status': '300 INCOMPLETE',
        'generated_timestamp': status_report3['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-other-company-id',
        'json': {'test': 'other'},
        'charge_reference': '31432153',
        'used_providers': ['provider01', 'provider02'],
    }


@freeze_time("2012-01-14")
def test_get_reports_with_history_date_range(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='14321532',
        used_providers=['provider01', 'provider02'],
    )
    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['provider01', 'provider02'],
    )
    status_report3 = factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-other-company-id',
        json_={'test': 'other'},
        charge_reference='31432153',
        used_providers=['provider01', 'provider02'],
    )

    start_date = (datetime.datetime.now() - datetime.timedelta(1)).strftime('%Y-%m-%d')
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')

    resp = app.post(
        API_URL + 'reports/move_old_to_history?start_date=%s&end_date=%s'
        % (start_date, end_date)
    )
    assert 201 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['moved'] == 1

    start_date = datetime.datetime.now().strftime('%Y-%m-%d')
    resp = app.get(API_URL + "reports?start_date={}&end_date={}".format(start_date, end_date))
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'

    reports = resp.json['reports']
    assert len(reports) == 2

    reports = sorted(reports, key=lambda r: r['charge_reference'])
    assert reports[0] == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '14321532',
        'used_providers': ['provider01', 'provider02'],
    }
    assert reports[1] == {
        'id': status_report3['external_id'],
        'tilaajavastuu_status': '300 INCOMPLETE',
        'generated_timestamp': status_report3['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-other-company-id',
        'json': {'test': 'other'},
        'charge_reference': '31432153',
        'used_providers': ['provider01', 'provider02'],
    }


@freeze_time("2012-01-14")
def test_get_reports_date_range(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='14321532',
        used_providers=['provider01', 'provider02'],
    )
    status_report2 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['provider01', 'provider02'],
    )
    factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now() + datetime.timedelta(days=2),
        interested_company_id='test-interested-company-id',
        company_id='test-other-company-id',
        json_={'test': 'other'},
        charge_reference='31432153',
        used_providers=['provider01', 'provider02'],
    )

    start_date = (datetime.datetime.now() - datetime.timedelta(1)).strftime('%Y-%m-%d')
    end_date = (datetime.datetime.now() + datetime.timedelta(1)).strftime('%Y-%m-%d')
    resp = app.get(API_URL + "reports?start_date={}&end_date={}".format(start_date, end_date))
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    reports = resp.json['reports']
    assert len(reports) == 2

    reports = sorted(reports, key=lambda r: r['charge_reference'])
    assert reports[0] == {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '14321532',
        'used_providers': ['provider01', 'provider02'],
    }
    assert reports[1] == {
        'id': status_report2['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report2['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '21432153',
        'used_providers': ['provider01', 'provider02'],
    }


@freeze_time("2012-01-14")
def test_get_reports_date_range_no_results(app, db):
    start_date = (datetime.datetime.now() - datetime.timedelta(1)).strftime('%Y-%m-%d')
    end_date = (datetime.datetime.now() + datetime.timedelta(1)).strftime('%Y-%m-%d')
    resp = app.get(API_URL + "reports?start_date={}&end_date={}".format(start_date, end_date))
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    reports = resp.json['reports']
    assert reports == []


@pytest.mark.parametrize(("start_date", "end_date"), [
    ("INVALID", "2012-01-15"),
    ("2012-01-13", "SELECT * FROM users"),
])
def test_get_reports_date_validation(app, start_date, end_date):
    resp = app.get(
        API_URL + 'reports?start_date=%s&end_date=%s'
        % (start_date, end_date),
        expect_errors=True
    )

    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json

    assert result['error'].get('start_date') == 'Must be in YYYY-MM-DD format' or \
        result['error'].get('end_date') == 'Must be in YYYY-MM-DD format'


def test_get_reports_validation_no_dates(app):
    resp = app.get(
        API_URL + 'reports',
        expect_errors=True
    )
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['reports'] == []


def test_get_reports_validation_bad_date_range(app):
    start_date = "2012-01-13"
    end_date = "2012-01-12"
    resp = app.get(
        API_URL + 'reports?start_date=%s&end_date=%s'
        % (start_date, end_date),
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.content_type == 'application/json'
    result = resp.json
    assert result['error'].get('end_date') == 'Must be greater than or equal to start_date'


def test_get_reports_company_ids_interested_company_id(app, db):
    status_report1 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id1',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='14321532',
        used_providers=['provider01', 'provider02'],
    )
    status_report2 = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now() - datetime.timedelta(days=1),
        interested_company_id='test-interested-company-id2',
        company_id='test-company-id',
        json_={'test': 'test'},
        charge_reference='21432153',
        used_providers=['provider01', 'provider02'],
    )
    status_report3 = factories.create_status_report(
        db,
        status='300 INCOMPLETE',
        generated_timestamp=datetime.datetime.now() + datetime.timedelta(days=2),
        interested_company_id='test-interested-company-id3',
        company_id='test-other-company-id',
        json_={'test': 'other'},
        charge_reference='31432153',
        used_providers=['provider01', 'provider02'],
    )

    resp = app.get(API_URL + "reports?company_ids=test-company-id,test-other-company-id")
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    reports = resp.json['reports']
    assert len(reports) == 3

    reports = sorted(reports, key=lambda r: r['charge_reference'])
    assert reports[0] == {
        'id': status_report1['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report1['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id1',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '14321532',
        'used_providers': ['provider01', 'provider02'],
    }
    assert reports[1] == {
        'id': status_report2['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report2['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id2',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '21432153',
        'used_providers': ['provider01', 'provider02'],
    }
    assert reports[2] == {
        'id': status_report3['external_id'],
        'tilaajavastuu_status': '300 INCOMPLETE',
        'generated_timestamp': status_report3['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id3',
        'org': 'test-other-company-id',
        'json': {'test': 'other'},
        'charge_reference': '31432153',
        'used_providers': ['provider01', 'provider02'],
    }

    resp = app.get(API_URL + "reports?interested_company_id=test-interested-company-id2")
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    reports = resp.json['reports']
    assert len(reports) == 1

    assert reports[0] == {
        'id': status_report2['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': status_report2['generated_timestamp'].isoformat(),
        'interested_org_id': 'test-interested-company-id2',
        'org': 'test-company-id',
        'json': {'test': 'test'},
        'charge_reference': '21432153',
        'used_providers': ['provider01', 'provider02'],
    }


# POST tests


def test_post_any_empty_report_validation_schema_used(app, db, mocker):
    # force validator to raise validation error with return_value
    validator_mock = mocker.patch(
        'boldataapi.controllers.reports_controller.ReportValidator', return_value=False)

    app.post_json(API_URL + 'reports', {}, expect_errors=True)
    validator_mock.assert_called_with(schema_new_base_report, allow_unknown=True)


def test_post_any_empty_report_validation_errors(app):
    # TODO test specific errors in schema tests
    resp = app.post_json(API_URL + 'reports', {}, expect_errors=True)

    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_post_validation_new_status_report_schema_used(app, mocker):
    status_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.company_report',
    }

    # TODO test specific rules inside schema validation tests
    validator_mock = mocker.patch(
        'boldataapi.controllers.reports_controller.ReportValidator')

    # make base report validation ok
    base_validator_mock = mocker.Mock()
    base_validator_mock.validate.return_value = True
    base_validator_mock.normalized.return_value = status_report

    validator_mock.side_effect = [base_validator_mock]

    app.post_json(API_URL + 'reports', status_report, expect_errors=True)
    validator_mock.assert_called_with(schema_new_status_report)


def test_post_validation_new_status_report_errors(app):
    status_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.company_report',
        'tilaajavastuu_status': 1,
    }

    resp = app.post_json(API_URL + 'reports', status_report, expect_errors=True)

    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_post_validation_new_notification_report_schema_used(app, mocker):
    notification_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.status_change_report',
    }

    # TODO test specific rules inside schema validation tests
    validator_mock = mocker.patch(
        'boldataapi.controllers.reports_controller.ReportValidator')

    # make base report validation ok
    base_validator_mock = mocker.Mock()
    base_validator_mock.validate.return_value = True
    base_validator_mock.normalized.return_value = notification_report

    validator_mock.side_effect = [base_validator_mock]

    app.post_json(API_URL + 'reports', notification_report, expect_errors=True)
    validator_mock.assert_called_with(schema_new_notification_report)


def test_post_validation_new_notification_report_errors(app):
    notification_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.status_change_report',
        'tilaajavastuu_status': 1,
    }

    resp = app.post_json(API_URL + 'reports', notification_report, expect_errors=True)

    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


@freeze_time("2012-01-14")
def test_post_status_report_ok(app, db):
    status_report = {
        'type': 'report',
        'org': 'test-org-id',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': datetime.datetime.now().isoformat(),
        'tilaajavastuu_status': '500 OK',
        'interested_org_id': 'interested-org-id',
        'pdf': {
            'content_type': 'application/json',
            'body': json.dumps({'test': 'test report'})
        },
        'charge_reference': '*********',
        'used_providers': ['creditsafe_ggs'],
    }
    resp = app.post_json(API_URL + 'reports', status_report)
    assert 201 == resp.status_code

    new_status_report = resp.json
    new_status_report.pop('id')
    expected = {
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': 'interested-org-id',
        'org': 'test-org-id',
        'report_type': 'bolagsfakta.company_report',
        'type': 'report',
        'charge_reference': '*********',
        'used_providers': ['creditsafe_ggs'],
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert new_status_report == expected


@freeze_time("2012-01-14")
def test_post_notification_report_ok(app, db):
    notification_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.status_change_report',
        'generated_timestamp': datetime.datetime.now().isoformat(),
        'pdf': {
            'content_type': 'application/json',
            'body': json.dumps({'test': 'test notification report'})
        }
    }
    resp = app.post_json(API_URL + 'reports', notification_report)
    assert 201 == resp.status_code

    new_notification_report = resp.json
    new_notification_report.pop('id')

    expected = {
        'tilaajavastuu_status': None,
        'generated_timestamp': '2012-01-14T00:00:00',
        'interested_org_id': None,
        'org': None,
        'report_type': 'bolagsfakta.status_change_report',
        'type': 'report',
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert new_notification_report == expected


def test_post_server_error(app, mocker, caplog):
    mocker.patch(
        'boldataapi.controllers.reports_controller.create_status_report',
        side_effect=Exception('Serious system problem occurred!'))

    status_report = {
        'type': 'report',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': '2012-01-14T00:00:00',
        'org': 'test-org',
        'pdf': {
            'content_type': 'application/json',
            'body': 'body',
        },
        'interested_org_id': 'test-interested-org-id',
        'tilaajavastuu_status': '200 INVESTIGATE',
    }
    resp = app.post_json(API_URL + 'reports', status_report, expect_errors=True)
    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500
    assert 'Serious system problem occurred' in caplog.text


# PUT tests


def test_put_not_found_report(app, db):
    not_existing_id = 'non-existing'

    resp = app.put_json(
        API_URL + 'reports/%s' % not_existing_id, {}, expect_errors=True
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {
            'external_id': not_existing_id,
        }
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_put_status_and_notification_report_error(app, db):
    conflicting_id = 'test-qvarn-id'
    factories.create_status_report(
        db,
        external_id=conflicting_id,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )
    factories.create_notification_report(
        db,
        external_id=conflicting_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )
    resp = app.put_json(
        API_URL + 'reports/%s' % conflicting_id, {}, expect_errors=True
    )

    expected_error = {
        'message': 'Internal Server Error',
        'error_code': 'InternalServerError',
        'error': {
            'external_id': [CONFLICTING_IDS_ERROR_MSG]
        }
    }

    assert 500 == resp.status_code
    assert expected_error == resp.json


# test if validation is applied to this endpoint
# and errors are propagated
def test_put_status_report_validation_error(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )

    resp = app.put_json(
        API_URL + f"reports/{status_report['external_id']}",
        {'non-valid-field': 'non valid value'},
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_put_notification_report_validation_error(app, db):
    notification_report = factories.create_notification_report(
        db,
    )

    resp = app.put_json(
        API_URL + f"reports/{notification_report['external_id']}",
        {'non-valid-field': 'non valid value'},
        expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


# test if specific validation schema is used
# If we need to test validation cases for schema
# tests/schema/report_test.py is for that
def test_put_status_report_validation_schema_used(app, db, mocker):
    validator_mock = mocker.patch(
        'boldataapi.controllers.reports_controller.ReportValidator')

    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )

    app.put_json(
        API_URL + f"reports/{status_report['external_id']}",
        {},
        expect_errors=True
    )
    validator_mock.assert_called_with(schema_existing_status_report)


def test_put_notification_report_validation_schema_used(app, db, mocker):
    validator_mock = mocker.patch(
        'boldataapi.controllers.reports_controller.ReportValidator')

    notification_report = factories.create_notification_report(db)
    app.put_json(
        API_URL + f"reports/{notification_report['external_id']}",
        {},
        expect_errors=True
    )
    validator_mock.assert_called_with(schema_existing_notification_report)


@freeze_time("2012-01-14")
def test_put_status_report_ok(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )
    updated_payload = {
        'type': 'report',
        'org': 'updated-test-org-id',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': (datetime.datetime.now() + datetime.timedelta(5)).isoformat(),
        'tilaajavastuu_status': '100 STOP',
        'interested_org_id': 'updated-interested-org-id',
        'pdf': {
            'content_type': 'application/json',
            'body': json.dumps({'updated-test': 'updated test report'})
        },
        'charge_reference': '32423423',
        'used_providers': ['creditsafe_connect', 'creditsafe_v2']
    }
    resp = app.put_json(
        API_URL + 'reports/%s' % status_report['external_id'],
        updated_payload,
    )
    assert 200 == resp.status_code
    updated_status_report = resp.json
    expected = {
        'id': status_report['external_id'],
        'tilaajavastuu_status': '100 STOP',
        'generated_timestamp': '2012-01-19T00:00:00',
        'interested_org_id': 'updated-interested-org-id',
        'org': 'updated-test-org-id',
        'report_type': 'bolagsfakta.company_report',
        'type': 'report',
        'charge_reference': '32423423',
        'used_providers': ['creditsafe_connect', 'creditsafe_v2'],
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert updated_status_report == expected


@freeze_time("2012-01-14")
def test_put_status_report_invalid_provider(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
        json_={'test': 'test'},
    )
    updated_payload = {
        'type': 'report',
        'org': 'updated-test-org-id',
        'report_type': 'bolagsfakta.company_report',
        'generated_timestamp': (datetime.datetime.now() + datetime.timedelta(5)).isoformat(),
        'tilaajavastuu_status': '100 STOP',
        'interested_org_id': 'updated-interested-org-id',
        'pdf': {
            'content_type': 'application/json',
            'body': json.dumps({'updated-test': 'updated test report'})
        },
        'charge_reference': '32423423',
        'used_providers': ['invalid_provider', 'creditsafe_v2']
    }
    resp = app.put_json(
        API_URL + 'reports/%s' % status_report['external_id'],
        updated_payload,
        expect_errors=True
    )
    assert 400 == resp.status_code
    updated_status_report = resp.json
    assert updated_status_report['error_code'] == 'ParameterValidationFailed'
    assert updated_status_report['error'] == {
        'used_providers': ["unallowed values ['invalid_provider']"]
    }


def test_put_status_report_sub_resources_ok(app, db):
    status_report = factories.create_status_report(db)
    resp = app.put_json(
        API_URL + 'reports/%s/sync' % status_report['external_id'],
        {},
    )
    assert 200 == resp.status_code
    updated_sub_resource = resp.json
    assert updated_sub_resource == {'revision': None}


@freeze_time("2012-01-14")
def test_put_notification_report_ok(app, db):
    notification_report = factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'test'},
    )
    updated_payload = {
        'generated_timestamp': (datetime.datetime.now() + datetime.timedelta(5)).isoformat(),
        'pdf': {
            'content_type': 'application/json',
            'body': json.dumps({'updated-test': 'updated test report'})
        }
    }
    resp = app.put_json(
        API_URL + 'reports/%s' % notification_report['external_id'],
        updated_payload,
    )
    assert 200 == resp.status_code
    updated_notification_report = resp.json
    expected = {
        'id': notification_report['external_id'],
        'tilaajavastuu_status': None,
        'generated_timestamp': '2012-01-19T00:00:00',
        'interested_org_id': None,
        'org': None,
        'report_type': 'bolagsfakta.status_change_report',
        'type': 'report',
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert updated_notification_report == expected


# test how imported report (having qvarn_id) is updated
@freeze_time("2012-01-14")
def test_put_notification_report_imported_ok(app, db):
    notification_report = factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'test'},
    )
    updated_payload = {
        'generated_timestamp': (datetime.datetime.now() + datetime.timedelta(5)).isoformat(),
        'pdf': {
            'content_type': 'application/json',
            'body': json.dumps({'updated-test': 'updated test report'})
        }
    }
    resp = app.put_json(
        API_URL + 'reports/%s' % notification_report['external_id'],
        updated_payload,
    )
    assert 200 == resp.status_code
    updated_notification_report = resp.json
    expected = {
        'id': notification_report['external_id'],
        'tilaajavastuu_status': None,
        'generated_timestamp': '2012-01-19T00:00:00',
        'interested_org_id': None,
        'org': None,
        'report_type': 'bolagsfakta.status_change_report',
        'type': 'report',
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert updated_notification_report == expected


@freeze_time("2012-01-14")
def test_put_notification_report_multiple_ok(app, db):
    external_id = 'test-qvarn-id'
    factories.create_notification_report(
        db,
        external_id=external_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )
    factories.create_notification_report(
        db,
        external_id=external_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )
    factories.create_notification_report(
        db,
        external_id=external_id,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report={'test': 'qvarn report'},
    )

    updated_payload = {
        'generated_timestamp': (datetime.datetime.now() + datetime.timedelta(5)).isoformat(),
        'pdf': {
            'content_type': 'application/json',
            'body': json.dumps({'updated-test': 'updated test report'})
        }
    }

    resp = app.put_json(API_URL + 'reports/%s' % external_id, updated_payload)

    assert 200 == resp.status_code
    tbl_notification_reports = db.meta.tables[NOTIFICATION_REPORTS_TABLE]
    qry = (
        sa.select([tbl_notification_reports])
        .where(tbl_notification_reports.c.external_id == external_id)
    )
    rez = db.session.execute(qry)
    updated_notification_reports = rez.fetchall()
    assert len(updated_notification_reports) == 3
    for report in updated_notification_reports:
        assert report[tbl_notification_reports.c.qvarn_report] == \
            json.dumps({'updated-test': 'updated test report'})
        assert report[tbl_notification_reports.c.generated_timestamp] == \
            datetime.datetime(2012, 1, 19, 0, 0)


@freeze_time("2020-12-12")
def test_put_status_report_pdf_ok(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
    )
    assert not status_report['json_']

    pdf_payload = {
        'clreports_interpretation': 'stop',
    }
    resp = app.put_json(
        API_URL + f"reports/{status_report['external_id']}/pdf",
        pdf_payload,
    )

    assert 200 == resp.status_code
    updated_status_report = resp.json
    expected = {
        'id': status_report['id'],
        'external_id': status_report['external_id'],
        'tilaajavastuu_status': '500 OK',
        'generated_timestamp': '2020-12-12T00:00:00',
        'interested_org_id': 'test-interested-company-id',
        'org': 'test-company-id',
        'pdf': {'clreports_interpretation': 'stop'},
        'report_type': 'bolagsfakta.company_report',
        'type': 'report',
        'charge_reference': None,
        'used_providers': None,
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    assert updated_status_report == expected


def test_put_status_report_pdf_validation_errors(app, db):
    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
    )

    resp = app.put_json(
        API_URL + f"reports/{status_report['external_id']}/pdf",
        None,
        expect_errors=True,
    )

    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_put_status_report_pdf_validation_schema_used(app, db, mocker):
    # this test is common for notification_reports also
    validator_mock = mocker.patch(
        'boldataapi.controllers.reports_controller.ReportValidator')

    status_report = factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id='test-interested-company-id',
        company_id='test-company-id',
    )

    app.put_json(
        API_URL + f"reports/{status_report['external_id']}/pdf",
        None,
        expect_errors=True,
    )

    validator_mock.assert_called_with(schema_reports_pdf)


def test_put_report_pdf_not_found(app):
    pdf_payload = {
        'clreports_interpretation': 'investigate',
    }
    resp = app.put_json(
        API_URL + 'reports/non-existing/pdf',
        pdf_payload,
        expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'non-existing'}
    }

    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_put_notification_report_pdf_ok(app, db):
    notification_report = factories.create_notification_report(db)
    assert not notification_report['qvarn_report']

    pdf_payload = {
        'clreports_interpretation': 'investigate',
    }
    resp = app.put_json(
        API_URL + f"reports/{notification_report['external_id']}/pdf",
        pdf_payload,
    )

    assert 200 == resp.status_code
    updated_report = factories.get_notification_report(db, notification_report['id'])
    assert updated_report['qvarn_report'] == pdf_payload
    assert resp.json['revision'] is None


def test_put_notification_report_pdf_large(app, db):
    notification_report = factories.create_notification_report(db)
    assert not notification_report['qvarn_report']

    pdf_payload = {
        'clreports_interpretation': 'investigate',
        'big_data': 'A' * 200 * 1024,
    }
    resp = app.put_json(
        API_URL + f"reports/{notification_report['external_id']}/pdf",
        pdf_payload,
    )

    assert 200 == resp.status_code
    updated_report = factories.get_notification_report(db, notification_report['id'])
    assert updated_report['qvarn_report'] == pdf_payload
    assert resp.json['revision'] is None


# SEARCH tests


def get_reports_search(search_url, app):
    resp = app.get(API_URL + f'reports/search/{search_url}')
    assert resp.content_type == 'application/json'
    assert 200 == resp.status_code
    result = json.loads(resp.body.decode('utf-8'))
    return result['resources']


def test_reports_search_id_status_reports(config, app, db):
    report = factories.create_status_report(
        db,
    )

    search_url = f"exact/id/{report['external_id']}"
    resp = app.get(API_URL + f'reports/search/{search_url}')
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [
            {'id': report['external_id']}
        ],
    }


def test_reports_search_id_notification_reports(config, app, db):
    report = factories.create_notification_report(
        db,
    )

    search_url = f"exact/id/{report['external_id']}"
    resp = app.get(API_URL + f'reports/search/{search_url}')
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [
            {'id': report['external_id']}
        ],
    }


@pytest.mark.parametrize("q_field, field", [
    ('tilaajavastuu_status', 'status'),
    ('org', 'company_id'),
    ('interested_org_id', 'interested_company_id'),
])
def test_reports_search_exact_fields_status_reports(app, db, q_field, field):
    report = factories.create_status_report(
        db,
        status='100 STOP',
        generated_timestamp=datetime.datetime.now(),
        company_id='test-company-id',
        interested_company_id='test-company-id',
    )
    search_for = report[field]
    search_url = f'exact/{q_field}/{search_for}'
    result = get_reports_search(search_url, app)
    assert {'id': report['external_id']} in result


@pytest.mark.parametrize("report_create_func", [
    factories.create_status_report,
    factories.create_notification_report,
])
def test_reports_search_generated_timestamp(app, db, report_create_func):
    report = report_create_func(
        db,
        generated_timestamp=datetime.datetime.now(),
    )
    search_for = report['generated_timestamp'].strftime('%Y-%m-%dT%H:%M:%S.%f')
    search_url = f'exact/generated_timestamp/{search_for}'
    result = get_reports_search(search_url, app)
    assert {'id': report['external_id']} in result


@pytest.mark.parametrize("report_create_func, report_type", [
    (factories.create_status_report, 'bolagsfakta.company_report'),
    (factories.create_notification_report, 'bolagsfakta.status_change_report'),
])
def test_reports_search_report_type(app, db, report_create_func, report_type):
    report = report_create_func(db,)
    search_url = f'exact/report_type/{report_type}'
    result = get_reports_search(search_url, app)
    assert {'id': report['external_id']} in result


def test_reports_search_not_found(app):
    search_url = 'exact/id/non-such'
    resp = app.get(API_URL + f"reports/search/{search_url}")
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [],
    }


@freeze_time("2012-01-14")
def test_reports_search_show_all(config, app, db):
    # Deprecated: no calls are made with /show_all/ notation
    report = factories.create_status_report(
        db,
        status='100 STOP',
        generated_timestamp=datetime.datetime.now(),
        company_id='test-company-id',
        interested_company_id='test-company-id',
    )

    search_url = f"show_all/exact/id/{report['external_id']}"
    result = get_reports_search(search_url, app)
    expected = [
        {
            'generated_timestamp': '2012-01-14T00:00:00.000000',
            'id': report['external_id'],
            'interested_org_id': 'test-company-id',
            'org': 'test-company-id',
            'report_type': 'bolagsfakta.company_report',
            'revision': None,
            'status': None,
            'tilaajavastuu_status': '100 STOP',
            'type': 'report',
            'archive_code': None,
            'certificates': [],
            'company_information': [],
            'company_relations': [],
            'interpretation': None,
            'notes': [],
            'oldest_source_date': None,
            'operating_licenses': [],
            'ratings': [],
            'registry_information': [],
            'registry_memberships': [],
            'report_version': None,
            'reported_information': [],
            'service_provider': None,
            'valid_from_date': None,
            'valid_until_date': None,
        }
    ]
    assert expected == result


# DELETE tests


@pytest.mark.parametrize("factory_create_fun, factory_get_fun", [
    (factories.create_status_report, factories.get_status_report),
    (factories.create_notification_report, factories.get_notification_report),
])
def test_delete_reports(app, db, factory_create_fun, factory_get_fun):
    report = factory_create_fun(db)
    control_report = factory_create_fun(db)
    resp = app.delete(API_URL + f"reports/{report['external_id']}")
    assert 200 == resp.status_code
    assert not factory_get_fun(db, report['id'])
    assert factory_get_fun(db, control_report['id'])


def test_delete_report_not_found(app):
    resp = app.delete(API_URL + 'reports/non-existing', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {
            'external_id': 'non-existing',
        }
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_get_reports_pagination(app, db):
    """Test pagination functionality of get_reports endpoint."""
    # Create test data
    status_report1 = {
        'external_id': 'test-id-1',
        'status': '100 STOP',
        'generated_timestamp': datetime.datetime(2019, 1, 1),
        'interested_company_id': 'test-interested-company-id',
        'company_id': 'test-company-id',
        'charge_reference': '11432153',
        'used_providers': ['provider01'],
        'json_': {'test': 'test1'},
    }
    status_report2 = {
        'external_id': 'test-id-2',
        'status': '200 INVESTIGATE',
        'generated_timestamp': datetime.datetime(2019, 1, 2),
        'interested_company_id': 'test-interested-company-id',
        'company_id': 'test-company-id',
        'charge_reference': '21432153',
        'used_providers': ['provider02'],
        'json_': {'test': 'test2'},
    }
    status_report3 = {
        'external_id': 'test-id-3',
        'status': '300 INCOMPLETE',
        'generated_timestamp': datetime.datetime(2019, 1, 3),
        'interested_company_id': 'test-interested-company-id',
        'company_id': 'test-other-company-id',
        'charge_reference': '31432153',
        'used_providers': ['provider03'],
        'json_': {'test': 'test3'},
    }

    for report in [status_report1, status_report2, status_report3]:
        db.session.execute(
            db.meta.tables['status_reports'].insert(),
            report
        )

    # Commit the transaction
    db.session.commit()

    # Test case 1: First page with limit
    response = app.get(API_URL + 'reports', params={'limit': '1'})
    assert response.status_code == 200
    result = response.json
    assert len(result['reports']) == 1
    assert result['reports'][0]['id'] == 'test-id-1'
    assert result['pagination']['limit'] == 1
    assert result['pagination']['has_more'] is True
    assert result['pagination']['next_cursor'] is not None

    # Test case 2: Next page using cursor
    next_cursor = result['pagination']['next_cursor']
    response = app.get(API_URL + 'reports', params={'limit': '1', 'cursor': next_cursor})
    assert response.status_code == 200
    result = response.json
    assert len(result['reports']) == 1
    assert result['reports'][0]['id'] == 'test-id-2'
    assert result['pagination']['limit'] == 1
    assert result['pagination']['has_more'] is True

    # Get the third page
    next_cursor = result['pagination']['next_cursor']
    response = app.get(API_URL + 'reports', params={'limit': '1', 'cursor': next_cursor})
    assert response.status_code == 200
    result = response.json
    assert len(result['reports']) == 1
    assert result['reports'][0]['id'] == 'test-id-3'
    assert result['pagination']['limit'] == 1
    assert result['pagination']['has_more'] is False


def test_get_reports_pagination_large_volume(app, db):
    """Test pagination of reports endpoint with large volume of data from both tables."""
    # Constants for test
    total_current_reports = 50
    total_historic_reports = 50
    page_sizes = [10, 25, 100]  # Different page sizes to test

    # Helper function to generate dynamic test data
    def create_report_data(prefix, index, day_offset):
        return {
            'external_id': f"{prefix}-id-{index:03d}",
            # Format: current-id-001, historic-id-001, etc.
            'status': [
                '100 STOP', '200 INVESTIGATE', '300 INCOMPLETE', '400 ATTENTION', '500 OK'
            ][index % 5],
            'generated_timestamp': datetime.datetime(2019, 1, 1) + datetime.timedelta(
                days=day_offset),
            'interested_company_id': f"interested-company-{index % 5}",
            'company_id': f"company-{index % 10}",
            'charge_reference': f"{index}432153",
            'used_providers': [f"provider{index % 4:02d}"],
            'json_': {'test': f"test-{prefix}-{index}"},
        }

    # Create test data in status_reports table
    current_reports = []
    for i in range(1, total_current_reports + 1):
        report = create_report_data("current", i, i)
        current_reports.append(report)
        db.session.execute(
            db.meta.tables['status_reports'].insert(),
            report
        )

    # Create test data in status_reports_history table
    historic_reports = []
    for i in range(1, total_historic_reports + 1):
        report = create_report_data("historic", i, i + 100)  # Different date range
        historic_reports.append(report)
        # Remove json_ for history table
        report_copy = report.copy()
        report_copy.pop('json_')
        db.session.execute(
            db.meta.tables['status_reports_history'].insert(),
            report_copy
        )

    # Commit the transaction
    db.session.commit()

    # Combined and sorted list of all external_ids for validation
    all_external_ids = sorted([r['external_id'] for r in current_reports + historic_reports])
    total_reports = len(all_external_ids)

    # Test different page sizes
    for page_size in page_sizes:
        # Calculate expected number of pages
        expected_pages = (total_reports + page_size - 1) // page_size

        # Track all received reports to ensure completeness
        received_reports = []

        # First page (no cursor)
        response = app.get(API_URL + 'reports', params={'limit': str(page_size)})
        assert response.status_code == 200
        result = response.json

        # Verify first page
        assert len(result['reports']) <= page_size
        assert result['pagination']['limit'] == page_size
        assert result['pagination']['has_more'] == (len(result['reports']) < total_reports)

        # Store received reports
        received_reports.extend(result['reports'])

        # Page through all results
        page_count = 1
        cursor = result['pagination']['next_cursor']

        while cursor:
            response = app.get(API_URL + 'reports', params={
                'limit': str(page_size),
                'cursor': cursor
            })
            assert response.status_code == 200
            result = response.json

            received_reports.extend(result['reports'])
            cursor = result['pagination']['next_cursor']
            page_count += 1

            # Verify we haven't gone over the expected number of pages
            assert page_count <= expected_pages

        # Verify total number of records received
        assert len(received_reports) == total_reports

        # Verify ordering is correct (by external_id)
        received_ids = [r['id'] for r in received_reports]
        assert received_ids == all_external_ids

        # Verify no duplicates
        assert len(set(received_ids)) == len(received_ids)

    # Test with no limit (should return all records)
    response = app.get(API_URL + 'reports')
    assert response.status_code == 200
    result = response.json
    assert len(result['reports']) == total_reports
    assert result['pagination']['limit'] is None
    assert result['pagination']['has_more'] is False
    assert result['pagination']['next_cursor'] is None

    # Test with filter on company_id
    company_id = "company-1"
    expected_count = sum(
        1 for r in current_reports + historic_reports if r['company_id'] == company_id)

    response = app.get(API_URL + 'reports', params={'company_ids': company_id})
    assert response.status_code == 200
    result = response.json
    assert len(result['reports']) == expected_count

    # Test with filter on interested_company_id
    interested_company_id = "interested-company-2"
    expected_count = sum(1 for r in current_reports + historic_reports
                         if r['interested_company_id'] == interested_company_id)

    response = app.get(API_URL + 'reports', params={'interested_company_id': interested_company_id})
    assert response.status_code == 200
    result = response.json
    assert len(result['reports']) == expected_count

    # Test with date range filter
    start_date = "2019-01-10"
    end_date = "2019-01-20"
    expected_count = sum(1 for r in current_reports + historic_reports
                         if datetime.datetime(2019, 1, 10) <= r['generated_timestamp'] <=
                         datetime.datetime(2019, 1, 20))
    assert expected_count > 0

    response = app.get(API_URL + 'reports',
                       params={'start_date': start_date, 'end_date': end_date})
    assert response.status_code == 200
    result = response.json
    assert len(result['reports']) == expected_count


def test_get_reports_boundary_cursor(app, db):
    """
    Test pagination when the cursor falls exactly between current and historical tables.
    This verifies the UNION query correctly handles transitions between data sources.
    """
    # Create current table reports with lower external_ids
    current_reports = []
    for i in range(1, 6):  # 5 reports in current table
        report = {
            'external_id': f"current-{i:03d}",  # These will sort before history reports
            'status': '500 OK',
            'generated_timestamp': datetime.datetime(2020, 1, i),
            'interested_company_id': 'test-interested-company',
            'company_id': 'test-company',
            'charge_reference': f"ref-{i}",
            'used_providers': ['provider1'],
            'json_': {'test': f"current-{i}"},
        }
        current_reports.append(report)
        db.session.execute(
            db.meta.tables['status_reports'].insert(),
            report
        )

    # Create history table reports with higher external_ids
    history_reports = []
    for i in range(1, 6):  # 5 reports in history table
        report = {
            'external_id': f"history-{i:03d}",  # These will sort after current reports
            'status': '300 INCOMPLETE',
            'generated_timestamp': datetime.datetime(2019, 1, i),
            'interested_company_id': 'test-interested-company',
            'company_id': 'test-company',
            'charge_reference': f"ref-{i+5}",
            'used_providers': ['provider2'],
        }
        history_reports.append(report)
        db.session.execute(
            db.meta.tables['status_reports_history'].insert(),
            report
        )

    db.session.commit()

    # Get first page with limit=5 (should return all current reports)
    response = app.get(API_URL + 'reports', params={'limit': '5'})
    assert response.status_code == 200
    result = response.json

    # Verify we got exactly 5 results
    assert len(result['reports']) == 5
    # Verify all reports are from current table
    assert all(r['id'].startswith('current-') for r in result['reports'])
    # Verify has_more is True
    assert result['pagination']['has_more'] is True
    # Get the cursor for next page
    cursor = result['pagination']['next_cursor']

    # Get second page with cursor
    response = app.get(API_URL + 'reports', params={'limit': '5', 'cursor': cursor})
    assert response.status_code == 200
    result = response.json

    # Verify we got exactly 5 results
    assert len(result['reports']) == 5
    # Verify all reports are from history table
    assert all(r['id'].startswith('history-') for r in result['reports'])
    # Verify has_more is False (no more results)
    assert result['pagination']['has_more'] is False

    # Verify the cursor transition worked correctly
    assert cursor > 'current-' and cursor < 'history-'


def test_get_reports_single_table(app, db):
    """
    Test when results come from only one table.
    This verifies the UNION query works correctly even when one subquery returns no results.
    """
    # Clear existing data
    db.session.execute(db.meta.tables['status_reports'].delete())
    db.session.execute(db.meta.tables['status_reports_history'].delete())

    # 1. Test with only current table data
    current_only = {
        'external_id': 'current-only',
        'status': '500 OK',
        'generated_timestamp': datetime.datetime(2020, 1, 1),
        'interested_company_id': 'test-company',
        'company_id': 'test-company',
        'charge_reference': 'ref-current',
        'used_providers': ['provider1'],
        'json_': {'test': 'current-only'},
    }
    db.session.execute(db.meta.tables['status_reports'].insert(), current_only)
    db.session.commit()

    # Query reports
    response = app.get(API_URL + 'reports')
    assert response.status_code == 200
    result = response.json

    # Verify we got only the current table record
    assert len(result['reports']) == 1
    assert result['reports'][0]['id'] == 'current-only'
    assert result['reports'][0]['json'] == {'test': 'current-only'}

    # Clean up
    db.session.execute(db.meta.tables['status_reports'].delete())
    db.session.commit()

    # 2. Test with only history table data
    history_only = {
        'external_id': 'history-only',
        'status': '300 INCOMPLETE',
        'generated_timestamp': datetime.datetime(2019, 1, 1),
        'interested_company_id': 'test-company',
        'company_id': 'test-company',
        'charge_reference': 'ref-history',
        'used_providers': ['provider2'],
    }
    db.session.execute(db.meta.tables['status_reports_history'].insert(), history_only)
    db.session.commit()

    # Query reports
    response = app.get(API_URL + 'reports')
    assert response.status_code == 200
    result = response.json

    # Verify we got only the history table record
    assert len(result['reports']) == 1
    assert result['reports'][0]['id'] == 'history-only'
    assert result['reports'][0]['json'] == {}  # Empty JSON for history record


def test_get_reports_exact_limit(app, db):
    """
    Test when limit exactly matches the number of available results.
    This ensures the has_more flag is correctly set to False in this case.
    """
    # Clear existing data
    db.session.execute(db.meta.tables['status_reports'].delete())
    db.session.execute(db.meta.tables['status_reports_history'].delete())

    # Create exactly 10 reports (5 in each table)
    for i in range(1, 6):
        # Current table
        current_report = {
            'external_id': f"exact-current-{i}",
            'status': '500 OK',
            'generated_timestamp': datetime.datetime(2020, 1, i),
            'interested_company_id': 'test-company',
            'company_id': 'test-company',
            'charge_reference': f"ref-current-{i}",
            'used_providers': ['provider1'],
            'json_': {'test': f"current-{i}"},
        }
        db.session.execute(db.meta.tables['status_reports'].insert(), current_report)

        # History table
        history_report = {
            'external_id': f"exact-history-{i}",
            'status': '300 INCOMPLETE',
            'generated_timestamp': datetime.datetime(2019, 1, i),
            'interested_company_id': 'test-company',
            'company_id': 'test-company',
            'charge_reference': f"ref-history-{i}",
            'used_providers': ['provider2'],
        }
        db.session.execute(db.meta.tables['status_reports_history'].insert(), history_report)

    db.session.commit()

    # Query with exactly the right limit
    response = app.get(API_URL + 'reports', params={'limit': '10'})
    assert response.status_code == 200
    result = response.json

    # Verify we got exactly 10 results
    assert len(result['reports']) == 10
    # Verify has_more is False since we got exactly the limit
    assert result['pagination']['has_more'] is False
    assert result['pagination']['next_cursor'] is None

    # Query with smaller limit to verify has_more=True works
    response = app.get(API_URL + 'reports', params={'limit': '5'})
    assert response.status_code == 200
    result = response.json

    # Verify we got exactly 5 results
    assert len(result['reports']) == 5
    # Verify has_more is True since we have more results
    assert result['pagination']['has_more'] is True
    assert result['pagination']['next_cursor'] is not None


def test_get_reports_data_types(app, db):
    """
    Test that complex data types (like JSON) are correctly handled in the UNION query.
    This verifies proper type casting between tables.
    """
    # Clear existing data
    db.session.execute(db.meta.tables['status_reports'].delete())
    db.session.execute(db.meta.tables['status_reports_history'].delete())

    # Create a current report with complex JSON
    complex_json = {
        'nested': {
            'array': [1, 2, 3],
            'string': 'test',
            'boolean': True,
            'null': None
        },
        'array_with_objects': [
            {'id': 1, 'name': 'item1'},
            {'id': 2, 'name': 'item2'}
        ]
    }

    current_report = {
        'external_id': 'complex-json-current',
        'status': '500 OK',
        'generated_timestamp': datetime.datetime(2020, 1, 1),
        'interested_company_id': 'test-company',
        'company_id': 'test-company',
        'charge_reference': 'ref-complex',
        'used_providers': ['provider1', 'provider2'],  # Test array type
        'json_': complex_json,
    }
    db.session.execute(db.meta.tables['status_reports'].insert(), current_report)

    # Create a history report
    history_report = {
        'external_id': 'complex-types-history',
        'status': '300 INCOMPLETE',
        'generated_timestamp': datetime.datetime(2019, 1, 1),
        'interested_company_id': 'test-company',
        'company_id': 'test-company',
        'charge_reference': 'ref-history',
        'used_providers': ['provider3', 'provider4', 'provider5'],  # Different array length
    }
    db.session.execute(db.meta.tables['status_reports_history'].insert(), history_report)

    db.session.commit()

    # Query reports
    response = app.get(API_URL + 'reports')
    assert response.status_code == 200
    result = response.json

    # Find the current report in results
    current_result = next(r for r in result['reports'] if r['id'] == 'complex-json-current')

    # Verify complex JSON is preserved correctly
    assert current_result['json'] == complex_json
    assert current_result['json']['nested']['array'] == [1, 2, 3]
    assert len(current_result['json']['array_with_objects']) == 2

    # Find the history report in results
    history_result = next(r for r in result['reports'] if r['id'] == 'complex-types-history')

    # Verify empty JSON for history and array types are preserved
    assert history_result['json'] == {}
    assert len(history_result['used_providers']) == 3
