import datetime
import json
import operator

import pytest
from freezegun import freeze_time

from boldataapi.fixtures import factories
from boldataapi.schema import (
    schema_existing_report_access,
    schema_new_report_access,
)


API_URL = '/api/v1/boldata/'


# GET tests


def test_get_not_found(app):
    resp = app.get(API_URL + 'report_accesses/not-existing', expect_errors=True)

    expected = {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {'external_id': 'not-existing'},
    }
    assert resp.json == expected
    assert resp.status_code == 404


@freeze_time("2012-01-14")
def test_get_returns_ok_status(app, db):
    report_access = (
        factories.create_report_access(db,
                                       customer_company_id='test-customer-id',
                                       company_id='test-company_id',
                                       person_id='test-person-id',
                                       status='active',
                                       access_time=datetime.datetime.now(),
                                       arkisto_id='test-arkisto_id',
                                       language='FI',
                                       report_id='test-report-id',
                                       company_gov_id='test-company-gov-id',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       template_version='1.0',
                                       )
    )
    resp = app.get(API_URL + 'report_accesses/%s' % report_access['external_id'])
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    new_report_access = json.loads(resp.body)
    assert new_report_access == {
        'id': report_access['external_id'],
        'access_time': '2012-01-14T00:00:00+00:00',
        'arkisto_id': 'test-arkisto_id',
        'report_id': 'test-report-id',
        'status': 'active',
        'language': 'FI',
        'customer_org_id': 'test-customer-id',
        'org_id': 'test-company_id',
        'gov_org_ids': [
            {
                'country': 'FI',
                'gov_org_id': 'test-company-gov-id',
                'org_id_type': 'registration_number',
            }
        ],
        'person_id': 'test-person-id',
        'client_id': None,
        'type': 'report_access',
        'revision': None,
        'template_version': '1.0',
    }


def test_get_server_error(app, mocker, caplog):
    mocker.patch(
        'boldataapi.controllers.report_accesses_controller.get_report_access',
        side_effect=Exception('Serious system problem occurred!'))

    resp = app.get(API_URL + 'report_accesses/any-id', expect_errors=True)

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500

    assert 'Serious system problem occurred' in caplog.text


# POST tests


def test_post_returns_created_status(app):
    payload = {
        'access_time': '2012-01-14T00:00:00+00:00',
        'arkisto_id': 'test-arkisto_id',
        'report_id': 'test-report-id',
        'status': 'active',
        'language': 'EE',
        'customer_org_id': 'test-customer-id',
        'org_id': 'test-company_id',
        'gov_org_ids': [
            {
                'country': 'FI',
                'gov_org_id': 'test-company-gov-id',
                'org_id_type': 'registration_number',
            }
        ],
        'person_id': 'test-person-id',
        'template_version': '1.0',
    }

    resp = app.post_json(API_URL + 'report_accesses', payload)
    assert 201 == resp.status_code
    new_report_access = json.loads(resp.body)
    new_report_access.pop('id')
    new_report_access.pop('revision')
    payload['type'] = 'report_access'
    payload['client_id'] = None
    assert new_report_access == payload


def test_post_server_error(app, mocker, caplog):
    mocker.patch(
        'boldataapi.controllers.report_accesses_controller.create_report_access',
        side_effect=Exception('Serious system problem occurred!'))
    gov_org_ids = [
        {
            'gov_org_id': 'test',
            'country': 'FI',
            'org_id_type': 'registration_number',
        }
    ]
    payload = {
        'org_id': 'any-org-id',
        'customer_org_id': 'any-id',
        'status': 'active',
        'access_time': '2012-01-14T00:00:00',
        'arkisto_id': 'test-arkisto-id',
        'gov_org_ids': gov_org_ids,
    }

    resp = app.post_json(API_URL + 'report_accesses', payload, expect_errors=True)

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500

    assert 'Serious system problem occurred' in caplog.text


def test_post_validation_schema_used(app, db, mocker):
    validator_mock = mocker.patch(
        'boldataapi.controllers.report_accesses_controller.ReportAccessValidator')

    app.post_json(API_URL + 'report_accesses', {}, expect_errors=True)
    validator_mock.assert_called_with(schema_new_report_access)


def test_post_validation_error(app):
    resp = app.post_json(API_URL + 'report_accesses', {}, expect_errors=True)

    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


# SEARCH tests


def get_report_accesses_search(search_url, app):
    resp = app.get(API_URL + 'report_accesses/search/{}'.format(search_url))
    assert resp.content_type == 'application/json'
    assert 200 == resp.status_code
    result = json.loads(resp.body.decode('utf-8'))
    return result['resources']


def test_report_accesses_search_id(config, app, db):
    report_access = (
        factories.create_report_access(
            db,
            customer_company_id='test-customer-id',
            company_id='test-company_id',
            person_id='test-person-id',
            status='active',
            access_time=datetime.datetime.now(),
            arkisto_id='test-arkisto_id',
            report_id='test-report-id',
            company_gov_id='test-company-gov-id',
            company_gov_id_country='FI',
            company_gov_id_type='registration_number',
        )
    )

    search_url = 'exact/id/{}'.format(report_access['external_id'])
    resp = app.get(API_URL + 'report_accesses/search/{}'.format(search_url))
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [
            {'id': report_access['external_id']}
        ],
    }


@pytest.mark.parametrize(('q_field', 'field'),
                         [('customer_org_id', 'customer_company_id'),
                          ('person_id', 'person_id'),
                          ('status', 'status'),
                          ('arkisto_id', 'arkisto_id'),
                          ('report_id', 'report_id')])
def test_report_accesses_search_exact_basic_fields_mapp(config, app, db, q_field, field):
    """Search finds report_access based on field values."""
    report_access = (
        factories.create_report_access(
            db,
            customer_company_id='test-customer-id',
            company_id='test-company_id',
            person_id='test-person-id',
            status='active',
            access_time=datetime.datetime.now(),
            arkisto_id='test-arkisto_id',
            report_id='test-report-id',
            company_gov_id='test-company-gov-id',
            company_gov_id_country='FI',
            company_gov_id_type='registration_number',
        )
    )
    search_url = 'exact/{q_field}/{search_for}'.format(q_field=q_field,
                                                       search_for=(report_access[field]))
    result = get_report_accesses_search(search_url, app)
    assert {'id': report_access['external_id']} in result


def test_report_accesses_search_exact_access_time_mapp(config, app, db):
    """Search finds report_access based on field values."""
    timestamp = datetime.datetime.now(datetime.timezone.utc).replace(microsecond=0).isoformat()
    report_access = (
        factories.create_report_access(
            db,
            customer_company_id='test-customer-id',
            company_id='test-company_id',
            person_id='test-person-id',
            access_time=timestamp
        )
    )
    search_url = 'exact/{q_field}/{search_for}'.format(q_field='access_time',
                                                       search_for=timestamp)
    result = get_report_accesses_search(search_url, app)
    assert {'id': report_access['external_id']} in result


@pytest.mark.parametrize(('q_field', 'search_for', 'found'),
                         [('gov_org_id', 'test-company-gov-id', True),
                          ('gov_org_id', 'non-such-gov-id', False),
                          ('country', 'FI', True),
                          ('country', 'SE', False),
                          ('org_id_type', 'registration_number', True),
                          ('org_id_type', 'f-tax', False),
                          ('language', 'EN', True),
                          ('language', 'SV', False)])
def test_report_accesses_search_exact_subresource(config, app, db, q_field, search_for, found):
    """Search finds supplier based on field values in sub_resources."""
    report_access = (
        factories.create_report_access(
            db,
            customer_company_id='test-customer-id',
            company_id='test-company_id',
            person_id='test-person-id',
            company_gov_id='test-company-gov-id',
            company_gov_id_country='FI',
            company_gov_id_type='registration_number',
            language='EN',
        )
    )

    search_url = 'exact/{q_field}/{search_for}'.format(q_field=q_field,
                                                       search_for=search_for)
    result = get_report_accesses_search(search_url, app)
    assert ([{'id': report_access['external_id']}] == result) == found


def test_bol_supppliers_search_show_all(config, app, db):
    # Deprecated: no calls are made with /show_all/ notation
    report_access = (
        factories.create_report_access(
            db,
            customer_company_id='test-customer-id',
            company_id='test-company_id',
            person_id='test-person-id',
            status='active',
            access_time=datetime.datetime.now(),
            arkisto_id='test-arkisto_id',
            report_id='test-report-id',
            company_gov_id='test-company-gov-id',
            company_gov_id_country='FI',
            company_gov_id_type='registration_number',
            language='EN',
            template_version='1.0',
        )
    )

    show_url = 'show_all'
    report_access_id = report_access['external_id']
    search_url = '{}/exact/id/{}'.format(show_url, report_access_id)
    result = get_report_accesses_search(search_url, app)

    assert [
        {
            'id': report_access['external_id'],
            'access_time': report_access['access_time'].replace(microsecond=0).isoformat(),
            'arkisto_id': 'test-arkisto_id',
            'report_id': 'test-report-id',
            'status': 'active',
            'customer_org_id': 'test-customer-id',
            'org_id': 'test-company_id',
            'gov_org_ids': [
                {
                    'country': 'FI',
                    'gov_org_id': 'test-company-gov-id',
                    'org_id_type': 'registration_number',
                }
            ],
            'person_id': 'test-person-id',
            'client_id': None,
            'revision': None,
            'type': 'report_access',
            'language': 'EN',
            'template_version': '1.0',
        }
    ] == result


def test_report_accesses_search_not_found(app):
    search_url = 'exact/id/non-such'
    resp = app.get(API_URL + 'report_accesses/search/{}'.format(search_url))
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [],
    }


# PUT tests

@freeze_time("2012-01-14")
def test_put_returns_ok_status(app, db):
    report_access = (
        factories.create_report_access(db,
                                       customer_company_id='test-customer-id',
                                       company_id='test-company_id',
                                       person_id='test-person-id',
                                       status='active',
                                       access_time=datetime.datetime.now(),
                                       arkisto_id='test-arkisto_id',
                                       report_id='test-report-id',
                                       company_gov_id='test-company-gov-id',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )

    payload = {
        'access_time': '2013-01-14T00:00:00',
        'arkisto_id': 'updated-arkisto_id',
        'report_id': 'updated-test-report-id',
        'status': 'hidden',
        'revision': None,
        'customer_org_id': 'updated-test-customer-id',
        'org_id': 'updated-test-company_id',
        'gov_org_ids': [
            {
                'country': 'EE',
                'gov_org_id': 'updated-gov-id',
                'org_id_type': 'registration_number',
            }
        ],
        'person_id': 'updated-person-id',
    }

    resp = app.put_json(API_URL + 'report_accesses/%s' % report_access['external_id'], payload)
    assert 200 == resp.status_code
    updated_report_access = resp.json

    assert updated_report_access == {
        'id': report_access['external_id'],
        'access_time': '2013-01-14T00:00:00+00:00',
        'arkisto_id': 'updated-arkisto_id',
        'report_id': 'updated-test-report-id',
        'status': 'hidden',
        'language': None,
        'customer_org_id': 'updated-test-customer-id',
        'org_id': 'updated-test-company_id',
        'person_id': 'updated-person-id',
        'gov_org_ids': [
            {
                'country': 'EE',
                'org_id_type': 'registration_number',
                'gov_org_id': 'updated-gov-id'
            }
        ],
        'client_id': None,
        'type': 'report_access',
        'revision': None,
        'template_version': None,
    }


def test_put_not_found_error(app):
    resp = app.put_json(
        API_URL + 'report_accesses/not-existing', {}, expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'not-existing'},
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_put_server_error(app, db, mocker, caplog):
    report_access = (
        factories.create_report_access(db,
                                       customer_company_id='test-customer-id',
                                       company_id='test-company_id',
                                       person_id='test-person-id',
                                       status='active',
                                       access_time=datetime.datetime.now(),
                                       arkisto_id='test-arkisto_id',
                                       report_id='test-report-id',
                                       company_gov_id='test-company-gov-id',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )

    mocker.patch(
        'boldataapi.controllers.report_accesses_controller.update_report_access',
        side_effect=Exception('Serious system problem occurred!'))

    resp = app.put_json(
        API_URL + 'report_accesses/%s' % report_access['external_id'],
        {}, expect_errors=True
    )

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500

    assert 'Serious system problem occurred' in caplog.text


def test_put_returns_validation_error(app, db):
    report_access = (
        factories.create_report_access(db,
                                       customer_company_id='test-customer-id',
                                       company_id='test-company_id',
                                       person_id='test-person-id',
                                       status='active',
                                       access_time=datetime.datetime.now(),
                                       arkisto_id='test-arkisto_id',
                                       report_id='test-report-id',
                                       company_gov_id='test-company-gov-id',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )
    payload = {
        'access_time': '1',
    }

    resp = app.put_json(
        API_URL + f"report_accesses/{report_access['external_id']}",
        payload, expect_errors=True
    )
    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_put_validation_schema_used(app, db, mocker):
    validator_mock = mocker.patch(
        'boldataapi.controllers.report_accesses_controller.ReportAccessValidator')
    payload = {
        'access_time': '1',
    }
    report_access = (
        factories.create_report_access(db,
                                       customer_company_id='test-customer-id',
                                       company_id='test-company_id',
                                       person_id='test-person-id',
                                       status='active',
                                       access_time=datetime.datetime.now(),
                                       arkisto_id='test-arkisto_id',
                                       report_id='test-report-id',
                                       company_gov_id='test-company-gov-id',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )
    app.put_json(
        API_URL + f"report_accesses/{report_access['external_id']}",
        payload, expect_errors=True
    )

    validator_mock.assert_called_with(schema_existing_report_access)


# delete report_accesses tests

def test_delete_report_access(app, db):
    report_access = factories.create_report_access(db,
                                                   customer_company_id='test-id',
                                                   company_id='test-id',
                                                   person_id='test-id')
    control_report = factories.create_report_access(db,
                                                    customer_company_id='test-id',
                                                    company_id='test-id',
                                                    person_id='test-id')
    resp = app.delete(API_URL + f"report_accesses/{report_access['external_id']}")
    assert 200 == resp.status_code
    assert not factories.get_report_access(db, report_access['id'])
    assert factories.get_report_access(db, control_report['id'])


def test_delete_report_access_not_found(app):
    resp = app.delete(API_URL + 'report_accesses/non-existing', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {
            'external_id': 'non-existing',
        }
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


# REPORT ACCESSES LIST SPEEDUP TESTS


def get_report_accesses_list(app, active_org_id, cols=None,
                             limit='', offset=''):
    params = {
        'user_active_org_id': active_org_id or '',
        'limit': limit,
        'offset': offset,
    }
    report_accesses = app.get(API_URL + 'report_accesses/list', params).json['resources']
    if cols is None:
        return report_accesses
    return list(map(operator.itemgetter(*cols), report_accesses))


def test_get_report_accesses_list(app, db):
    active_org_id = 'test-active-org-id'
    report_access = (
        factories.create_report_access(db,
                                       customer_company_id=active_org_id,
                                       company_id='test-company-id',
                                       person_id='test-person-id',
                                       status='active',
                                       company_gov_id='fake-gov-org-id',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )

    assert get_report_accesses_list(app, active_org_id) == [
        {
            'id': report_access['external_id'],
            'gov_org_ids': [
                {
                    'country': 'FI',
                    'org_id_type': 'registration_number',
                    'gov_org_id': 'fake-gov-org-id'
                }
            ]
        }
    ]


def test_get_report_accesses_list_customer_org_id(app, db):
    active_org_id = 'test-active-org-id'

    report_access1 = (
        factories.create_report_access(db,
                                       customer_company_id=active_org_id,
                                       company_id='test-company-id',
                                       person_id='test-person-id',
                                       status='active',
                                       company_gov_id='fake-gov-org-id-1',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )
    report_access2 = (
        factories.create_report_access(db,
                                       customer_company_id=active_org_id,
                                       company_id='test-company-id',
                                       person_id='test-person-id',
                                       status='active',
                                       company_gov_id='fake-gov-org-id-2',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )
    factories.create_report_access(db,
                                   customer_company_id='spam-company-id',
                                   company_id='test-company-id',
                                   person_id='test-person-id',
                                   status='active',
                                   company_gov_id='fake-gov-org-id',
                                   company_gov_id_country='FI',
                                   company_gov_id_type='registration_number',
                                   )

    assert sorted(get_report_accesses_list(
        app, active_org_id=active_org_id, cols=['id']
    )) == sorted([report_access1['external_id'], report_access2['external_id']])


@pytest.mark.parametrize(
    "status_inserted,report_access_found",
    [
        ("active", True),
        ("hidden", False),
    ])
def test_get_report_accesses_list_status(
        app,
        db,
        status_inserted,
        report_access_found
):
    active_org_id = 'test-active-org-id'
    factories.create_report_access(db,
                                   customer_company_id=active_org_id,
                                   company_id='test-company-id',
                                   person_id='test-person-id',
                                   status=status_inserted,
                                   company_gov_id='fake-gov-org-id-1',
                                   company_gov_id_country='FI',
                                   company_gov_id_type='registration_number',
                                   )
    assert bool(get_report_accesses_list(
        app, active_org_id=active_org_id, cols=['id']
    )) == report_access_found


def test_get_report_accesses_list_limit_offset(app, db):
    active_org_id = 'test-active-org-id'

    access_report1 = (
        factories.create_report_access(db,
                                       customer_company_id=active_org_id,
                                       company_id='test-company-id',
                                       person_id='test-person-id',
                                       status='active',
                                       company_gov_id='ABC-123456',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )
    access_report2 = (
        factories.create_report_access(db,
                                       customer_company_id=active_org_id,
                                       company_id='test-company-id',
                                       person_id='test-person-id',
                                       status='active',
                                       company_gov_id='CGZ-123458',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )
    access_report3 = (
        factories.create_report_access(db,
                                       customer_company_id=active_org_id,
                                       company_id='test-company-id',
                                       person_id='test-person-id',
                                       status='active',
                                       company_gov_id='DEA-1232222',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )
    access_report4 = (
        factories.create_report_access(db,
                                       customer_company_id=active_org_id,
                                       company_id='test-company-id',
                                       person_id='test-person-id',
                                       status='active',
                                       company_gov_id='ECA-12345',
                                       company_gov_id_country='FI',
                                       company_gov_id_type='registration_number',
                                       )
    )

    # LIMIT
    assert sorted(get_report_accesses_list(
        app, active_org_id=active_org_id, limit=2, cols=['id']
    )) == sorted([
        (access_report1['external_id']),
        (access_report2['external_id']),
    ])

    # LIMIT overflow
    assert sorted(get_report_accesses_list(
        app, active_org_id=active_org_id, limit=5, cols=['id']
    )) == sorted([
        (access_report1['external_id']),
        (access_report2['external_id']),
        (access_report3['external_id']),
        (access_report4['external_id']),
    ])

    # OFFSET
    assert sorted(get_report_accesses_list(
        app, active_org_id=active_org_id, offset=2, cols=['id']
    )) == sorted([
        (access_report3['external_id']),
        (access_report4['external_id']),
    ])

    # OFFSET overflow
    assert get_report_accesses_list(
        app, active_org_id=active_org_id, offset=5, cols=['id']
    ) == []
