import uuid

from boldataapi.fixtures import factories

API_URL = "/api/v1/boldata/"


def test_get_project_supplier_comments_batch_success(app, db):
    """Test successful batch fetching of project comments."""
    # Create projects and comments
    project1 = factories.create_project(db, name="Project 1")
    project2 = factories.create_project(db, name="Project 2")
    project3 = factories.create_project(db, name="Project 3")

    supplier1 = factories.create_supplier(db, project_id=project1['id'])
    supplier2 = factories.create_supplier(db, project_id=project2['id'])

    # Create comments for project1 and project2
    factories.create_comment(
        db,
        project_id=project1['id'],
        supplier_id=supplier1['id'],
        comment="Comment for project 1"
    )
    factories.create_comment(
        db,
        project_id=project2['id'],
        supplier_id=supplier2['id'],
        comment="Comment for project 2"
    )

    # Test batch request
    payload = {
        "project_ids": [project1['external_id'], project2['external_id'], project3['external_id']]
    }

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)

    assert resp.status_code == 200
    assert resp.json['resource_type'] == 'project_supplier_comments_batch'
    assert 'resources' in resp.json

    resources = resp.json['resources']

    # Check that all requested projects are in the response
    assert project1['external_id'] in resources
    assert project2['external_id'] in resources
    assert project3['external_id'] in resources

    # Check comments for project1
    project1_comments = resources[project1['external_id']]
    assert len(project1_comments) == 1
    assert project1_comments[0]['comment'] == "Comment for project 1"
    assert project1_comments[0]['project_id'] == project1['external_id']

    # Check comments for project2
    project2_comments = resources[project2['external_id']]
    assert len(project2_comments) == 1
    assert project2_comments[0]['comment'] == "Comment for project 2"
    assert project2_comments[0]['project_id'] == project2['external_id']

    # Check that project3 has no comments
    project3_comments = resources[project3['external_id']]
    assert len(project3_comments) == 0


def test_get_project_supplier_comments_batch_with_filters(app, db):
    """Test batch fetching with org_id and hide_deleted filters."""
    project = factories.create_project(db, name="Test Project")
    supplier = factories.create_supplier(db, project_id=project['id'])

    # Create comments with different org_ids
    factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Comment from org1",
        org_id="org1"
    )
    factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Comment from org2",
        org_id="org2"
    )

    # Create a deleted comment
    deleted_comment = factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Deleted comment",
        org_id="org1"
    )
    # Mark as deleted
    from boldataapi.services.project_supplier_comments import mark_comment_as_deleted
    mark_comment_as_deleted(db, deleted_comment['id'], "deleter-org", "deleter-person")

    # Test with org_id filter
    payload = {
        "project_ids": [project['external_id']],
        "org_id": "org1"
    }

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    comments = resp.json['resources'][project['external_id']]
    assert len(comments) == 2  # Should include deleted comment by default

    # Test with hide_deleted=True
    payload = {
        "project_ids": [project['external_id']],
        "org_id": "org1",
        "hide_deleted": True
    }

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    comments = resp.json['resources'][project['external_id']]
    assert len(comments) == 1  # Should exclude deleted comment
    assert comments[0]['comment'] == "Comment from org1"


def test_get_project_supplier_comments_batch_with_read_status(app, db):
    """Test batch fetching with read status."""
    project = factories.create_project(db, name="Test Project")
    supplier = factories.create_supplier(db, project_id=project['id'])
    person_id = str(uuid.uuid4())

    comment = factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Test comment"
    )

    # Mark comment as read by the person
    from boldataapi.services.project_comment_viewers import create_project_comment_viewer
    create_project_comment_viewer(db, comment['id'], person_id)

    # Test with read_by parameter
    payload = {
        "project_ids": [project['external_id']],
        "read_by": person_id
    }

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    comments = resp.json['resources'][project['external_id']]
    assert len(comments) == 1
    assert comments[0]['is_read'] is True


def test_get_project_supplier_comments_batch_validation_errors(app):
    """Test validation errors for batch endpoint."""

    # Test missing request body
    resp = app.post_json(API_URL + 'projects/comments/batch', None, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'

    # Test missing project_ids
    resp = app.post_json(API_URL + 'projects/comments/batch', {}, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert 'project_ids' in resp.json['error']

    # Test empty project_ids list
    payload = {"project_ids": []}
    resp = app.post_json(API_URL + 'projects/comments/batch', payload, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'

    # Test non-list project_ids
    payload = {"project_ids": "not-a-list"}
    resp = app.post_json(API_URL + 'projects/comments/batch', payload, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'

    # Test too many project_ids
    payload = {"project_ids": [f"project-{i}" for i in range(101)]}
    resp = app.post_json(API_URL + 'projects/comments/batch', payload, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'

    # Test invalid project_id types
    payload = {"project_ids": ["valid-id", 123, None]}
    resp = app.post_json(API_URL + 'projects/comments/batch', payload, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'

    # Test empty string project_id
    payload = {"project_ids": ["valid-id", ""]}
    resp = app.post_json(API_URL + 'projects/comments/batch', payload, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'


def test_get_project_supplier_comments_batch_duplicate_removal(app, db):
    """Test that duplicate project IDs are handled correctly."""
    project = factories.create_project(db, name="Test Project")
    supplier = factories.create_supplier(db, project_id=project['id'])

    factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Test comment"
    )

    # Request with duplicate project IDs
    payload = {
        "project_ids": [
            project['external_id'],
            project['external_id'],
            project['external_id']
        ]
    }

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    resources = resp.json['resources']

    # Should only have one entry for the project
    assert len(resources) == 1
    assert project['external_id'] in resources

    comments = resources[project['external_id']]
    assert len(comments) == 1
    assert comments[0]['comment'] == "Test comment"


def test_get_project_supplier_comments_batch_multiple_comments_ordering(app, db):
    """Test that comments are properly ordered within each project."""
    project = factories.create_project(db, name="Test Project")
    supplier = factories.create_supplier(db, project_id=project['id'])

    # Create multiple comments and store their IDs to verify ordering
    comment1 = factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="First comment"
    )

    comment2 = factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Second comment"
    )

    comment3 = factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Third comment"
    )

    payload = {"project_ids": [project['external_id']]}

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    comments = resp.json['resources'][project['external_id']]
    assert len(comments) == 3

    # Comments should be ordered by created_timestamp desc (newest first)
    # Since we can't rely on sleep for timestamp differences, let's verify the ordering
    # by checking that timestamps are in descending order
    timestamps = [comment['created_timestamp'] for comment in comments]
    assert timestamps == sorted(timestamps, reverse=True), "Comments should be ordered by timestamp descending"

    # Also verify that all our comments are present
    comment_texts = {comment['comment'] for comment in comments}
    expected_texts = {"First comment", "Second comment", "Third comment"}
    assert comment_texts == expected_texts, "All comments should be present"


def test_get_project_supplier_comments_batch_empty_response(app, db):
    """Test batch request for non-existent projects."""
    payload = {
        "project_ids": ["non-existent-1", "non-existent-2"]
    }

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    resources = resp.json['resources']
    assert len(resources) == 2
    assert resources["non-existent-1"] == []
    assert resources["non-existent-2"] == []


def test_get_project_supplier_comments_batch_large_dataset(app, db):
    """Test batch endpoint with a larger dataset to verify performance."""
    projects = []

    # Create 10 projects with multiple comments each
    for i in range(10):
        project = factories.create_project(db, name=f"Project {i}")
        projects.append(project)

        supplier = factories.create_supplier(db, project_id=project['id'])

        # Create 5 comments per project
        for j in range(5):
            factories.create_comment(
                db,
                project_id=project['id'],
                supplier_id=supplier['id'],
                comment=f"Comment {j} for project {i}",
                org_id=f"org-{i % 3}"  # Vary org_ids
            )

    # Request all projects at once
    project_ids = [p['external_id'] for p in projects]
    payload = {"project_ids": project_ids}

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    resources = resp.json['resources']
    assert len(resources) == 10

    # Verify each project has 5 comments
    for project in projects:
        comments = resources[project['external_id']]
        assert len(comments) == 5

        # Verify comments are properly ordered (newest first)
        for k in range(len(comments) - 1):
            assert comments[k]['created_timestamp'] >= comments[k + 1]['created_timestamp']


def test_get_project_supplier_comments_batch_mixed_projects(app, db):
    """Test batch request with mix of existing and non-existing projects."""
    # Create some real projects
    project1 = factories.create_project(db, name="Real Project 1")
    project2 = factories.create_project(db, name="Real Project 2")

    supplier1 = factories.create_supplier(db, project_id=project1['id'])
    supplier2 = factories.create_supplier(db, project_id=project2['id'])

    factories.create_comment(
        db,
        project_id=project1['id'],
        supplier_id=supplier1['id'],
        comment="Real comment 1"
    )
    factories.create_comment(
        db,
        project_id=project2['id'],
        supplier_id=supplier2['id'],
        comment="Real comment 2"
    )

    # Mix real and fake project IDs
    payload = {
        "project_ids": [
            project1['external_id'],
            "fake-project-1",
            project2['external_id'],
            "fake-project-2"
        ]
    }

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    resources = resp.json['resources']
    assert len(resources) == 4

    # Real projects should have comments
    assert len(resources[project1['external_id']]) == 1
    assert len(resources[project2['external_id']]) == 1
    assert resources[project1['external_id']][0]['comment'] == "Real comment 1"
    assert resources[project2['external_id']][0]['comment'] == "Real comment 2"

    # Fake projects should have empty arrays
    assert resources["fake-project-1"] == []
    assert resources["fake-project-2"] == []


def test_get_project_supplier_comments_batch_service_function(db):
    """Test the service function directly."""
    from boldataapi.services.project_supplier_comments import query_project_supplier_comments_batch

    # Create test data
    project1 = factories.create_project(db, name="Service Test Project 1")
    project2 = factories.create_project(db, name="Service Test Project 2")

    supplier1 = factories.create_supplier(db, project_id=project1['id'])
    supplier2 = factories.create_supplier(db, project_id=project2['id'])

    factories.create_comment(
        db,
        project_id=project1['id'],
        supplier_id=supplier1['id'],
        comment="Service test comment 1",
        org_id="test-org"
    )
    factories.create_comment(
        db,
        project_id=project2['id'],
        supplier_id=supplier2['id'],
        comment="Service test comment 2",
        org_id="test-org"
    )

    # Test the service function directly
    result = query_project_supplier_comments_batch(
        db,
        project_ids=[project1['external_id'], project2['external_id']],
        org_id="test-org"
    )

    assert isinstance(result, dict)
    assert len(result) == 2
    assert project1['external_id'] in result
    assert project2['external_id'] in result

    # Check comment content
    project1_comments = result[project1['external_id']]
    project2_comments = result[project2['external_id']]

    assert len(project1_comments) == 1
    assert len(project2_comments) == 1
    assert project1_comments[0]['comment'] == "Service test comment 1"
    assert project2_comments[0]['comment'] == "Service test comment 2"


def test_get_project_supplier_comments_batch_service_empty_input(db):
    """Test service function with empty input."""
    from boldataapi.services.project_supplier_comments import query_project_supplier_comments_batch

    # Test with empty project_ids list
    result = query_project_supplier_comments_batch(db, project_ids=[])
    assert result == {}

    # Test with None (should not happen in practice but good to be safe)
    result = query_project_supplier_comments_batch(db, project_ids=None)
    assert result == {}


def test_get_project_supplier_comments_batch_whitespace_handling(app):
    """Test handling of whitespace in project IDs."""
    payload = {"project_ids": ["  ", "\t", "\n"]}
    resp = app.post_json(API_URL + 'projects/comments/batch', payload, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'


def test_get_project_supplier_comments_batch_single_project(app, db):
    """Test batch endpoint with single project (edge case)."""
    project = factories.create_project(db, name="Single Project")
    supplier = factories.create_supplier(db, project_id=project['id'])

    factories.create_comment(
        db,
        project_id=project['id'],
        supplier_id=supplier['id'],
        comment="Single project comment"
    )

    payload = {"project_ids": [project['external_id']]}

    resp = app.post_json(API_URL + 'projects/comments/batch', payload)
    assert resp.status_code == 200

    resources = resp.json['resources']
    assert len(resources) == 1
    assert project['external_id'] in resources

    comments = resources[project['external_id']]
    assert len(comments) == 1
    assert comments[0]['comment'] == "Single project comment"
