import datetime
import json

import pytest
from freezegun import freeze_time

from boldataapi.fixtures import factories
from boldataapi.schema import (
    schema_existing_report_cache,
    schema_new_report_cache,
)
from boldataapi.services.report_cache import get_report_cache


API_URL = '/api/v1/boldata/'


# GET tests

def test_get_not_found(app):
    resp = app.get(API_URL + 'report_cache/not-existing', expect_errors=True)

    expected = {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {'external_id': 'not-existing'},
    }
    assert resp.json == expected
    assert resp.status_code == 404


@freeze_time("2012-01-14")
def test_get_returns_ok_status(app, db):
    report_cache = (
        factories.create_report_cache(db,
                                      key='test-key',
                                      value="{'some key 1': 'some value 1', "
                                            "'some key 2': 'some value 2'}",
                                      type='statusreports_raw',
                                      expires_at=datetime.datetime.now(),
                                      correlation_id='test-correlation_id',
                                      interested_org_id='test-interested_org_id',
                                      provider='creditsafe_connect',
                                      )
    )
    resp = app.get(API_URL + 'report_cache/%s' % report_cache['external_id'])
    assert resp.status_code == 200
    assert resp.content_type == 'application/json'
    new_report_cache = json.loads(resp.body)

    assert new_report_cache == {
        'id': report_cache['external_id'],
        'correlation_id': 'test-correlation_id',
        'expires_at': '2012-01-14T00:00:00',
        'interested_org_id': 'test-interested_org_id',
        'key': 'test-key',
        'provider': 'creditsafe_connect',
        'type': 'statusreports_raw',
        'value': "{'some key 1': 'some value 1', 'some key 2': 'some value 2'}"
    }


# POST tests

def test_post_returns_created_status(app):
    payload = {
        'correlation_id': 'test-correlation_id',
        'expires_at': '2012-01-14T00:00:00',
        'interested_org_id': 'test-interested_org_id',
        'key': 'test-key',
        'provider': 'creditsafe_connect',
        'type': 'statusreports_raw',
        'value': "{'some key 1': 'some value 1', 'some key 2': 'some value 2'}"
    }

    resp = app.post_json(API_URL + 'report_cache', payload)
    assert resp.status_code == 201
    new_report_cache = json.loads(resp.body)
    new_report_cache.pop('id')
    assert new_report_cache == payload


def test_post_validation_schema_used(app, mocker):
    validator_mock = mocker.patch(
        'boldataapi.controllers.report_cache_controller.ReportCacheValidator')

    app.post_json(API_URL + 'report_cache', {}, expect_errors=True)
    validator_mock.assert_called_with(schema_new_report_cache)


def test_post_validation_error(app):
    resp = app.post_json(API_URL + 'report_cache', {}, expect_errors=True)

    assert resp.status_code == 400
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


# PUT tests

@freeze_time("2012-01-14")
def test_put_returns_ok_status(app, db):
    report_cache = (
        factories.create_report_cache(db,
                                      key='test-key',
                                      value="{'some key 1': 'some value 1', "
                                            "'some key 2': 'some value 2'}",
                                      type='statusreports_raw',
                                      expires_at=datetime.datetime.now(),
                                      correlation_id='test-correlation_id',
                                      interested_org_id='test-interested_org_id',
                                      provider='creditsafe_connect',
                                      )
    )

    payload = {
        'expires_at': '2013-01-14T00:00:00',
        'key': 'test-key-new',
        'value': "{'some key 1': 'some new value 12', "
                 "'some key 2': 'some new value 21', "
                 "'some key 3': 'some new value 23'}",
        'correlation_id': 'test-correlation_id1',
        'interested_org_id': 'test-interested_org_id',
        'type': 'statusreports_parsed',
        'provider': 'creditsafe_ggs',
    }

    resp = app.put_json(API_URL + 'report_cache/%s' % report_cache['external_id'], payload)
    assert resp.status_code == 200
    updated_report_cache = resp.json

    assert updated_report_cache == {
        'id': report_cache['external_id'],
        'expires_at': '2013-01-14T00:00:00',
        'correlation_id': 'test-correlation_id1',
        'interested_org_id': 'test-interested_org_id',
        'key': 'test-key-new',
        'provider': 'creditsafe_ggs',
        'type': 'statusreports_parsed',
        'value': "{'some key 1': 'some new value 12', "
                 "'some key 2': 'some new value 21', "
                 "'some key 3': 'some new value 23'}"
    }


def test_put_not_found_error(app):
    resp = app.put_json(
        API_URL + 'report_cache/not-existing', {}, expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'not-existing'},
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_put_returns_validation_error(app, db):
    report_cache = (
        factories.create_report_cache(db,
                                      key='test-key',
                                      value="{'some key 1': 'some value 1',"
                                            "'some key 2': 'some value 2'}",
                                      type='statusreports_raw',
                                      expires_at=datetime.datetime.now(),
                                      correlation_id='test-correlation_id',
                                      interested_org_id='test-interested_org_id',
                                      provider='creditsafe_connect',
                                      )
    )
    payload = {
        'expires_at': '1',
    }

    resp = app.put_json(
        API_URL + f"report_cache/{report_cache['external_id']}",
        payload, expect_errors=True
    )
    assert resp.status_code == 400
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_put_validation_schema_used(app, db, mocker):
    validator_mock = mocker.patch(
        'boldataapi.controllers.report_cache_controller.ReportCacheValidator')
    payload = {
        'expires_at': '1',
    }
    report_cache = (
        factories.create_report_cache(db,
                                      key='test-key',
                                      value="{'some key 1': 'some value 1',"
                                            "'some key 2': 'some value 2'}",
                                      type='statusreports_raw',
                                      expires_at=datetime.datetime.now(),
                                      correlation_id='test-correlation_id',
                                      interested_org_id='test-interested_org_id',
                                      provider='creditsafe_connect',
                                      )
    )
    app.put_json(
        API_URL + f"report_cache/{report_cache['external_id']}",
        payload, expect_errors=True
    )

    validator_mock.assert_called_with(schema_existing_report_cache)


# delete report_cache tests

def test_delete_report_cache(app, db):
    report_cache = factories.create_report_cache(db,
                                                 key='test-key',
                                                 value="{'some key': 'some value'}")
    control_report = factories.create_report_cache(db,
                                                   key='test-key',
                                                   value="{'some key': 'some value'}")
    resp = app.delete(API_URL + f"report_cache/{report_cache['external_id']}")
    assert resp.status_code == 200
    assert not factories.get_report_cache(db, report_cache['id'])
    assert factories.get_report_cache(db, control_report['id'])


def test_delete_report_cache_not_found(app):
    resp = app.delete(API_URL + 'report_cache/non-existing', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {
            'external_id': 'non-existing',
        }
    }
    assert resp.status_code == 404
    assert expected_error == resp.json


# query report_cache tests

@pytest.mark.parametrize(('q_field', 'db_field'), [
    ('id', 'external_id'),
    ('expires_at', 'expires_at'),
    ('correlation_id', 'correlation_id'),
    ('interested_org_id', 'interested_org_id'),
    ('key', 'key'),
    ('provider', 'provider'),
    ('type', 'type'),
    ('value', 'value'),
])
def test_query_report_cache(app, db, q_field, db_field):
    rc = factories.create_report_cache(db,
                                       expires_at='2023-01-12T00:00:00',
                                       key='test-key1',
                                       value='{some key 1: some value 1}',
                                       provider='creditsafe_ggs',
                                       type='statusreports_parsed',
                                       interested_org_id='some org id 1',
                                       correlation_id='some other id 1',
                                       )
    rc = get_report_cache(db, report_cache_id=rc['id'])
    # mangle rc['expires_at'] to prevent error: datetime not JSON serializeble
    rc['expires_at'] = '2023-01-12T00:00:00'

    query = {q_field: rc[db_field]}
    resp = app.get(f'{API_URL}report_cache/query?q={json.dumps(query)}')
    result = resp.json['resources']

    excepted = [{
        'correlation_id': 'some other id 1',
        'expires_at': '2023-01-12T00:00:00',
        'id': rc['external_id'],
        'interested_org_id': 'some org id 1',
        'key': 'test-key1',
        'provider': 'creditsafe_ggs',
        'type': 'statusreports_parsed',
        'value': '{some key 1: some value 1}',
    }]
    assert result == excepted
    assert resp.status_code == 200


def test_query_report_cache_multiple(app, db):
    rc1 = factories.create_report_cache(db,
                                        expires_at='2023-01-11T00:00:00',
                                        key='test-key1',
                                        value='{some key 1: some value 1}',
                                        provider='creditsafe_ggs',
                                        type='statusreports_parsed',
                                        interested_org_id='some org id 1',
                                        correlation_id='some other id 1',
                                        )
    rc2 = factories.create_report_cache(db,
                                        expires_at='2023-01-12T00:00:00',
                                        key='test-key2',
                                        value='{some key 2: some value 2}',
                                        provider='creditsafe_connect',
                                        type='statusreports_parsed',
                                        interested_org_id='some org id 2',
                                        correlation_id='some other id 2',
                                        )
    rc3 = factories.create_report_cache(db,
                                        expires_at='2023-01-13T00:00:00',
                                        key='test-key3',
                                        value='{some key 3: some value 3}',
                                        provider='creditsafe_ggs',
                                        type='statusreports_parsed',
                                        interested_org_id='some org id 3',
                                        correlation_id='some other id 3',
                                        )

    query = {'provider': 'creditsafe_ggs'}

    resp = app.get(f'{API_URL}report_cache/query?q={json.dumps(query)}')
    result = resp.json['resources']

    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([rc1['external_id'], rc3['external_id']])

    query = {'type': 'statusreports_parsed'}

    resp = app.get(f'{API_URL}report_cache/query?q={json.dumps(query)}')
    result = resp.json['resources']

    assert len(result) == 3
    assert set([i['id'] for i in result]) == set([rc1['external_id'],
                                                  rc2['external_id'],
                                                  rc3['external_id']])
