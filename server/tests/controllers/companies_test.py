import datetime
from operator import itemgetter

import pytest
import sqlalchemy as sa
from freezegun import freeze_time

from boldataapi.fixtures import factories
from boldataapi.schema import (
    BOL_STATUS_ATTENTION,
    STATUS_ATTENTION,
    STATUS_OK,
    STATUS_STOP,
)

API_URL = '/api/v1/boldata/'


def get_company_list(app, active_org_id, user_role='main',
                     is_admin=False, user_projects_ids=None, filter_search='',
                     limit='', offset='', cols=None, ff_block_project_client=None):
    params = {
        'user_active_org_id': active_org_id or '',
        'user_active_org_role': user_role,
        'user_projects_ids': user_projects_ids,
        'user_is_admin': is_admin,
        'filter.search': filter_search,
        'limit': limit,
        'offset': offset,
    }

    if ff_block_project_client:
        params['ff_block_project_client'] = ff_block_project_client

    companies = app.post_json(API_URL + 'companies/list', params).json['resources']
    if cols is None:
        return companies
    return list(map(itemgetter(*cols), companies))


def test_company_list(app, db):
    client_company_id = 'client-comp-id'
    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id
    )
    factories.create_supplier(db, project_id=project['id'], company_id=client_company_id)

    assert get_company_list(app, active_org_id=client_company_id) == [
        {
            'country': None,
            'gov_org_id': None,
            'id': client_company_id,
            'latest_report_status': None,
            'name': None,
            'project_count': 1,
            'report_available': False,
            'vat_number': None,
        }
    ]


def test_company_list_project_count(app, db):
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    project_a = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
    )
    project_b = factories.create_project(
        db,
        name='B Project',
        client_company_id=client_company_id,
    )
    factories.create_project(
        db,
        name='C Project',
        client_company_id=client_company_id,
    )

    factories.create_supplier(db, project_id=project_a['id'], company_id=supplier_company_id)
    factories.create_supplier(db, project_id=project_b['id'], company_id=supplier_company_id)
    factories.create_supplier(db, project_id=project_b['id'], company_id=client_company_id)

    result = get_company_list(
        app, active_org_id=client_company_id, user_role='main',
        cols=['id', 'project_count']
    )
    expected = [
        (client_company_id, 1),
        (supplier_company_id, 2)
    ]
    assert sorted(result) == sorted(expected)

    project_d = factories.create_project(
        db,
        name='D Project',
        client_company_id=supplier_company_id,
    )
    factories.create_project(
        db,
        name='E Project',
        client_company_id=supplier_company_id,
    )

    factories.create_supplier(db, project_id=project_d['id'], company_id=client_company_id)

    result = get_company_list(
        app, active_org_id=supplier_company_id, user_role='main',
        cols=['id', 'project_count']
    )
    expected = [
        (client_company_id, 1),
        (supplier_company_id, 0)
    ]
    assert sorted(result) == sorted(expected)


def test_company_list_closed_project(app, db):
    # This test looks like ported to BOL
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    project_a = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state='active',
    )
    project_b = factories.create_project(
        db,
        name='B Project',
        client_company_id=client_company_id,
        state='closed',
    )
    factories.create_project(
        db,
        name='B Project',
        client_company_id=client_company_id,
        state='active',
    )

    factories.create_supplier(db, project_id=project_a['id'], company_id=supplier_company_id)
    factories.create_supplier(db, project_id=project_a['id'], company_id=client_company_id)

    factories.create_supplier(db, project_id=project_b['id'], company_id=supplier_company_id)
    factories.create_supplier(db, project_id=project_b['id'], company_id=client_company_id)

    result = get_company_list(
        app, active_org_id=client_company_id, user_role='main',
        cols=['id', 'project_count']
    )
    expected = [
        (client_company_id, 1),
        (supplier_company_id, 1)
    ]
    assert sorted(result) == sorted(expected)


def test_company_list_report_status(app, db):
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'

    project_a = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state='active',
    )
    project_b = factories.create_project(
        db,
        name='B Project',
        client_company_id=client_company_id,
        state='closed',
    )
    factories.create_supplier(
        db, project_id=project_a['id'], company_id=supplier_company_id)
    factories.create_supplier(
        db, project_id=project_b['id'], company_id=supplier_company_id)

    with freeze_time('2020-01-01'):
        factories.create_status_report(
            db,
            status=STATUS_ATTENTION,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
    with freeze_time('2020-01-01'):
        factories.create_status_report(
            db,
            status=STATUS_OK,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
    with freeze_time('2019-01-01'):
        factories.create_status_report(
            db,
            status=STATUS_STOP,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )

    result = get_company_list(app, active_org_id=client_company_id)
    expected = [
        {
            'country': None,
            'gov_org_id': None,
            'id': supplier_company_id,
            'latest_report_status': BOL_STATUS_ATTENTION,
            'name': None,
            'project_count': 1,
            'report_available': True,
            'vat_number': None,
        },
        {
            'country': None,
            'gov_org_id': None,
            'id': 'client-comp-id',
            'latest_report_status': None,
            'name': None,
            'project_count': 0,
            'report_available': False,
            'vat_number': None
        }
    ]

    assert sorted(result, key=itemgetter('id')) == sorted(expected, key=itemgetter('id'))


def test_company_list_basic_user_empty(app, db):
    # Basic user should not see this project

    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state='active',
    )

    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)

    # only active company must appear on the list
    assert get_company_list(
        app, active_org_id=client_company_id, user_role='basic'
    ) == [
        {
            'country': None,
            'gov_org_id': None,
            'id': client_company_id,
            'latest_report_status': None,
            'name': None,
            'project_count': 0,
            'report_available': False,
            'vat_number': None
        }
    ]


def test_company_list_basic_user(app, db):
    client_company_id = 'client-comp-id'
    supplier_company_a_id = 'supplier-company-a-id'
    supplier_company_b_id = 'supplier-company-b-id'

    project_a = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state='active',
    )
    project_b = factories.create_project(
        db,
        name='B Project',
        client_company_id=client_company_id,
        state='active',
    )
    factories.create_supplier(
        db, project_id=project_a['id'], company_id=supplier_company_a_id)
    factories.create_supplier(
        db, project_id=project_b['id'], company_id=supplier_company_b_id)

    with freeze_time('2020-01-01'):
        factories.create_status_report(
            db,
            status=STATUS_ATTENTION,
            interested_company_id=client_company_id,
            company_id=supplier_company_a_id,
            generated_timestamp=datetime.datetime.now(),
        )
    result = get_company_list(app, active_org_id=client_company_id,
                              user_role='basic', user_projects_ids=[project_a['external_id']])

    expected = [
        {
            'country': None,
            'gov_org_id': None,
            'id': supplier_company_a_id,
            'latest_report_status': BOL_STATUS_ATTENTION,
            'name': None,
            'project_count': 1,
            'report_available': True,
            'vat_number': None,
        },
        {
            'country': None,
            'gov_org_id': None,
            'id': 'client-comp-id',
            'latest_report_status': None,
            'name': None,
            'project_count': 0,
            'report_available': False,
            'vat_number': None
        }
    ]

    assert sorted(result, key=itemgetter('id')) == sorted(expected, key=itemgetter('id'))


@pytest.mark.parametrize('user_role',
                         ['main',
                          'basic'])
def test_bol_company_list_admin_user(app, db, user_role):
    admin_company_id = 'test-admin-company-id'
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state='active',
    )

    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)

    # Admin user should see all orgs that are suppliers or project responsible
    # orgs
    result = get_company_list(
        app, active_org_id=admin_company_id, user_role=user_role,
        is_admin=True,
        cols=['id', 'project_count']
    )
    expected = [
        (admin_company_id, 0),
        (client_company_id, 0),
        (supplier_company_id, 1),
    ]
    assert sorted(result) == sorted(expected)


@pytest.mark.parametrize(
    ('client_company_id', 'created_by_org_id', 'added_client_confirmed', 'in_list'), [
        ('any_org', None, None, True),
        ('any_org', None, False, True),  # is this possible?
        ('any_org', None, True, True),  # is this possible?
        ('creator_org', 'creator_org', None, True),
        ('creator_org', 'creator_org', False, True),  # is this possible?
        ('creator_org', 'creator_org', True, True),
        ('different_org', 'creator_org', None, False),
        ('different_org', 'creator_org', False, False),  # is this possible?
        ('different_org', 'creator_org', True, True),
     ])
def test_company_list_client_confirmed(app, db,
                                       client_company_id, created_by_org_id,
                                       added_client_confirmed, in_list):
    project = factories.create_project(
        db,
        name='Added client project',
        client_company_id=client_company_id,
        created_by_org_id=created_by_org_id,
        added_client_confirmed=added_client_confirmed,
    )

    # Main Contractor supplier would be created in added client projects
    if created_by_org_id is not None:
        factories.create_supplier(db, project_id=project['id'],
                                  company_id=created_by_org_id)
    # An extra supplier in case client==main_contractor
    factories.create_supplier(db, project_id=project['id'],
                              company_id='S1')

    result = (
        get_company_list(
            app, active_org_id=client_company_id,
            cols=['id']
        )
    )
    # active_org_id is always in the list even if no projects visible.
    # Remove active_org_id to remove false positives.
    result.remove(client_company_id)

    # Are project suppliers present in the list?
    assert bool(result) == in_list


def test_company_list_client_not_confirmed_supplier_visible(app, db):
    active_org_id = 'active-org-id'
    client_org_id = 'client-org-id'
    supplier_org_id = 'supplier-org-id'
    project = factories.create_project(
        db,
        name='Added client project',
        client_company_id=client_org_id,
        created_by_org_id=active_org_id,
        added_client_confirmed=False,
        added_client_can_view=True,
    )

    # Main Contractor supplier would be created in added client project
    factories.create_supplier(db, project_id=project['id'],
                              company_id=active_org_id)
    # An extra supplier
    factories.create_supplier(db, project_id=project['id'],
                              company_id=supplier_org_id)

    result = get_company_list(
        app,
        active_org_id=client_org_id,
        cols=['id'],
        ff_block_project_client=True,
    )
    assert client_org_id in result
    assert supplier_org_id not in result


def test_bol_company_list_limit_offset(app, db):
    a = 'a-company-id'
    b = 'b-company-id'
    c = 'c-company-id'

    client_company_id = 'client-company-id'
    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state='active',
    )
    factories.create_supplier(db, project_id=project['id'], company_id=a)
    factories.create_supplier(db, project_id=project['id'], company_id=b)
    factories.create_supplier(db, project_id=project['id'], company_id=c)

    # LIMIT
    result = get_company_list(
        app, active_org_id=client_company_id, limit=2, cols=['id']
    )
    assert len(result) == 2

    # LIMIT overflow
    result = get_company_list(
        app, active_org_id=client_company_id, limit=5, cols=['id']
    )
    assert len(result) == 4

    # OFFSET
    result = get_company_list(
        app, active_org_id=client_company_id, offset=3, cols=['id']
    )
    assert len(result) == 1

    # OFFSET overflow
    result = get_company_list(
        app, active_org_id=client_company_id, offset=5, cols=['id']
    )
    # pylint: disable=len-as-condition
    assert len(result) == 0


def test_get_all_company_ids_basic_functionality(app, db):
    """Test basic functionality of the get_all_company_ids endpoint."""
    # Create test data across multiple tables
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'
    creator_company_id = 'creator-company-id'

    # Create a project with client and creator companies
    project = factories.create_project(
        db,
        name='Test Project',
        client_company_id=client_company_id,
        created_by_org_id=creator_company_id
    )

    # Create a supplier for the project
    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=supplier_company_id
    )

    # Test 1: Default parameters (all tables)
    resp = app.get(API_URL + 'companies/all-ids')
    assert resp.status_code == 200
    assert 'company_ids' in resp.json
    assert 'next_cursor' in resp.json
    assert 'has_more' in resp.json

    # Verify that all company IDs are in the response
    company_ids = resp.json['company_ids']
    assert client_company_id in company_ids
    assert supplier_company_id in company_ids
    assert creator_company_id in company_ids

    # Test 2: Specific table (suppliers only)
    resp = app.get(API_URL + 'companies/all-ids?table=suppliers')
    assert resp.status_code == 200
    assert supplier_company_id in resp.json['company_ids']

    # Test 3: Specific table (projects only)
    resp = app.get(API_URL + 'companies/all-ids?table=projects')
    assert resp.status_code == 200
    project_ids = resp.json['company_ids']
    assert client_company_id in project_ids
    assert creator_company_id in project_ids

    # Test 4: Basic batch size functionality
    # Create additional projects to test batching
    for i in range(3):
        factories.create_project(
            db,
            name=f'Batch Test Project {i}',
            client_company_id=f'batch-client-{i}'
        )

    # Test with batch_size=2
    resp = app.get(API_URL + 'companies/all-ids?batch_size=2')
    assert resp.status_code == 200
    assert len(resp.json['company_ids']) == 2
    assert resp.json['has_more'] is True

    # Get next batch using the cursor
    next_cursor = resp.json['next_cursor']
    resp2 = app.get(API_URL + f'companies/all-ids?batch_size=2&last_id={next_cursor}')
    assert resp2.status_code == 200
    assert len(resp2.json['company_ids']) == 2

    # Ensure no duplicates between batches
    assert not set(resp.json['company_ids']).intersection(set(resp2.json['company_ids']))


def test_get_suppliers_company_ids_batch_size(app, db):
    """Test pagination with different batch sizes and verify consistency of results."""
    # Create a project
    project = factories.create_project(
        db,
        name='Project',
        client_company_id='client-id'
    )

    # Create 10 suppliers with predictable IDs
    expected_ids = []
    for i in range(10):
        supplier_id = f'supplier-company-id-{i:02d}'
        factories.create_supplier(
            db,
            project_id=project['id'],
            company_id=supplier_id
        )
        expected_ids.append(supplier_id)

    # Sort expected IDs to match how they'll be returned from the API
    expected_ids.sort()

    # Test with various batch sizes to ensure consistency
    batch_sizes = [1, 2, 3, 4, 5, 10]
    all_ids_by_batch_size = {}

    for batch_size in batch_sizes:
        # Retrieve all IDs using the current batch_size
        all_ids = []
        has_more = True
        last_id = ''
        batch_count = 0

        while has_more and batch_count < 20:  # Safety limit to prevent infinite loops
            url = (
                API_URL +
                f'companies/all-ids?table=suppliers&batch_size={batch_size}&last_id={last_id}'
            )
            resp = app.get(url)

            # Verify response structure
            assert resp.status_code == 200
            assert 'company_ids' in resp.json
            assert 'next_cursor' in resp.json
            assert 'has_more' in resp.json

            # Get batch data
            batch = resp.json['company_ids']
            has_more = resp.json['has_more']

            # Verify batch size is correct (should be ≤ batch_size)
            assert len(batch) <= batch_size, \
                f"Batch {batch_count} has incorrect size for batch_size={batch_size}"

            # If we have results, update cursor and add to collected IDs
            if batch:
                last_id = resp.json['next_cursor']
                all_ids.extend(batch)

                # If this isn't the first batch, verify no overlap with previous batches
                if batch_count > 0:
                    previous_ids = all_ids[:-len(batch)]
                    error_msg = (
                        f"Batch {batch_count} overlaps with previous batches "
                        f"for batch_size={batch_size}"
                    )
                    assert not set(batch).intersection(set(previous_ids)), error_msg

            batch_count += 1

        # Special case: Check if we need to make one more request for the last ID
        # This is needed because the has_more flag might be False for the last ID
        if not has_more and last_id:
            url = (
                API_URL +
                f'companies/all-ids?table=suppliers&batch_size={batch_size}&last_id={last_id}'
            )
            resp = app.get(url)

            if resp.json['company_ids']:
                all_ids.extend(resp.json['company_ids'])

        # Store the results for this batch size
        all_ids_by_batch_size[batch_size] = all_ids

        # Verify we got all expected IDs with this batch size
        assert len(all_ids) == len(expected_ids), \
            f"Expected {len(expected_ids)} IDs with batch_size={batch_size}, got {len(all_ids)}"

        # Verify the content matches the expected IDs
        assert set(all_ids) == set(expected_ids), \
            f"IDs retrieved with batch_size={batch_size} don't match expected IDs"

    # Verify all batch sizes retrieved the same IDs
    for i in range(len(batch_sizes) - 1):
        current_batch_size = batch_sizes[i]
        next_batch_size = batch_sizes[i + 1]
        # Verify that IDs are consistent across different batch sizes
        ids_current = set(all_ids_by_batch_size[current_batch_size])
        ids_next = set(all_ids_by_batch_size[next_batch_size])
        error_msg = (
            f"IDs with batch_size={current_batch_size} "
            f"don't match those with batch_size={next_batch_size}"
        )
        assert ids_current == ids_next, error_msg

    # PART 4: Verify a single large batch retrieves all IDs at once
    resp = app.get(API_URL + 'companies/all-ids?table=suppliers&batch_size=20')
    assert resp.status_code == 200

    # The large batch should return exactly the same number of IDs as expected
    assert len(resp.json['company_ids']) == len(expected_ids)

    # The large batch should contain all expected IDs
    assert set(resp.json['company_ids']) == set(expected_ids)

    # The has_more flag should be False for a large enough batch
    assert resp.json['has_more'] is False


def test_get_all_company_ids_cursor_pagination(app, db):
    # Create test data with predictable IDs
    company_ids = ['company-1', 'company-2', 'company-3', 'company-4', 'company-5']

    # Create projects with these company IDs
    for company_id in company_ids:
        factories.create_project(
            db,
            name=f'Project for {company_id}',
            client_company_id=company_id
        )

    # Get first batch with cursor after company-2
    resp = app.get(API_URL + 'companies/all-ids?last_id=company-2')

    # Verify we get companies after company-2
    assert resp.status_code == 200
    result_ids = resp.json['company_ids']
    assert all(cid > 'company-2' for cid in result_ids)
    assert 'company-1' not in result_ids
    assert 'company-2' not in result_ids


def test_get_all_company_ids_invalid_params(app):
    # Test invalid batch_size
    resp = app.get(API_URL + 'companies/all-ids?batch_size=invalid', expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'

    # Test batch_size too large
    resp = app.get(API_URL + 'companies/all-ids?batch_size=20000', expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'

    # Test invalid table name
    resp = app.get(API_URL + 'companies/all-ids?table=invalid_table', expect_errors=True)
    assert resp.status_code == 400
    assert resp.json['error_code'] == 'ParameterValidationFailed'


def test_get_all_company_ids_edge_cases(app, db):
    """Test edge cases for the get_all_company_ids endpoint."""
    # Test 1: Multiple tables with data
    project = factories.create_project(
        db,
        name='Test Project',
        client_company_id='client-id',
        created_by_org_id='creator-id'
    )

    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id='supplier-id'
    )

    factories.create_project_user(
        db,
        project_id=project['id'],
        represented_company_id='user-company-id'
    )

    # Test getting IDs from all tables
    resp = app.get(API_URL + 'companies/all-ids')
    assert resp.status_code == 200

    # Verify all IDs are present
    company_ids = resp.json['company_ids']
    assert 'client-id' in company_ids
    assert 'creator-id' in company_ids
    assert 'supplier-id' in company_ids
    assert 'user-company-id' in company_ids

    # Test 2: Empty result with cursor beyond all data
    resp = app.get(API_URL + 'companies/all-ids?last_id=zzzzzzzzz')
    assert resp.status_code == 200
    assert resp.json['company_ids'] == []
    assert resp.json['has_more'] is False


@pytest.mark.parametrize('table', [
    'suppliers',
    'projects',
    'project_users',
    'status_reports',
    'status_reports_history',
    'bulk_import_jobs',
    'internal_project_ids',
    'creditsafe_account',
    'creditsafe_account_history',
    'report_cache',
    'notification_reports',
])
def test_get_all_company_ids_each_table(app, db, table):
    # Test each table type individually
    resp = app.get(API_URL + f'companies/all-ids?table={table}')

    # Basic validation
    assert resp.status_code == 200
    assert isinstance(resp.json['company_ids'], list)
    assert isinstance(resp.json['has_more'], bool)
    assert isinstance(resp.json['next_cursor'], str)


def test_get_all_company_ids_materialized_path(app, db):
    # Create a supplier with materialized_path
    project = factories.create_project(db)
    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id='supplier-id',
        materialized_path=['path-company-1', 'path-company-2']
    )

    # Test getting IDs from suppliers table
    resp = app.get(API_URL + 'companies/all-ids?table=suppliers')

    # Verify materialized_path IDs are included
    company_ids = resp.json['company_ids']
    assert 'supplier-id' in company_ids
    assert 'path-company-1' in company_ids
    assert 'path-company-2' in company_ids


def test_get_all_company_ids_notification_reports(app, db):
    """Test extracting company IDs from notification_reports table."""
    # Create a notification report with company IDs in different parts of the qvarn_report
    org_id = 'org-id-from-org-ids'
    user_org_id = 'user-org-id-from-users-to-notify'
    company_id = 'company-id-from-projects'

    # Create a qvarn_report with the structure we need to test
    qvarn_report = {
        'kind': 'monthly',
        'org_ids': [org_id],
        'users_to_notify': {
            '<EMAIL>': {
                'user_info': {
                    'name': 'Test User',
                    'email': '<EMAIL>',
                },
                'user_org_id': user_org_id,
                'projects': {
                    'project-id': {
                        'project_name': 'Test Project',
                        'companies': {
                            company_id: {
                                'company_name': 'Test Company',
                                'old_status': 'incomplete',
                                'new_status': 'stop',
                            }
                        }
                    }
                }
            }
        }
    }

    # Create the notification report
    factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report=qvarn_report
    )

    # Test getting IDs from notification_reports table
    resp = app.get(API_URL + 'companies/all-ids?table=notification_reports')

    # Verify response
    assert resp.status_code == 200
    assert 'company_ids' in resp.json
    assert 'next_cursor' in resp.json
    assert 'has_more' in resp.json

    # Verify that all expected company IDs are in the response
    company_ids = resp.json['company_ids']
    assert org_id in company_ids
    assert user_org_id in company_ids
    assert company_id in company_ids

    # Test getting IDs from all tables
    resp = app.get(API_URL + 'companies/all-ids')

    # Verify all IDs are present when querying all tables
    company_ids = resp.json['company_ids']
    assert org_id in company_ids
    assert user_org_id in company_ids
    assert company_id in company_ids


def test_get_all_company_ids_from_all_tables(app, db):
    """Test extracting company IDs from all supported tables with exact ID verification."""
    # Create unique IDs for each table to ensure we can verify them exactly
    supplier_id = 'supplier-company-id-test'
    project_client_id = 'project-client-id-test'
    project_creator_id = 'project-creator-id-test'
    project_user_id = 'project-user-id-test'
    status_report_company_id = 'status-report-company-id-test'
    status_report_interested_id = 'status-report-interested-id-test'
    bulk_import_job_id = 'bulk-import-job-id-test'
    internal_project_id = 'internal-project-id-test'
    creditsafe_account_id = 'creditsafe-account-id-test'
    creditsafe_history_id = 'creditsafe-history-id-test'
    report_cache_id = 'report-cache-id-test'
    notification_report_org_id = 'notification-report-org-id-test'
    notification_report_user_id = 'notification-report-user-id-test'
    notification_report_company_id = 'notification-report-company-id-test'

    # 1. Create project with client and creator
    project = factories.create_project(
        db,
        name='Test Project for All Tables',
        client_company_id=project_client_id,
        created_by_org_id=project_creator_id
    )

    # 2. Create supplier
    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=supplier_id
    )

    # 3. Create project user
    factories.create_project_user(
        db,
        project_id=project['id'],
        represented_company_id=project_user_id
    )

    # 4. Create status report
    factories.create_status_report(
        db,
        status='500 OK',
        generated_timestamp=datetime.datetime.now(),
        interested_company_id=status_report_interested_id,
        company_id=status_report_company_id
    )

    # 5. Create bulk import job
    factories.create_bulk_import_job(
        db,
        project_id=project['id'],
        interested_org_id=bulk_import_job_id
    )

    # 6. Create internal project ID
    factories.create_internal_project_id(
        db,
        payload={
            'project_id': project['id'],
            'internal_project_id': 'test-internal-id',
            'company_id': internal_project_id
        }
    )

    # 7. Create creditsafe account
    creditsafe_account = db.meta.tables['creditsafe_account']
    db.session.execute(
        creditsafe_account.insert().values(
            org_id=creditsafe_account_id,
            person_id='test-person-id',
            username='test-username',
            password='test-password',
            state='active'
        )
    )

    # Get the ID from the inserted creditsafe_account
    result = db.session.execute(sa.select([creditsafe_account.c.id]))
    account_id = result.fetchone()[0]

    # 8. Create creditsafe account history
    creditsafe_account_history = db.meta.tables['creditsafe_account_history']
    db.session.execute(
        creditsafe_account_history.insert().values(
            creditsafe_account_id=account_id,
            changed_by_person_id='test-person-id',
            person_id='test-person-id',
            org_id=creditsafe_history_id,
            username='test-username',
            state='active',
            comment='test comment'
        )
    )

    # 9. Create report cache
    report_cache = db.meta.tables['report_cache']
    db.session.execute(
        report_cache.insert().values(
            external_id='test-external-id',
            correlation_id='test-correlation-id',
            expires_at=sa.func.now(),
            interested_org_id=report_cache_id,
            key='test-key',
            provider='test-provider',
            type='test-type',
            value='test-value'
        )
    )

    # 10. Create notification report
    qvarn_report = {
        'kind': 'monthly',
        'org_ids': [notification_report_org_id],
        'users_to_notify': {
            '<EMAIL>': {
                'user_info': {
                    'name': 'Test User',
                    'email': '<EMAIL>',
                },
                'user_org_id': notification_report_user_id,
                'projects': {
                    'project-id': {
                        'project_name': 'Test Project',
                        'companies': {
                            notification_report_company_id: {
                                'company_name': 'Test Company',
                                'old_status': 'incomplete',
                                'new_status': 'stop',
                            }
                        }
                    }
                }
            }
        }
    }

    factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report=qvarn_report
    )

    # Test getting IDs from all tables
    resp = app.get(API_URL + 'companies/all-ids')

    # Verify response
    assert resp.status_code == 200
    company_ids = resp.json['company_ids']

    # Verify all expected IDs are present
    expected_ids = [
        supplier_id,
        project_client_id,
        project_creator_id,
        project_user_id,
        status_report_company_id,
        status_report_interested_id,
        bulk_import_job_id,
        internal_project_id,
        creditsafe_account_id,
        creditsafe_history_id,
        report_cache_id,
        notification_report_org_id,
        notification_report_user_id,
        notification_report_company_id
    ]

    for expected_id in expected_ids:
        assert expected_id in company_ids, f"Expected ID {expected_id} not found in response"

    # Test each table individually to verify exact IDs
    table_id_map = {
        'suppliers': [supplier_id],
        'projects': [project_client_id, project_creator_id],
        'project_users': [project_user_id],
        'status_reports': [status_report_company_id, status_report_interested_id],
        'bulk_import_jobs': [bulk_import_job_id],
        'internal_project_ids': [internal_project_id],
        'creditsafe_account': [creditsafe_account_id],
        'creditsafe_account_history': [creditsafe_history_id],
        'report_cache': [report_cache_id],
        'notification_reports': [
            notification_report_org_id,
            notification_report_user_id,
            notification_report_company_id
        ]
    }

    for table, expected_table_ids in table_id_map.items():
        resp = app.get(API_URL + f'companies/all-ids?table={table}')
        assert resp.status_code == 200
        table_company_ids = resp.json['company_ids']

        for expected_id in expected_table_ids:
            assert expected_id in table_company_ids, \
                f"Expected ID {expected_id} not found in {table} table response"


def test_get_company_ids_all_tables_batch_size(app, db):    # noqa: C901
    """Test pagination with different batch sizes across all supported tables."""
    # Define the tables to test and the IDs to create for each
    tables_to_test = {
        'suppliers': [],
        'projects': [],
        'project_users': [],
        'status_reports': [],
        'bulk_import_jobs': [],
        'internal_project_ids': [],
        'creditsafe_account': [],
        'creditsafe_account_history': [],
        'report_cache': [],
        'notification_reports': []
    }

    # Create a project for related entities
    project = factories.create_project(
        db,
        name='Batch Size Test Project',
        client_company_id='batch-test-client-id'
    )
    tables_to_test['projects'].append('batch-test-client-id')

    # Create test data for each table
    # 1. Suppliers
    for i in range(100):
        supplier_id = f'batch-test-supplier-{i}'
        factories.create_supplier(
            db,
            project_id=project['id'],
            company_id=supplier_id
        )
        tables_to_test['suppliers'].append(supplier_id)

    # 2. Project Users
    for i in range(100):
        company_id = f'batch-test-project-user-{i}'
        factories.create_project_user(
            db,
            project_id=project['id'],
            represented_company_id=company_id
        )
        tables_to_test['project_users'].append(company_id)

    # 3. Status Reports
    for i in range(100):
        company_id = f'batch-test-status-report-company-{i}'
        interested_id = f'batch-test-status-report-interested-{i}'
        factories.create_status_report(
            db,
            status='200 INVESTIGATE',
            generated_timestamp=datetime.datetime.now(),
            company_id=company_id,
            interested_company_id=interested_id
        )
        tables_to_test['status_reports'].append(company_id)
        tables_to_test['status_reports'].append(interested_id)

    # 4. Bulk Import Jobs
    for i in range(100):
        org_id = f'batch-test-bulk-import-{i}'
        factories.create_bulk_import_job(
            db,
            project_id=project['id'],
            interested_org_id=org_id
        )
        tables_to_test['bulk_import_jobs'].append(org_id)

    # 5. Internal Project IDs
    for i in range(100):
        company_id = f'batch-test-internal-project-{i}'
        factories.create_internal_project_id(
            db,
            payload={
                'project_id': project['id'],
                'internal_project_id': f'internal-id-{i}',
                'company_id': company_id
            }
        )
        tables_to_test['internal_project_ids'].append(company_id)

    # 6. Creditsafe Account
    creditsafe_account = db.meta.tables['creditsafe_account']
    for i in range(100):
        org_id = f'batch-test-creditsafe-account-{i}'
        db.session.execute(
            creditsafe_account.insert().values(
                org_id=org_id,
                person_id=f'person-{i}',
                username=f'user-{i}',
                password='password',
                state='active'
            )
        )
        tables_to_test['creditsafe_account'].append(org_id)

    # 7. Creditsafe Account History
    result = db.session.execute(sa.select([creditsafe_account.c.id]))
    account_id = result.fetchone()[0]
    creditsafe_account_history = db.meta.tables['creditsafe_account_history']
    for i in range(100):
        org_id = f'batch-test-creditsafe-history-{i}'
        db.session.execute(
            creditsafe_account_history.insert().values(
                creditsafe_account_id=account_id,
                changed_by_person_id=f'person-{i}',
                person_id=f'person-{i}',
                org_id=org_id,
                username=f'user-{i}',
                state='active',
                comment=f'comment-{i}'
            )
        )
        tables_to_test['creditsafe_account_history'].append(org_id)

    # 8. Report Cache
    report_cache = db.meta.tables['report_cache']
    for i in range(100):
        org_id = f'batch-test-report-cache-{i}'
        db.session.execute(
            report_cache.insert().values(
                external_id=f'external-id-{i}',
                correlation_id=f'correlation-id-{i}',
                expires_at=sa.func.now(),
                interested_org_id=org_id,
                key=f'key-{i}',
                provider='test-provider',
                type='test-type',
                value=f'value-{i}'
            )
        )
        tables_to_test['report_cache'].append(org_id)

    # 9. Notification Reports
    for i in range(100):
        org_id = f'batch-test-notification-org-{i}'
        user_org_id = f'batch-test-notification-user-{i}'
        company_id = f'batch-test-notification-company-{i}'

        qvarn_report = {
            'kind': 'monthly',
            'org_ids': [org_id],
            'users_to_notify': {
                f'test{i}@example.com': {
                    'user_info': {
                        'name': f'Test User {i}',
                        'email': f'test{i}@example.com',
                    },
                    'user_org_id': user_org_id,
                    'projects': {
                        f'project-id-{i}': {
                            'project_name': f'Test Project {i}',
                            'companies': {
                                company_id: {
                                    'company_name': f'Test Company {i}',
                                    'old_status': 'incomplete',
                                    'new_status': 'stop',
                                }
                            }
                        }
                    }
                }
            }
        }

        factories.create_notification_report(
            db,
            generated_timestamp=datetime.datetime.now(),
            qvarn_report=qvarn_report
        )

        tables_to_test['notification_reports'].append(org_id)
        tables_to_test['notification_reports'].append(user_org_id)
        tables_to_test['notification_reports'].append(company_id)

    # Test each table with different batch sizes
    batch_sizes = [1, 2, 3, 4, 5, 10, 50, 100]

    for table_name, expected_ids in tables_to_test.items():
        if not expected_ids:
            continue  # Skip tables with no test data

        # Sort expected IDs to match how they'll be returned from the API
        expected_ids.sort()

        # Store results for each batch size
        all_ids_by_batch_size = {}

        for batch_size in batch_sizes:
            # Retrieve all IDs using the current batch_size
            all_ids = []
            has_more = True
            last_id = ''
            batch_count = 0

            while has_more and batch_count <= 1000:  # Safety limit
                url = (
                    API_URL +
                    f'companies/all-ids?table={table_name}'
                    f'&batch_size={batch_size}&last_id={last_id}'
                )
                resp = app.get(url)

                # Verify response structure
                assert resp.status_code == 200
                assert 'company_ids' in resp.json
                assert 'next_cursor' in resp.json
                assert 'has_more' in resp.json

                # Get batch data
                batch = resp.json['company_ids']
                has_more = resp.json['has_more']

                # Verify batch size is correct
                error_msg = (
                    f"Table {table_name}: Batch {batch_count} incorrect size "
                    f"for batch_size={batch_size}"
                )
                assert len(batch) <= batch_size, error_msg

                # If we have results, update cursor and add to collected IDs
                if batch:
                    last_id = resp.json['next_cursor']
                    all_ids.extend(batch)

                    # If this isn't the first batch, verify no overlap with previous batches
                    if batch_count > 0:
                        previous_ids = all_ids[:-len(batch)]
                        error_msg = (
                            f"Table {table_name}: Batch {batch_count} overlaps "
                            f"with previous batches for batch_size={batch_size}"
                        )
                        assert not set(batch).intersection(set(previous_ids)), error_msg

                batch_count += 1

            # Special case: Check if we need to make one more request for the last ID
            if not has_more and last_id:
                url = (
                    API_URL +
                    f'companies/all-ids?table={table_name}'
                    f'&batch_size={batch_size}&last_id={last_id}'
                )
                resp = app.get(url)

                if resp.json['company_ids']:
                    all_ids.extend(resp.json['company_ids'])

            # Store the results for this batch size
            all_ids_by_batch_size[batch_size] = all_ids

            # Verify all expected IDs are included in the results
            # Note: The API might return more IDs than we created (from existing data)
            # So we check that our expected IDs are a subset of the returned IDs
            missing_ids = set(expected_ids) - set(all_ids)
            assert not missing_ids, \
                f"Table {table_name}: Missing IDs with batch_size={batch_size}: {missing_ids}"

        # Verify all batch sizes retrieved the same IDs
        for i in range(len(batch_sizes) - 1):
            current_batch_size = batch_sizes[i]
            next_batch_size = batch_sizes[i + 1]

            # Verify that IDs are consistent across different batch sizes
            ids_current = set(all_ids_by_batch_size[current_batch_size])
            ids_next = set(all_ids_by_batch_size[next_batch_size])

            error_msg = (
                f"Table {table_name}: IDs with batch_size={current_batch_size} "
                f"don't match those with batch_size={next_batch_size}"
            )
            assert ids_current == ids_next, error_msg


def test_get_company_ids_ordering_and_deduplication(app, db):
    """Test ID ordering and deduplication across tables."""
    batch_size = 2

    # Test 1: Verify ordering (alphabetical)
    project = factories.create_project(
        db, name="Order Test Project", client_company_id="supplier-id-2"
    )
    factories.create_project_user(
        db, project_id=project["id"], represented_company_id="supplier-id-1"
    )

    resp = app.get(API_URL + f"companies/all-ids?batch_size={batch_size}")
    assert resp.status_code == 200
    assert resp.json["company_ids"] == ["supplier-id-1", "supplier-id-2"]

    # Test 2: Verify deduplication (same ID in multiple records)
    project2 = factories.create_project(
        db, name="Dedup Test Project", client_company_id="dedup-id-1"
    )
    # Create multiple project users with same company_id
    factories.create_project_user(
        db, project_id=project2["id"], represented_company_id="dedup-id-1"
    )
    factories.create_project_user(
        db, project_id=project2["id"], represented_company_id="dedup-id-1"
    )
    factories.create_project_user(
        db, project_id=project2["id"], represented_company_id="dedup-id-2"
    )

    # Query for all IDs and verify deduplication
    resp = app.get(API_URL + "companies/all-ids")
    assert resp.status_code == 200
    company_ids = resp.json["company_ids"]

    # Verify dedup-id-1 appears only once despite multiple records
    assert company_ids.count("dedup-id-1") == 1
    assert "dedup-id-2" in company_ids


def test_get_company_ids_duplicates(app, db):
    """Test that duplicate company IDs are properly handled across all tables."""
    # Use a shared company ID that will appear in multiple tables
    shared_company_id = "shared-company-id"
    unique_company_id = "unique-company-id"
    batch_size = 10

    # Create a project that will be used for related entities
    project = factories.create_project(
        db,
        name="Duplicates Test Project",
        client_company_id=shared_company_id,  # This goes to projects table
        created_by_org_id=unique_company_id   # This also goes to projects table
    )

    # 1. Test suppliers table - create multiple suppliers with same company_id
    factories.create_supplier(
        db,
        project_id=project["id"],
        company_id=shared_company_id
    )
    factories.create_supplier(
        db,
        project_id=project["id"],
        company_id=shared_company_id  # Duplicate in suppliers table
    )

    # 2. Test project_users table - create multiple project users with same company_id
    factories.create_project_user(
        db,
        project_id=project["id"],
        represented_company_id=shared_company_id
    )
    factories.create_project_user(
        db,
        project_id=project["id"],
        represented_company_id=shared_company_id  # Duplicate in project_users table
    )
    factories.create_project_user(
        db,
        project_id=project["id"],
        represented_company_id=unique_company_id
    )

    # 3. Test status_reports table - create multiple status reports with same company_id
    factories.create_status_report(
        db,
        status="500 OK",
        generated_timestamp=datetime.datetime.now(),
        company_id=shared_company_id,
        interested_company_id=unique_company_id
    )
    factories.create_status_report(
        db,
        status="500 OK",
        generated_timestamp=datetime.datetime.now(),
        company_id=shared_company_id,  # Duplicate company_id
        interested_company_id=shared_company_id  # Also duplicate interested_company_id
    )

    # 4. Test bulk_import_jobs table - create multiple jobs with same org_id
    factories.create_bulk_import_job(
        db,
        project_id=project["id"],
        interested_org_id=shared_company_id
    )
    factories.create_bulk_import_job(
        db,
        project_id=project["id"],
        interested_org_id=shared_company_id  # Duplicate in bulk_import_jobs table
    )

    # 5. Test internal_project_ids table - create multiple entries with same company_id
    # Note: internal_project_ids has unique constraint on (project_id, company_id)
    # so we need to create a second project to test duplicates
    project2 = factories.create_project(
        db,
        name="Duplicates Test Project 2",
        client_company_id=unique_company_id + "-2"
    )

    factories.create_internal_project_id(
        db,
        payload={
            "project_id": project["id"],
            "internal_project_id": "internal-id-1",
            "company_id": shared_company_id
        }
    )
    factories.create_internal_project_id(
        db,
        payload={
            "project_id": project2["id"],  # Different project to avoid unique constraint
            "internal_project_id": "internal-id-2",
            "company_id": shared_company_id  # Same company_id to test duplicates
        }
    )

    # 6. Test creditsafe_account table - create multiple accounts with same org_id
    creditsafe_account = db.meta.tables["creditsafe_account"]
    db.session.execute(
        creditsafe_account.insert().values(
            org_id=shared_company_id,
            person_id="person-1",
            username="user-1",
            password="pass-1",
            state="active"
        )
    )
    db.session.execute(
        creditsafe_account.insert().values(
            org_id=shared_company_id,  # Duplicate in creditsafe_account table
            person_id="person-2",
            username="user-2",
            password="pass-2",
            state="inactive",
        )
    )

    # Get account IDs for history table
    result = db.session.execute(sa.select([creditsafe_account.c.id]))
    account_ids = [row[0] for row in result.fetchall()]

    # 7. Test creditsafe_account_history table - create multiple history entries with same org_id
    creditsafe_account_history = db.meta.tables["creditsafe_account_history"]
    db.session.execute(
        creditsafe_account_history.insert().values(
            creditsafe_account_id=account_ids[0],
            changed_by_person_id="person-1",
            person_id="person-1",
            org_id=shared_company_id,
            username="user-1",
            state="active",
            comment="comment-1"
        )
    )
    db.session.execute(
        creditsafe_account_history.insert().values(
            creditsafe_account_id=account_ids[1] if len(account_ids) > 1 else account_ids[0],
            changed_by_person_id="person-2",
            person_id="person-2",
            org_id=shared_company_id,  # Duplicate in creditsafe_account_history table
            username="user-2",
            state="active",
            comment="comment-2"
        )
    )

    # 8. Test report_cache table - create multiple cache entries with same org_id
    report_cache = db.meta.tables["report_cache"]
    db.session.execute(
        report_cache.insert().values(
            external_id="external-1",
            correlation_id="correlation-1",
            expires_at=sa.func.now(),
            interested_org_id=shared_company_id,
            key="key-1",
            provider="provider-1",
            type="type-1",
            value="value-1"
        )
    )
    db.session.execute(
        report_cache.insert().values(
            external_id="external-2",
            correlation_id="correlation-2",
            expires_at=sa.func.now(),
            interested_org_id=shared_company_id,  # Duplicate in report_cache table
            key="key-2",
            provider="provider-2",
            type="type-2",
            value="value-2"
        )
    )

    # 9. Test notification_reports table - create multiple reports with same company IDs
    qvarn_report_1 = {
        "kind": "monthly",
        "org_ids": [shared_company_id],
        "users_to_notify": {
            "<EMAIL>": {
                "user_info": {
                    "name": "Test User 1",
                    "email": "<EMAIL>",
                },
                "user_org_id": shared_company_id,
                "projects": {
                    "project-1": {
                        "project_name": "Test Project 1",
                        "companies": {
                            shared_company_id: {
                                "company_name": "Shared Company",
                                "old_status": "incomplete",
                                "new_status": "stop",
                            }
                        }
                    }
                }
            }
        }
    }

    qvarn_report_2 = {
        "kind": "monthly",
        "org_ids": [shared_company_id],  # Duplicate org_id
        "users_to_notify": {
            "<EMAIL>": {
                "user_info": {
                    "name": "Test User 2",
                    "email": "<EMAIL>",
                },
                "user_org_id": shared_company_id,  # Duplicate user_org_id
                "projects": {
                    "project-2": {
                        "project_name": "Test Project 2",
                        "companies": {
                            shared_company_id: {  # Duplicate company_id
                                "company_name": "Shared Company",
                                "old_status": "ok",
                                "new_status": "attention",
                            }
                        }
                    }
                }
            }
        }
    }

    factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report=qvarn_report_1
    )
    factories.create_notification_report(
        db,
        generated_timestamp=datetime.datetime.now(),
        qvarn_report=qvarn_report_2
    )

    # Test each table individually to ensure duplicates are handled correctly
    tables_to_test = [
        "suppliers",
        "projects",
        "project_users",
        "status_reports",
        "bulk_import_jobs",
        "internal_project_ids",
        "creditsafe_account",
        "creditsafe_account_history",
        "report_cache",
        "notification_reports"
    ]

    for table in tables_to_test:
        resp = app.get(API_URL + f"companies/all-ids?table={table}&batch_size={batch_size}")
        assert resp.status_code == 200

        company_ids = resp.json["company_ids"]

        # Verify no duplicates in the response
        assert len(company_ids) == len(set(company_ids)), \
            f"Table {table} returned duplicate company IDs: {company_ids}"

        # Verify shared_company_id appears only once (if it appears at all)
        shared_id_count = company_ids.count(shared_company_id)
        assert shared_id_count <= 1, \
            f"Table {table} returned shared_company_id {shared_id_count} times, expected at most 1"

    # Test all tables together
    resp = app.get(API_URL + f"companies/all-ids?batch_size={batch_size}")
    assert resp.status_code == 200

    all_company_ids = resp.json["company_ids"]

    # Verify no duplicates in the combined response
    assert len(all_company_ids) == len(set(all_company_ids)), \
        f"Combined response returned duplicate company IDs: {all_company_ids}"

    # Verify both shared and unique company IDs are present
    assert shared_company_id in all_company_ids, \
        f"shared_company_id {shared_company_id} not found in combined response"
    assert unique_company_id in all_company_ids, \
        f"unique_company_id {unique_company_id} not found in combined response"

    # Verify shared_company_id appears exactly once in the combined response
    shared_id_count = all_company_ids.count(shared_company_id)
    assert shared_id_count == 1, \
        f"shared_company_id appears {shared_id_count} times in combined response, expected 1"
