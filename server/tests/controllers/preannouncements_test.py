import datetime
import json

import pytest

from boldataapi.fixtures import factories
from boldataapi.schema import (
    CLEANING,
    CONTRACTING,
    DEMOLITION,
    PA_STATUS_CONFIRMED,
    PA_STATUS_CREATED,
    PA_STATUS_REGISTERED,
    PA_STATUS_REJECTED,
    schema_new_preannouncement,
)
from boldataapi.services.preannouncements import get_preannouncement


API_URL = '/api/v1/boldata/'


def get_result(resp, cols):
    assert 200 == resp.status_code
    body = json.loads(resp.body)
    return {k: v for k, v in body.items() if k in cols}


# GET tests


def test_get_preannouncement_ok(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])

    pa = factories.create_preannouncement(
        db,
        status=PA_STATUS_CREATED,
        created_by_supplier_id=s1['id'],
        for_supplier_id=s2['id'],
        project_id=project['id'],
        assigned_to_company_id='any text id really',
        assigned_to_supplier_id=s2['id'],
        assigned_to_time='2022-03-03T00:00:00')

    resp = app.get(API_URL + f"preannouncements/{pa['external_id']}")

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'

    new_pa = json.loads(resp.body)

    # We can not freeze time that is set in DB.
    new_pa.pop('supplier_created_on')

    assert new_pa == {
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'id': pa['external_id'],
        'project_id': project['external_id'],
        'status': 'created',
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00',
        'active_pa_form': None,
    }


def test_get_preannouncement_not_found_raises_error(app):
    resp = app.get(API_URL + 'preannouncements/non-existing', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'non-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_get_preannouncement_server_error(app, mocker, caplog):
    mocker.patch(
        'boldataapi.controllers.preannouncements_controller.get_preannouncement',
        side_effect=Exception('Serious system problem occurred!'))
    resp = app.get(API_URL + 'preannouncements/any-id', expect_errors=True)

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500
    assert 'Serious system problem occurred' in caplog.text


# POST tests


def test_post_preannouncement_validation_error(app):
    preannouncement = {}
    resp = app.post_json(API_URL + 'preannouncements', preannouncement, expect_errors=True)

    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'for_supplier_id': ['required field'],
            'project_id': ['required field'],
            'status': ['required field'],
        },
    }
    assert resp.status_code == 400


def test_post_preannouncement_validation_schema_used(app, mocker):
    preannouncement = {}
    validator_mock = mocker.patch(
        'boldataapi.controllers.preannouncements_controller.PreannouncementValidator')

    app.post_json(API_URL + 'preannouncements', preannouncement, expect_errors=True)
    validator_mock.assert_called_with(schema_new_preannouncement)


@pytest.mark.parametrize("field", [
    "created_by_supplier_id",
    "for_supplier_id",
    "project_id",
])
def test_post_preannouncement_bad_reference(app, db, field):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
    }
    payload[field] = 'oops-wrong'
    resp = app.post_json(API_URL + 'preannouncements', payload, expect_errors=True)

    assert resp.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {
            field: 'oops-wrong',
        },
    }
    assert resp.status_code == 404


def test_post_preannouncement_bad_status(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': 'oops-wrong',
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
    }
    resp = app.post_json(API_URL + 'preannouncements', payload, expect_errors=True)

    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'status': ['unallowed value oops-wrong'],
        },
    }
    assert resp.status_code == 400


def test_post_preannouncement_success(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00'
    }
    resp = app.post_json(API_URL + 'preannouncements', payload)

    assert 201 == resp.status_code

    new_pa = json.loads(resp.body)
    ext_id = new_pa.pop('id')

    # We can not freeze time that is set in DB.
    new_pa.pop('supplier_created_on')

    assert new_pa == {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00',
        'active_pa_form': None,
    }
    assert get_preannouncement(db, external_id=ext_id) is not None


@pytest.mark.parametrize("extra", [
    {},
    {"created_by_supplier_id": ""},
])
def test_post_preannouncement_created_by_supplier_id_is_optional(app, db, extra):
    project = factories.create_project(db)
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': PA_STATUS_CREATED,
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
        **extra
    }
    resp = app.post_json(API_URL + 'preannouncements', payload)

    assert 201 == resp.status_code

    new_pa = json.loads(resp.body)
    new_pa.pop('id')

    # We can not freeze time that is set in DB.
    new_pa.pop('supplier_created_on')

    assert new_pa == {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': None,
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
        'assigned_to_company_id': None,
        'assigned_to_supplier_id': None,
        'assigned_to_time': None,
        'active_pa_form': None,
    }


@pytest.mark.parametrize("extra, error_message", [
    ({}, 'required field'),
    ({"for_supplier_id": ""}, 'empty values not allowed'),
])
def test_post_preannouncement_for_supplier_id_is_mandatory(app, db, extra, error_message):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'project_id': project['external_id'],
        **extra
    }
    resp = app.post_json(API_URL + 'preannouncements', payload, expect_errors=True)

    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'for_supplier_id': [error_message],
        },
    }
    assert resp.status_code == 400


@pytest.mark.parametrize("extra, error_message", [
    ({}, 'required field'),
    ({"project_id": ""}, 'empty values not allowed'),
])
def test_post_preannouncement_project_id_is_mandatory(app, db, extra, error_message):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        **extra
    }
    resp = app.post_json(API_URL + 'preannouncements', payload, expect_errors=True)

    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'project_id': [error_message],
        },
    }
    assert resp.status_code == 400


@pytest.mark.parametrize("extra, error_message", [
    ({}, 'required field'),
    ({"status": ""}, 'empty values not allowed'),
])
def test_post_preannouncement_status_is_mandatory(app, db, extra, error_message):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
        **extra
    }
    resp = app.post_json(API_URL + 'preannouncements', payload, expect_errors=True)

    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'status': [error_message],
        },
    }
    assert resp.status_code == 400


def test_put_preannouncement_success(app, db):
    pa = factories.create_preannouncement(db, status=PA_STATUS_CREATED)
    pa_updated = {
        'status': PA_STATUS_CONFIRMED
    }
    update_pa = app.put_json(API_URL + 'preannouncements/' + pa["external_id"], pa_updated)
    updated_pa_body = update_pa.json

    assert updated_pa_body["status"] == PA_STATUS_CONFIRMED
    assert 200 == update_pa.status_code


def test_put_preannouncement_not_found(app, db):
    resp = app.put_json(API_URL + 'preannouncements/no-such-id', {}, expect_errors=True)
    assert resp.status_code == 404
    assert resp.json == {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'no-such-id'}
    }


def test_put_preannouncement_empty_form(app, db):
    pa = factories.create_preannouncement(db)
    resp = app.put_json(API_URL + 'preannouncements/' + pa["external_id"], {})
    assert resp.status_code == 200


def test_put_preannouncement_empty_form_when_pa_form_exists(app, db):
    pa = factories.create_preannouncement(db)
    factories.create_pa_form(db, pa['id'])
    resp = app.put_json(API_URL + 'preannouncements/' + pa["external_id"], {})
    assert resp.status_code == 200


def test_put_preannouncement_unknown_field(app, db):
    new_pa = factories.create_preannouncement(db)
    pa_updated = {
        'status': 'created',
        'estatus': 'nese',
        'bstatus': None,
    }
    resp = app.put_json(API_URL + 'preannouncements/' + new_pa["external_id"],
                        pa_updated, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'estatus': ['unknown field'],
            'bstatus': ['unknown field'],
        },
    }


@pytest.mark.parametrize("status, error", [
    ('oops-wrong', 'unallowed value oops-wrong'),
    ('', 'empty values not allowed'),
    (None, 'null value not allowed'),
])
def test_put_preannouncement_bad_status(app, db, status, error):
    new_pa = factories.create_preannouncement(db)
    pa_updated = {
        'status': status,
    }
    resp = app.put_json(API_URL + 'preannouncements/' + new_pa["external_id"],
                        pa_updated, expect_errors=True)
    assert resp.status_code == 400
    assert resp.json == {
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
        'error': {
            'status': [error],
        },
    }


@pytest.mark.parametrize('field', [
    'created_by_supplier_id',
    'for_supplier_id',
    'project_id',
    'assigned_to_supplier_id',
])
def test_put_preannouncement_bad_reference(app, db, field):
    pa = factories.create_preannouncement(db)
    payload = {
        field: 'oops-wrong',
    }
    resp = app.put_json(API_URL + 'preannouncements/' + pa["external_id"], payload,
                        expect_errors=True)
    assert resp.json == {
        'error_code': 'NotFound',
        'message': 'Not found',
        'error': {
            field: 'oops-wrong',
        },
    }
    assert resp.status_code == 404


@pytest.mark.parametrize('field', [
    ('created_by_supplier_id'),
    ('for_supplier_id'),
    ('project_id'),
    ('assigned_to_supplier_id'),
])
def test_put_preannouncement_good_reference(app, db, field):
    project = factories.create_project(db)
    pa = factories.create_preannouncement(db)
    if field == 'project_id':
        thingy = project
    else:
        thingy = factories.create_supplier(db, project_id=project['id'])
    payload = {
        field: thingy['external_id'],
    }
    resp = app.put_json(API_URL + 'preannouncements/' + pa["external_id"], payload)
    updated_pa_body = resp.json
    assert updated_pa_body[field] == thingy['external_id']
    assert resp.status_code == 200


def test_delete_preannouncement(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    pa = {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
    }
    resp = app.post_json(API_URL + 'preannouncements', pa)

    assert 201 == resp.status_code

    new_pa = json.loads(resp.body)
    delete_pa = app.delete(API_URL + 'preannouncements/' + new_pa["id"])

    assert 200 == delete_pa.status_code

    get_deleted_pa = app.get(API_URL + 'preannouncements/'+new_pa["id"], expect_errors=True)

    assert 404 == get_deleted_pa.status_code


def test_delete_unknown_preannouncement(app):
    delete_pa = app.delete(API_URL + 'preannouncements/1111', expect_errors=True)

    assert 404 == delete_pa.status_code


def test_register_pa_form(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
    }
    resp = app.post_json(API_URL + 'preannouncements', payload)
    new_pa = json.loads(resp.body)

    # We can not freeze time that is set in DB.
    new_pa.pop('supplier_created_on')

    update_payload = {
        'company_name': 'Migrationsverket',
        'company_gov_org_id': 'company_1',
        'company_id_type': 'type_1',
        'company_country': 'EN',
        'has_permanent_establishment': True,
        'is_one_man_company': False,
        'has_collective_agreement': True,
        'collective_agreement_name': 'Collective agreement 1',
        'buyer_name': 'Trafikverket',
        'buyer_gov_org_id': 'buyer_gov_org_id',
        'buyer_id_type': 'type-buyer',
        'buyer_country': 'EN',
        'foreman_is_on_site': True,
        'foreman_first_name': 'John',
        'foreman_last_name': 'Johnson',
        'foreman_phone_number': '1111111',
        'foreman_email': '<EMAIL>',
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00',
        'contract_type': CONTRACTING,
        'contract_start_date': '2022-03-03',
        'contract_end_date': '2023-03-03',
        'contract_work_areas': [CLEANING, DEMOLITION],
        'informant_supplier_email': '<EMAIL>',
        'informant_supplier_first_name': 'Peter',
        'informant_supplier_last_name': 'Peterson',
        'informant_supplier_phone': '0701234567',
        'submitted_by_first_name': 'Ilkin',
        'submitted_by_last_name': 'Mammadov',
        'submitted_by_phone': None,
        'submitted_by_email': '<EMAIL>',
        'last_assigned_to_supplier': s1['external_id'],
        'last_assigned_to_company': 'Company 1',
        'last_assigned_to_business_id': 'ID',
        'last_assigned_to_business_id_type': None,
        'last_assigned_to_time': '2022-03-03T00:00:00',
    }
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + new_pa['id'], update_payload)
    updated_pa = json.loads(update_pa_resp.body)

    # We can not freeze time that is set in DB.
    updated_pa.pop('supplier_created_on')

    assert 200 == update_pa_resp.status_code
    expected_result = {
        'id': new_pa['id'],
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
        'foreman_is_on_site': True,
        'foreman_first_name': 'John',
        'foreman_last_name': 'Johnson',
        'foreman_phone_number': '1111111',
        'foreman_email': '<EMAIL>',
        'buyer_name': 'Trafikverket',
        'buyer_country': 'EN',
        'buyer_id_type': 'type-buyer',
        'buyer_gov_org_id': 'buyer_gov_org_id',
        'collective_agreement_name': 'Collective agreement 1',
        'has_collective_agreement': True,
        'informant_supplier_email': '<EMAIL>',
        'informant_supplier_first_name': 'Peter',
        'informant_supplier_last_name': 'Peterson',
        'informant_supplier_phone': '0701234567',
        'is_one_man_company': False,
        'has_permanent_establishment': True,
        'company_name': 'Migrationsverket',
        'company_country': 'EN',
        'company_id_type': 'type_1',
        'company_gov_org_id': 'company_1',
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00',
        'contract_type': CONTRACTING,
        'contract_start_date': '2022-03-03',
        'contract_end_date': '2023-03-03',
        'contract_work_areas': [CLEANING, DEMOLITION],
        'confirmed_name': None,
        'confirmed_gov_org_id': None,
        'confirmed_id_type': None,
        'confirmed_time': None,
        'confirmed_country': None,
        'rejected_name': None,
        'rejected_gov_org_id': None,
        'rejected_id_type': None,
        'rejected_time': None,
        'rejected_country': None,
        'submitted_time': None,
        'submitted_by_first_name': 'Ilkin',
        'submitted_by_last_name': 'Mammadov',
        'submitted_by_phone': None,
        'submitted_by_email': '<EMAIL>',
        'active_pa_form': None,
        'last_assigned_to_supplier': s1['external_id'],
        'last_assigned_to_company': 'Company 1',
        'last_assigned_to_business_id': 'ID',
        'last_assigned_to_business_id_type': None,
        'last_assigned_to_time': '2022-03-03T00:00:00',
    }
    assert updated_pa == expected_result

    get_pa_resp = app.get(API_URL + 'preannouncements/' + new_pa['id'])
    pa_from_get = json.loads(get_pa_resp.body)

    # We can not freeze time that is set in DB.
    pa_from_get.pop('supplier_created_on')

    expected_result['active_pa_form'] = pa_from_get['active_pa_form']

    assert pa_from_get == expected_result


def test_register_pa_form_after_restarting_pa(app, db, caplog):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    pa = factories.create_preannouncement(db, created_by_supplier_id=s1['id'],
                                          for_supplier_id=s2['id'], project_id=project['id'])
    old_pa_form = factories.create_pa_form(db, pa['id'])

    # Restart the PA
    caplog.set_level('INFO')
    update_payload = {
        'status': PA_STATUS_CREATED,
        'active_pa_form': None,
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_company_id': s2['company_id'],
        'last_assigned_to_time': datetime.datetime.utcnow().isoformat(),
        'last_assigned_to_supplier': pa.get('assigned_to_supplier_id'),
        'last_assigned_to_company': pa.get('assigned_to_company_id'),
        'last_assigned_to_business_id': pa.get('buyer_gov_org_id'),
        'last_assigned_to_business_id_type': pa.get('buyer_id_type'),
    }
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'], update_payload)

    # Now register again
    update_payload = {
        'company_name': 'Migrationsverket',
        'company_gov_org_id': 'company_1',
        'company_id_type': 'type_1',
        'company_country': 'EN',
        'has_permanent_establishment': True,
        'is_one_man_company': False,
        'has_collective_agreement': True,
        'collective_agreement_name': 'Collective agreement 1',
        'buyer_name': 'Trafikverket',
        'buyer_gov_org_id': 'buyer_gov_org_id',
        'buyer_id_type': 'type-buyer',
        'buyer_country': 'EN',
        'foreman_is_on_site': True,
        'foreman_first_name': 'John',
        'foreman_last_name': 'Johnson',
        'foreman_phone_number': '1111111',
        'foreman_email': '<EMAIL>',
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00',
        'contract_type': CONTRACTING,
        'contract_start_date': '2022-03-03',
        'contract_end_date': '2023-03-03',
        'contract_work_areas': [CLEANING, DEMOLITION],
        'informant_supplier_email': '<EMAIL>',
        'informant_supplier_first_name': 'Peter',
        'informant_supplier_last_name': 'Peterson',
        'informant_supplier_phone': '0701234567',
        'submitted_by_first_name': 'Ilkin',
        'submitted_by_last_name': 'Mammadov',
        'submitted_by_phone': None,
        'submitted_by_email': '<EMAIL>',
        'last_assigned_to_supplier': s1['external_id'],
        'last_assigned_to_company': 'Company 1',
        'last_assigned_to_business_id': 'ID',
        'last_assigned_to_business_id_type': None,
        'last_assigned_to_time': '2022-03-03T00:00:00',
    }
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'], update_payload)
    updated_pa = json.loads(update_pa_resp.body)

    # We can not freeze time that is set in DB.
    updated_pa.pop('supplier_created_on')

    assert 200 == update_pa_resp.status_code
    expected_result = {
        'id': pa['external_id'],
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
        'foreman_is_on_site': True,
        'foreman_first_name': 'John',
        'foreman_last_name': 'Johnson',
        'foreman_phone_number': '1111111',
        'foreman_email': '<EMAIL>',
        'buyer_name': 'Trafikverket',
        'buyer_country': 'EN',
        'buyer_id_type': 'type-buyer',
        'buyer_gov_org_id': 'buyer_gov_org_id',
        'collective_agreement_name': 'Collective agreement 1',
        'has_collective_agreement': True,
        'informant_supplier_email': '<EMAIL>',
        'informant_supplier_first_name': 'Peter',
        'informant_supplier_last_name': 'Peterson',
        'informant_supplier_phone': '0701234567',
        'is_one_man_company': False,
        'has_permanent_establishment': True,
        'company_name': 'Migrationsverket',
        'company_country': 'EN',
        'company_id_type': 'type_1',
        'company_gov_org_id': 'company_1',
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00',
        'contract_type': CONTRACTING,
        'contract_start_date': '2022-03-03',
        'contract_end_date': '2023-03-03',
        'contract_work_areas': [CLEANING, DEMOLITION],
        'confirmed_name': None,
        'confirmed_gov_org_id': None,
        'confirmed_id_type': None,
        'confirmed_time': None,
        'confirmed_country': None,
        'rejected_name': None,
        'rejected_gov_org_id': None,
        'rejected_id_type': None,
        'rejected_time': None,
        'rejected_country': None,
        'submitted_time': None,
        'submitted_by_first_name': 'Ilkin',
        'submitted_by_last_name': 'Mammadov',
        'submitted_by_phone': None,
        'submitted_by_email': '<EMAIL>',
        'active_pa_form': None,
        'last_assigned_to_supplier': s1['external_id'],
        'last_assigned_to_company': 'Company 1',
        'last_assigned_to_business_id': 'ID',
        'last_assigned_to_business_id_type': None,
        'last_assigned_to_time': '2022-03-03T00:00:00',
    }
    assert updated_pa == expected_result

    get_pa_resp = app.get(API_URL + 'preannouncements/' + pa['external_id'])
    pa_from_get = json.loads(get_pa_resp.body)

    # We can not freeze time that is set in DB.
    pa_from_get.pop('supplier_created_on')

    expected_result['active_pa_form'] = pa_from_get['active_pa_form']

    assert pa_from_get == expected_result

    # Make sure a new PA form was created
    assert pa_from_get['active_pa_form'] != old_pa_form['external_id']


def test_update_pa_form(app, db):
    pa = factories.create_preannouncement(db)
    factories.create_pa_form(db, pa['id'])

    update_payload = {
        'confirmed_country': 'SE',
        'confirmed_name': 'Client company',
        'confirmed_gov_org_id': '5555-123456',
        'confirmed_id_type': 'gov_org_id',
        'confirmed_time': datetime.datetime.utcnow().isoformat(),
    }

    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'], update_payload)

    assert update_payload == get_result(
        update_pa_resp,
        cols=['confirmed_country', 'confirmed_name', 'confirmed_gov_org_id',
              'confirmed_id_type', 'confirmed_time'])

    update_payload = {
        'rejected_country': 'FI',
        'rejected_name': 'Some parent supplier',
        'rejected_gov_org_id': '5555-123457',
        'rejected_id_type': 'gov_org_id',
        'rejected_time': datetime.datetime.utcnow().isoformat(),
        'confirmed_country': None,
        'confirmed_name': None,
        'confirmed_time': None,
        'confirmed_gov_org_id': None,
        'confirmed_id_type': None,
        'submitted_time': datetime.datetime.utcnow().isoformat(),
    }

    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'], update_payload)

    assert update_payload == get_result(
        update_pa_resp,
        cols=[
            'confirmed_country',
            'confirmed_name',
            'confirmed_gov_org_id',
            'confirmed_id_type',
            'confirmed_time',
            'rejected_country',
            'rejected_name',
            'rejected_gov_org_id',
            'rejected_id_type',
            'rejected_time',
            'submitted_time',
        ])


def test_update_pa_form_last_assigned_to(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    pa = factories.create_preannouncement(db, project_id=project['id'])
    factories.create_pa_form(db, pa['id'])

    update_payload = {
        'last_assigned_to_supplier': s1['external_id'],
    }
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'], update_payload)
    assert update_payload == get_result(
        update_pa_resp,
        cols=['last_assigned_to_supplier'])


def test_put_pa_form_and_set_permanent_establishment(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    payload = {
        'status': PA_STATUS_CREATED,
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'project_id': project['external_id'],
    }
    resp = app.post_json(API_URL + 'preannouncements', payload)

    new_pa = json.loads(resp.body)

    update_payload = {
        'company_gov_org_id': 'company_1',
        'company_id_type': 'type_1',
        'company_country': 'EN',
        'company_name': 'Kungliga biblioteket',
        'has_permanent_establishment': False,
        'is_one_man_company': False,
        'has_collective_agreement': True,
        'collective_agreement_name': 'Collective agreement 1',
        'buyer_name': 'Trafikverket',
        'buyer_gov_org_id': 'buyer_gov_org_id',
        'buyer_id_type': 'type-buyer',
        'buyer_country': 'EN',
    }
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + new_pa['id'], update_payload)
    updated_pa = json.loads(update_pa_resp.body)

    assert 200 == update_pa_resp.status_code
    assert updated_pa["has_permanent_establishment"] == False


@pytest.mark.parametrize('field_name, value', [
    ('status', 1),
    ('created_by_supplier_id', 123),
    ('buyer_country', 1),
    ('buyer_id_type',  3),
    ('buyer_name', 123),
    ('buyer_gov_org_id', ''),
    ('has_collective_agreement', 'string'),
    ('is_one_man_company', 'string'),
    ('has_permanent_establishment', 'string'),
    ('collective_agreement_name', True),
    ('company_name', 321),
    ('company_country', 1),
    ('company_id_type', True),
    ('company_gov_org_id', ''),
    ('foreman_is_on_site', 'str'),
    ('foreman_first_name', 2),
    ('foreman_last_name', 3),
    ('foreman_phone_number', 4),
    ('foreman_email', 5),
    ('last_assigned_to_time', 'next week sometime'),
])
def test_put_invalid_values_when_creating_pa_form(app, db, field_name, value):
    '''A smoke test for parameter validation.

    Look for more thorough validation test under
    /server/tests/schema/preannouncements_test.py
    '''
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    pa = factories.create_preannouncement(
        db, created_by_supplier_id=s1['id'], for_supplier_id=s2['id'],
        project_id=project['id'])

    update_payload = {
        # some fields are required, if we skip them we'll get unrelated errors
        'company_gov_org_id': 'XXXX-XXX',
        'company_country': 'SWE',
        'company_name': 'Strålmyndigheten',
        'buyer_gov_org_id': 'XXXX-XXX',
        'buyer_country': 'SWE',
        'buyer_name': 'Trafikverket',
        field_name: value,
    }
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'],
                                  update_payload, expect_errors=True)

    assert 400 == update_pa_resp.status_code
    assert update_pa_resp.json['message'] == 'Failed to validate parameters'
    assert update_pa_resp.json['error_code'] == 'ParameterValidationFailed'
    assert list(update_pa_resp.json['error']) == [field_name]


@pytest.mark.parametrize('field_name, value', [
    ('confirmed_country', 1),
    ('confirmed_name', 1),
    ('confirmed_time', 'last week sometime'),
    ('rejected_country', 1),
    ('rejected_name', 3),
    ('rejected_time', 'never ever'),
    ('last_assigned_to_time', 'next week sometime'),
])
def test_put_invalid_values_when_updating_pa_form(app, db, field_name, value):
    '''A smoke test for parameter validation.

    Look for more thorough validation test under
    /server/tests/schema/preannouncements_test.py
    '''
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    pa = factories.create_preannouncement(
        db, created_by_supplier_id=s1['id'], for_supplier_id=s2['id'],
        project_id=project['id'])
    factories.create_pa_form(db, pa['id'])

    update_payload = {field_name: value}
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'],
                                  update_payload, expect_errors=True)

    assert 400 == update_pa_resp.status_code
    assert update_pa_resp.json['message'] == 'Failed to validate parameters'
    assert update_pa_resp.json['error_code'] == 'ParameterValidationFailed'
    assert update_pa_resp.json['error']


@pytest.mark.parametrize(('field_name'), [
    'assigned_to_company_id',
    'assigned_to_supplier_id',
    'assigned_to_time',
])
def test_put_nullify_values(app, db, field_name):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    pa = factories.create_preannouncement(
        db,
        assigned_to_company_id='<some ID>',
        assigned_to_supplier_id=s1['id'],
        assigned_to_time=datetime.datetime.utcnow().isoformat()
    )
    payload = {
        field_name: None,
    }
    resp = app.put_json(API_URL + 'preannouncements/' + pa["external_id"], payload)

    assert resp.json[field_name] is None


# QUERY tests

# NB: No test for query operators (__ne, __any, etc.) as table query filtering
# is tested for projects (for example, test_projects_query__ne()).

@pytest.mark.parametrize(('q_field', 'db_field'), [
    ('id', 'external_id'),
    ('status', 'status'),
    ('assigned_to_company_id', 'assigned_to_company_id'),
    ('assigned_to_time', 'assigned_to_time'),
    ('assigned_to_supplier_id', 'assigned_to_supplier_id'),
    ('created_by_supplier_id', 'created_by_supplier_id'),
    ('for_supplier_id', 'for_supplier_id'),
    ('project_id', 'project_id'),
])
def test_preannouncements_query(app, db, q_field, db_field):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])

    pa = factories.create_preannouncement(
        db,
        status=PA_STATUS_CREATED,
        created_by_supplier_id=s1['id'],
        for_supplier_id=s2['id'],
        project_id=project['id'],
        assigned_to_company_id='any text id really',
        assigned_to_supplier_id=s2['id'],
        assigned_to_time='2022-03-03T00:00:00')

    pa = get_preannouncement(db, preannouncement_id=pa['id'])
    # mangle pa['assigned_to_time'] to prevent error: datetime not JSON serializeble
    pa['assigned_to_time'] = '2022-03-03T00:00:00'

    query = {q_field: pa[db_field]}

    response = app.get(f'{API_URL}preannouncements/query?q={json.dumps(query)}')
    result = response.json['resources']

    expected = [{
        'assigned_to_company_id': 'any text id really',
        'assigned_to_supplier_id': s2['external_id'],
        'assigned_to_time': '2022-03-03T00:00:00',
        'created_by_supplier_id': s1['external_id'],
        'for_supplier_id': s2['external_id'],
        'id': pa['external_id'],
        'project_id': project['external_id'],
        'status': PA_STATUS_CREATED,
    }]
    assert result == expected
    assert response.status_code == 200


def test_preannouncements_query_result_multiple(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])
    s3 = factories.create_supplier(db, project_id=project['id'])

    pa1 = factories.create_preannouncement(
        db,
        status=PA_STATUS_CREATED,
        created_by_supplier_id=s1['id'],
        for_supplier_id=s2['id'],
        project_id=project['id'])
    pa2 = factories.create_preannouncement(
        db,
        status=PA_STATUS_CREATED,
        created_by_supplier_id=s2['id'],
        for_supplier_id=s1['id'],
        project_id=project['id'])
    factories.create_preannouncement(
        db,
        status=PA_STATUS_REJECTED,
        created_by_supplier_id=s1['id'],
        for_supplier_id=s3['id'],
        project_id=project['id'])

    query = {'status': PA_STATUS_CREATED}

    response = app.get(f'{API_URL}preannouncements/query?q={json.dumps(query)}')
    result = response.json['resources']

    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([pa1['external_id'], pa2['external_id']])


def test_preannouncements_query_result_empty(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])

    factories.create_preannouncement(
        db,
        status=PA_STATUS_CREATED,
        created_by_supplier_id=s1['id'],
        for_supplier_id=s2['id'],
        project_id=project['id'])

    query = {'status': PA_STATUS_REJECTED}

    response = app.get(f'{API_URL}preannouncements/query?q={json.dumps(query)}')
    result = response.json['resources']

    assert result == []


def test_put_preannouncement_restart(app, db):
    project = factories.create_project(db)
    s1 = factories.create_supplier(db, project_id=project['id'])
    s2 = factories.create_supplier(db, project_id=project['id'])

    pa = factories.create_preannouncement(
        db,
        status=PA_STATUS_REGISTERED,
        created_by_supplier_id=s1['id'],
        for_supplier_id=s2['id'],
        project_id=project['id']
    )
    pa = get_preannouncement(db, preannouncement_id=pa['id'])
    pa_updated = {
        'status': PA_STATUS_CREATED,
        'active_pa_form': None,
        'assigned_to_supplier_id': pa['for_supplier_id'],
        'assigned_to_company_id': pa['for_supplier_id'],
    }
    update_pa = app.put_json(API_URL + 'preannouncements/' + pa["external_id"], pa_updated)
    updated_pa_body = update_pa.json
    assert updated_pa_body["status"] == PA_STATUS_CREATED
    assert updated_pa_body["active_pa_form"] is None
    assert 200 == update_pa.status_code


def test_update_pa_active_form_id(app, db):
    project = factories.create_project(db)
    factories.create_supplier(db, project_id=project['id'])
    pa = factories.create_preannouncement(db, project_id=project['id'])
    pa_form = factories.create_pa_form(db, pa['id'])

    update_payload = {
        'active_pa_form': pa_form['external_id'],
    }
    update_pa_resp = app.put_json(API_URL + 'preannouncements/' + pa['external_id'], update_payload)
    assert update_pa_resp.json.get('active_pa_form') == pa_form['external_id']

    pa = get_preannouncement(db, preannouncement_id=pa['id'])
    assert pa['active_pa_form'] == pa_form['external_id']
