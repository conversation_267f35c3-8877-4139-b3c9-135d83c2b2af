import datetime
import json
import operator
import uuid
from operator import itemgetter

import pytest
import sqlalchemy as sa
from freezegun import freeze_time

from boldataapi.fixtures import factories
from boldataapi.schema import (
    DEMOLITION,
    LINKED,
    MAIN_CONTRACTOR_ROLE,
    PA_STATUS_CREATED,
    QVARN_COMPAT_FIELDS_FOR_PROJECTS,
    schema_existing_project,
    schema_new_project,
    STATUS_ATTENTION,
    STATUS_INCOMPLETE,
    STATUS_INVESTIGATE,
    STATUS_OK,
    STATUS_STOP,
    SUPERVISOR_ROLE,
    SUPPLIER_ROLE,
    UNLINKED,
    VISITOR,
)
from boldataapi.services.project_supplier_comments import mark_comment_as_deleted
from boldataapi.services.projects import (
    ACTIVE,
    CLOSED,
    DRAFT,
    get_project_ids,
    get_tax_id,
    NOT_CLOSED,
    PROJECT_ID_TYPE,
    TAX_ID,
)
from boldataapi.storage.db import INTERNAL_PROJECT_IDS_TABLE


API_URL = '/api/v1/boldata/'


# GET tests

def test_get_projects(config, app, db):
    factories.create_project(
        db,
        name='Test project 1',
        external_id='EXTERNAL_ID_2'
    )
    factories.create_project(
        db,
        name='Test project 1',
        external_id='EXTERNAL_ID_1'
    )

    resp = app.get(API_URL + 'projects')

    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    expected = {
        'resource_type': 'project',
        'resources': [
            {'id': 'EXTERNAL_ID_1'},
            {'id': 'EXTERNAL_ID_2'}
        ],
    }
    result = json.loads(resp.body)
    result['resources'] = sorted(result['resources'], key=itemgetter('id'))
    assert result == expected


def test_get_not_found_raises_error(app):
    resp = app.get(API_URL + 'projects/non-existing', expect_errors=True)
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'non-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_get_returns_ok_status(app, db):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id='test tax id',
        client_company_id='client-comp-id',
        client_contact_person_id='person-id',
        client_contact_person_email='<EMAIL>',
        created_by_org_id='created-be-some-org-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
        pa_form_enabled=True,
        added_client_confirmed=True,
        added_client_can_view=True,
    )
    resp = app.get(API_URL + 'projects/%s' % project['external_id'])
    assert 200 == resp.status_code
    assert resp.content_type == 'application/json'
    expected = {
        'id': project['external_id'],
        'names': ['Test project'],
        'project_responsible_org': 'client-comp-id',
        'project_responsible_person': None,
        'project_ids': [
            {
                'project_id': 'test tax id',
                'project_id_type': 'tax_id'
            }
        ],
        'start_date': '2020-01-01',
        'end_date': '2020-12-01',
        'state': ACTIVE,
        'pa_form_enabled': True,
        'created_by_org_id': 'created-be-some-org-id',
        'client_contact_person_id': 'person-id',
        'client_contact_person_email': '<EMAIL>',
        'added_client_confirmed': True,
        'project_creator_role': None,
        'added_client_can_view': True,
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_PROJECTS)
    assert json.loads(resp.body) == expected


def test_get_sub_resource_returns_ok_status(app, db):
    project = factories.create_project(db)
    resp = app.get(API_URL + 'projects/%s/sync' % project['external_id'])

    assert 200 == resp.status_code
    assert resp.json == {}


@pytest.mark.parametrize(
    "tax_id,project_ids_expected", [
        (None, []),
        ('', []),
        ('test-tax-id', [
            {
                'project_id_type': TAX_ID,
                'project_id': 'test-tax-id',
            }
        ])
    ])
def test_get_returns_ok_with_tax_id(app, db, tax_id, project_ids_expected):
    project = factories.create_project(
        db,
        client_company_id='client-comp-id',
        tax_id=tax_id,
    )
    resp = app.get(API_URL + 'projects/%s' % project['external_id'])
    assert 200 == resp.status_code
    new_project = json.loads(resp.body)
    new_project['project_ids'] == project_ids_expected


@pytest.mark.parametrize(['pa_form_enabled', 'expected'], [
    (None, False),
    (False, False),
    (True, True),
])
def test_get_pa_form_enabled(app, db, pa_form_enabled, expected):
    project = factories.create_project(
        db,
        pa_form_enabled=pa_form_enabled,
    )
    resp = app.get(API_URL + 'projects/%s' % project['external_id'])
    assert 200 == resp.status_code
    new_project = json.loads(resp.body)
    new_project['pa_form_enabled'] == expected


def test_get_server_error(app, mocker, caplog):
    mocker.patch(
        'boldataapi.controllers.projects_controller.get_project',
        side_effect=Exception('Serious system problem occurred!'))
    resp = app.get(API_URL + 'projects/any-id', expect_errors=True)

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500
    assert 'Serious system problem occurred' in caplog.text


def test_post_server_error(app, mocker, caplog):
    mocker.patch(
        'boldataapi.controllers.projects_controller.create_project',
        side_effect=Exception('Serious system problem occurred!'))
    data = {
        'project_responsible_org': 'test-org-id',
        'state': ACTIVE,
        'names': ['Test project'],
        'project_ids': [
            {
                'project_id_type': TAX_ID,
                'project_id': 'test-tax-id',
            }
        ]
    }

    resp = app.post_json(API_URL + 'projects', data, expect_errors=True)

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500

    assert 'Serious system problem occurred' in caplog.text


def test_post_success(app, db):
    project_ids = [
        {
            'project_id_type': TAX_ID,
            'project_id': 'test-tax-id',
        },
    ]

    project = {
        'names': ['Test project'],
        'project_responsible_org': 'client-comp-id',
        'client_contact_person_id': 'person-id',
        'client_contact_person_email': '<EMAIL>',
        'created_by_org_id': 'created-by-some-org-id',
        'start_date': '2020-01-01',
        'end_date': '2020-12-01',
        'state': ACTIVE,
        'project_ids': project_ids,
        'project_responsible_person': None,
        'pa_form_enabled': True,
        'added_client_confirmed': False,
        'project_creator_role': 'client',
        'added_client_can_view': False,
    }
    resp = app.post_json(API_URL + 'projects', project)
    assert 201 == resp.status_code
    new_project = json.loads(resp.body)
    new_project.pop('id')
    project.update(QVARN_COMPAT_FIELDS_FOR_PROJECTS)
    assert new_project == project


def test_post_success_project_ids(app, db):
    project_ids = [
        {
            'project_id_type': TAX_ID,
            'project_id': 'test-tax-id',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        },
    ]
    project = {
        'names': ['Test project'],
        'project_responsible_org': 'test-company-id',
        'state': ACTIVE,
        'project_ids': project_ids,
    }
    resp = app.post_json(API_URL + 'projects', project)
    assert 201 == resp.status_code
    new_project = json.loads(resp.body)
    assert new_project['project_ids'] == [
        {
            'project_id_type': TAX_ID,
            'project_id': 'test-tax-id',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        },
    ]


def test_post_validation_error(app):
    project = {}
    resp = app.post_json(API_URL + 'projects', project, expect_errors=True)

    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_post_validation_schema_used(app, mocker):
    project = {}
    validator_mock = mocker.patch(
        'boldataapi.controllers.projects_controller.ProjectValidator')

    app.post_json(API_URL + 'projects', project, expect_errors=True)
    validator_mock.assert_called_with(schema_new_project)


def test_company_gets_own_internal_project_ids(app, db):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id='test tax id',
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    project_id = {
        'company_id': 'client-comp-id',
        'internal_project_id': 'test1_id',
        'project_id': project['id'],
    }

    factories.create_internal_project_id(db, project_id)

    # create project ids for other company
    table_internal_project_ids = db.meta.tables[INTERNAL_PROJECT_IDS_TABLE]
    query = sa.insert(table_internal_project_ids).values(
        id=str(uuid.uuid4()),
        project_id=project['id'],
        internal_project_id='project_id_other_company',
        company_id='other-company-id',
    )
    db.session.execute(query)

    resp = app.get(API_URL + 'projects/%s' % project['external_id'])
    assert 200 == resp.status_code
    new_project = json.loads(resp.body)

    assert new_project['id'] == project['external_id']
    assert new_project['project_ids'] == [
        {
            'project_id_type': TAX_ID,
            'project_id': 'test tax id',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test1_id',
        },
    ]


@pytest.mark.parametrize('created_by_org_id', [
    'created-by-some-org-id',
    None,
])
def test_created_by_org_gets_own_internal_project_ids(app, created_by_org_id):
    project_ids = [
        {
            'project_id_type': TAX_ID,
            'project_id': 'internal-project-id',
        },
    ]
    project = {
        'names': ['Test project'],
        'project_responsible_org': 'client-some-org-id',
        'client_contact_person_id': 'person-id',
        'client_contact_person_email': '<EMAIL>',
        'created_by_org_id': created_by_org_id,
        'start_date': '2020-01-01',
        'end_date': '2020-12-01',
        'state': ACTIVE,
        'project_ids': project_ids,
        'project_responsible_person': None,
        'pa_form_enabled': False,
        'added_client_confirmed': False,
        'project_creator_role': MAIN_CONTRACTOR_ROLE,
    }
    resp = app.post_json(API_URL + 'projects', project)
    assert resp.status_code == 201
    query = {'name': 'Test project'}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']

    assert len(result) == 1
    actual_project = result[0]
    assert actual_project['names'] == project['names']
    assert actual_project['created_by_org_id'] == project['created_by_org_id']
    assert actual_project['project_ids'] == project['project_ids']


# PUT tests


def test_put_returns_not_found_status(app):
    resp = app.put_json(
        API_URL + 'projects/not-existing', expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'not-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_put_returns_ok_status(app, db):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id='test tax id',
        client_company_id='client-comp-id',
        client_contact_person_id='person-id',
        client_contact_person_email='<EMAIL>',
        created_by_org_id='created-by-some-org-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'],
        {
            'names': ['Updated project name'],
            'start_date': '2020-02-01',
            'end_date': '2020-12-01',
            'state': DRAFT,
        }
    )
    assert 200 == resp.status_code
    updated_project = resp.json
    expected = {
        'id': project['external_id'],
        'names': ['Updated project name'],
        'project_ids': [
            {
                'project_id_type': TAX_ID,
                'project_id': 'test tax id',
            },
        ],
        'project_responsible_org': 'client-comp-id',
        'project_responsible_person': None,
        'start_date': datetime.date(2020, 2, 1).isoformat(),
        'end_date': datetime.date(2020, 12, 1).isoformat(),
        'state': DRAFT,
        'pa_form_enabled': False,
        'created_by_org_id': 'created-by-some-org-id',
        'client_contact_person_id': 'person-id',
        'client_contact_person_email': '<EMAIL>',
        'added_client_confirmed': False,
        'project_creator_role': None,
        'added_client_can_view': False,
    }
    expected.update(QVARN_COMPAT_FIELDS_FOR_PROJECTS)
    assert updated_project == expected


@pytest.mark.parametrize(
    "initial, payload, expected",
    [
        # if you omit fields, their value doesn't change
        (
            {
                'client_contact_person_id': 'eafb-0c2b3d7678f770517ad25560070e721f-94456254',
                'client_contact_person_email': '<EMAIL>',
            },
            {
            },
            {
                'client_contact_person_id': 'eafb-0c2b3d7678f770517ad25560070e721f-94456254',
                'client_contact_person_email': '<EMAIL>',
            },
        ),
        # if you pass null values to fields, their value doesn't change
        (
            {
                'client_contact_person_id': 'eafb-0c2b3d7678f770517ad25560070e721f-94456254',
                'client_contact_person_email': '<EMAIL>',
            },
            {
                'client_contact_person_id': None,
                'client_contact_person_email': None,
            },
            {
                'client_contact_person_id': 'eafb-0c2b3d7678f770517ad25560070e721f-94456254',
                'client_contact_person_email': '<EMAIL>',
            },
        ),
        # it must be possible to reset client_contact_person_id to None when
        # changing client_contact_person_email
        (
            {
                'client_contact_person_id': 'eafb-0c2b3d7678f770517ad25560070e721f-94456254',
                'client_contact_person_email': '<EMAIL>',
            },
            {
                'client_contact_person_id': None,
                'client_contact_person_email': '<EMAIL>',
            },
            {
                'client_contact_person_id': None,
                'client_contact_person_email': '<EMAIL>',
            },
        ),
    ],
)
def test_put_partial_updates(app, db, initial, payload, expected):
    project = factories.create_project(
        db,
        name='Test project',
        **initial
    )
    resp = app.put_json(f"{API_URL}projects/{project['external_id']}", payload)
    assert 200 == resp.status_code
    updated_project = resp.json
    expected_project = {
        'id': project['external_id'],
        'names': ['Test project'],
        'project_ids': [],
        'project_responsible_org': None,
        'project_responsible_person': None,
        'start_date': None,
        'end_date': None,
        'state': ACTIVE,
        'pa_form_enabled': False,
        'created_by_org_id': None,
        'client_contact_person_id': None,
        'client_contact_person_email': None,
        'added_client_confirmed': False,
        'project_creator_role': None,
        'added_client_can_view': False,
    }
    expected_project.update(QVARN_COMPAT_FIELDS_FOR_PROJECTS)
    expected_project.update(expected)
    assert updated_project == expected_project


def test_put_sub_resources_returns_ok_status(app, db):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id='test tax id',
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    resp = app.put_json(
        API_URL + 'projects/%s/sync' % project['external_id'],
        {
            'names': ['Updated project name'],
            'start_date': '2020-02-01',
            'end_date': '2020-12-01',
            'state': DRAFT,
        }
    )
    assert 200 == resp.status_code
    updated_sub_resource = resp.json
    assert updated_sub_resource == {'revision': None}


def test_put_returns_validation_error(app, db):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id='test tax id',
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )

    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'],
        {
            'names': 1000,
        },
        expect_errors=True,
    )
    assert 400 == resp.status_code
    assert resp.json['message'] == 'Failed to validate parameters'
    assert resp.json['error_code'] == 'ParameterValidationFailed'
    assert resp.json['error']


def test_put_validation_schema_used(app, db, mocker):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id='test tax id',
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )

    project_payload = {}
    validator_mock = mocker.patch(
        'boldataapi.controllers.projects_controller.ProjectValidator')

    app.put_json(
        API_URL + f"projects/{project['external_id']}",
        project_payload, expect_errors=True
    )
    validator_mock.assert_called_with(schema_existing_project)


def test_put_server_error(app, db, mocker, caplog):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id='test tax id',
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    mocker.patch(
        'boldataapi.controllers.projects_controller.update_project',
        side_effect=Exception('Serious system problem occurred!'))
    data = {
        'state': CLOSED,
    }

    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'], data, expect_errors=True
    )

    expected = {
        'error_code': 'InternalServerError',
        'message': 'Internal Server Error'
    }
    assert resp.json == expected
    assert resp.status_code == 500

    assert 'Serious system problem occurred' in caplog.text


@pytest.mark.parametrize("initial_project_ids, updated_project_ids", [
    # cases with empty initial project ids
    # and new project ids added
    (
        [],
        [],
    ),
    (
        [],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
        ]
    ),
    (
        [],
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'any-id',
            },
        ]
    ),
    (
        [],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'any-id',
            },
        ]
    ),
    # cases with initial projects ids not empty
    # and removed with PUT
    (
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            }
        ],
        []
    ),
    (
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'any-ids',
            }
        ],
        []
    ),
    (
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'any-id',
            },
        ],
        []
    ),
    # cases with initial ids not empty
    # and updated project ids
    (
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
        ],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
        ]
    ),
    (
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
        ],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'updated-id',
            },
        ]
    ),
    (
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
        ],
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id',
            },
        ],
    ),
    (
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'any-tax-id',
            },
        ],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'updated-tax-id',
            },
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id',
            },
        ],
    ),
    (
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id'
            }
        ],
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id'
            }
        ],
    ),
    (
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id'
            }
        ],
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'updated-project-id'
            }
        ],
    ),
    (
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id'
            }
        ],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'tax-id'
            }
        ],
    ),
    (
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id'
            }
        ],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'tax-id'
            },
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'updated-project-id'
            },
        ],
    ),
    (
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'tax-id'
            },
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'project-id'
            },
        ],
        [
            {
                'project_id_type': TAX_ID,
                'project_id': 'updated-tax-id'
            },
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': 'updated-project-id'
            },
        ],
    ),
])
def test_put_project_ids(
    app, db, initial_project_ids, updated_project_ids
):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id=get_tax_id(initial_project_ids),
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    initial_project_ids = get_project_ids(initial_project_ids)
    for initial_project_id in initial_project_ids:
        factories.create_internal_project_id(
            db,
            {
                'project_id': project['id'],
                'company_id': 'client-comp-id',
                'internal_project_id': initial_project_id,
            }
        )

    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'],
        {
            'project_ids': updated_project_ids,
        }
    )
    assert resp.status_code == 200
    updated_project = resp.json
    assert updated_project['project_ids'] == updated_project_ids


@pytest.mark.parametrize("initial_project_ids", [
    [],
    [
        {
            'project_id_type': TAX_ID,
            'project_id': 'test id'
        }
    ],
    [
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        }
    ],
    [
        {
            'project_id_type': TAX_ID,
            'project_id': 'test id'
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test project id'
        }
    ],
])
def test_put_project_ids_none(
    app, db, initial_project_ids
):
    project = factories.create_project(
        db,
        name='Test project',
        tax_id=get_tax_id(initial_project_ids),
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    project_ids = get_project_ids(initial_project_ids)
    for project_id in project_ids:
        factories.create_internal_project_id(
            db,
            {
                'project_id': project['id'],
                'company_id': 'client-comp-id',
                'internal_project_id': project_id,
            }
        )
    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'], {}
    )
    assert resp.status_code == 200
    updated_project = resp.json
    assert updated_project['project_ids'] == initial_project_ids


@pytest.mark.parametrize("updated_project_ids", [
    (
        []
    ),
    (
        [
            {
                'project_id_type': PROJECT_ID_TYPE,
                'project_id': None,
            },
            {
                'project_id_type': TAX_ID,
                'project_id': None,
            },
        ]
    ),
])
def test_put_project_ids_delete_existing(app, db,  updated_project_ids):
    initial_project_ids = [
        {
            'project_id_type': TAX_ID,
            'project_id': 'initial TAX ID'
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'initial INTERNAL ID'
        },
    ]

    project = factories.create_project(
        db,
        name='Test project',
        tax_id=get_tax_id(initial_project_ids),
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    project_ids = get_project_ids(initial_project_ids)
    for project_id in project_ids:
        factories.create_internal_project_id(
            db,
            {
                'project_id': project['id'],
                'company_id': 'client-comp-id',
                'internal_project_id': project_id,
            }
        )
    resp = app.put_json(
        API_URL + 'projects/%s' % project['external_id'],
        {
            'project_ids': updated_project_ids,
        },
    )
    assert resp.status_code == 200
    updated_project = resp.json
    expected = []
    assert updated_project['project_ids'] == expected


# SEARCH tests


def get_projects_search(search_url, app):
    resp = app.get(API_URL + f'projects/search/{search_url}')
    assert resp.content_type == 'application/json'
    assert 200 == resp.status_code
    result = json.loads(resp.body.decode('utf-8'))
    return result['resources']


def test_projects_search_id(config, app, db):
    project = factories.create_project(
        db,
        name='Test project',
    )

    search_url = f"exact/id/{project['external_id']}"
    resp = app.get(API_URL + f'projects/search/{search_url}')
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [
            {'id': project['external_id']}
        ],
    }


@pytest.mark.parametrize(('q_field', 'field'),
                         [('project_responsible_org', 'client_company_id'),
                          ('start_date', 'start_date'),
                          ('end_date', 'end_date'),
                          ('state', 'state')])
def test_projects_search_exact_basic_fields_map(config, app, db, q_field, field):
    """Search finds projects based on field values."""
    project = factories.create_project(
        db,
        name='Test project',
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    search_url = f'exact/{q_field}/{project[field]}'
    result = get_projects_search(search_url, app)
    assert [{'id': project['external_id']}] == result


def test_projects_search_exact_names_map(config, app, db):
    """Search finds projects based name values."""
    project = factories.create_project(
        db,
        name='Test project',
    )
    search_url = 'exact/names/Test project'
    result = get_projects_search(search_url, app)
    assert [{'id': project['external_id']}] == result


@pytest.mark.xfail(reason="Wait for project POST to be implemented")
def test_projects_search_exact_subresource(config, app, db):
    """Search finds projects based on sub_field values."""
    project_ids = [
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'test1_id',
        }
    ]
    project = factories.create_project(
        db,
        tax_id='test tax id',
        project_ids=project_ids
    )

    search_url_type = f'exact/project_id_type/{PROJECT_ID_TYPE}'
    result = get_projects_search(search_url_type, app)
    assert [{'id': project['id']}] == result

    search_url_id = 'exact/project_id/test1_id'
    result = get_projects_search(search_url_id, app)
    assert [{'id': project['id']}] == result

    search_url_combined = '/'.join(search_url_type, search_url_id)
    result = get_projects_search(search_url_combined, app)
    assert [{'id': project['id']}] == result


def test_projects_search_show_all(config, app, db):
    # Deprecated: no calls are made with /show_all/ notation
    project = factories.create_project(
        db,
        name='Test project',
        client_company_id='client-comp-id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
    )
    factories.create_internal_project_id(
        db,
        {
            'internal_project_id': 'test1_id',
            'project_id': project['id'],
            'company_id': 'client-comp-id',
        }
    )

    search_url = f"show_all/exact/id/{project['external_id']}"
    result = get_projects_search(search_url, app)

    assert [
        {
            'id': project['external_id'],
            'project_responsible_person': None,
            'names': [project['name']],
            'start_date': '2020-01-01',
            'end_date': '2020-12-01',
            'state': project['state'],
            'project_responsible_org': project['client_company_id'],
            'project_ids': [{'project_id': 'test1_id',
                             'project_id_type': 'trafikverket_project_id'}],
            'project_responsible_person': None,
            'revision': None,
            'type': 'project',
            'country': None,
        }
    ] == result


def test_projects_search_not_found(app):
    search_url = 'exact/id/non-such'
    resp = app.get(API_URL + f'projects/search/{search_url}')
    assert 200 == resp.status_code
    assert json.loads(resp.body.decode('utf-8')) == {
        'resources': [],
    }


# QUERY tests


@pytest.mark.parametrize(('q_field', 'db_field'), [
    ('project_responsible_org', 'client_company_id'),
    ('id', 'external_id'),
    ('name', 'name'),
    ('state', 'state'),
    ('pa_form_enabled', 'pa_form_enabled'),
    ('project_creator_role', 'project_creator_role'),
    ('added_client_confirmed', 'added_client_confirmed'),
    ('added_client_can_view', 'added_client_can_view'),
])
def test_projects_query(app, db, q_field, db_field):
    project = factories.create_project(
        db,
        name='Test project',
        client_company_id='client-comp-id',
        client_contact_person_id='person-id',
        client_contact_person_email='<EMAIL>',
        created_by_org_id='created-by-org-id',
        tax_id='test tax id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
        pa_form_enabled=False,
        added_client_confirmed=True,
        project_creator_role=MAIN_CONTRACTOR_ROLE,
        added_client_can_view=True,
    )
    factories.create_project(
        db,
        name='Unrelated project please do not return it',
        state=DRAFT,
        pa_form_enabled=True,
    )
    query = {q_field: project[db_field]}
    response = app.get(f'{API_URL}projects/query?q={json.dumps(query)}')
    result = response.json['resources']
    expected = [
        {
            'id': project['external_id'],
            'names': [project['name']],
            'project_responsible_org': 'client-comp-id',
            'project_responsible_person': None,
            'project_ids': [
                {
                    'project_id': 'test tax id',
                    'project_id_type': 'tax_id',
                },
            ],
            'start_date': '2020-01-01',
            'end_date': '2020-12-01',
            'pa_form_enabled': False,
            'state': 'active',
            'created_by_org_id': 'created-by-org-id',
            'client_contact_person_id': 'person-id',
            'client_contact_person_email': '<EMAIL>',
            'added_client_confirmed': True,
            'project_creator_role': MAIN_CONTRACTOR_ROLE,
            'added_client_can_view': True,
        },
    ]
    assert result == expected
    assert response.status_code == 200


def test_projects_query_result_multiple(app, db):
    p1 = factories.create_project(db, name='Test project')
    p2 = factories.create_project(db, name='Test project')
    factories.create_project(db, name='Unrelated project please do not return it')
    query = {'name': 'Test project'}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']

    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([p1['external_id'], p2['external_id']])


def test_projects_query_result_empty(app, db):
    factories.create_project(db, name='Unrelated project')
    query = {'name': 'Test project'}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']

    assert result == []


@pytest.mark.parametrize(('q_field', 'db_field'), [
    ('project_responsible_org', 'client_company_id'),
    ('id', 'external_id'),
    ('name', 'name'),
    ('state', 'state'),
    ('pa_form_enabled', 'pa_form_enabled'),
    ('project_creator_role', 'project_creator_role'),
    ('added_client_confirmed', 'added_client_confirmed'),
])
def test_projects_query__ne(app, db, q_field, db_field):
    exclude_project = factories.create_project(
        db,
        name='Test project that we do NOT want to find',
        client_company_id='bad-comp-id',
        tax_id='bad tax id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 11, 1),
        state=ACTIVE,
        pa_form_enabled=False,
        added_client_confirmed=False,
        project_creator_role=None,
    )
    project = factories.create_project(
        db,
        name='The other project that we want to find',
        client_company_id='client-comp-id',
        client_contact_person_id='person-id',
        client_contact_person_email='<EMAIL>',
        created_by_org_id='created-by-org-id',
        tax_id='test tax id',
        start_date=datetime.date(2022, 2, 2),
        end_date=datetime.date(2022, 12, 2),
        state=DRAFT,
        pa_form_enabled=True,
        added_client_confirmed=True,
        project_creator_role=MAIN_CONTRACTOR_ROLE,
        added_client_can_view=True,
    )
    query = {q_field+'__ne': exclude_project[db_field]}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']
    expected = [
        {
            'id': project['external_id'],
            'names': [project['name']],
            'project_responsible_org': 'client-comp-id',
            'project_responsible_person': None,
            'project_ids': [
                {
                    'project_id': 'test tax id',
                    'project_id_type': 'tax_id',
                },
            ],
            'start_date': '2022-02-02',
            'end_date': '2022-12-02',
            'pa_form_enabled': True,
            'state': 'draft',
            'created_by_org_id': 'created-by-org-id',
            'client_contact_person_id': 'person-id',
            'client_contact_person_email': '<EMAIL>',
            'added_client_confirmed': True,
            'project_creator_role': MAIN_CONTRACTOR_ROLE,
            'added_client_can_view': True,
        },
    ]
    assert result == expected


def test_projects_query__ne_result_multiple(app, db):
    p1 = factories.create_project(db, name='Test project')
    p2 = factories.create_project(db, name='Test project')
    factories.create_project(db, name='Unrelated project')
    query = {'name__ne': 'Unrelated project'}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']

    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([p1['external_id'], p2['external_id']])


def test_projects_query__ne_result_empty(app, db):
    factories.create_project(db, name='Unrelated project')
    query = {'name__ne': 'Unrelated project'}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']

    assert result == []


def test_projects_query_any(app, db):
    project = factories.create_project(
        db,
        name='Test project',
        client_company_id='client-comp-id',
        tax_id='test tax id',
        start_date=datetime.date(2020, 1, 1),
        end_date=datetime.date(2020, 12, 1),
        state=ACTIVE,
        pa_form_enabled=False,
    )
    factories.create_project(
        db,
        name='Unrelated project please do not return it',
        state=DRAFT,
        pa_form_enabled=True,
    )
    query = {'project_responsible_org__any': ['client-comp-id', 'other-id']}
    result = [
        project['id']
        for project in app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']
    ]
    expected = [project['external_id']]
    assert result == expected


@pytest.mark.parametrize("query, expected", [
    ({'start_date__lt': '2022-01-03'}, {1, 2}),
    ({'start_date__gt': '2022-01-07'}, {8, 9, 10}),
    ({'start_date__le': '2022-01-03'}, {1, 2, 3}),
    ({'start_date__ge': '2022-01-07'}, {7, 8, 9, 10}),
    ({'start_date__ge': '2022-01-03', 'start_date__lt': '2022-01-07'}, {3, 4, 5, 6}),
])
def test_projects_query_inequality_tests(app, db, query, expected):
    for n in range(10):
        factories.create_project(
            db,
            name=f'Test project {n + 1}',
            client_company_id='client-comp-id',
            tax_id='test tax id',
            start_date=datetime.date(2022, 1, 1) + datetime.timedelta(n),
            end_date=datetime.date(2022, 1, 2) + datetime.timedelta(n),
            state=ACTIVE,
            pa_form_enabled=False,
        )
    result = {
        int(project['names'][0].split()[-1])
        for project in app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']
    }
    assert result == expected


def test_projects_query__any_result_multiple(app, db):
    p1 = factories.create_project(db, name='Test1')
    factories.create_project(db, name='Test2')
    p3 = factories.create_project(db, name='Test3')
    query = {'name__any': ['Test1', 'Test3']}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']

    assert len(result) == 2
    assert set([i['id'] for i in result]) == set([p1['external_id'], p3['external_id']])


def test_projects_query__any_result_empty(app, db):
    factories.create_project(db, name='Unrelated project')
    query = {'name__any': ['No such']}
    result = app.get(f'{API_URL}projects/query?q={json.dumps(query)}').json['resources']

    assert result == []


@pytest.mark.parametrize("query, error", [
    ("stuff", 'Could not parse search condition'),
    (json.dumps({'gold': 42}), 'Unsupported search field for projects: gold'),
    (json.dumps({'name__smells_like': 'roses'}), 'Unsupported search operator: smells_like'),
    (json.dumps(['gold']), 'Bad search query structure'),
    (json.dumps({'name__eq': [42]}), 'Bad value type for eq'),
    (json.dumps({'name__eq': {42: 42}}), 'Bad value type for eq'),
    (json.dumps({'name__any': 42}), 'Bad value type for any'),
])
def test_projects_query_bad_syntax(app, query, error):
    result = app.get(f'{API_URL}projects/query?q={query}', expect_errors=True)
    assert (result.status_code, result.json) == (400, {
        'error_code': 'BadSearchCondition',
        'message': error,
    })


# PROJECTS LIST TESTS

def get_project_list(app, active_org_id, user_role='main',
                     is_admin=False, filter_state='', filter_search='', filter_status='',
                     include_active_org_projects=False,
                     limit='', offset='', cols=None, expect_status=200):
    params = {
        'user_active_org_id': active_org_id if active_org_id else '',
        'user_active_org_role': user_role,
        'user_is_admin': is_admin,
        'include_active_org_projects': include_active_org_projects,
        'filter.state': filter_state,
        'filter.search': filter_search,
        'filter.status': filter_status,
        'limit': limit,
        'offset': offset,
    }
    resp = app.get(API_URL + 'projects/list', params, status=expect_status)
    if resp.status_code != 200:
        return resp.json
    projects = resp.json['resources']
    if cols is None:
        return projects
    return list(map(operator.itemgetter(*cols), projects))


def post_project_list(app, active_org_id, user_role='main',
                      is_admin=False, user_projects_ids=None,
                      filter_state='', filter_search='', filter_status='',
                      include_active_org_projects=False,
                      limit='', offset='', cols=None, expect_status=200,
                      ff_block_project_client=False):
    params = {
        'user_active_org_id': active_org_id if active_org_id else '',
        'user_active_org_role': user_role,
        'user_is_admin': is_admin,
        'user_projects_ids': user_projects_ids,
        'include_active_org_projects': include_active_org_projects,
        'filter.state': filter_state,
        'filter.search': filter_search,
        'filter.status': filter_status,
        'limit': limit,
        'offset': offset,
        'ff_block_project_client': ff_block_project_client,
    }
    resp = app.post_json(API_URL + 'projects/list', params, status=expect_status)
    if resp.status_code != 200:
        return resp.json
    projects = resp.json['resources']
    if cols is None:
        return projects
    return list(map(operator.itemgetter(*cols), projects))


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_basic(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'
    factories.create_status_report(
        db,
        status=STATUS_INVESTIGATE,
        interested_company_id=client_company_id,
        company_id=supplier_company_id,
        generated_timestamp=datetime.datetime.now(),
    )
    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_id_payload = {
        'project_id': project['id'],
        'internal_project_id': 'PROJ-1',
        'company_id': client_company_id,
    }
    factories.create_internal_project_id(db, project_id_payload)

    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)

    assert project_list_func(
        app, active_org_id=client_company_id
    ) == [
        {
            'added_client_confirmed': False,
            'added_client_can_view': False,
            'id': project['external_id'],
            'name': 'A project',
            'project_id': 'PROJ-1',
            'project_creator_role': None,
            'project_responsible_org': client_company_id,
            'state': ACTIVE,
            'supplier_count': 1,
            'status': STATUS_INVESTIGATE,
        }
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_status_visitor(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'
    visitor_company_id = 'visitor-company-id'
    factories.create_status_report(
        db,
        status=STATUS_INVESTIGATE,
        interested_company_id=client_company_id,
        company_id=supplier_company_id,
        generated_timestamp=datetime.datetime.now(),
    )
    factories.create_status_report(
        db,
        status=STATUS_STOP,
        interested_company_id=client_company_id,
        company_id=visitor_company_id,
        generated_timestamp=datetime.datetime.now(),
    )

    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_id_payload = {
        'project_id': project['id'],
        'internal_project_id': 'PROJ-1',
        'company_id': client_company_id,
    }
    factories.create_internal_project_id(db, project_id_payload)

    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)
    factories.create_supplier(
        db, project_id=project['id'], company_id=visitor_company_id, type='visitor'
    )

    assert project_list_func(
        app, active_org_id=client_company_id
    ) == [
        {
            'added_client_confirmed': False,
            'added_client_can_view': False,
            'id': project['external_id'],
            'name': 'A project',
            'project_id': 'PROJ-1',
            'project_creator_role': None,
            'project_responsible_org': client_company_id,
            'state': ACTIVE,
            'supplier_count': 1,  # VISITOR is not counted in
            'status': STATUS_INVESTIGATE,
        }
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_projects_list_project_id(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'

    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_internal_project_id(db, {
        'project_id': project['id'],
        'internal_project_id': 'PROJ-1',
        'company_id': client_company_id,
    })
    factories.create_internal_project_id(db, {
        'project_id': project['id'],
        'internal_project_id': 'SUPP Project',
        'company_id': supplier_company_id,
    })

    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)

    assert project_list_func(
        app, active_org_id=client_company_id, cols=['project_id']
    ) == ['PROJ-1']


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_status_min_status(app, db, project_list_func):
    # select worst status for multiple reports with same ts
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'

    with freeze_time('2020-04-01'):
        factories.create_status_report(
            db,
            status=STATUS_OK,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
        factories.create_status_report(
            db,
            status=STATUS_STOP,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )

    project = factories.create_project(
        db,
        name='Stop project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )

    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)

    assert project_list_func(
        app, active_org_id=client_company_id, cols=['id', 'status']
    ) == [
        (
            project['external_id'],
            STATUS_STOP,
        )
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_status_last_report(app, db, project_list_func):
    # get status of latest report
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'

    with freeze_time('2020-01-01'):
        factories.create_status_report(
            db,
            status=STATUS_OK,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
    with freeze_time('2019-01-01'):
        factories.create_status_report(
            db,
            status=STATUS_STOP,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )

    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_id_payload = {
        'project_id': project['id'],
        'internal_project_id': 'PROJ-1',
        'company_id': client_company_id,
    }
    factories.create_internal_project_id(db, project_id_payload)

    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)

    assert project_list_func(
        app, active_org_id=client_company_id, cols=['id', 'status']
    ) == [
        (
            project['external_id'],
            STATUS_OK,
        )
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_status_no_report(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'

    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)

    assert project_list_func(
        app, active_org_id=client_company_id, cols=['id', 'status']
    ) == [
        (
            project['external_id'],
            STATUS_INCOMPLETE,
        )
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_status_no_report_with_visitor(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    supplier_company_id = 'supplier-company-id'
    visitor_company_id = 'visitor-company-id'

    factories.create_status_report(
        db,
        status=STATUS_STOP,
        interested_company_id=client_company_id,
        company_id=visitor_company_id,
        generated_timestamp=datetime.datetime.now(),
    )
    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_supplier(db, project_id=project['id'], company_id=supplier_company_id)
    factories.create_supplier(
        db, project_id=project['id'], company_id=visitor_company_id, type='visitor'
    )

    assert project_list_func(
        app, active_org_id=client_company_id, cols=['id', 'status']
    ) == [
        (
            project['external_id'],
            STATUS_INCOMPLETE,
        )
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_empty_project(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_id_payload = {
        'project_id': project['id'],
        'internal_project_id': 'PROJ-1',
        'company_id': client_company_id,
    }
    factories.create_internal_project_id(db, project_id_payload)

    assert project_list_func(
        app, active_org_id=client_company_id
    ) == [
        {
            'added_client_confirmed': False,
            'added_client_can_view': False,
            'id': project['external_id'],
            'name': u'A project',
            'project_creator_role': None,
            'project_id': 'PROJ-1',
            'project_responsible_org': client_company_id,
            'state': ACTIVE,
            'supplier_count': 0,
            'status': STATUS_OK,
        }
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_added_client(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    project = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        project_creator_role=MAIN_CONTRACTOR_ROLE,
        added_client_confirmed=True,
        added_client_can_view=True
    )
    project_id_payload = {
        'project_id': project['id'],
        'internal_project_id': 'PROJ-1',
        'company_id': client_company_id,
    }
    factories.create_internal_project_id(db, project_id_payload)

    assert project_list_func(
        app, active_org_id=client_company_id
    ) == [
        {
            'added_client_confirmed': True,
            'added_client_can_view': True,
            'id': project['external_id'],
            'name': u'A project',
            'project_creator_role': MAIN_CONTRACTOR_ROLE,
            'project_id': 'PROJ-1',
            'project_responsible_org': client_company_id,
            'state': ACTIVE,
            'status': STATUS_OK,
            'supplier_count': 0,
        }
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_count(app, db, project_list_func):
    client_company_id = 'client-comp-id'
    s1_company_id = 's1-company-id'
    s2_company_id = 's2-company-id'

    factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project1 = factories.create_project(
        db,
        name='B project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project2 = factories.create_project(
        db,
        name='C project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project3 = factories.create_project(
        db,
        name='D project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project4 = factories.create_project(
        db,
        name='E project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )

    # B project
    factories.create_supplier(db, project_id=project1['id'], company_id=s1_company_id)

    # C project
    factories.create_supplier(db, project_id=project2['id'], company_id=s1_company_id)
    factories.create_supplier(db, project_id=project2['id'], company_id=s2_company_id)
    factories.create_supplier(db, project_id=project2['id'], company_id=client_company_id)

    # D project
    factories.create_supplier(db, project_id=project3['id'], company_id=s1_company_id)

    # E project
    factories.create_supplier(db, project_id=project4['id'], company_id=s1_company_id)
    factories.create_supplier(db, project_id=project4['id'], company_id=s1_company_id)
    factories.create_supplier(db, project_id=project4['id'], company_id=s1_company_id)

    assert project_list_func(
        app, active_org_id=client_company_id,
        cols=['name', 'supplier_count']
    ) == sorted([
        ('A project', 0),
        ('B project', 1),
        ('C project', 3),
        ('D project', 1),
        ('E project', 1),
    ])


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_project_sort(app, db, project_list_func):
    client_company_id = 'client-comp-id'

    project_c = factories.create_project(
        db,
        name='C project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_a = factories.create_project(
        db,
        name='A project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_b = factories.create_project(
        db,
        name='B project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_d = factories.create_project(
        db,
        name='D project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )

    factories.create_internal_project_id(db, {
        'project_id': project_c['id'],
        'company_id': client_company_id,
        'internal_project_id': 'PROJ-1'
    })
    factories.create_internal_project_id(db, {
        'project_id': project_a['id'],
        'company_id': client_company_id,
        'internal_project_id': 'PROJ-2'
    })
    factories.create_internal_project_id(db, {
        'project_id': project_b['id'],
        'company_id': client_company_id,
        'internal_project_id': None,
    })
    factories.create_internal_project_id(db, {
        'project_id': project_d['id'],
        'company_id': client_company_id,
        'internal_project_id': 'PROJ-3',
    })

    assert project_list_func(
        app, active_org_id=client_company_id,
        cols=['name', 'project_id']
    ) == [
        ('A project', 'PROJ-2'),
        ('B project', None),
        ('C project', 'PROJ-1'),
        ('D project', 'PROJ-3'),
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
@pytest.mark.parametrize('user_role',
                         ['main',
                          'basic'])
def test_project_list_admin_user(app, db, user_role, project_list_func):
    admin_company_id = 'admin-company-id'
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    factories.create_project(
        db,
        name='Empty Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_project(
        db,
        name='Admin Project',
        client_company_id=admin_company_id,
        state=ACTIVE,
    )
    project_w_suppliers = factories.create_project(
        db,
        name='Project with Suppliers',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_project(
        db,
        name='Spam Project',
        client_company_id=None,
        state=ACTIVE,
    )
    # suppliers
    factories.create_supplier(
        db,
        project_id=project_w_suppliers['id'],
        company_id=supplier_company_id
    )
    factories.create_supplier(
        db,
        project_id=project_w_suppliers['id'],
        company_id=supplier_company_id,
    )
    # Admin user should see all projects that have suppliers or project
    # responsible orgs
    assert project_list_func(
        app, active_org_id=admin_company_id,
        user_role=user_role, is_admin=True,
        cols=['name', 'supplier_count']
    ) == [
        ('Admin Project', 0),
        ('Empty Project', 0),
        ('Project with Suppliers', 1),
    ]


def test_get_project_list_basic_user(app, db):
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    # basic user should not see any project

    project_a = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_project(
        db,
        name='Spam project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )

    # suppliers
    factories.create_supplier(
        db,
        project_id=project_a['id'],
        company_id=supplier_company_id
    )
    assert get_project_list(
        app, active_org_id=client_company_id, user_role='basic',
        cols=['name', 'supplier_count']
    ) == []


@pytest.mark.parametrize("user_projects_ids, expected_result", [
    (None, []),
    (['test-external_id'], [('A Project', 1)])
])
def test_post_project_list_basic_user(app, db, user_projects_ids, expected_result):
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    project_a = factories.create_project(
        db,
        name='A Project',
        external_id='test-external_id',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_project(
        db,
        name='Spam project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    # suppliers
    factories.create_supplier(
        db,
        project_id=project_a['id'],
        company_id=supplier_company_id
    )
    assert post_project_list(
        app, active_org_id=client_company_id, user_role='basic',
        user_projects_ids=user_projects_ids,
        cols=['name', 'supplier_count']
    ) == expected_result


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_main_user(app, db, project_list_func):
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    # main user sees this project
    # is responsible org in the project
    factories.create_project(
        db,
        name='Project A',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    # main user sees this project
    # is main contractor in the project
    project_b = factories.create_project(
        db,
        name='Project B',
        client_company_id=supplier_company_id,
        state=ACTIVE,
    )
    # doesn't see this project
    # not responsible org only low level supplier
    project_c = factories.create_project(
        db,
        name='Spam project',
        client_company_id=supplier_company_id,
        state=ACTIVE,
    )
    # doesn't see this project
    # not responsible or nor supplier
    factories.create_project(
        db,
        name='Spam project',
        client_company_id=supplier_company_id,
        state=ACTIVE,
    )

    # suppliers
    factories.create_supplier(
        db,
        project_id=project_b['id'],
        company_id=client_company_id,
        role='main_contractor',
    )
    factories.create_supplier(
        db,
        project_id=project_c['id'],
        company_id=client_company_id,
        role='supplier',
    )
    assert project_list_func(
        app, active_org_id=client_company_id, user_role='main',
        cols=['name', 'supplier_count']
    ) == [
        ('Project A', 0),
        ('Project B', 1),
    ]


def test_project_list_added_client_can_view(app, db):
    client_company_id = 'client-company-id'
    supplier_company_id = 'supplier-company-id'

    # main contractor in the project
    # client_company_id=active_org_id
    # added_client_can_view=True
    project_a = factories.create_project(
        db,
        name='Project A',
        client_company_id=client_company_id,
        added_client_can_view=True,
    )
    # main contractor in the project
    # added_client_can_view=False
    project_b = factories.create_project(
        db,
        name='Project B',
        client_company_id=supplier_company_id,
        added_client_can_view=False,
    )
    # low level supplier in the project
    # added_client_can_view=False
    project_c = factories.create_project(
        db,
        name='Project C',
        client_company_id=supplier_company_id,
        added_client_can_view=False,
    )
    # no supplier
    # added_client_can_view=True
    factories.create_project(
        db,
        name='Project D',
        client_company_id=supplier_company_id,
        added_client_can_view=True,
    )
    # legacy project
    project_z = factories.create_project(
        db,
        name='Project Z',
        added_client_can_view=None,
    )
    # suppliers
    factories.create_supplier(
        db,
        project_id=project_a['id'],
        company_id=client_company_id,
        role='main_contractor',
    )
    factories.create_supplier(
        db,
        project_id=project_b['id'],
        company_id=client_company_id,
        role='main_contractor',
    )
    factories.create_supplier(
        db,
        project_id=project_c['id'],
        company_id=client_company_id,
        role='supplier',
    )
    factories.create_supplier(
        db,
        project_id=project_z['id'],
        company_id=client_company_id,
        role='main_contractor',
    )
    project_list = post_project_list(
        app, active_org_id=client_company_id, user_role='main',
        cols=['name', 'supplier_count'], ff_block_project_client=True
    )

    assert project_list == [
        ('Project A', 1),
        ('Project B', 1),
        ('Project Z', 1),
    ]


@pytest.mark.parametrize(('project_list_func', 'user_role'), [
    (get_project_list, 'main'),
    (post_project_list, 'main'),
])
def test_project_list_active_org_supplier(app, db, project_list_func, user_role):
    supplier_company_id = 'supplier-company-id'

    # Project with a simple supplier at the top level
    project_a = factories.create_project(
        db,
        name='Project A',
        state=ACTIVE,
        pa_form_enabled=True
    )
    # my company supplier - visible
    factories.create_supplier(
        db,
        project_id=project_a['id'],
        # make this project appear in Projects list
        company_id=supplier_company_id,
        # add this supplier to supplier count
        materialized_path=[supplier_company_id],
    )
    # 1 spam supplier - not visible
    factories.create_supplier(
        db,
        project_id=project_a['id'],
    )

    # Project with a nested simple supplier
    project_b = factories.create_project(
        db,
        name='Project B',
        state=ACTIVE,
    )
    # ancestor supplier - not visible
    factories.create_supplier(
        db,
        project_id=project_b['id'],
        company_id='supp1_org_id',
        materialized_path=['supp1_org_id'],
    )
    # my company supplier - visible
    factories.create_supplier(
        db,
        project_id=project_b['id'],
        # make this project appear in Projects list
        company_id=supplier_company_id,
        # add this supplier to supplier count
        materialized_path=['supp1_org_id', supplier_company_id],
    )
    # descendant supplier - visible
    factories.create_supplier(
        db,
        project_id=project_b['id'],
        company_id='supp1_1_1_org_id',
        # add this supplier to supplier count
        materialized_path=['supp1_org_id', supplier_company_id, 'supp1_1_1_org_id'],
    )

    assert project_list_func(
        app, active_org_id=supplier_company_id, user_role=user_role,
        include_active_org_projects=True,
        cols=['name', 'supplier_count']
    ) == [
        ('Project A', 1),
    ]


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_filter_state(app, db, project_list_func):
    client_company_id = 'client-company-id'
    factories.create_project(
        db,
        name='Active Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_project(
        db,
        name='Draft Project',
        client_company_id=client_company_id,
        state=DRAFT,
    )
    factories.create_project(
        db,
        name='Closed Project',
        client_company_id=client_company_id,
        state=CLOSED,
    )

    # Filter active state
    filter_state = ACTIVE
    assert project_list_func(
        app, active_org_id=client_company_id, filter_state=filter_state,
        cols=['name', 'state']
    ) == [
        ('Active Project', ACTIVE,),
    ]

    # Filter disabled state
    filter_state = CLOSED
    assert project_list_func(
        app, active_org_id=client_company_id, filter_state=filter_state,
        cols=['name', 'state']
    ) == [
        ('Closed Project', CLOSED),
    ]

    # Filter not:closed
    filter_state = NOT_CLOSED
    assert project_list_func(
        app, active_org_id=client_company_id, filter_state=filter_state,
        cols=['name', 'state']
    ) == [
        ('Active Project', ACTIVE),
        ('Draft Project', DRAFT),
    ]

    # admin filters not:closed
    filter_state = NOT_CLOSED
    assert project_list_func(
        app, active_org_id=client_company_id, filter_state=filter_state, is_admin=True,
        cols=['name', 'state']
    ) == [
        ('Active Project', ACTIVE),
        ('Draft Project', DRAFT),
    ]

    # Error handling
    filter_state = "inebriated"
    assert project_list_func(
        app, active_org_id=client_company_id, filter_state=filter_state,
        cols=['name', 'state'], expect_status=400,
    ) == {
        'error': {
            'filter.state': 'unallowed value inebriated',
        },
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
    }


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_filter_status(app, db, project_list_func):
    client_company_id = 'client-company-id'
    ok_supplier_company_id = 'ok-test-company-id'
    stop_supplier_company_id = 'stop-test-company-id'

    factories.create_status_report(
        db,
        status=STATUS_OK,
        interested_company_id=client_company_id,
        company_id=ok_supplier_company_id,
        generated_timestamp=datetime.datetime.now(),
    )
    factories.create_status_report(
        db,
        status=STATUS_STOP,
        interested_company_id=client_company_id,
        company_id=stop_supplier_company_id,
        generated_timestamp=datetime.datetime.now(),
    )

    # OK Project
    ok_project = factories.create_project(
        db,
        name='OK Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_supplier(
        db,
        project_id=ok_project['id'],
        company_id=ok_supplier_company_id,
        role='main_contractor',
    )

    # Stop Project
    stop_project = factories.create_project(
        db,
        name='Stop Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_supplier(
        db,
        project_id=stop_project['id'],
        company_id=stop_supplier_company_id,
        role='main_contractor',
    )
    factories.create_supplier(
        db,
        project_id=stop_project['id'],
        company_id=ok_supplier_company_id,
        role='supplier',
    )
    # Empty project
    factories.create_project(
        db,
        name='Empty Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    filter_status = 'ok'
    assert project_list_func(
        app, active_org_id=client_company_id, filter_status=filter_status,
        cols=['name', 'status']
    ) == [
        ('Empty Project', '500 OK'),
        ('OK Project', '500 OK'),
    ]

    filter_status = 'not:ok'
    assert project_list_func(
        app, active_org_id=client_company_id, filter_status=filter_status,
        cols=['name', 'status']
    ) == [
        ('Stop Project', '100 STOP'),
    ]

    filter_status = ''
    assert project_list_func(
        app, active_org_id=client_company_id, filter_status=filter_status,
        cols=['name', 'status']
    ) == [
        ('Empty Project', '500 OK'),
        ('OK Project', '500 OK'),
        ('Stop Project', '100 STOP'),
    ]

    filter_status = "furious"
    assert project_list_func(
        app, active_org_id=client_company_id, filter_status=filter_status,
        cols=['name', 'status'], expect_status=400,
    ) == {
        'error': {
            'filter.status': 'unallowed value furious',
        },
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
    }


@pytest.mark.parametrize('project_list_func',
                         [get_project_list, post_project_list])
def test_project_list_filter_search(app, db, project_list_func):
    client_company_id = 'client-company-id'
    project_a = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    project_b = factories.create_project(
        db,
        name='B Project',
        client_company_id=client_company_id,
        state=DRAFT,
    )
    project_cb = factories.create_project(
        db,
        name='CB Project',
        client_company_id=client_company_id,
        state=CLOSED,
    )

    factories.create_internal_project_id(db, {
        'project_id': project_a['id'],
        'company_id': client_company_id,
        'internal_project_id': 'PROJ-572',
    })
    factories.create_internal_project_id(db, {
        'project_id': project_b['id'],
        'company_id': client_company_id,
        'internal_project_id': 'PROJ-715',
    })
    factories.create_internal_project_id(db, {
        'project_id': project_cb['id'],
        'company_id': client_company_id,
        'internal_project_id': 'PROJ-559',
    })
    # Filter ID
    filter_search = 'j-5'
    assert project_list_func(
        app, active_org_id=client_company_id, filter_search=filter_search,
        cols=['name', 'project_id']
    ) == [
        ('A Project', 'PROJ-572'),
        ('CB Project', 'PROJ-559'),
    ]

    # Filter name
    filter_search = 'b'
    assert project_list_func(
        app, active_org_id=client_company_id, filter_search=filter_search,
        cols=['name', 'project_id']
    ) == [
        ('B Project', 'PROJ-715'),
        ('CB Project', 'PROJ-559'),
    ]


def test_project_list_parameter_validation_user_is_admin(app):
    resp = app.get(API_URL + 'projects/list?user_is_admin=maybe', status=400)
    assert resp.json == {
        'error': {'user_is_admin': "bad boolean value ('maybe'), expected true/false"},
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
    }


def test_project_list_parameter_validation_include_active_org_projects(app):
    resp = app.get(API_URL + 'projects/list?include_active_org_projects=on+tuesdays', status=400)
    assert resp.json == {
        'error': {
            'include_active_org_projects': "bad boolean value ('on tuesdays'), expected true/false",
        },
        'error_code': 'ParameterValidationFailed',
        'message': 'Failed to validate parameters',
    }


# PROJECTS SUPPLIERS TESTS

def get_project_suppliers(app, external_id, user_active_org_id, cols=None):
    params = {
        'project_id': external_id,
        'user_active_org_id': user_active_org_id,
    }
    suppliers = app.get(API_URL + 'projects/suppliers', params).json['resources']
    if cols is None or 'supplier_contacts' in cols:
        for s in suppliers:
            s['supplier_contacts'] = sorted(
                s['supplier_contacts'],
                key=lambda item: item['supplier_contact_email'])
    if cols is None:
        return suppliers
    return list(map(operator.itemgetter(*cols), suppliers))


def test_project_suppliers_smoke(app, db):
    client_company_id = 'test-client-company-id'
    supplier_company_id = 'test-supplier-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    factories.create_internal_project_id(db, {
        'project_id': project['id'],
        'company_id': client_company_id,
        'internal_project_id': 'PROJ-1',
    })

    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=supplier_company_id,
        contract_start_date=datetime.date(2007, 12, 5),
        contract_end_date=datetime.date(2007, 12, 31),
        contract_work_areas=[DEMOLITION],
        materialized_path=[project['client_company_id'], supplier_company_id],
        first_visited=datetime.datetime(2020, 10, 1, 0, 0, 0),
        last_visited=datetime.datetime(2020, 10, 15, 0, 0, 0),
    )

    with freeze_time('2018-10-23'):
        factories.create_status_report(
            db,
            status=STATUS_INVESTIGATE,
            generated_timestamp=datetime.datetime.now(),
            company_id=supplier_company_id,
            interested_company_id=client_company_id,
        )
    factories.create_internal_project_id(db, {
        'project_id': project['id'],
        'company_id': supplier_company_id,
        'internal_project_id': 'SUP-PROJ-1',
    })
    factories.create_supplier_contact(db, {
        'supplier_id': supplier['id'],
        'person_id': 'stubbed person_id',
        'person_email': 'stubbed email',
    })

    assert get_project_suppliers(
        app, external_id=project['external_id'],
        user_active_org_id=client_company_id,
    ) == [
        {
            'bolagsfakta_status': 'investigate',
            'latest_report_timestamp': '2018-10-23T00:00:00',
            'contract_end_date': '2007-12-31',
            'contract_start_date': '2007-12-05',
            'contract_work_areas': [DEMOLITION],
            'id': supplier['external_id'],
            'internal_project_id': 'SUP-PROJ-1',
            'materialized_path': [
                project['client_company_id'],
                supplier_company_id,
            ],
            'parent_org_id': None,
            'parent_supplier_id': None,
            'revision': supplier['revision'],
            'project_resource_id': project['external_id'],
            'supplier_contacts': [
                {'supplier_contact_email': 'stubbed email',
                 'supplier_contact_person_id': 'stubbed person_id'}],
            'supplier_org': None,
            'supplier_org_id': supplier_company_id,
            'supplier_role': 'supplier',
            'supplier_type': 'linked',
            'first_visited': '2020-10-01T00:00:00',
            'last_visited': '2020-10-15T00:00:00',
            'visitor_type': None,
            'type': 'bol_supplier',
            'contract_type': None,
            'pa_status': None,
            'pa_id': None,
            'pa_assigned_to_company_id': None,
            'is_one_man_company': None,
            'has_collective_agreement': None,
            'collective_agreement_name': None,
        }
    ]


def test_preannouncement_data_exist_via_get_project_suppliers(app, db):
    client_company_id = 'test-client-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        state=ACTIVE,
    )
    s1 = factories.create_supplier(
        db,
        project_id=project['id'],
        contract_start_date=datetime.date(2007, 12, 5),
        contract_end_date=datetime.date(2007, 12, 31),
        contract_work_areas=[DEMOLITION],
        first_visited=datetime.datetime(2020, 10, 1, 0, 0, 0),
        last_visited=datetime.datetime(2020, 10, 15, 0, 0, 0),
        is_one_man_company=False,
        has_collective_agreement=True,
        collective_agreement_name='unionen'
    )
    s2 = factories.create_supplier(
        db,
        project_id=project['id'],
        contract_start_date=datetime.date(2007, 6, 5),
        contract_end_date=datetime.date(2007, 11, 25),
        contract_work_areas=[DEMOLITION],
        first_visited=datetime.datetime(2020, 9, 1, 0, 0, 0),
        last_visited=datetime.datetime(2020, 10, 15, 0, 0, 0),
        is_one_man_company=True,
        has_collective_agreement=None,
        collective_agreement_name=None
    )

    pa = factories.create_preannouncement(
        db,
        status=PA_STATUS_CREATED,
        created_by_supplier_id=s1['id'],
        for_supplier_id=s2['id'],
        project_id=project['id'],
        assigned_to_company_id='any text id really',
        assigned_to_supplier_id=s2['id'],
        assigned_to_time='2022-03-03T00:00:00')

    result = sorted(get_project_suppliers(
        app, external_id=project['external_id'],
        user_active_org_id=client_company_id,
    ), key=lambda x: x['id'])
    expected = sorted([
        {
            'id': s1['external_id'],
            'bolagsfakta_status': 'incomplete',
            'latest_report_timestamp': None,
            'contract_type': None,
            'contract_end_date': '2007-12-31',
            'contract_start_date': '2007-12-05',
            'contract_work_areas': ['demolition'],
            'internal_project_id': None,
            'materialized_path': [],
            'parent_org_id': None,
            'parent_supplier_id': None,
            'project_resource_id': project['external_id'],
            'revision': s1['revision'],
            'supplier_contacts': [],
            'supplier_org': None,
            'supplier_org_id': None,
            'supplier_role': 'supplier',
            'supplier_type': 'linked',
            'last_visited': '2020-10-15T00:00:00',
            'type': 'bol_supplier',
            'pa_status': None,
            'pa_id': None,
            'pa_assigned_to_company_id': None,
            'first_visited': '2020-10-01T00:00:00',
            'is_one_man_company': False,
            'has_collective_agreement': True,
            'collective_agreement_name': 'unionen',
            'visitor_type': None
        },
        {
            'id': s2['external_id'],
            'bolagsfakta_status': 'incomplete',
            'latest_report_timestamp': None,
            'contract_type': None,
            'contract_end_date': '2007-11-25',
            'contract_start_date': '2007-06-05',
            'contract_work_areas': ['demolition'],
            'internal_project_id': None,
            'materialized_path': [],
            'parent_org_id': None,
            'parent_supplier_id': None,
            'project_resource_id': project['external_id'],
            'revision': s2['revision'],
            'supplier_contacts': [],
            'supplier_org': None,
            'supplier_org_id': None,
            'supplier_role': 'supplier',
            'supplier_type': 'linked',
            'last_visited': '2020-10-15T00:00:00',
            'type': 'bol_supplier',
            'pa_status': 'created',
            'pa_id': pa['external_id'],
            'pa_assigned_to_company_id': 'any text id really',
            'first_visited': '2020-09-01T00:00:00',
            'is_one_man_company': True,
            'has_collective_agreement': None,
            'collective_agreement_name': None,
            'visitor_type': None
        }
    ], key=lambda x: x['id'])

    assert expected == result


def test_project_suppliers_first_visited_disabled(app, db, featureflags):
    client_company_id = 'test-client-company-id'
    project = factories.create_project(db, client_company_id=client_company_id)
    factories.create_supplier(
        db,
        project_id=project['id'],
        first_visited=datetime.datetime(2020, 10, 1, 0, 0, 0),
    )

    featureflags({'first_visited': False})

    suppliers = get_project_suppliers(
        app,
        external_id=project['external_id'],
        user_active_org_id=client_company_id,
    )
    assert suppliers[0]['first_visited'] is None


def test_project_suppliers_multiple_projects(app, db):
    client_company_id = 'test-client-company-id'
    supplier_1_company_id = 'test-supplier-1-company-id'
    supplier_2_company_id = 'test-supplier-2-company-id'

    project_a = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
    )
    project_b = factories.create_project(
        db,
        name='B Project',
        client_company_id=client_company_id,
    )
    factories.create_internal_project_id(db, {
        'project_id': project_a['id'],
        'company_id': client_company_id,
        'internal_project_id': 'Client-PROJ-A',
    })
    factories.create_internal_project_id(db, {
        'project_id': project_a['id'],
        'company_id': supplier_1_company_id,
        'internal_project_id': 'SUPP1-PROJ-A',
    })
    factories.create_internal_project_id(db, {
        'project_id': project_b['id'],
        'company_id': supplier_2_company_id,
        'internal_project_id': 'SUPP2-PROJ-B',
    })
    client_supplier_on_a = factories.create_supplier(
        db,
        project_id=project_a['id'],
        company_id=client_company_id,
    )
    supplier_1_on_a = factories.create_supplier(
        db,
        project_id=project_a['id'],
        company_id=supplier_1_company_id,
    )
    factories.create_supplier(
        db,
        project_id=project_b['id'],
        company_id=supplier_2_company_id,
    )

    result = get_project_suppliers(
        app, external_id=project_a['external_id'],
        user_active_org_id=client_company_id,
        cols=['id', 'supplier_org_id', 'project_resource_id', 'internal_project_id']
    )
    expected = [
        (
            client_supplier_on_a['external_id'],
            client_company_id,
            project_a['external_id'],
            'Client-PROJ-A',
        ),
        (
            supplier_1_on_a['external_id'],
            supplier_1_company_id,
            project_a['external_id'],
            'SUPP1-PROJ-A',
        ),
    ]
    result.sort()
    expected.sort()
    assert result == expected


def test_project_suppliers_multiple_contacts(app, db):
    client_company_id = 'test-client-company-id'
    supplier_company_id = 'test-supplier-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
    )
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=supplier_company_id,
    )
    factories.create_supplier_contact(db, {
        'supplier_id': supplier['id'],
        'person_id': 'stubbed person_id 1',
        'person_email': 'stubbed email 1',
    })
    factories.create_supplier_contact(db, {
        'supplier_id': supplier['id'],
        'person_id': 'stubbed person_id 2',
        'person_email': 'stubbed email 2',
    })

    assert get_project_suppliers(
        app, external_id=project['external_id'],
        user_active_org_id=client_company_id,
        cols=['id', 'project_resource_id', 'supplier_contacts']
    ) == [(
        supplier['external_id'],
        project['external_id'],
        [
            {
                'supplier_contact_person_id': 'stubbed person_id 1',
                'supplier_contact_email': 'stubbed email 1'
            },
            {
                'supplier_contact_person_id': 'stubbed person_id 2',
                'supplier_contact_email': 'stubbed email 2'
            },
        ]
    )]


@pytest.mark.parametrize("has_report, report_status", [
    (True, 'ok'),
    (False, 'incomplete')
])
def test_project_suppliers_status_no_report(
    app, db, has_report, report_status
):
    client_company_id = 'test-client-company-id'
    supplier_company_id = 'test-supplier-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
    )
    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=supplier_company_id,
    )
    if has_report:
        factories.create_status_report(
            db,
            status=STATUS_OK,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
    assert get_project_suppliers(
        app, external_id=project['external_id'], user_active_org_id=client_company_id,
        cols=['bolagsfakta_status']
    ) == [report_status]


def test_project_suppliers_status_multiple_reports(app, db):
    client_company_id = 'test-client-company-id'
    supplier_company_id = 'test-supplier-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
    )
    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=supplier_company_id,
    )
    with freeze_time('2019-12-24'):
        factories.create_status_report(
            db,
            status=STATUS_ATTENTION,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
    with freeze_time('2020-01-10'):
        factories.create_status_report(
            db,
            status=STATUS_OK,
            interested_company_id=client_company_id,
            company_id=supplier_company_id,
            generated_timestamp=datetime.datetime.now(),
        )

    assert get_project_suppliers(
        app, external_id=project['external_id'], user_active_org_id=client_company_id,
        cols=['bolagsfakta_status', 'latest_report_timestamp']
    ) == [('ok', '2020-01-10T00:00:00')]


@pytest.mark.parametrize(
    ('sibling_type', 's1_role', 'sibling_role'), [
        (LINKED, SUPPLIER_ROLE, MAIN_CONTRACTOR_ROLE),
        (LINKED, SUPPLIER_ROLE, SUPERVISOR_ROLE),
        (LINKED, SUPPLIER_ROLE, SUPPLIER_ROLE),
        (LINKED, SUPERVISOR_ROLE, SUPPLIER_ROLE),
        (LINKED, MAIN_CONTRACTOR_ROLE, SUPPLIER_ROLE),
        (LINKED, SUPPLIER_ROLE, SUPPLIER_ROLE),
        (VISITOR, SUPPLIER_ROLE, SUPPLIER_ROLE),
    ]
)
def test_visible_project_supplier_status_pa_enabled(app, db,
                                                    sibling_type,
                                                    s1_role,
                                                    sibling_role):
    # Pretend that the tree looks like:
    #
    # client (STOP)
    # +- s1 (ATTENTION)
    # |   +- s1_1 (OK)
    # +- sibling (INVESTIGATE)
    #
    # We'll be checking status through the eyes of s1_1 and should always get OK

    client_company_id = 'test-client-company-id'
    s1_company_id = 'test-supplier-1-company-id'
    s1_1_company_id = 'test-supplier-1-1-company-id'
    sibling_company_id = 'test-sibling-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        pa_form_enabled=True
    )

    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=s1_company_id,
        role=s1_role,
        type=LINKED,
        materialized_path=[client_company_id, s1_company_id]
    )

    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=s1_1_company_id,
        role=SUPPLIER_ROLE,
        type=LINKED,
        materialized_path=[client_company_id, s1_company_id, s1_1_company_id]
    )

    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=sibling_company_id,
        role=sibling_role,
        type=sibling_type,
        materialized_path=[client_company_id, sibling_company_id]
    )

    with freeze_time('2020-01-10'):
        factories.create_status_report(
            db,
            status=STATUS_STOP,
            interested_company_id=s1_1_company_id,
            company_id=client_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
        factories.create_status_report(
            db,
            status=STATUS_ATTENTION,
            interested_company_id=s1_1_company_id,
            company_id=s1_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
        factories.create_status_report(
            db,
            status=STATUS_OK,
            interested_company_id=s1_1_company_id,
            company_id=s1_1_company_id,
            generated_timestamp=datetime.datetime.now(),
        )
        factories.create_status_report(
            db,
            status=STATUS_INVESTIGATE,
            interested_company_id=s1_1_company_id,
            company_id=sibling_company_id,
            generated_timestamp=datetime.datetime.now(),
        )

    assert get_project_list(
        app, active_org_id=s1_1_company_id,
        include_active_org_projects=True, user_role='main',
        cols=['name', 'status']
    ) == [('A Project', STATUS_OK)]


@pytest.mark.parametrize(
    ('supplier_type'), [
        UNLINKED, VISITOR
    ]
)
def test_invisible_project_for_unlinked_and_visitors_status_pa_enabled(app, db, supplier_type):
    client_company_id = 'test-client-company-id'
    s1_company_id = 'test-supplier-1-company-id'
    s1_1_company_id = 'test-supplier-1-1-company-id'

    project = factories.create_project(
        db,
        name='A Project',
        client_company_id=client_company_id,
        pa_form_enabled=True
    )

    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=s1_company_id,
        role=MAIN_CONTRACTOR_ROLE,
        type=LINKED,
        materialized_path=[client_company_id, s1_company_id]
    )

    factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=s1_1_company_id,
        role=SUPPLIER_ROLE,
        type=supplier_type,
        materialized_path=[client_company_id, s1_company_id, s1_1_company_id]
    )

    assert get_project_list(
        app, active_org_id=s1_1_company_id,
        include_active_org_projects=True, user_role='main',
        cols=['name', 'status']
    ) == []


# Project delete tests


def test_project_delete_not_found(app):
    resp = app.delete(
        API_URL + 'projects/not-existing', expect_errors=True,
    )
    expected_error = {
        'message': 'Not found',
        'error_code': 'NotFound',
        'error': {'external_id': 'not-existing'}
    }
    assert 404 == resp.status_code
    assert expected_error == resp.json


def test_project_delete(app, db):
    project = factories.create_project(
        db,
        name='Test project 1',
        external_id='test-external-id'
    )
    control_project = factories.create_project(
        db,
        name='Control Project',
        external_id='test-control-external-id'
    )

    resp = app.delete(API_URL + 'projects/test-external-id')

    assert 200 == resp.status_code
    assert not factories.get_project(db, project['id'])
    assert factories.get_project(db, control_project['id'])


def test_project_delete_deletes_suppliers(app, db):
    project = factories.create_project(
        db,
        name='Test project 1',
        external_id='test-external-id'
    )
    supplier = factories.create_supplier(db, project_id=project['id'])
    resp = app.delete(API_URL + 'projects/test-external-id')
    assert 200 == resp.status_code

    assert not factories.get_project(db, project['id'])
    assert not factories.get_supplier(db, supplier['id'])


def test_project_delete_deletes_preannouncements(app, db):
    project = factories.create_project(
        db,
        name='Test project 1',
        external_id='test-external-id'
    )
    sup1 = factories.create_supplier(db, project_id=project['id'])
    sup2 = factories.create_supplier(db, project_id=project['id'])
    sup3 = factories.create_supplier(db, project_id=project['id'])
    pa = factories.create_preannouncement(
        db, project_id=project['id'], for_supplier_id=sup1['id'],
        created_by_supplier_id=sup2['id'], assigned_to_supplier_id=sup3['id'])
    resp = app.delete(API_URL + 'projects/test-external-id')
    assert 200 == resp.status_code

    assert not factories.get_project(db, project['id'])
    assert not factories.get_supplier(db, sup1['id'])
    assert not factories.get_supplier(db, sup2['id'])
    assert not factories.get_supplier(db, sup3['id'])
    new_pa = factories.get_preannouncement(db, pa['id'])
    assert new_pa['project_id'] is None
    assert new_pa['created_by_supplier_id'] is None
    assert new_pa['for_supplier_id'] is None
    assert new_pa['assigned_to_supplier_id'] is None


def test_project_delete_internal_project_ids(app, db):
    project = factories.create_project(
        db,
        name='Test project 1',
        external_id='test-external-id'
    )
    control_project = factories.create_project(
        db,
        name='Control Project',
        external_id='test-control-external-id'
    )

    factories.create_internal_project_id(db, {
        'company_id': 'test-company-id',
        'internal_project_id': 'test1_id',
        'project_id': project['id'],
    })
    factories.create_internal_project_id(db, {
        'company_id': 'test-company-id',
        'internal_project_id': 'test2_id',
        'project_id': control_project['id'],
    })

    resp = app.delete(API_URL + 'projects/test-external-id')

    assert 200 == resp.status_code
    assert not factories.get_project(db, project['id'])
    assert not factories.get_internal_project_ids(db, project['id'])
    assert factories.get_internal_project_ids(db, control_project['id'])


def test_get_project_supplier_comments(app, db):
    """Test getting all comments for a project."""
    project = factories.create_project(db)
    supplier1 = factories.create_supplier(db, project_id=project["id"])
    supplier2 = factories.create_supplier(db, project_id=project["id"])
    expected_comment1 = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier1["id"]
    )
    expected_comment2 = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier2["id"]
    )
    mark_comment_as_deleted(db, expected_comment2["id"], "", "")

    resp = app.get(API_URL + f'projects/{project["external_id"]}/comments')

    assert resp.status_code == 200
    assert resp.json["resource_type"] == "project_supplier_comments"
    comments = resp.json["resources"]
    assert len(comments) == 2
    assert any(comment["id"] == expected_comment1["id"] for comment in comments)
    assert any(comment["id"] == expected_comment2["id"] for comment in comments)


def test_get_project_supplier_comments_deleted(app, db):
    """Test hiding deleted comments for a supplier."""
    project = factories.create_project(db)
    supplier1 = factories.create_supplier(db, project_id=project["id"])
    supplier2 = factories.create_supplier(db, project_id=project["id"])
    expected_comment1 = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier1["id"]
    )
    expected_comment2 = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier2["id"]
    )
    mark_comment_as_deleted(db, expected_comment2["id"], "", "")

    resp = app.get(
        API_URL + f'projects/{project["external_id"]}/comments?hide_deleted=true'
    )

    assert resp.status_code == 200
    assert resp.json["resource_type"] == "project_supplier_comments"
    comments = resp.json["resources"]
    assert len(comments) == 1
    assert comments[0]["id"] == expected_comment1["id"]


def test_get_project_supplier_comments_with_org_filter(app, db):
    """Test getting comments for a project filtered by organization ID."""
    project = factories.create_project(db)
    supplier1 = factories.create_supplier(db, project_id=project["id"])
    supplier2 = factories.create_supplier(db, project_id=project["id"])
    comment1 = factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier1["id"], org_id="org1-id"
    )
    factories.create_comment(
        db, project_id=project["id"], supplier_id=supplier2["id"], org_id="org2-id"
    )

    resp = app.get(
        API_URL + f'projects/{project["external_id"]}/comments?org_id=org1-id'
    )

    assert resp.status_code == 200
    assert resp.json["resource_type"] == "project_supplier_comments"
    assert len(resp.json["resources"]) == 1
    assert resp.json["resources"][0]["id"] == comment1["id"]
    assert resp.json["resources"][0]["org_id"] == "org1-id"


def test_get_project_supplier_comments_empty(app, db):
    """Test getting comments for a project with no comments."""
    project = factories.create_project(db)

    resp = app.get(API_URL + f'projects/{project["external_id"]}/comments')

    assert resp.status_code == 200
    assert resp.json["resource_type"] == "project_supplier_comments"
    assert len(resp.json["resources"]) == 0


def test_get_project_supplier_comments_nonexisting_project(app, db):
    """Test getting comments for a project that doesn't exist."""

    resp = app.get(API_URL + "projects/missing-project/comments", expect_errors=True)

    assert resp.status_code == 404
