# Copied from https://git.vaultit.org/Foretagsdeklaration/foretagsdeklaration/blob/c5d63ec80934f40cbade09e60e60e294c03929b5/server/test/test_logging.py # noqa
import contextlib
import io
import json
import logging
import logging.config
import pathlib
import time

import bottle
import pytest
import webtest
from freezegun.api import freeze_time

from boldataapi.logging import Formatter, traverse


logger = logging.getLogger()


@pytest.fixture()
def app(mocker):
    app = bottle.Bottle(catchall=False)

    @app.hook('before_request')
    def setup_request():
        bottle.request.request_id = 'aa0bc2b8-0cc1-49bd-9b5c-c475b366bee5'
        bottle.request.user_id = '5e6a-af7701c5ed1e499e70505552dbf36e75-576a8652'

    @app.get('/')
    def debug():
        logger.debug('app test')
        return 'ok'

    @app.get('/warn')
    def warn():
        logger.warning('a warning message')
        return 'ok'

    return webtest.TestApp(app)


def test_traverse():
    data = [1, 2, {'a': 3}]
    assert [node[key] for node, key in traverse(data)] == [1, 2, 3]


def test_traverse_update():
    data = [1, 2, {'a': 3}]
    for node, key in traverse(data):
        node[key] += 1
    assert data == [2, 3, {'a': 4}]


@contextlib.contextmanager
def formatter(*args, **kwargs):
    formatter = Formatter(*args, **kwargs)
    formatter.converter = time.gmtime  # don't want test to depend on local timezone offset
    handler = logging.StreamHandler(io.StringIO())
    handler.setFormatter(formatter)
    old_level = logger.level
    old_propagate = logger.propagate
    logger.addHandler(handler)
    try:
        logger.propagate = False
        logger.setLevel(logging.NOTSET)
        yield handler
    finally:
        logger.setLevel(old_level)
        logger.propagate = old_propagate
        logger.removeHandler(handler)


def test_formatter():
    fmt = '%(levelname)s %(requestId)s %(user)s %(message)s'
    with formatter(fmt, style='%') as handler:
        logger.info('test')
        assert handler.stream.getvalue() == 'INFO   test\n'


@freeze_time('2020-09-02 12:27:21.0537819Z')
def test_formatter_milliseconds(mocker):
    fmt = '%(asctime)s %(message)s'
    with formatter(fmt, datefmt='%H:%M:%S.%f') as handler:
        logger.info('hi')
        assert handler.stream.getvalue() == '12:27:21.053 hi\n'


def test_json_style_formatter():
    fmt = json.dumps(['levelname', 'requestId', 'user', {'message': 'message'}])
    with formatter(fmt, style='json') as handler:
        logger.info('test')
        assert json.loads(handler.stream.getvalue()) == [
            'INFO', '', '', {'message': 'test'}
        ]


def test_formatter_extra_context(app):
    with formatter('%(levelname)s %(requestId)s %(user)s', style='%') as handler:
        app.get('/')
        assert handler.stream.getvalue() == (
            'DEBUG aa0bc2b8-0cc1-49bd-9b5c-c475b366bee5 '
            '5e6a-af7701c5ed1e499e70505552dbf36e75-576a8652\n'
        )


@pytest.mark.parametrize('filename', [
    'development.cfg',
    '../infrastructure/boldataapi.cfg',
])
def test_cfg_files(mocker, app, caplog, filename, monkeypatch):
    real_getLogger = logging.getLogger
    real_StreamHandler = logging.StreamHandler

    logger = logging.getLogger('test.logger')
    logger.level = logging.NOTSET
    logger.handlers = []
    logger.propagate = False

    buffer = io.StringIO()

    class FakeStreamHandler(real_StreamHandler):
        def __init__(self, *args):
            real_StreamHandler.__init__(self, buffer)

    mocker.patch('logging.root', logger)
    mocker.patch('logging.StreamHandler', FakeStreamHandler)
    mocker.patch('logging.getLogger',
                 lambda name=None: real_getLogger('test.logger.' + name) if name else logger)
    mocker.patch('logging.config._handle_existing_loggers')

    monkeypatch.chdir(str(pathlib.Path(__file__).parents[1]))

    logging.config.fileConfig(filename, disable_existing_loggers=False)
    logger.warning('a warning message')
    logging.getLogger('bda-access').info('{"msg_type": "accesslog"}')
    print(buffer.getvalue())
    assert 'a warning message' in buffer.getvalue()
    assert '{"msg_type": "accesslog"}' in buffer.getvalue()
