import datetime
import uuid

import pytest
import sqlalchemy as sa

from boldataapi import exceptions
from boldataapi.fixtures import factories
from boldataapi.schema import (
    CLEANING,
    CONTRACTING,
    DEMOLITION,
    PA_STATUS_REJECTED,
)
from boldataapi.services.bol_suppliers import (
    _update_supplier_contacts,
    delete_supplier,
    get_supplier,
    update_supplier,
)
from boldataapi.storage.db import SUPPLIER_CONTACTS_TABLE


def test_get_supplier_not_existing(db):
    assert not get_supplier(db, external_id='not-existing')


def test_get_supplier(db, config):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        contract_type=CONTRACTING,
        contract_work_areas=[CLEANING, DEMOLITION],
        materialized_path=['item1', 'item2'],
    )
    assert get_supplier(db, supplier_id=supplier['id']) == {
        'id': supplier['id'],
        'external_id': supplier['external_id'],
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': datetime.date(2020, 1, 1),
        'contract_end_date': datetime.date(2020, 12, 1),
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': None,
        'pa_status': None,
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'revision': supplier['revision'],
        'internal_project_id': None,
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': None,
        'last_visited': None,
        'visitor_type': None,
        'is_one_man_company': None,
        'has_collective_agreement': None,
        'collective_agreement_name': None,
    }


def test_get_supplier_with_pa(db, config):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        contract_type=CONTRACTING,
        contract_work_areas=[CLEANING, DEMOLITION],
        materialized_path=['item1', 'item2'],
    )
    pa = factories.create_preannouncement(db, status=PA_STATUS_REJECTED,
                                          for_supplier_id=supplier['id'])
    assert get_supplier(db, supplier_id=supplier['id']) == {
        'id': supplier['id'],
        'external_id': supplier['external_id'],
        'supplier_role': 'supplier',
        'supplier_type': 'linked',
        'contract_start_date': datetime.date(2020, 1, 1),
        'contract_end_date': datetime.date(2020, 12, 1),
        'contract_type': CONTRACTING,
        'contract_work_areas': [CLEANING, DEMOLITION],
        'materialized_path': ['item1', 'item2'],
        'project_resource_id': project['external_id'],
        'pa_id': pa['external_id'],
        'pa_status': pa['status'],
        'parent_supplier_id': 'any-parent-supplier-id',
        'parent_org_id': 'any-parent-id',
        'supplier_org_id': 'any-company-id',
        'revision': supplier['revision'],
        'internal_project_id': None,
        'supplier_contacts': [],
        'bolagsfakta_status': None,
        'type': 'bol_supplier',
        'first_visited': None,
        'last_visited': None,
        'visitor_type': None,
        'is_one_man_company': None,
        'has_collective_agreement': None,
        'collective_agreement_name': None,
    }


def test_get_supplier_search_params(db):
    project = factories.create_project(db)
    factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    with pytest.raises(exceptions.InternalServerError):
        get_supplier(db)

    with pytest.raises(exceptions.InternalServerError):
        get_supplier(db, external_id='xxx', supplier_id='xxx')


def test_update_supplier_revision_not_provided(db, mocker):
    update_suppliers_mock = mocker.patch(
        'boldataapi.services.bol_suppliers._update_supplier_contacts'
    )
    with pytest.raises(KeyError) as execinfo:
        update_supplier(db, 'any-id', {})
    assert 'revision' in str(execinfo.value)
    assert not update_suppliers_mock.called


def test_update_supplier_not_existing_supplier(db, mocker):
    supplier_id = str(uuid.uuid4())
    update_suppliers_mock = mocker.patch(
        'boldataapi.services.bol_suppliers._update_supplier_contacts'
    )
    data = {
        'revision': 'any',
        'role': 'supervisor',
    }
    update_supplier(db, supplier_id, data)
    assert not update_suppliers_mock.called


def test_update_supplier_wrong_revision(db, mocker, config):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        role='supplier',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    update_suppliers_mock = mocker.patch(
        'boldataapi.services.bol_suppliers._update_supplier_contacts'
    )
    data = {
        'revision': 'any',
        'role': 'supervisor',
    }
    update_supplier(db, supplier['id'], data)
    updated_supplier = get_supplier(db, supplier_id=supplier['id'])
    assert updated_supplier['supplier_role'] == 'supplier'
    assert not update_suppliers_mock.called


def test_update_supplier(db, mocker, config):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        role='supplier',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    update_suppliers_mock = mocker.patch(
        'boldataapi.services.bol_suppliers._update_supplier_contacts'
    )
    data = {
        'revision': supplier['revision'],
        'role': 'supervisor',
    }
    update_supplier(db, supplier['id'], data)
    updated_supplier = get_supplier(db, supplier_id=supplier['id'])
    assert updated_supplier['supplier_role'] == 'supervisor'
    assert not update_suppliers_mock.called


def test_update_supplier_updates_contacts(db, mocker, config):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        role='supplier',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    update_suppliers_mock = mocker.patch(
        'boldataapi.services.bol_suppliers._update_supplier_contacts'
    )
    data = {
        'revision': supplier['revision'],
        'role': 'supervisor',
    }
    update_supplier(db, supplier['id'], data, supplier_contacts=[])
    updated_supplier = get_supplier(db, supplier_id=supplier['id'])
    assert updated_supplier['supplier_role'] == 'supervisor'
    assert update_suppliers_mock.called


def test_update_supplier_contacts_removes_existing(db):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        role='supplier',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )
    supplier_contact_payload = {
        'person_id': 'fake-person-id',
        'person_email': '<EMAIL>',
        'supplier_id': supplier['id'],
    }
    factories.create_supplier_contact(db, supplier_contact_payload)
    _update_supplier_contacts(db, supplier['id'], [])
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]
    qry = (
        sa.select([s_contacts]).where(s_contacts.c.supplier_id == supplier['id'])
    )
    rez = db.session.execute(qry)
    assert not [row for row in rez]


def test_update_supplier_contact_adds_new(db):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        parent_company_id='any-parent-id',
        company_id='any-company-id',
        role='supplier',
        parent_supplier_id='any-parent-supplier-id',
        contract_start_date=datetime.date(2020, 1, 1),
        contract_end_date=datetime.date(2020, 12, 1),
        materialized_path=['item1', 'item2'],
    )

    supplier_contacts = [
        {
            'supplier_contact_person_id': 'fake-person-id',
            'supplier_contact_email': '<EMAIL>'
        }
    ]

    _update_supplier_contacts(db, supplier['id'], supplier_contacts)
    s_contacts = db.meta.tables[SUPPLIER_CONTACTS_TABLE]
    qry = (
        sa.select([s_contacts]).where(s_contacts.c.supplier_id == supplier['id'])
    )
    rez = db.session.execute(qry)
    added_contacts = [row for row in rez]
    assert len(added_contacts) == 1
    assert added_contacts[0]['supplier_id'] == supplier['id']
    assert added_contacts[0]['person_id'] == 'fake-person-id'
    assert added_contacts[0]['person_email'] == '<EMAIL>'


def test_update_supplier_contacts(db):
    assert not _update_supplier_contacts(db, str(uuid.uuid4()), [])


def test_delete_supplier(db):
    project = factories.create_project(db)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        company_id='test-id'
    )
    delete_supplier(db, supplier['id'])
    assert not factories.get_supplier(db, supplier['id'])


def test_delete_supplier_leaves_internal_project_id_of_client(db):
    client_org_id = 'test-client-org-id'
    project = factories.create_project(db, client_company_id=client_org_id)
    assert not factories.get_internal_project_ids(db, project['id'])

    internal_project_id_payload = {
        'internal_project_id': 'Test Internal Project ID',
        'project_id': project['id'],
        'company_id': client_org_id,
    }
    factories.create_internal_project_id(db, internal_project_id_payload)
    supplier = factories.create_supplier(
        db,
        project_id=project['id'],
        company_id=client_org_id,
        role='supervisor',
    )
    delete_supplier(db, supplier['id'])
    internal_project_id = factories.get_internal_project_ids(db, project['id'])[0]
    internal_project_id.pop('id')

    expected_internal_project_id = {
        'internal_project_id': 'Test Internal Project ID',
        'project_id': project['id'],
        'company_id': client_org_id,
    }
    assert internal_project_id == expected_internal_project_id
