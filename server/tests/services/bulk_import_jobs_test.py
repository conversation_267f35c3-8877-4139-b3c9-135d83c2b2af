import datetime

import pytest

from boldataapi.fixtures import factories
from boldataapi.services.bulk_import_jobs import (
    create_bulk_import_job,
    get_bulk_import_job,
    get_last_bulk_import_job,
    update_bulk_import_job,
)


def test_create_bulk_import_job(db, set_feature_flag):
    set_feature_flag('core', False)
    payload = {
        'status': 'pending',
        'project_id': '7d91590b-23a8-4513-a2c2-6dd4bbb4c01c',
        'imported': None,
        'canceled': None,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None, None, None],
        ],
        'interested_org_id': '581f998a-cc59-4205-b4c1-dd4e7c190bb5',
    }
    job = create_bulk_import_job(db, payload.copy())
    assert job == {
        'id': job['id'],
        'status': payload['status'],
        'project_id': payload['project_id'],
        'imported': payload['imported'],
        'canceled': payload['canceled'],
        'companies': payload['companies'],
        'interested_org_id': payload['interested_org_id'],
        'last_changed': job['last_changed'],
        'created_on': job['created_on'],
    }


def test_create_bulk_import_job_adds_null(db, set_feature_flag):
    # Ensure that all new jobs have 9 elements in the companies array
    set_feature_flag('core', False)
    payload = {
        'status': 'pending',
        'project_id': '7d91590b-23a8-4513-a2c2-6dd4bbb4c01c',
        'imported': None,
        'canceled': None,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None, None, None],
        ],
        'interested_org_id': '581f998a-cc59-4205-b4c1-dd4e7c190bb5',
    }
    job = create_bulk_import_job(db, payload.copy())
    set_feature_flag('core', True)
    core_job = get_bulk_import_job(db, job['id'])
    assert core_job == {
        'id': job['id'],
        'status': payload['status'],
        'project_id': payload['project_id'],
        'imported': payload['imported'],
        'canceled': payload['canceled'],
        'companies': [[*payload['companies'][0], None, None]],
        'interested_org_id': payload['interested_org_id'],
        'last_changed': job['last_changed'],
        'created_on': job['created_on'],
    }


def test_create_bulk_import_job_with_9_company_values(db, set_feature_flag):
    # Test that a provided external_id (the last element for each company) is added, but only
    # exposed when core is enabled.
    set_feature_flag('core', False)
    payload = {
        'status': 'pending',
        'project_id': '7d91590b-23a8-4513-a2c2-6dd4bbb4c01c',
        'imported': None,
        'canceled': None,
        'companies': [
            ['123456-7890', None, 'pending', None, None, None, None, None, '123'],
        ],
        'interested_org_id': '581f998a-cc59-4205-b4c1-dd4e7c190bb5',
    }
    job = create_bulk_import_job(db, payload.copy())
    # Only the first 8 elements are exposed when core is disabled, keeping the API unchanged.
    assert job == {
        'id': job['id'],
        'status': payload['status'],
        'project_id': payload['project_id'],
        'imported': payload['imported'],
        'canceled': payload['canceled'],
        'companies': [payload['companies'][0][:8]],
        'interested_org_id': payload['interested_org_id'],
        'last_changed': job['last_changed'],
        'created_on': job['created_on'],
    }
    set_feature_flag('core', True)
    core_job = get_bulk_import_job(db, job['id'])
    # When core is enabled, all 9 elements are exposed.
    assert core_job == {
        'id': job['id'],
        'status': payload['status'],
        'project_id': payload['project_id'],
        'imported': payload['imported'],
        'canceled': payload['canceled'],
        'companies': payload['companies'],
        'interested_org_id': payload['interested_org_id'],
        'last_changed': job['last_changed'],
        'created_on': job['created_on'],
    }


@pytest.mark.parametrize("job_id", [
    "b1826fa3-7fd2-4112-bbae-d0b3423d507d",
    "no-such-id",  # because PostgreSQL complains about invalid syntax when it's not an UUID
    "",
])
def test_get_bulk_import_job_when_missing(db, job_id):
    job = get_bulk_import_job(db, job_id)
    assert job is None


def test_get_bulk_import_job(db, set_feature_flag):
    set_feature_flag('core', False)
    job = factories.create_bulk_import_job(db)
    assert get_bulk_import_job(db, job['id']) == job


def s(s):
    """A brief way to spell seconds."""
    return datetime.timedelta(seconds=s)


def test_get_last_bulk_import_job_when_db_is_empty(db):
    project = factories.create_project(db)
    assert get_last_bulk_import_job(db, project['id']) is None


def test_get_last_bulk_import_job(db, set_feature_flag):
    set_feature_flag('core', False)
    projects = [factories.create_project(db) for n in range(2)]
    now = datetime.datetime.now()
    jobs = [
        # this is not very realistic data, but the point is to demostrate that
        # we choose first by status, then by creation time
        factories.create_bulk_import_job(
            db, project_id=projects[0]['id'], created_on=now, status='done'),
        factories.create_bulk_import_job(
            db, project_id=projects[0]['id'], created_on=now, status='failed'),
        factories.create_bulk_import_job(
            db, project_id=projects[0]['id'], created_on=now, status='pending'),
        # these have the best status and will be ordered by creation time
        factories.create_bulk_import_job(
            db, project_id=projects[0]['id'], created_on=now - s(2), status='in_progress'),
        factories.create_bulk_import_job(
            db, project_id=projects[0]['id'], created_on=now - s(1), status='in_progress'),
        # this one has the best status and creation time, but is for a different project
        factories.create_bulk_import_job(
            db, project_id=projects[1]['id'], created_on=now, status='in_progress'),
    ]
    assert get_last_bulk_import_job(db, projects[0]['id']) == jobs[-2]


@pytest.mark.parametrize("job_id", [
    "b1826fa3-7fd2-4112-bbae-d0b3423d507d",
    "no-such-id",  # because PostgreSQL complains about invalid syntax when it's not an UUID
    "",
])
def test_update_bulk_import_job_when_missing(db, job_id):
    payload = {
        'status': 'in_progress',
    }
    update_bulk_import_job(db, job_id, payload)
