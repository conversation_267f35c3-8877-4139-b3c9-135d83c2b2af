import uuid

import pytest
import sqlalchemy as sa

from boldataapi import exceptions
from boldataapi.fixtures.factories import create_comment, create_comment_viewer
from boldataapi.services.project_comment_viewers import (
    create_project_comment_viewer,
    delete_project_comment_viewer,
    get_project_comment_viewer_by_comment_id_and_person_id,
)


def test_get_project_comment_viewer_by_comment_id(db):
    # Create a comment
    comment = create_comment(db)

    # Create multiple viewers for the same comment
    person_id1 = str(uuid.uuid4())
    person_id2 = str(uuid.uuid4())

    create_comment_viewer(db, comment["id"], person_id1)
    create_comment_viewer(db, comment["id"], person_id2)

    # Create another comment with a different viewer (should not be returned)
    comment2 = create_comment(db)
    create_comment_viewer(db, comment2["id"], person_id1)

    # Get viewers by comment_id
    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, comment_id=comment["id"]
    )

    # Should return both viewers and the creator viewer
    assert len(viewers) == 3
    viewer_person_ids = [v["read_by_person_id"] for v in viewers]
    assert str(person_id1) in viewer_person_ids
    assert str(person_id2) in viewer_person_ids


def test_get_project_comment_viewer_by_person_id(db):
    """Test getting viewers by person_id."""
    # Create two comments
    comment1 = create_comment(db)
    comment2 = create_comment(db)

    # Create viewers with the same person for different comments
    person_id1 = str(uuid.uuid4())
    person_id2 = str(uuid.uuid4())

    create_comment_viewer(db, comment1["id"], person_id1)
    create_comment_viewer(db, comment2["id"], person_id1)

    # Create a viewer for a different person_id (should not be returned)
    create_comment_viewer(db, comment2["id"], person_id2)

    # Get viewers by person_id
    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, person_id=person_id1
    )

    # Should return both viewers
    assert len(viewers) == 2
    viewer_comment_ids = [viewer["comment_id"] for viewer in viewers]
    assert str(comment1["id"]) in viewer_comment_ids
    assert str(comment2["id"]) in viewer_comment_ids


def test_get_project_comment_viewer_by_comment_id_and_person_id(db):
    """Test getting viewers by both comment_id and person_id."""
    # Create two comments
    comment1 = create_comment(db)
    comment2 = create_comment(db)

    # Create viewers with different persons for both comments
    person_id1 = str(uuid.uuid4())
    person_id2 = str(uuid.uuid4())

    create_comment_viewer(db, comment1["id"], person_id1)
    create_comment_viewer(db, comment1["id"], person_id2)
    create_comment_viewer(db, comment2["id"], person_id1)
    create_comment_viewer(db, comment2["id"], person_id2)

    # Get viewers by both comment_id and person_id
    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, comment_id=comment1["id"], person_id=person_id1
    )

    # Should return only the matching viewer
    assert len(viewers) == 1
    assert viewers[0]["comment_id"] == str(comment1["id"])
    assert viewers[0]["read_by_person_id"] == str(person_id1)


def test_get_project_comment_viewer_with_nonexistent_ids(db):
    """Test getting viewers with non-existent IDs."""
    # Get viewers with non-existent comment_id
    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, comment_id=str(uuid.uuid4())
    )
    assert len(viewers) == 0

    # Get viewers with non-existent person_id
    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, person_id=str(uuid.uuid4())
    )
    assert len(viewers) == 0


def test_create_project_comment_viewer(db):
    """Test creating a project comment viewer."""
    # Create a comment
    comment = create_comment(db)
    person_id = str(uuid.uuid4())

    # Create a viewer
    viewer = create_project_comment_viewer(db, str(comment["id"]), person_id)

    # Verify the viewer was created
    assert viewer["comment_id"] == str(comment["id"])
    assert viewer["read_by_person_id"] == person_id
    assert "id" in viewer

    # Verify it can be retrieved
    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, comment_id=str(comment["id"]), person_id=person_id
    )
    assert len(viewers) == 1
    assert viewers[0]["id"] == viewer["id"]


def test_create_project_comment_viewer_with_nonexistent_comment(db):
    """Test creating a viewer with a non-existent comment ID."""
    non_existent_comment_id = str(uuid.uuid4())
    person_id = str(uuid.uuid4())

    with pytest.raises(exceptions.NotFound) as excinfo:
        create_project_comment_viewer(db, non_existent_comment_id, person_id)

    # Verify the error message
    assert excinfo.value.error == {"comment_id": non_existent_comment_id}


def test_create_duplicate_project_comment_viewer(db):
    """Test that creating a duplicate viewer raises an IntegrityError."""
    comment = create_comment(db)
    person_id = str(uuid.uuid4())

    # Create a viewer
    create_project_comment_viewer(db, str(comment["id"]), person_id)

    # Try to create a duplicate viewer
    with pytest.raises(sa.exc.IntegrityError) as excinfo:
        create_project_comment_viewer(db, str(comment["id"]), person_id)

    # Verify the error is related to unique constraint
    error_msg = str(excinfo.value).lower()
    assert (
        "duplicate key value violates unique constraint "
        '"uq_project_comment_viewers_project_comment_id_read_by_person_id"' in error_msg
    )


def test_delete_project_comment_viewer(db):
    """Test deleting a project comment viewer."""
    comment = create_comment(db)
    person_id = str(uuid.uuid4())

    # Create a viewer
    viewer = create_comment_viewer(db, comment["id"], person_id)

    # Delete the viewer
    result = delete_project_comment_viewer(db, viewer["id"])
    assert result is True

    # Verify it's deleted
    viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db, comment_id=str(comment["id"]), person_id=person_id
    )
    assert len(viewers) == 0


def test_delete_nonexistent_project_comment_viewer(db):
    """Test deleting a non-existent project comment viewer."""
    result = delete_project_comment_viewer(db, str(uuid.uuid4()))
    assert result is False
