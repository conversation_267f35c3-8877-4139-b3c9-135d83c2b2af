import uuid

from boldataapi.fixtures import factories
from boldataapi.services.project_comment_viewers import (
    create_project_comment_viewer,
    get_project_comment_viewer_by_comment_id_and_person_id,
)
from boldataapi.services.project_supplier_comments import (
    create_project_supplier_comment,
    delete_project_supplier_comment,
    get_project_supplier_comment,
    mark_comment_as_deleted,
    query_project_supplier_comments,
    update_project_supplier_comment,
)


def test_get_project_supplier_comment_not_existing(db):
    """Test retrieving a non-existing project supplier comment."""
    assert not get_project_supplier_comment(db, comment_id=str(uuid.uuid4()))


def test_get_project_supplier_comment_by_id(db):
    """Test retrieving a project supplier comment by ID."""

    created_comment = factories.create_comment(db)
    comment_id = created_comment["id"]

    comment = get_project_supplier_comment(db, comment_id=comment_id)

    assert comment == created_comment


def test_create_project_supplier_comment(db):
    """Test creating a project supplier comment."""
    project = factories.create_project(db)
    supplier = factories.create_supplier(db, project_id=project["id"])
    created_comment = create_project_supplier_comment(
        db,
        {
            "project_id": project["id"],
            "supplier_id": supplier["id"],
            "org_id": "some-org-id",
            "comment": "Original text",
            "created_by_org_id": "create-org-id",
            "created_by_person_id": "create-person-id",
        },
    )
    created_comment_viewers = get_project_comment_viewer_by_comment_id_and_person_id(
        db,
        person_id="create-person-id",
        comment_id=created_comment["id"],
    )

    assert len(created_comment_viewers) == 1
    assert created_comment is not None
    assert created_comment["id"] is not None
    assert created_comment["comment"] == "Original text"
    assert created_comment["project_id"] == project["external_id"]
    assert created_comment["supplier_id"] == supplier["external_id"]
    assert created_comment["org_id"] == "some-org-id"
    assert created_comment["created_by_org_id"] == "create-org-id"
    assert created_comment["created_by_person_id"] == "create-person-id"
    assert created_comment["created_timestamp"] is not None
    assert created_comment["is_deleted"] is False
    assert created_comment["deleted_by_org_id"] is None
    assert created_comment["deleted_by_person_id"] is None
    assert created_comment["deleted_timestamp"] is None
    assert created_comment["is_updated"] is False
    assert created_comment["updated_by_org_id"] is None
    assert created_comment["updated_by_person_id"] is None
    assert created_comment["updated_timestamp"] is None
    assert created_comment["modified_timestamp"] is None


def test_update_project_supplier_comment(db):
    """Test updating a project supplier comment."""

    created_comment = factories.create_comment(db)
    comment_id = created_comment["id"]

    updated_comment = update_project_supplier_comment(
        db, comment_id, "Updated comment", "update-org-id", "update-person-id"
    )

    assert updated_comment is not None
    assert updated_comment["id"] == comment_id
    assert updated_comment["comment"] == "Updated comment"
    assert updated_comment["is_updated"] is True
    assert updated_comment["updated_by_org_id"] == "update-org-id"
    assert updated_comment["updated_by_person_id"] == "update-person-id"
    assert updated_comment["updated_timestamp"] is not None
    assert updated_comment["modified_timestamp"] is not None


def test_mark_comment_as_deleted(db):
    """Test marking a project supplier comment as deleted."""
    created_comment = factories.create_comment(db)
    comment_id = created_comment["id"]
    assert created_comment["deleted_timestamp"] is None
    assert created_comment["is_deleted"] is False

    deleted_comment = mark_comment_as_deleted(
        db, comment_id, "delete-org-id", "delete-person-id"
    )

    assert deleted_comment is not None
    assert deleted_comment["id"] == comment_id
    assert deleted_comment["is_deleted"] is True
    assert deleted_comment["deleted_by_org_id"] == "delete-org-id"
    assert deleted_comment["deleted_by_person_id"] == "delete-person-id"
    assert deleted_comment["deleted_timestamp"] is not None
    assert deleted_comment["modified_timestamp"] is not None


def test_delete_project_supplier_comment(db):
    """Test permanently deleting a project supplier comment."""
    created_comment = factories.create_comment(db)
    comment_id = created_comment["id"]

    delete_project_supplier_comment(db, comment_id)

    comment = get_project_supplier_comment(db, comment_id=comment_id)
    assert comment is None


def test_query_project_supplier_comments_by_project(db):
    """Test querying project supplier comments by project ID."""
    project1 = factories.create_project(db, name="Project 1")
    project2 = factories.create_project(db, name="Project 2")
    supplier1 = factories.create_supplier(db, project_id=project1["id"])
    supplier2 = factories.create_supplier(db, project_id=project2["id"])

    # Create comments for two projects and two suppliers
    factories.create_comment(
        db,
        project_id=project1["id"],
        supplier_id=supplier1["id"],
        comment="Comment for project 1",
    )
    factories.create_comment(
        db,
        project_id=project2["id"],
        supplier_id=supplier2["id"],
        comment="Comment for project 2",
    )

    comments = query_project_supplier_comments(db, project_id=project1["external_id"])

    # Verify only comments for project 1 are returned
    assert len(comments) == 1
    assert comments[0]["project_id"] == project1["external_id"]
    assert comments[0]["comment"] == "Comment for project 1"
    assert comments[0]["is_read"] is None


def test_query_project_supplier_comments_by_supplier(db):
    """Test querying project supplier comments by supplier ID."""
    project = factories.create_project(db, name="Project 1")
    supplier1 = factories.create_supplier(db, project_id=project["id"])
    supplier2 = factories.create_supplier(db, project_id=project["id"])

    # Create comments for two projects
    factories.create_comment(
        db,
        project_id=project["id"],
        supplier_id=supplier1["id"],
        comment="Comment for project 1",
    )
    factories.create_comment(
        db,
        project_id=project["id"],
        supplier_id=supplier2["id"],
        comment="Comment for project 2",
    )

    comments = query_project_supplier_comments(db, supplier_id=supplier1["external_id"])

    # Verify only comments for supplier 1 are returned
    assert len(comments) == 1
    assert comments[0]["supplier_id"] == supplier1["external_id"]
    assert comments[0]["comment"] == "Comment for project 1"
    assert comments[0]["is_read"] is None


def test_query_project_supplier_comments_by_supplier_id_and_org_id(db):
    """Test querying project supplier comments by supplier ID and org ID."""
    project = factories.create_project(db)
    supplier1 = factories.create_supplier(db, project_id=project["id"])
    supplier2 = factories.create_supplier(db, project_id=project["id"])

    # Create comments for two suppliers
    factories.create_comment(
        db,
        project_id=project["id"],
        supplier_id=supplier1["id"],
        org_id="org_id1",
        comment="Comment for supplier 1",
    )
    factories.create_comment(
        db,
        project_id=project["id"],
        supplier_id=supplier2["id"],
        org_id="org_id2",
        comment="Comment for supplier 2",
    )

    comments = query_project_supplier_comments(
        db, project_id=project["external_id"], org_id="org_id1"
    )

    # Verify only comments for supplier 1 are returned
    assert len(comments) == 1
    assert comments[0]["supplier_id"] == supplier1["external_id"]
    assert comments[0]["comment"] == "Comment for supplier 1"
    assert comments[0]["is_read"] is None


def test_query_project_supplier_comments_with_read_status(db):
    """Test querying project supplier comments with read status by including a person_id."""
    project1 = factories.create_project(db, name="Project 1")
    supplier1 = factories.create_supplier(db, project_id=project1["id"])
    supplier2 = factories.create_supplier(db, project_id=project1["id"])
    person_id = str(uuid.uuid4())

    c1 = factories.create_comment(
        db,
        project_id=project1["id"],
        supplier_id=supplier1["id"],
        comment="Comment1",
    )
    create_project_comment_viewer(db, c1["id"], person_id)
    factories.create_comment(
        db,
        project_id=project1["id"],
        supplier_id=supplier2["id"],
        comment="Comment2",
    )

    comments = query_project_supplier_comments(
        db, project_id=project1["external_id"], read_by_person_id=person_id
    )

    assert len(comments) == 2
    assert any(c["comment"] == "Comment1" and c["is_read"] == True for c in comments)
    assert any(c["comment"] == "Comment2" and c["is_read"] == False for c in comments)
