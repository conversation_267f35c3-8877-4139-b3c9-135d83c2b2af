import pytest

from boldataapi.services.project_users import get_project_user


def test_get_project_user_bad_id_format(db):
    assert not get_project_user(db, project_user_id='this-is-not-an-uuid')


def test_get_project_user_not_enough_arguments(db):
    with pytest.raises(
        TypeError,
        match=r"^Exactly one of project_user_id or external_id must be provided$",
    ):
        get_project_user(db)


def test_get_project_user_too_many_arguments(db):
    with pytest.raises(
        TypeError,
        match=r"^Exactly one of project_user_id or external_id must be provided$",
    ):
        get_project_user(db, project_user_id='one-id', external_id='another-id')
