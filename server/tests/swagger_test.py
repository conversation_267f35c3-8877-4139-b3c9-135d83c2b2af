import textwrap

import yaml

from boldataapi.schema import (
    QVARN_COMPAT_FIELDS_FOR_PROJECTS,
    QVARN_COMPAT_FIELDS_FOR_REPORTS,
)
from boldataapi.swagger import (
    qvarn_compat_schema_for_reading,
    qvarn_compat_schema_for_writing,
    schema_variant,
)


def test_schema_variant():
    SCHEMA_CONTACT = yaml.safe_load(textwrap.dedent('''\
    properties:
      id:
        type: string
      name:
        type: string
      email:
        type: string
    '''))

    SCHEMA_NEW_CONTACT = schema_variant(SCHEMA_CONTACT, textwrap.dedent('''\
    required:
      - name
      - email
      - verify_email
    properties:
      id: <DELETED>
      email:
        description: first copy for typo checking
      verify_email:
        type: string
        description: second copy for typo checking
    '''))

    assert SCHEMA_NEW_CONTACT == yaml.safe_load(textwrap.dedent('''\
    required:
      - name
      - email
      - verify_email
    properties:
      name:
        type: string
      email:
        type: string
        description: first copy for typo checking
      verify_email:
        type: string
        description: second copy for typo checking
    '''))


def test_qvarn_compat_schema():
    # let's make sure it doesn't raise
    qvarn_compat_schema_for_reading(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    qvarn_compat_schema_for_writing(QVARN_COMPAT_FIELDS_FOR_REPORTS)
    qvarn_compat_schema_for_reading(QVARN_COMPAT_FIELDS_FOR_PROJECTS)
    qvarn_compat_schema_for_writing(QVARN_COMPAT_FIELDS_FOR_PROJECTS)
