# Based on the Debian default /etc/nginx/nginx.conf
error_log /dev/stdout info;   # <-- override ASAP, /var/log is not writable
#user www-data;               # <-- ignored, since we run everything as non-root
worker_processes auto;
#pid /run/nginx.pid;          # <-- /run is not writable
pid /var/lib/nginx/nginx.pid; # <-- it's pointless, but we can't turn it off
include /etc/nginx/modules-enabled/*.conf;

events {
  worker_connections 768;
  # multi_accept on;
}

http {

  ##
  # Basic Settings
  ##

  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 65;
  types_hash_max_size 2048;
  server_tokens off;          # <-- for security through obscurity reasons

  # server_names_hash_bucket_size 64;
  # server_name_in_redirect off;

  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  ##
  # SSL Settings
  ##

  ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
  ssl_prefer_server_ciphers on;

  ##
  # Logging Settings
  ##

  #access_log /var/log/nginx/access.log;  # <-- this is Spart^H^H^H^H
  #error_log /var/log/nginx/error.log;    #     Kubernetes!

  ##
  # Gzip Settings
  ##

  gzip on;

  # gzip_vary on;
  # gzip_proxied any;
  # gzip_comp_level 6;
  # gzip_buffers 16 8k;
  # gzip_http_version 1.1;
  # gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

  ##
  # Virtual Host Configs
  ##

  #include /etc/nginx/conf.d/*.conf;              # <-- nothing there in our container
  #include /etc/nginx/sites-enabled/*;            # <-- ditto

# --- direct inclusion of our virtual host config here, apologies for the indentation ---
# uncomment to suppress the Server: header if you're paranoid
# server_tokens off;
# more_clear_headers Server;

log_format custom_format escape=json '{'
    '"time_local":"$time_iso8601",'
    '"status":$status,'
    '"method":"$request_method",'
    '"path":"$uri",'
    '"request_size":$request_length,'
    '"bytes_sent":$bytes_sent,'
    '"request_time":"$request_time",'
    '"http_referrer":"$http_referer",'
    '"http_user_agent":"$http_user_agent",'
    '"remote_addr":"$remote_addr",'
    '"source":"nginx"'
'}';

# suppress logspam from kubernetes liveness/readiness probes
map $status $loggable_status {
  200 0;
  default 1;
}
map $uri $loggable {
  /api/v1/boldata/version $loggable_status;
  default 1;
}
access_log /dev/stdout custom_format if=$loggable;

server {
    listen PORT;
    server_name _;
    keepalive_timeout 300s;
    server_tokens off;

    client_max_body_size 5m;

    location / {
        root /usr/bin/bol-data-api;
        try_files $uri $uri/ @uwsgi;
    }

    location @uwsgi {
        include uwsgi_params;
        uwsgi_param HTTP_X_SCRIPT_NAME /api;
        uwsgi_pass 127.0.0.1:8080;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
        send_timeout 300;
        uwsgi_read_timeout 300;
        uwsgi_connect_timeout 300;
        uwsgi_send_timeout 300;
    }
}
# --- end direct inclusion of our virtual host config here, apologies for the indentation ---

}
