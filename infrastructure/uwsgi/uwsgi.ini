[uwsgi]
master = true
plugins = python3
socket = 127.0.0.1:8080

virtualenv = /opt/app/env
wsgi = boldataapi.application

processes = 4
threads = 1

# We need to run as root, otherwise the PostgreSQL client certificate is not readable
# uid = www-data
# gid = www-data

env = PYTHONIOENCODING=utf-8

# This might mean uwsgi will buffer request body up to this amount
# before proxying it to the application, but I now think it's actually a
# boolean option that enables post buffering, so the actual value doesn't
# matter, as long as it's not 0?  uwsgi docs are terrible :(
post-buffering = 81920

# Maximum size of request size (around 5 MB)
limit-post = 5120000

# This badly-named option specifies the maximum size of request
# headers (actually, maximum size of the entire request, excluding
# body).
buffer-size = 32768

# We need enable-threads so Sentry can report errors in a background thread
# also newrelic requires both --enable-threads and --single-interpreter
enable-threads = 1
single-interpreter = 1

# Docker sends SIGTERM on shutdown, which uwsgi by default inteprets as "do a graceful reload"
die-on-term = true

# Suppress logs of Kubernetes readiness and liveness probes
plugins = logfile
logger = devnull file:/dev/null
log-req-route = devnull GET /api/v1/boldata/version .* \(HTTP/[0-9.]+ 200\)

# For application insights to work we need to set lazy-apps to true
lazy-apps = true
