#!/bin/bash
set -o errexit

if [ -n "${BUILD_NUMBER}" ]; then
    echo "bol-data-api image tag $BUILD_NUMBER"
    export BOLDATAAPI_MAIN_DOCKER_TAG="${BUILD_NUMBER}"
fi

# our k8s secrets have the lowercase spelling, but <PERSON><PERSON><PERSON> wants the uppercase with the extra _
if [ -z "$NEW_RELIC_LICENSE_KEY" ] && [ -n "$newrelic_license_key" ]; then
    export NEW_RELIC_LICENSE_KEY="$newrelic_license_key"
fi

if [ "$1" = uwsgi ]; then
    if [ "$BOLDATAAPI_RUN_ALEMBIC_ON_STARTUP" = true ]; then
        (cd /opt/app/alembic/ && PYTHONPATH=/opt/app/alembic/migrations alembic upgrade head) || exit 1
    fi

    /usr/sbin/nginx -g "error_log /dev/stdout info;"
    if [ -n "$NEW_RELIC_LICENSE_KEY" ]; then
        echo "Enabling NewRelic integration"
        exec /opt/app/env/bin/newrelic-admin run-program "$@"
    else
        echo "Not enabling NewRelic integration"
    fi
fi
if [ "$1" = alembic ]; then
    echo "Setting up environment for Alembic"
    export PYTHONPATH=/opt/app/alembic/migrations
    cd /opt/app/alembic/
    if [ "$*" = "alembic revision --autogenerate -m new" ]; then
        "$@" || exit 1
        echo "---------------"
        cat /opt/app/alembic/migrations/versions/*_new.py
        exit
    fi
fi
exec "$@"
