[pytest]
addopts = --tb=short -ra
testpaths = server/tests
filterwarnings =
    once
    # pkg_resources complains about pkg_resources.declare_namespace('google') in 3rd-party packages
    ignore::DeprecationWarning:google
    # no idea why sometimes that warning is attributed to pkg_resources/__init__.py instead of
    # the code in google.whatever that calls declare_namespace()!
    ignore::DeprecationWarning:pkg_resources
    # bottle imports cgi from the stdlib, which will be gone in Python 3.13
    ignore::DeprecationWarning:bottle
    # opencensus.ext.azure.common.utils uses the deprecated locale.getdefaultlocale()
    ignore::DeprecationWarning:opencensus
    # bottle has an _ImportRedirect() that isn't ported to the new importlib API yet, see
    # https://github.com/bottlepy/bottle/issues/1422
    ignore::ImportWarning
