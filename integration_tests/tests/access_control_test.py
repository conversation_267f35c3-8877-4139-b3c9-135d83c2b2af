import pytest
import requests

from fixtures.factories import (
    generate_project_json,
    generate_report_access_json,
    generate_status_report_json,
    generate_supplier_json,
)
from tests import config
from tests.config import (
    PROJECT_ENDPOINT,
    REPORT_ACCESS_ENDPOINT,
    REPORT_ENDPOINT,
    SUPPLIER_ENDPOINT,
)
from tests.conftest import BDARequestsSender


@pytest.mark.parametrize(('endpoint', 'data_factory_fn'),
                         [(PROJECT_ENDPOINT, generate_project_json),
                          (REPORT_ENDPOINT, generate_status_report_json),
                          (REPORT_ACCESS_ENDPOINT, generate_report_access_json),
                          (SUPPLIER_ENDPOINT, generate_supplier_json)])
def test_post_request_should_return_401_when_no_auth_header(
        base_addr, endpoint, data_factory_fn):
    test_data = data_factory_fn()
    response = requests.post(base_addr + endpoint,
                             json=test_data,
                             timeout=config.GENERAL_TIMEOUT)

    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_get_request_should_return_401_when_no_auth_header(base_addr, endpoint):
    response = requests.get(base_addr + endpoint + '/123',
                            timeout=config.GENERAL_TIMEOUT)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_search_request_should_return_401_when_no_auth_header(base_addr, endpoint):
    response = requests.get(base_addr + endpoint + '/search/exact/id/123',
                            timeout=config.GENERAL_TIMEOUT)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(SUPPLIER_ENDPOINT)])
def test_delete_request_should_return_401_when_no_auth_header(base_addr, endpoint):
    response = requests.delete(base_addr + endpoint + '/123',
                               timeout=config.GENERAL_TIMEOUT)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT)])
def test_delete_request_should_return_405_when_not_implemented(base_addr, endpoint):
    response = requests.delete(base_addr + endpoint + '/123',
                               timeout=config.GENERAL_TIMEOUT)
    assert response.status_code == 405
    assert response.json()['error'] == 'Method not allowed.'


@pytest.mark.parametrize(('endpoint', 'data_factory_fn'),
                         [(PROJECT_ENDPOINT, generate_project_json),
                          (REPORT_ENDPOINT, generate_status_report_json),
                          (REPORT_ACCESS_ENDPOINT, generate_report_access_json),
                          (SUPPLIER_ENDPOINT, generate_supplier_json)])
def test_post_request_should_return_401_when_empty_auth_header(
        base_addr, endpoint, data_factory_fn):
    test_data = data_factory_fn()
    headers = {'Authorization': 'bearer '}

    response = requests.post(base_addr + endpoint,
                             json=test_data,
                             timeout=config.GENERAL_TIMEOUT,
                             headers=headers)

    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_get_request_should_return_401_when_empty_auth_header(base_addr, endpoint):
    headers = {'Authorization': 'bearer '}
    response = requests.get(base_addr + endpoint + '/123',
                            timeout=config.GENERAL_TIMEOUT,
                            headers=headers)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_search_request_should_return_401_empty_auth_header(base_addr, endpoint):
    headers = {'Authorization': 'bearer '}
    response = requests.get(base_addr + endpoint + '/search/exact/id/123',
                            timeout=config.GENERAL_TIMEOUT, headers=headers)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(SUPPLIER_ENDPOINT)])
def test_delete_request_should_return_401_empty_auth_header(base_addr, endpoint):
    headers = {'Authorization': 'bearer '}
    response = requests.delete(base_addr + endpoint + '/123',
                               timeout=config.GENERAL_TIMEOUT, headers=headers)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint', 'data_factory_fn'),
                         [(PROJECT_ENDPOINT, generate_project_json),
                          (REPORT_ENDPOINT, generate_status_report_json),
                          (REPORT_ACCESS_ENDPOINT, generate_report_access_json),
                          (SUPPLIER_ENDPOINT, generate_supplier_json)])
def test_post_request_should_return_401_when_token_is_wrong(
        base_addr, endpoint, data_factory_fn):
    test_data = data_factory_fn()
    headers = {'Authorization': 'bearer some_token'}

    response = requests.post(base_addr + endpoint,
                             json=test_data,
                             timeout=config.GENERAL_TIMEOUT,
                             headers=headers)

    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_get_request_should_return_401_when_token_is_wrong(base_addr, endpoint):
    headers = {'Authorization': 'bearer some token'}
    response = requests.get(base_addr + endpoint + '/123',
                            timeout=config.GENERAL_TIMEOUT,
                            headers=headers)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_search_request_should_return_401_token_is_wrong(base_addr, endpoint):
    headers = {'Authorization': 'bearer some token'}
    response = requests.get(base_addr + endpoint + '/search/exact/id/123',
                            timeout=config.GENERAL_TIMEOUT,
                            headers=headers)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint'),
                         [(SUPPLIER_ENDPOINT)])
def test_delete_request_should_return_401_token_is_wrong(base_addr, endpoint):
    headers = {'Authorization': 'bearer some token'}
    response = requests.delete(base_addr + endpoint + '/123',
                               timeout=config.GENERAL_TIMEOUT,
                               headers=headers)
    assert response.status_code == 401
    assert response.json()['error_code'] == 'InvalidTokenError'


@pytest.mark.parametrize(('endpoint', 'data_factory_fn'),
                         [(PROJECT_ENDPOINT, generate_project_json),
                          (REPORT_ENDPOINT, generate_status_report_json),
                          (REPORT_ACCESS_ENDPOINT, generate_report_access_json),
                          (SUPPLIER_ENDPOINT, generate_supplier_json)])
def test_post_request_should_return_403_when_wrong_scope_is_given(
        base_addr, auth_addr, test_client_creds, endpoint, data_factory_fn):

    test_data = data_factory_fn()
    requests_sender = BDARequestsSender(auth_addr,
                                        base_addr,
                                        'uapi_bad_scope_id_get',
                                        test_client_creds)

    response = requests_sender.send_post_request(endpoint, test_data)
    assert response.status_code == 403
    assert response.json()['error_code'] == 'InsufficientScopeError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_get_request_should_return_403_when_wrong_scope_is_given(
        base_addr, auth_addr, test_client_creds, endpoint):
    requests_sender = BDARequestsSender(auth_addr,
                                        base_addr,
                                        'uapi_bad_scope_search_id_get',
                                        test_client_creds)

    response = requests_sender.send_get_request(endpoint + '/1234')

    assert response.status_code == 403
    assert response.json()['error_code'] == 'InsufficientScopeError'


@pytest.mark.parametrize(('endpoint'),
                         [(PROJECT_ENDPOINT),
                          (REPORT_ENDPOINT),
                          (REPORT_ACCESS_ENDPOINT),
                          (SUPPLIER_ENDPOINT)])
def test_search_request_should_return_403_when_wrong_scope_is_given(
        base_addr, auth_addr, test_client_creds, endpoint):
    requests_sender = BDARequestsSender(auth_addr,
                                        base_addr,
                                        'uapi_bad_scope_id_get',
                                        test_client_creds)

    response = requests_sender.send_get_request(endpoint + '/search/exact/id/123')

    assert response.status_code == 403
    assert response.json()['error_code'] == 'InsufficientScopeError'


@pytest.mark.parametrize(('endpoint', 'data_factory_fn'),
                         [(PROJECT_ENDPOINT, generate_project_json),
                          (REPORT_ENDPOINT, generate_status_report_json),
                          (REPORT_ACCESS_ENDPOINT, generate_report_access_json),
                          (SUPPLIER_ENDPOINT, generate_supplier_json)])
def test_put_request_should_return_403_when_wrong_scope_is_given(
        base_addr, auth_addr, test_client_creds, endpoint, data_factory_fn):
    test_data = data_factory_fn()
    requests_sender = BDARequestsSender(auth_addr,
                                        base_addr,
                                        'uapi_bad_scope_id_get',
                                        test_client_creds)

    response = requests_sender.send_put_request(endpoint + '/123', test_data)
    assert response.status_code == 403
    assert response.json()['error_code'] == 'InsufficientScopeError'


@pytest.mark.parametrize(('endpoint', 'data_factory_fn'),
                         [(SUPPLIER_ENDPOINT, generate_supplier_json)])
def test_delete_request_should_return_403_when_wrong_scope_is_given(
        base_addr, auth_addr, test_client_creds, endpoint, data_factory_fn):
    requests_sender = BDARequestsSender(auth_addr,
                                        base_addr,
                                        'uapi_bad_scope_id_get',
                                        test_client_creds)

    response = requests_sender.send_delete_request(endpoint + '/123')
    assert response.status_code == 403
    assert response.json()['error_code'] == 'InsufficientScopeError'
