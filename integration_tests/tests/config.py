import urllib3
urllib3.disable_warnings()

VERSION_ENDPOINT = '/version'
PROJECT_ENDPOINT = '/projects'
REPORT_ENDPOINT = '/reports'
REPORT_ACCESS_ENDPOINT = '/report_accesses'
SUPPLIER_ENDPOINT = '/bol_suppliers'
CURRENT_VERSION = '0.1.0'
PROJECT_SCOPES = 'uapi_projects_post uapi_projects_id_get ' \
                 'uapi_projects_search_id_get uapi_projects_id_delete ' \
                 'uapi_projects_id_put'
REPORT_SCOPES = 'uapi_reports_post uapi_reports_id_get ' \
                 'uapi_reports_search_id_get uapi_reports_id_delete ' \
                 'uapi_reports_id_put'
REPORT_ACCESS_SCOPES = 'uapi_report_accesses_post uapi_report_accesses_id_get ' \
                 'uapi_report_accesses_search_id_get uapi_report_accesses_id_delete ' \
                 'uapi_report_accesses_id_put'
BOL_SUPPLIER_SCOPES = 'uapi_bol_suppliers_post uapi_bol_suppliers_id_get ' \
                 'uapi_bol_suppliers_search_id_get uapi_bol_suppliers_id_delete ' \
                 'uapi_bol_suppliers_id_put'
API_SCOPES = ' '.join([PROJECT_SCOPES, REPORT_SCOPES, REPORT_ACCESS_SCOPES, BOL_SUPPLIER_SCOPES])
GENERAL_TIMEOUT = 60

# SKIP FIELDS
# Fields present in BDA/Qvarn that should not be matched in Qvarn/BDA

# REASON: `bda` exposes project file as more fields for future use or for compatibility.
SKIP_FIELDS_PROJECTS = {'bda': ['sync'],
                        'qvarn': []}

# REASON: `bda` exposes report file as a field `report.pdf` for future use.
SKIP_FIELDS_REPORTS = {'bda': ['pdf'],
                       'qvarn': []}

# REASON: `bolagsfakta_status` is a legacy field that contains data in Qvarn
# but was NULLed in bda.
SKIP_FIELDS_SUPPLIERS = {'bda': ['bolagsfakta_status', 'last_visited', 'first_visited'],
                         'qvarn': ['bolagsfakta_status', 'last_visited']}

# REASON: `bda` exposes report file as a field `report.pdf` for future use.
SKIP_FIELDS_REPORT_ACCESSES = {'bda': ['client_id', 'language'],
                               'qvarn': ['client_id']}

# END SKIP FIELDS
