import base64
import json
import logging
import urllib.parse
from typing import Dict

import pytest
import requests

from tests import config


def pytest_addoption(parser):
    parser.addoption('--base-addr', action='store', help='Base under test API address')
    parser.addoption('--auth-addr', action='store', help='Gluu address')
    parser.addoption('--qvarn-addr', action='store', help='Qvarn address')
    parser.addoption('--client-creds', action='store',
                     help='Client id and secret of test application')


@pytest.fixture
def base_addr(request):
    return request.config.getoption('--base-addr')


@pytest.fixture
def qvarn_addr(request):
    return request.config.getoption('--qvarn-addr')


@pytest.fixture
def auth_addr(request):
    return request.config.getoption('--auth-addr')


@pytest.fixture
def test_client_creds(request):
    return request.config.getoption('--client-creds')


@pytest.fixture(scope='session')
def requests_sender(request):
    base_addr = request.config.getoption('--base-addr')
    auth_addr = request.config.getoption('--auth-addr')
    client_creds = request.config.getoption('--client-creds')
    requests_sender = BDARequestsSender(auth_addr, base_addr, config.API_SCOPES, client_creds)
    return requests_sender


@pytest.fixture(scope='session')
def qvarn_sender(request):
    qvarn_addr = request.config.getoption('--qvarn-addr')
    auth_addr = request.config.getoption('--auth-addr')
    client_creds = request.config.getoption('--client-creds')
    qvarn_sender = QvarnRequestsSender(auth_addr, qvarn_addr, config.API_SCOPES, client_creds)
    return qvarn_sender


def get_dedicated_logger(name):
    logger = logging.getLogger(__name__)

    # Remove handlers from previous runs
    for old_hdlr in logger.handlers:
        logger.removeHandler(old_hdlr)

    log_file = f'logs/{__name__}-{name}.log'
    hdlr = logging.FileHandler(log_file, mode='w')
    formatter = logging.Formatter('%(message)s')
    hdlr.setFormatter(formatter)
    logger.addHandler(hdlr)
    logger.setLevel(logging.INFO)
    return logger


def _remove_skippable(dictionary, skip_fields=None):
    # Don't compare identical fields because their content is auto-generated
    # and can not be identical
    skip_always = ['id', 'revision']

    skip = skip_always
    if skip_fields is not None:
        skip.extend(skip_fields)

    for f in skip:
        if f in dictionary:
            del dictionary[f]

    return dictionary


def _compare_lists_of_dicts(qvarn_list, api_list, skip_fields=None):
    """
    Adopted from https://devqa.io/python-compare-two-lists-of-dictionaries/
    Compare two lists and logs the difference.
    :param list1: first list.
    :param list2: second list.
    :return:      if there is difference between both lists.
    """
    skip_fields = skip_fields or {}
    q_l = _remove_skippable(qvarn_list, skip_fields.get('qvarn'))
    a_l = _remove_skippable(api_list, skip_fields.get('bda'))

    diff = [i for i in a_l + q_l if i not in a_l or i not in q_l]
    result = len(diff) == 0
    if not result:
        print(f'There are {len(diff)} differences:\n{diff}')
    return result


def compare_dict_responses(qvarn_response, api_response, skip_fields=None):
    skip_fields = skip_fields or {}
    q_response = _remove_skippable(qvarn_response, skip_fields.get('qvarn'))
    a_response = _remove_skippable(api_response, skip_fields.get('bda'))

    keys = q_response.keys()
    assert sorted(q_response.keys()) == sorted(a_response.keys())

    simple_keys = []
    nested_dict_keys = []
    for k in keys:
        q_v = q_response[k]
        a_v = a_response[k]
        if list in (type(q_v), type(a_v)) and q_v and a_v:
            # both are non-empty lists
            if type(q_v[0]) == dict:
                # comparing list of dicts
                nested_dict_keys.append(k)
        else:
            simple_keys.append(k)

    # Compare simple dict items
    q_response_simple = {}
    a_response_simple = {}
    for k in simple_keys:
        q_response_simple[k] = q_response[k]
        a_response_simple[k] = a_response[k]

    assert q_response_simple == a_response_simple

    # Compare nested dict items
    for k in nested_dict_keys:
        q_v = q_response[k]
        a_v = a_response[k]

        assert _compare_lists_of_dicts(q_v, a_v, skip_fields)


def compare_responses(qvarn_response, api_response, skip_fields=None):
    qvarn_response = json.loads(json.dumps(qvarn_response).lower())
    api_response = json.loads(json.dumps(api_response).lower())

    compare_dict_responses(qvarn_response, api_response, skip_fields)


def compare_responses_search_one(qvarn_response, api_response, skip_fields=None):
    qvarn_response = json.loads(json.dumps(qvarn_response).lower())
    api_response = json.loads(json.dumps(api_response).lower())

    assert 1 == len(qvarn_response['resources']) == len(api_response['resources'])

    q_r = qvarn_response['resources']
    a_r = api_response['resources']

    compare_dict_responses(q_r[0], a_r[0], skip_fields)


class _RequestsSender:
    def __init__(self, auth_url: str, base_url: str, auth_scope: str, client_creds: str) -> None:
        self._auth_url = auth_url
        self._base_url = base_url
        self._auth_scope = auth_scope
        self._client_creds = client_creds
        self._session = self._create_session()

    def keep_gluu_token_alive(request_fn):
        def wrap(self, *args, **kwargs):
            response = request_fn(self, *args, **kwargs)
            if response.status_code in [401, 403]:
                print('\nRefreshing GLUU token')
                self._session = self._create_session()
                response = request_fn(self, *args, **kwargs)
            return response
        return wrap

    @keep_gluu_token_alive
    def send_get_request(self, endpoint: str) -> requests.Response:
        full_url = self._base_url + endpoint
        response = self._session.get(full_url, timeout=config.GENERAL_TIMEOUT)
        return response

    @keep_gluu_token_alive
    def send_delete_request(self, endpoint: str) -> requests.Response:
        full_url = self._base_url + endpoint
        response = self._session.delete(full_url, timeout=config.GENERAL_TIMEOUT)
        return response

    @keep_gluu_token_alive
    def send_post_request(self, endpoint: str, data: Dict) -> requests.Response:
        return NotImplementedError('Please use specific subclass instead.')

    @keep_gluu_token_alive
    def send_put_request(self, endpoint: str, data: Dict) -> requests.Response:
        full_url = self._base_url + endpoint
        response = self._session.put(full_url, json=data, timeout=config.GENERAL_TIMEOUT)
        return response

    def _create_session(self) -> requests.Session:
        data = {
            'scope': self._auth_scope,
            'grant_type': 'client_credentials'
        }
        auth = 'Basic %s' % base64.b64encode(self._client_creds.encode('utf-8')).decode('utf-8')
        headers = {
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': auth
        }
        encoded_data = urllib.parse.urlencode(data)
        address = '/oxauth/restv1/token'
        response = requests.post(self._auth_url + address, encoded_data, verify=False,
                                 headers=headers)
        if response.status_code != 200:
            raise Exception(response.text)
        response_json = response.json()
        token = response_json['access_token']
        auth_t = 'bearer ' + str(token)
        headers = {'Authorization': auth_t}
        session = requests.session()
        for header, value in headers.items():
            session.headers[header] = value
        session.verify = False
        return session


class BDARequestsSender(_RequestsSender):

    def send_post_request(self, endpoint: str, data: Dict) -> requests.Response:
        full_url = self._base_url + endpoint
        created = self._session.post(full_url, json=data, timeout=config.GENERAL_TIMEOUT)
        return created


class QvarnRequestsSender(_RequestsSender):

    def send_post_request(self, endpoint: str, data: Dict) -> requests.Response:
        full_url = self._base_url + endpoint
        if 'pdf' in data:
            file_data = data.pop('pdf')

        created = self._session.post(full_url, json=data, timeout=config.GENERAL_TIMEOUT)

        if 'pdf' in data:
            # Qvarn expects: PUT /endpoint/123/<file_resource_name>
            file_url = full_url + '/pdf'
            headers = {'Content-Type': file_data['content_type']}
            self._session.put(file_url,
                              json=file_data['body'],
                              headers=headers,
                              timeout=config.GENERAL_TIMEOUT)
        return created
