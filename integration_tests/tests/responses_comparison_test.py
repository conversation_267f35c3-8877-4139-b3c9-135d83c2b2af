import pytest

from fixtures.factories import (
    generate_notification_report_json,
    generate_project_json,
    generate_report_access_json,
    generate_status_report_json,
    generate_supplier_json,
    prepare_supplier,
)
from tests.config import (
    PROJECT_ENDPOINT,
    REPORT_ACCESS_ENDPOINT,
    REPORT_ENDPOINT,
    SKIP_FIELDS_REPORTS,
    SUPPLIER_ENDPOINT,
)
from tests.conftest import compare_responses, compare_responses_search_one


@pytest.mark.parametrize(
    ('endpoint', 'data_generator_fn', 'skip_fields'),
    [(REPORT_ACCESS_ENDPOINT, generate_report_access_json, None),
     (PROJECT_ENDPOINT, generate_project_json, None),
     (SUPPLIER_ENDPOINT, prepare_supplier, None),
     (REPORT_ENDPOINT, generate_status_report_json, SKIP_FIELDS_REPORTS),
     (REPORT_ENDPOINT, generate_notification_report_json, SKIP_FIELDS_REPORTS)])
def test_get_success(requests_sender, qvarn_sender, endpoint, data_generator_fn, skip_fields):
    payload = data_generator_fn(requests_sender)

    qvarn_create_response = qvarn_sender.send_post_request(endpoint, payload)
    qvarn_created_item_id = qvarn_create_response.json()['id']
    qvarn_get_response = qvarn_sender.send_get_request(endpoint + '/' + qvarn_created_item_id)

    bda_create_response = requests_sender.send_post_request(endpoint, payload)
    bda_created_item_id = bda_create_response.json()['id']
    bda_get_response = requests_sender.send_get_request(endpoint + '/' + bda_created_item_id)

    assert 200 == qvarn_get_response.status_code
    assert 200 == bda_get_response.status_code

    compare_responses(qvarn_get_response.json(), bda_get_response.json(), skip_fields)


@pytest.mark.parametrize(
    ('endpoint', 'data_generator_fn', 'skip_fields'),
    [(REPORT_ACCESS_ENDPOINT, generate_report_access_json, None),
     (PROJECT_ENDPOINT, generate_project_json, None),
     (SUPPLIER_ENDPOINT, prepare_supplier, None),
     (REPORT_ENDPOINT, generate_status_report_json, SKIP_FIELDS_REPORTS),
     (REPORT_ENDPOINT, generate_notification_report_json, SKIP_FIELDS_REPORTS)])
def test_search_show_all_success(requests_sender, qvarn_sender, endpoint,
                                 data_generator_fn, skip_fields):
    payload = data_generator_fn(requests_sender)

    qvarn_create_response = qvarn_sender.send_post_request(endpoint, payload)
    qvarn_created_item_id = qvarn_create_response.json()['id']
    qvarn_url = endpoint + '/search/show_all/exact/id/' + qvarn_created_item_id
    qvarn_get_response = qvarn_sender.send_get_request(qvarn_url)

    bda_create_response = requests_sender.send_post_request(endpoint, payload)
    bda_created_item_id = bda_create_response.json()['id']
    bda_url = endpoint + '/search/show_all/exact/id/' + bda_created_item_id
    bda_get_response = requests_sender.send_get_request(bda_url)

    assert 200 == qvarn_get_response.status_code
    assert 200 == bda_get_response.status_code

    compare_responses_search_one(qvarn_get_response.json(), bda_get_response.json(), skip_fields)


@pytest.mark.parametrize(('endpoint', 'data_generator_fn'),
                         [(SUPPLIER_ENDPOINT, generate_supplier_json)])
def test_delete_success(requests_sender, qvarn_sender, endpoint, data_generator_fn):
    payload = data_generator_fn()

    qvarn_create_response = qvarn_sender.send_post_request(endpoint, payload)
    qvarn_created_item_id = qvarn_create_response.json()['id']
    qvarn_created_item_url = endpoint + '/' + qvarn_created_item_id

    qvarn_delete_response = qvarn_sender.send_delete_request(qvarn_created_item_url)

    bda_create_response = requests_sender.send_post_request(endpoint, payload)
    bda_created_item_id = bda_create_response.json()['id']
    bda_created_item_url = endpoint + '/' + bda_created_item_id

    bda_delete_response = requests_sender.send_delete_request(bda_created_item_url)

    assert 200 == qvarn_delete_response.status_code
    assert 200 == bda_delete_response.status_code


@pytest.mark.parametrize(
    ('endpoint', 'data_generator_fn', 'skip_fields'),
    [(REPORT_ACCESS_ENDPOINT, generate_report_access_json, None),
     (PROJECT_ENDPOINT, generate_project_json, None),
     (SUPPLIER_ENDPOINT, prepare_supplier, None),
     (REPORT_ENDPOINT, generate_status_report_json, SKIP_FIELDS_REPORTS),
     (REPORT_ENDPOINT, generate_notification_report_json, SKIP_FIELDS_REPORTS)])
def test_post_success(requests_sender, qvarn_sender,
                      endpoint, data_generator_fn, skip_fields):
    payload = data_generator_fn(requests_sender)

    qvarn_create_response = qvarn_sender.send_post_request(endpoint, payload)
    created_item_id = qvarn_create_response.json()['id']
    created_item_url = endpoint + '/' + created_item_id
    qvarn_get_response = qvarn_sender.send_get_request(created_item_url)
    qvarn_sender.send_delete_request(created_item_url)

    bda_sender = requests_sender
    bda_create_response = bda_sender.send_post_request(endpoint, payload)
    bda_item_id = bda_create_response.json()['id']
    bda_get_response = bda_sender.send_get_request(endpoint + '/' + bda_item_id)

    assert 201 == qvarn_create_response.status_code
    assert 201 == bda_create_response.status_code

    compare_responses(qvarn_get_response.json(), bda_get_response.json(), skip_fields)


@pytest.mark.parametrize(('endpoint', 'data_generator_fn'),
                         [(REPORT_ACCESS_ENDPOINT, generate_report_access_json),
                          (PROJECT_ENDPOINT, generate_project_json),
                          (SUPPLIER_ENDPOINT, generate_supplier_json),
                          (REPORT_ENDPOINT, generate_status_report_json)])
def test_post_error(requests_sender, qvarn_sender, endpoint, data_generator_fn):
    payload = {'BAD_KEY': 'BAD_VALUE'}

    qvarn_response = qvarn_sender.send_post_request(endpoint, payload)

    bda_sender = requests_sender
    bda_response = bda_sender.send_post_request(endpoint, payload)

    assert 400 == qvarn_response.status_code
    assert 400 == bda_response.status_code
    # Neither BOL nor qvarnclient seem to use `error_code`s so different codes
    # are OK.
    assert 'UnknownKeys' == qvarn_response.json()['error_code']
    assert 'ParameterValidationFailed' == bda_response.json()['error_code']


@pytest.mark.parametrize(('endpoint'),
                         [(REPORT_ACCESS_ENDPOINT),
                          (PROJECT_ENDPOINT),
                          (SUPPLIER_ENDPOINT),
                          (REPORT_ENDPOINT)])
def test_put_error_not_found(requests_sender, qvarn_sender, endpoint):
    url = endpoint + '/123456'
    payload = {'BAD_KEY': 'BAD_VALUE'}

    qvarn_response = qvarn_sender.send_put_request(url, payload)

    bda_sender = requests_sender
    bda_response = bda_sender.send_put_request(url, payload)

    # Neither BOL nor qvarnclient seem to use `status_code`s so as long as it
    # is an error different code could be OK.
    assert 409 == qvarn_response.status_code
    assert 404 == bda_response.status_code
    # Neither BOL nor qvarnclient seem to use `error_code`s so different codes
    # are OK.
    assert 'NoItemRevision' == qvarn_response.json()['error_code']
    assert 'NotFound' == bda_response.json()['error_code']


@pytest.mark.parametrize(('endpoint'),
                         [(REPORT_ACCESS_ENDPOINT),
                          (PROJECT_ENDPOINT),
                          (SUPPLIER_ENDPOINT),
                          (REPORT_ENDPOINT)])
def test_get_error_not_found(requests_sender, qvarn_sender, endpoint):
    url = endpoint + '/123456'

    bda_response = requests_sender.send_get_request(url)
    qvarn_response = qvarn_sender.send_get_request(url)

    assert 404 == qvarn_response.status_code
    assert 404 == bda_response.status_code
    # Neither BOL nor qvarnclient seem to use `error_code`s so different codes
    # are OK.
    assert 'ItemDoesNotExist' == qvarn_response.json()['error_code']
    assert 'NotFound' == bda_response.json()['error_code']


@pytest.mark.parametrize(('endpoint'),
                         [(REPORT_ACCESS_ENDPOINT),
                          (PROJECT_ENDPOINT),
                          (SUPPLIER_ENDPOINT),
                          (REPORT_ENDPOINT)])
def test_search_exact_error_parse(requests_sender, qvarn_sender, endpoint):
    """Search any fails on empty list."""
    url = endpoint + '/search/bad-search-url'

    bda_response = requests_sender.send_get_request(url)
    qvarn_response = qvarn_sender.send_get_request(url)

    assert 400 == qvarn_response.status_code
    assert 400 == bda_response.status_code
    assert 'BadSearchCondition' == qvarn_response.json()['error_code']
    assert 'BadSearchCondition' == bda_response.json()['error_code']


@pytest.mark.parametrize(('endpoint'),
                         [(REPORT_ACCESS_ENDPOINT),
                          (PROJECT_ENDPOINT),
                          (SUPPLIER_ENDPOINT),
                          (REPORT_ENDPOINT)])
def test_search_exact_parse_error(requests_sender, qvarn_sender, endpoint):
    """Search any fails on malformed search condition."""
    url = endpoint + '/search/BAD-SEARCH-URL'

    bda_response = requests_sender.send_get_request(url)
    qvarn_response = qvarn_sender.send_get_request(url)

    assert 400 == qvarn_response.status_code
    assert 400 == bda_response.status_code
    assert 'BadSearchCondition' == qvarn_response.json()['error_code']
    assert 'BadSearchCondition' == bda_response.json()['error_code']


@pytest.mark.parametrize(('endpoint'),
                         [(REPORT_ACCESS_ENDPOINT),
                          (PROJECT_ENDPOINT),
                          (SUPPLIER_ENDPOINT),
                          (REPORT_ENDPOINT)])
def test_search_any_error_empty(requests_sender, qvarn_sender, endpoint):
    """Search any fails on empty list."""
    url = endpoint + '/search/any/exact/id/[]'

    bda_response = requests_sender.send_get_request(url)
    qvarn_response = qvarn_sender.send_get_request(url)

    assert 400 == qvarn_response.status_code
    assert 400 == bda_response.status_code
    assert 'FieldNotInResource' == qvarn_response.json()['error_code']
    assert 'FieldNotInResource' == bda_response.json()['error_code']


@pytest.mark.parametrize(('endpoint'),
                         [(REPORT_ACCESS_ENDPOINT),
                          (PROJECT_ENDPOINT),
                          (SUPPLIER_ENDPOINT),
                          (REPORT_ENDPOINT)])
def test_search_any_error_search_value(requests_sender, qvarn_sender, endpoint):
    """Search any fails on empty list."""
    url = endpoint + '/search/any/exact/id/BAD-VALUE'

    bda_response = requests_sender.send_get_request(url)
    qvarn_response = qvarn_sender.send_get_request(url)

    assert 400 == qvarn_response.status_code
    assert 400 == bda_response.status_code
    assert 'BadAnySearchValue' == qvarn_response.json()['error_code']
    assert 'BadAnySearchValue' == bda_response.json()['error_code']
