import random
import os
from pprint import pformat

from tqdm import tqdm
import pytest

from tests.config import (
    PROJECT_ENDPOINT,
    REPORT_ACCESS_ENDPOINT,
    REPORT_ENDPOINT,
    SKIP_FIELDS_PROJECTS,
    SKIP_FIELDS_REPORT_ACCESSES,
    SKIP_FIELDS_REPORTS,
    SKIP_FIELDS_SUPPLIERS,
    SUPPLIER_ENDPOINT,
)
from tests.conftest import compare_responses, get_dedicated_logger


ENVIRONMENT = os.environ.get('ENVIRONMENT')
pytestmark = pytest.mark.skipif(ENVIRONMENT not in ['alpha', 'beta', 'prod'],
                                reason=f'Nothing to compare on {ENVIRONMENT}')

# For checking ALL matching items set
# SAMPLE_SIZE = None
if ENVIRONMENT == 'alpha':
    SAMPLE_SIZE = None

if ENVIRONMENT == 'beta':
    SAMPLE_SIZE = None

if ENVIRONMENT == 'prod':
    SAMPLE_SIZE = 1000

SEARCH_REPORT_ACCESSES = '/search/exact/type/report_access'
SEARCH_PROJECTS = '/search/exact/type/project'
SEARCH_SUPPLIERS = '/search/exact/type/bol_supplier'
SEARCH_STATUS_REPORTS = '/search/exact/report_type/bolagsfakta.company_report'
SEARCH_NOTIFICATION_REPORTS = '/search/exact/report_type/bolagsfakta.status_change_report'


@pytest.mark.parametrize(
    ('endpoint', 'search_predicate', 'skip_fields', 'log_name'),
    [
     (PROJECT_ENDPOINT, SEARCH_PROJECTS, SKIP_FIELDS_PROJECTS, 'project'),
     (REPORT_ACCESS_ENDPOINT, SEARCH_REPORT_ACCESSES, SKIP_FIELDS_REPORT_ACCESSES, 'report_access'),
     (SUPPLIER_ENDPOINT, SEARCH_SUPPLIERS, SKIP_FIELDS_SUPPLIERS, 'bol_supplier'),
     (REPORT_ENDPOINT, SEARCH_STATUS_REPORTS, SKIP_FIELDS_REPORTS, 'status_report'),
     (REPORT_ENDPOINT, SEARCH_NOTIFICATION_REPORTS, SKIP_FIELDS_REPORTS, 'notification_report')
    ])
def test_get_all_data(requests_sender, qvarn_sender, endpoint,
                      search_predicate, skip_fields, log_name):

    logger = get_dedicated_logger(log_name)

    log_file_name = logger.handlers[0].baseFilename

    search_url = endpoint + search_predicate

    bda_search_response = requests_sender.send_get_request(search_url)
    bda_items = bda_search_response.json()['resources']
    bda_ids = [i['id'] for i in bda_items]

    qvarn_search_response = qvarn_sender.send_get_request(search_url)
    qvarn_items = qvarn_search_response.json()['resources']
    qvarn_ids = [i['id'] for i in qvarn_items]

    total_qvarn = len(qvarn_ids)
    total_bda = len(bda_ids)
    logger.info(f'Comparing {endpoint}.')
    logger.info(f'TOTAL ITEMS Qvarn: {total_qvarn}')
    logger.info(f'TOTAL ITEMS bol-data-api: {total_bda}')

    common_ids = set(qvarn_ids).intersection(bda_ids)
    qvarn_extra_ids = set(qvarn_ids).difference(common_ids)
    bda_extra_ids = set(bda_ids).difference(common_ids)

    logger.info(f'TOTAL ITEMS common: %s' % len(common_ids))
    logger.info(f'TOTAL extra in Qvarn: %s' % len(qvarn_extra_ids))
    logger.info(f'TOTAL extra in bol-data-api: %s' % len(bda_extra_ids))

    if SAMPLE_SIZE is None:
        selected_ids = common_ids
    else:
        random.seed('Lets fix the order of selected ids')
        selected_ids = random.choices(sorted(common_ids),
                                      k=min(SAMPLE_SIZE, len(common_ids)))

    logger.info(f'Comparing {endpoint}. Total selected items: %s' % len(selected_ids))
    print(f'\nComparing {endpoint}. Total items: %s' % len(common_ids))
    print(f'Comparing {endpoint}. Total selected items: %s' % len(selected_ids))
    print(f'Results logged to file: {log_file_name}')

    find_ok = []
    find_failed = []
    match_ok = []
    match_failed = []

    pbar = tqdm(total=len(selected_ids))
    for num, id in enumerate(selected_ids):
        total_ids = len(selected_ids)
        done_ids = num
        remaining_ids = total_ids - done_ids
        logger.info(f'Comparing {endpoint}/{id}')

        get_url = endpoint + '/' + id

        qvarn_get_response = qvarn_sender.send_get_request(get_url)

        bda_get_response = requests_sender.send_get_request(get_url)

        pbar.update(1)

        if qvarn_get_response.status_code not in [200, 201]:
        # if qvarn_get_response.status_code not in [200]:
            logger.error(f'Find failed (QvarnDB HTTP %s): {endpoint}/{id}' % qvarn_get_response.status_code)
            logger.error(f'{qvarn_get_response.text}')
            find_failed.append(id)
            continue
        elif bda_get_response.status_code not in [200, 201]:
        # elif bda_get_response.status_code not in [200]:
            logger.error(f'Find failed (VaultDB HTTP %s): {endpoint}/{id}' % bda_get_response.status_code)
            logger.error(f'{bda_get_response.text}')
            find_failed.append(id)
            continue
        else:
            logger.info(f'Find succeeded: {endpoint}/{id}')
            find_ok.append(id)

        try:
            compare_responses(qvarn_get_response.json(), bda_get_response.json(), skip_fields)
        except AssertionError as e:
            match_failed.append(id)
            trace = pformat(e.args)
            logger.warning(f'Match failed: {endpoint}/{id}\n{trace}')
            continue
        else:
            match_ok.append(id)
            logger.info(f'Match succeeded: {endpoint}/{id}')

    pbar.close()

    logger.info(f'Extra ids in Qvarn: {qvarn_extra_ids}\n')
    logger.info(f'Extra ids in bol-data-api: {bda_extra_ids}\n')
    # Don't print thousands of OK ids.
    # logger.info(f'Find OK: {find_ok}')
    logger.info(f'Find failed: {find_failed}')
    # Don't print thousands of OK ids.
    # logger.info(f'Match OK: {match_ok}')
    logger.info(f'Match failed: {match_failed}')

    logger.info(f'Total extra ids in Qvarn: %s' % len(qvarn_extra_ids))
    logger.info(f'Total extra ids in bol-data-api: %s' % len(bda_extra_ids))
    logger.info(f'Total find OK: %s' % len(find_ok))
    logger.info(f'Total find failed: %s' % len(find_failed))
    logger.info(f'Total match OK: %s' % len(match_ok))
    logger.info(f'Total match failed: %s' % len(match_failed))

    print('Done.')
    if find_failed or match_failed or qvarn_extra_ids or bda_extra_ids:
        logger.info('Result: FAILURE')
        print('Result: FAILURE')
    else:
        logger.info('Result: SUCCESS')
        print('Result: SUCCESS')
    print(f'Results logged to file: {log_file_name}')
