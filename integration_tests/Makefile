# select default environmentL: development, alpha, beta,
ENVIRONMENT ?= development
CONNECTION_CONFIGS = server_configs/$(ENVIRONMENT).mk

# Include DB connection configs from a file
include $(CONNECTION_CONFIGS)

PYTHON = python3.11
ENV = env
PYBIN = $(ENV)/bin
PIP = $(PYBIN)/pip
PIP_COMPILE = $(PYBIN)/pip-compile --no-emit-index-url
PYTEST = $(PYBIN)/pytest
FLAKE8 = $(PYBIN)/flake8 tests
TESTDIR = tests
VALIDATION_TESTS = /data_identity_test.py

# We can use either Docker Compose v1 (docker-compose), or v2 (docker compose)
DOCKER_COMPOSE = docker compose -f ../docker-compose.yaml

CMD_OPTS =  --base-addr $(BASE_ADDR) --auth-addr $(AUTH_ADDR) --qvarn-addr $(QVARN_ADDR) --client-creds $(CLIENT_CREDS)


.PHONY: environ
environ: $(ENV)/.done

.PHONY: help
help:
	@echo "make                  # build everything"
	@echo "make test             # run tests"
	@echo "make lint             # run all linters"
	@echo "make flake8           # run flake8 linter"

$(PIP):
	virtualenv --no-download -p $(PYTHON) $(ENV)
	$(PIP) install -U pip setuptools wheel pip-tools
	$(PIP) install -I setuptools   # workaround for https://github.com/pypa/setuptools/issues/887

$(ENV)/.done: $(PIP) requirements.txt
	$(PIP) install -r requirements.txt
	mkdir -p logs
	touch $(ENV)/.done

.PHONY: test
test: environ
	$(MAKE) app ; \
	status=0 ; \
	$(PYTEST) $(TESTDIR) $(CMD_OPTS) || status=$$? ; \
	$(FLAKE8) || status=$$? ; \
	$(MAKE) app-down ; \

.PHONY: validate-data
validate-data: environ
	$(MAKE) app; \
	status=0 ; \
	$(PYTEST) -s $(TESTDIR)${VALIDATION_TESTS} $(CMD_OPTS) -vv || status=$$? ; \
	$(MAKE) app-down; \
	exit $$status

.PHONY: validate-data-alpha
validate-data-alpha: environ
  ifneq ($(ENVIRONMENT),alpha)
	  $(MAKE) $@ ENVIRONMENT=alpha
  else
	  ENVIRONMENT=$(ENVIRONMENT) $(PYTEST) -s $(TESTDIR)${VALIDATION_TESTS} $(CMD_OPTS) -vv
  endif

.PHONY: validate-data-beta
validate-data-beta: environ
  ifneq ($(ENVIRONMENT),beta)
	  $(MAKE) $@ ENVIRONMENT=beta
  else
	  ENVIRONMENT=$(ENVIRONMENT) $(PYTEST) -s $(TESTDIR)${VALIDATION_TESTS} $(CMD_OPTS) -vv
  endif

.PHONY: validate-data-prod
validate-data-prod: environ
  ifneq ($(ENVIRONMENT),beta)
	  $(MAKE) $@ ENVIRONMENT=prod
  else
	  ENVIRONMENT=$(ENVIRONMENT) $(PYTEST) -s $(TESTDIR)${VALIDATION_TESTS} $(CMD_OPTS) -vv
  endif

.PHONY: app
app:
	$(DOCKER_COMPOSE) up -d bol-data-api

.PHONY: app-down
app-down:
	$(DOCKER_COMPOSE) rm -sf bol-data-api
	${DOCKER_COMPOSE} rm -sf db
	-docker image rm bol-data-api_db
	-docker volume rm bol-data-api_pg-data

.PHONY: coverage
coverage: environ
	$(PYBIN)/coverage run -m pytest $(TESTDIR)
	$(PYBIN)/coverage report -m

.PHONY: diff-cover
diff-cover: coverage
	$(PYBIN)/coverage xml
	$(PYBIN)/diff-cover coverage.xml

.PHONY: lint
lint: flake8 mypy

.PHONY: flake8
flake8: environ
	$(FLAKE8)

.PHONY: clean
clean: clean_pycache
	rm -rf $(ENV)
	${MAKE} db-test-clean

.PHONY: clean_pycache
clean_pycache:
	find . -name '__pycache__' -type d -exec rm -rf "{}" +
	find . -name '.pytest_cache' -type d -exec rm -rf "{}" +
	find . -name '*.pyc' -delete

.PHONY: db-test-server
db-test-server:
	${DOCKER_COMPOSE} up -d db-test

.PHONY: db-test-server-down
db-test-server-down:
	${DOCKER_COMPOSE} rm -sf db-test

.PHONY: db-test-clean
db-test-clean:
	${MAKE} db-test-server-down
	-docker image rm bol-data-api_db-test
	-docker volume rm bol-data-api_pg-test-data
