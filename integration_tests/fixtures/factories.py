import datetime
import json


# copied from boldataapi.services.projects
PROJECT_ID_TYPE = 'trafikverket_project_id'
TAX_ID = 'tax_id'


project_fixture = {
    'names': ['Test project'],
    'project_responsible_org': 'client-comp-id',
    'start_date': '2020-01-01',
    'end_date': '2020-12-01',
    'state': 'active',
    'project_ids': [
        {
            'project_id_type': TAX_ID,
            'project_id': 'Integration Test TAX ID',
        },
        {
            'project_id_type': PROJECT_ID_TYPE,
            'project_id': 'Integration Test INTERNAL ID'
        },
    ],
    'project_responsible_person': None,
}


def generate_project_json(*args, **kwargs):
    return project_fixture


def generate_project(sender):
    endpoint = '/projects'
    payload = generate_project_json()
    response = sender.send_post_request(endpoint, payload)
    item_id = response.json()['id']
    get_response = sender.send_get_request(endpoint + '/' + item_id)
    return get_response.json()


status_report_fixture = {
    'type': 'report',
    'org': 'test-org-id',
    'report_type': 'bolagsfakta.company_report',
    'generated_timestamp': datetime.datetime.now().isoformat(),
    'tilaajavastuu_status': '500 OK',
    'interested_org_id': 'interested-org-id',
    'pdf': {
        'content_type': 'application/json',
        'body': json.dumps({'test': 'test report'})
    }
}


def generate_status_report_json(*args, **kwargs):
    return status_report_fixture


notification_report_fixture = {
    'type': 'report',
    'report_type': 'bolagsfakta.status_change_report',
    'generated_timestamp': datetime.datetime.now().isoformat(),
    'pdf': {
        'content_type': 'application/json',
        'body': json.dumps({'test': 'test notification report'})
    }
}


def generate_notification_report_json(*args, **kwargs):
    return notification_report_fixture


report_access_fixture = {
    'access_time': '2012-01-14T00:00:00+00:00',
    'arkisto_id': 'test-arkisto_id',
    'report_id': 'test-report-id',
    'status': 'active',
    'customer_org_id': 'test-customer-id',
    'org_id': 'test-company_id',
    'gov_org_ids': [
        {
            'country': 'FI',
            'gov_org_id': 'test-company-gov-id',
            'org_id_type': 'registration_number',
        }
    ],
    'person_id': 'test-person-id',
}


def generate_report_access_json(*args, **kwargs):
    return report_access_fixture


supplier_fixture = {
    'supplier_role': 'supplier',
    'supplier_type': 'linked',
    'contract_start_date': '2020-01-01',
    'contract_end_date': '2020-12-01',
    'materialized_path': ['item1', 'item2'],
    'project_resource_id': 'any-project-id',
    'parent_supplier_id': 'any-parent-supplier-id',
    'parent_org_id': 'any-parent-id',
    'supplier_org_id': 'any-company-id',
    'supplier_contacts': [
        {
            'supplier_contact_person_id': 'other-person-id',
            'supplier_contact_email': '<EMAIL>'
        }
    ]
}


def generate_supplier_json(*args, **kwargs):
    return supplier_fixture


def prepare_supplier(sender):
    project = generate_project(sender)

    supplier_fixture = generate_supplier_json()
    supplier_fixture['project_resource_id'] = project['id']
    return supplier_fixture
