# BOL Data API Integration Tests


## Local development

### Build integration tests locally from CLI

Execute the following statement in the project root directory

    make

### Run integration tests locally from CLI

Execute the following statement

    make test

Execute any specific test/tests. You will need to provide parameters on command line. Look at the first lines that `make test` prints out on the terminal for inspiration, like:

    make app
    env/bin/pytest tests --base-addr http://localhost:9700/api/v1/boldata --auth-addr https://auth-azure-alpha.id06.se --qvarn-addr https://bolagsfakta-qvarn-testing2.pov.lt --client-creds '@<GLUU.ID.XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX>:<PASSWORD>'


After DB schema update you may need to rebuild docker image for `app_db`

    make app-down
    make app


## Running integration tests against Alpha/Beta

**NB: THIS MAY CREATE GARBAGE IN ALPHA QVARN AND VAULTDB***

You need Client<PERSON> and secret.

Paste ClientID and and secret into file corresponding to environment configuration file in folder `server_configs`, e.g., `server_configs/alpha.mk`

    # server_configs/development.mk
    CLIENT_ID = @!D0B3.42FF.3A77.681D!0001!0105.03F6!0008!6214.003E  # Change value
    CLIENT_SECRET = eejiw1ooreet3ohK  # Change value

Set environment variable `ENVIRONMENT` to corresponding environment name

    export ENVIRONMENT=alpha

Run tests

    make test


## Running data validation against Alpha/Beta

You need ClientID and secret.

Paste ClientID and and secret into file corresponding to environment configuration file in folder `server_configs`, e.g., `server_configs/alpha.mk`:

    # server_configs/development.mk
    CLIENT_ID = @!D0B3.42FF.3A77.681D!0001!0105.03F6!0008!6214.003E  # Change value
    CLIENT_SECRET = eejiw1ooreet3ohK  # Change value

Set environment variable `ENVIRONMENT` to corresponding environment name:

    export ENVIRONMENT=alpha

Run tests:

    make validate-data-alpha

Similarly:

    export ENVIRONMENT=beta
    make validate-data-beta


