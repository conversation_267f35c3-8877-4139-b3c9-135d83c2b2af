# The following variables need to be configured in Project "CI/CD Pipelines" settings:
# REGISTRY_USERNAME - docker registry username
# REGISTRY_PASSWORD - docker registry password
# CI_JENKINS_TOKEN - Jenkins token used to trigger deploy builds

variables:
  REGISTRY: "registry.vaultit.org"
  IMAGE_PREFIX: "registry.vaultit.org/boldataapi"
  IMAGE_TAG: $CI_PIPELINE_ID
  IMAGE_NAME: "bol-data-api"
  IMAGE: $IMAGE_PREFIX/$IMAGE_NAME
  IMAGE_BUILD_DOCKER: $IMAGE_PREFIX/${IMAGE_NAME}_build
  IMAGE_BUILD_DIST: ${IMAGE_NAME}_build

  JENKINS_URL: "https://jenkins.vaultit.org"
  JENKINS_JOB_NAME_SUFFIX: "bol-data-api-deploy"
  JENKINS_BUILD_CAUSE: "Triggered by Gitlab CI Build"

stages:
  - test
  - build-docker
  - deploy

mirror-to-azure:
  stage: test
  dependencies: []
  image: alpine:latest
  allow_failure: true
  variables:
    AZURE_GIT_URL: https://dev.azure.com/vaultit/BOL/_git/BOL-Data-API
  before_script:
    - apk add --no-cache git
  script:
    - git config credential.helper '!f() { printf "username=%s\npassword=%s\n" "$AZURE_GIT_USERNAME" "$AZURE_GIT_PASSWORD"; }; f'
    - git remote add azure $AZURE_GIT_URL || git remote set-url azure $AZURE_GIT_URL
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        git push azure "$CI_COMMIT_TAG"
      else
        git push azure "origin/$CI_COMMIT_REF_NAME:refs/heads/$CI_COMMIT_REF_NAME"
      fi
    # If the push failed because the token expired, you'll get an authentication failed error
    # You have to go to Azure https://dev.azure.com/vaultit/_usersSettings/tokens, generate
    # a new personal access token with Code (Read & write) access, and paste it into GitLab CI
    # secret variable https://git.vaultit.org/Foretagsdeklaration/bol-data-api/variables/696

# NB: could be simplified by using `artifacts:reports:dotenv` starting GitLab version 12.9.
build-git-version:
  stage: test
  dependencies: []
  image: python:3.11-bookworm
  before_script:
    - pip install setuptools
  script:
    - cd server
    - python3 setup.py --version | tee ../version.txt
  artifacts:
    paths:
      - version.txt

test_newrelic:
  image: python:3.11-bookworm
  stage: test
  dependencies: []
  script:
    - pip3 install newrelic -c server/constraints.txt
    - newrelic-admin local-config infrastructure/newrelic.ini

test_alembic:
  stage: test
  dependencies: []
  image: python:3.11-bookworm
  services:
    - docker:dind
  variables:
    # build dependencies
    BUILD_DEPS: postgresql-client libpq-dev docker.io
    # https://pytest-mock-resources.readthedocs.io/en/latest/ci.html#gitlab
    DOCKER_TLS_CERTDIR: ""
    DOCKER_HOST: tcp://docker:2375
    PYTEST_MOCK_RESOURCES_HOST: docker
  before_script:
    - apt-get update -qq && apt-get install --no-install-recommends -y $BUILD_DEPS
    - pip3 install tox
  script:
    - (cd db/alembic && tox -e py3)
  tags:
    - docker

test:
  stage: test
  dependencies: []
  image: python:3.11-bookworm
  services:
    - postgres:latest
  variables:
    # build dependencies
    BUILD_DEPS: postgresql-client libssl-dev libpq-dev libjpeg-dev libffi-dev libxml2-dev libxslt1-dev
    # configuration for the postgres service
    POSTGRES_DB: bol
    POSTGRES_USER: dbuser
    POSTGRES_PASSWORD: dbpwd
    # configuration for alembic (our env.py reads DATABASE_URL)
    DATABASE_URL: **************************************
    # configuraton for our test suite
    BOL_TEST_SQLA_URL: postgresql://postgres/bol
    BOL_TEST_SQLA_USERNAME: dbuser
    BOL_TEST_SQLA_PASSWORD: dbpwd
  before_script:
    - apt-get update -qq && apt-get install --no-install-recommends -y $BUILD_DEPS
    - pip install tox -r db/alembic/requirements.txt
    - (cd db/alembic && PYTHONPATH=migrations alembic upgrade head)
  script:
    - (cd server && tox)
  tags:
    - docker

build-docker-amd64:
  image: docker:20.10.17
  stage: build-docker
  dependencies:
    - build-git-version
  before_script:
    - docker login -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD $REGISTRY
  script:
    - echo VERSION=`cat version.txt`
    - export VERSION=`cat version.txt`
    - docker build --pull --no-cache -t $IMAGE:$IMAGE_TAG --build-arg build=$CI_PIPELINE_ID --build-arg VERSION=$VERSION -f Dockerfile .
    - docker push $IMAGE:$IMAGE_TAG
  after_script:
    - docker rmi $IMAGE:$IMAGE_TAG
  tags:
    - docker

build-docker-arm64:
  image: docker:20.10.17
  stage: build-docker
  dependencies:
    - build-git-version
  before_script:
    - docker login -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD $REGISTRY
  script:
    - echo VERSION=`cat version.txt`
    - export VERSION=`cat version.txt`
    - docker buildx create --use
    - docker run --rm --privileged multiarch/qemu-user-static --reset -p yes # https://github.com/docker/buildx/issues/584#issuecomment-827122004
    - docker buildx build --platform linux/arm64 --pull --no-cache -t $IMAGE:$IMAGE_TAG-arm64 --push --build-arg build=$CI_PIPELINE_ID --build-arg VERSION=$VERSION -f Dockerfile .
  tags:
    - docker

tag-docker-image:
  image: docker:20.10.17
  stage: deploy
  dependencies:
    - build-docker-amd64
    - build-docker-arm64
  before_script:
    - docker login -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD $REGISTRY
  script:
    - docker pull $IMAGE:$IMAGE_TAG
    - docker pull $IMAGE:$IMAGE_TAG-arm64
    - docker manifest create $IMAGE:latest $IMAGE:$IMAGE_TAG $IMAGE:$IMAGE_TAG-arm64
    - docker manifest push $IMAGE:latest
  after_script:
    - docker rmi $IMAGE:$IMAGE_TAG || true
    - docker rmi $IMAGE:$IMAGE_TAG-arm64 || true
    - docker manifest rm $IMAGE:latest || true
  tags:
    - docker
  only:
    - master

.deploy_job_template: &deploy_job_definition
  stage: deploy
  image: alpine:latest
  dependencies:
    - build-docker-amd64
    - build-docker-arm64
  before_script:
    - apk add --no-cache curl
  script:
    - curl -k -G "$JENKINS_URL/buildByToken/buildWithParameters"
           --data-urlencode "job=$JENKINS_JOB_NAME_PREFIX-$JENKINS_JOB_NAME_SUFFIX"
           --data-urlencode "token=$CI_JENKINS_TOKEN"
           --data-urlencode "docker_tag=$CI_PIPELINE_ID"
           --data-urlencode "deploy_environment=$ENVIRONMENT"
           --data-urlencode "git_commit_ref=$CI_COMMIT_SHA"
           --data-urlencode "cause=$JENKINS_BUILD_CAUSE"
           $EXTRA_ARGS
  variables:
    EXTRA_ARGS: ""
  when: manual

deploy_alpha:
  <<: *deploy_job_definition
  environment:
    name: alpha
    url: https://bol-data-api.alpha.vaultit.org/api/v1/boldata/version
  variables:
    JENKINS_JOB_NAME_PREFIX: id06
    JENKINS_TOKEN: "CI_JENKINS_TOKEN"
    ENVIRONMENT: alpha

deploy_beta:
  <<: *deploy_job_definition
  environment:
    name: beta
    url: https://bol-data-api.beta.vaultit.org/api/v1/boldata/version
  variables:
    JENKINS_JOB_NAME_PREFIX: id06
    JENKINS_TOKEN: "CI_JENKINS_TOKEN"
    ENVIRONMENT: beta
  only:
    - master

alembic_alpha:
  <<: *deploy_job_definition
  variables:
    JENKINS_JOB_NAME_PREFIX: id06
    JENKINS_JOB_NAME_SUFFIX: "bol-data-api-db-migration"
    JENKINS_TOKEN: "CI_JENKINS_TOKEN"
    ENVIRONMENT: alpha
    EXTRA_ARGS: "--data-urlencode k8s_job_name=boldataapi-db-migration-0"

alembic_beta:
  <<: *deploy_job_definition
  variables:
    JENKINS_JOB_NAME_PREFIX: id06
    JENKINS_JOB_NAME_SUFFIX: "bol-data-api-db-migration"
    JENKINS_TOKEN: "CI_JENKINS_TOKEN"
    ENVIRONMENT: beta
    EXTRA_ARGS: "--data-urlencode k8s_job_name=boldataapi-db-migration-0"
  only:
    - master
